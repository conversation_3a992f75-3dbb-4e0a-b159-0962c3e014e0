问题规划
ES6 | R\RN | Redux | TypeScript | CSS 
--- | --- | --- | --- | --- 
5 | 7| 2 | 4 | 3

Webpack | web通识 | 英语
--- | --- | ---
1 | 3 | 1

总计: 26

# ES6
## 1. 使用箭头函数(arrow functions)的优点是什么
作用域安全：在箭头函数之前，每一个新创建的函数都有定义自身的 this 值(在构造函数中是新对象；在严格模式下，函数调用中的 this 是未定义的；如果函数被称为“对象方法”，则为基础对象等)，但箭头函数不会，它会使用封闭执行上下文的 this 值。
简单：箭头函数易于阅读和书写
清晰：当一切都是一个箭头函数，任何常规函数都可以立即用于定义作用域。开发者总是可以查找 next-higher 函数语句，以查看 this 的值

## 2. 何为纯函数(pure function)
一个纯函数是一个不依赖于其作用域之外的变量,且不改变外部变量状态的函数，这也意味着一个纯函数对于同样的参数总是返回同样的结果。

## 3. 闭包是个啥
闭包就是将变量和函数一同打包返回，以供其它方法调用的模式，这种模式超越了调用栈的逐级返回模式，或者说返回的「包」超越了调用栈本身，所以显得格外特别。通常的外在表现是，函数在一个更低的调用栈中用到被打包的变量，或者说，函数在在一个更小的作用域用到外部的变量。
### 4. 闭包在哪里用到过。（引申问题）
之前所述的箭头函数中，只要调用了定义域意外的变量（特别是显眼的`this`），均可视作闭包。

## 5. 为什么虚拟 dom 会提高性能?
虚拟 dom 相当于在 js 和真实 dom 中间加了一个缓存，利用 dom diff 算法避免了没有必要的 dom 操作，从而提高性能。
用 JavaScript 对象结构表示 DOM 树的结构；然后用这个树构建一个真正的 DOM 树，插到文档当中当状态变更的时候，重新构造一棵新的对象树。然后用新的树和旧的树进行比较，记录两棵树差异把 2 所记录的差异应用到步骤 1 所构建的真正的 DOM 树上，视图就更新了。

# React，React-Native

## 6. react diff 原理
* 把树形结构按照层级分解，只比较同级元素。
* 给列表结构的每个单元添加唯一的 key 属性，方便比较。
* React 只会匹配相同 class 的 component（这里面的 class 指的是组件的名字）
* 合并操作，调用 component 的 setState 方法的时候, React 将其标记为 dirty.到每一个事件循环结束, React 检查所有标记 dirty 的 component 重新绘制.
* 选择性子树渲染。开发人员可以重写 shouldComponentUpdate 提高 diff 的性能。

## 7. 何为高阶组件(higher order component)
高阶组件是一个以组件为参数并返回一个新组件的函数，即组件装饰器。HOC 运行重用代码、逻辑和引导抽象。最常见的可能是 Redux 的 connect 函数。除了简单分享工具库和简单的组合，HOC最好的方式是共享 React 组件之间的行为。如果你发现你在不同的地方写了大量代码来做同一件事时，就应该考虑将代码重构为可重用的 HOC。

## 8. 展示组件(Presentational component)和容器组件(Container component)之间有何不同
* 展示组件关心组件看起来是什么。展示专门通过 props 接受数据和回调，并且几乎不会有自身的状态，但当展示组件拥有自身的状态时，通常也只关心 UI 状态而不是数据的状态。
* 容器组件则更关心组件是如何运作的。容器组件会为展示组件或者其它容器组件提供数据和行为(behavior)，它们会调用 Flux actions，并将其作为回调提供给展示组件。容器组件经常是有状态的，因为它们是(其它组件的)数据源。

## 9. 调用 setState 之后发生了什么？
在代码中调用setState函数之后，React 会将传入的参数对象与组件当前的状态合并，然后触发所谓的调和过程（Reconciliation）。经过调和过程，React 会以相对高效的方式根据新的状态构建 React 元素树并且着手重新渲染整个UI界面。在 React 得到元素树之后，React 会自动计算出新的树与老树的节点差异，然后根据差异对界面进行最小化重渲染。在差异计算算法中，React 能够相对精确地知道哪些位置发生了改变以及应该如何改变，这就保证了按需更新，而不是全部重新渲染。

## 10. 应该在生命周期中的哪一步发起AJAX请求？
我们应当将AJAX 请求放到 componentDidMount 函数中执行，主要原因有下：

* React 下一代调和算法 Fiber 会通过开始或停止渲染的方式优化应用性能，其会影响到 componentWillMount 的触发次数。对于 componentWillMount 这个生命周期函数的调用次数会变得不确定，React 可能会多次频繁调用 componentWillMount。如果我们将 AJAX 请求放到 componentWillMount 函数中，那么显而易见其会被触发多次，自然也就不是好的选择。

* 如果我们将 AJAX 请求放置在生命周期的其他函数中，我们并不能保证请求仅在组件挂载完毕后才会要求响应。如果我们的数据请求在组件挂载之前就完成，并且调用了setState函数将数据添加到组件状态中，对于未挂载的组件则会报错。而在 componentDidMount 函数中进行 AJAX 请求则能有效避免这个问题。

## 11. 组件的状态(state)和属性(props)之间有何不同
* State 是一种数据结构，用于组件挂载时所需数据的默认值。State 可能会随着时间的推移而发生突变，但多数时候是作为用户事件行为的结果。
* Props(properties 的简写)则是组件的配置。props 由父组件传递给子组件，并且就子组件而言，props 是不可变的(immutable)。组件不能改变自身的 props，但是可以把其子组件的 props 放在一起(统一管理)。Props 也不仅仅是数据--回调函数也可以通过 props 传递。

## 12. shouldComponentUpdate 的作用是啥以及为何它这么重要？
shouldComponentUpdate 允许我们手动地判断是否要进行组件更新，根据组件的应用场景设置函数的合理返回值能够帮我们避免不必要的更新。

# Redux

## 13. redux是个啥
Redux 的基本思想是整个应用的 state 保持在一个单一的 store 中。store 就是一个简单的 javascript 对象，而改变应用 state 的唯一方式是在应用中触发 actions，然后为这些 actions 编写 reducers 来修改 state。整个 state 转化是在 reducers 中完成，并且不应该有任何副作用。


## 14. Redux Thunk 的作用是什么
Redux thunk 是一个允许你编写返回一个函数而不是一个 action 的 actions creators 的中间件。如果满足某个条件，thunk 则可以用来延迟 action 的派发(dispatch)，这可以处理异步 action 的派发(dispatch)。

# TypeScript

## 15. 类型声明里 「&」和「|」有什么作用？
&符号代表联合两个类型，相当于合并类型，| 代表两类型之一，即「或」

## 16. `interface`是个啥。
`interface`即「接口」，是在应用程序中充当契约的结构。它定义了要遵循的类的语法，意味着实现接口的类必须实现它的所有成员。
它不能被实例化，但是可以被实现它的类对象引用。

### 17. 如何定义可选属性、只读属性（引申问题）
带有可选属性的接口与普通的接口定义差不多，只是在可选属性名字定义的后面加一个`?`符号。
一些对象属性只能在对象刚刚创建的时候修改其值。 你可以在属性名前用 readonly来指定只读属性。初始化赋值后， 属性便被改变了。

## 18. `keyof` 关键词有什么用
在JavaScript中属性名称作为参数的API是相当普遍的，但是到目前为止还没有表达在那些API中出现的类型关系。
输入索引类型查询或keyof，索引类型查询keyof T产生的类型是T的属性名称。keyof T的类型被认为是string的子类型。

# CSS相关

## 19. box-sizing是什么

设置CSS盒模型为标准模型或IE模型。标准模型的宽度只包括content，而IE模型包括border和padding
`box-sizing`属性可以为三个值之一：
`content-box`(内容)，默认值，只计算内容的宽度，border和padding不计算入width之内
`padding-box`(内边距)，padding计算入宽度内
`border-box`(边框)，border和padding计算入宽度之内

## 20. 流式布局与响应式布局的区别

流式布局
使用非固定像素来定义网页内容，也就是百分比布局，通过盒子的宽度设置成百分比来根据屏幕的宽度来进
行伸缩，不受固定像素的限制，内容向两侧填充。
响应式开发
利用CSS3 中的 Media Query(媒介查询)，通过查询 screen 的宽度来指定某个宽度区间的网页布局。

超小屏幕(移动设备) 768px 以下
小屏设备 768px-992px
中等屏幕 992px-1200px
宽屏设备 1200px 以上

由于响应式开发显得繁琐些，一般使用第三方响应式框架来完成，比如 bootstrap 来完成一部分工作，当然也 可以自己写响应式。

## 21. `line-height1.5`和`line-height:150%`的区别

区别体现在子元素继承时，如下：

父元素设置`line-height:1.5`会直接继承给子元素，子元素根据自己的`font-size`再去计算子元素自己的`line-height`。
父元素设置`line-height:150%`是计算好了`line-height`值，然后把这个计算值给子元素继承，子元素继承拿到的就是最终的值了。此时子元素设置`font-size就对其line-height`无影响了。

# Webpack

## 22. 如何告诉 React 它应该编译生产环境版本？
通常情况下我们会使用 Webpack 的 DefinePlugin 方法来将 NODE_ENV 变量值设置为 production。编译版本中 React 会忽略 propType 验证以及其他的告警信息，同时还会降低代码库的大小，React 使用了 Uglify 插件来移除生产环境下不必要的注释等信息。

# web通识

## 23. 从输入URL到页面加载的过程？
输入url后会开一个新的网络线程
整的http请求中间的过程, dns查询，tcp/ip链接，五层因特网协议栈等等，
服务器接收到请求，后台应用接收到请求，后台代码处理业务请求。
后台和前台的http交互，http报文结构，场景头部，cookie，跨域，web安全，http缓存，http2.0，https等
浏览器接收到http数据包后的解析流程，解析html，词法分析然后解析成dom树、解析css生成css规则树、合并成render树，然后layout、painting渲染、里面可能还包括复合图层的合成、GPU绘制、外链处理、加载顺序等
JS引擎解析过程，JS的解释，预处理，执行上下文，VO，作用域链，this，回收机制等。

## 24. 啥叫异步操作
所谓"异步"，简单说就是一个任务分成两段，先执行第一段，然后转而执行其他任务，等做好了准备，再回过头执行第二段。
比如，有一个任务是读取文件进行处理，任务的第一段是向操作系统发出请求，要求读取文件。然后，程序执行其他任务，等到操作系统返回文件，再接着执行任务的第二段（处理文件）。这种不连续的执行，就叫做异步。
相应地，连续的执行就叫做同步。由于是连续执行，不能插入其他任务，所以操作系统从硬盘读取文件的这段时间，程序只能干等着。
### 25. 在js中如何实现异步操作（引申问题）
回调函数，Promise，Generator，async


# 英语

## 26. 文档阅读
以下说的啥？

The Difference Between Interfaces and Type Aliases.

As we mentioned, type aliases can act sort of like interfaces; however, there are some subtle differences.
One difference is that interfaces create a new name that is used everywhere. Type aliases don’t create a new name — for instance, error messages won’t use the alias name. In the code below, hovering over interfaced in an editor will show that it returns an Interface, but will show that aliased returns object literal type.
```ts
type Alias = { num: number }
interface Interface {
    num: number;
}
declare function aliased(arg: Alias): Alias;
declare function interfaced(arg: Interface): Interface;
```
In older versions of TypeScript, type aliases couldn’t be extended or implemented from (nor could they extend/implement other types). As of version 2.7, type aliases can be extended by creating a new intersection type e.g. `type Cat = Animal & { purrs: true }`.
Because an ideal property of software is being open to extension, you should always use an interface over a type alias if possible.
On the other hand, if you can’t express some shape with an interface and you need to use a union or tuple type, type aliases are usually the way to go.
