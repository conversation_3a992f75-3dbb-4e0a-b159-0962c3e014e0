/**
 * @description 菜单信息
 */
import * as deleteSysFunction from './deleteSysFunction';
import * as getFunctionDTOTreeList from './getFunctionDTOTreeList';
import * as getFunctionTreeList from './getFunctionTreeList';
import * as getFunctionsByUserId from './getFunctionsByUserId';
import * as postGetFunctionsByUserId from './postGetFunctionsByUserId';
import * as getSpecialTypeByFunction from './getSpecialTypeByFunction';
import * as insertSysFunction from './insertSysFunction';
import * as isFunctionRecordByUserRole from './isFunctionRecordByUserRole';
import * as isRepeatFunctionRecord from './isRepeatFunctionRecord';
import * as postIsRepeatFunctionRecord from './postIsRepeatFunctionRecord';
import * as queryFunctionListByRoleId from './queryFunctionListByRoleId';
import * as updateSysFunction from './updateSysFunction';

export {
  deleteSysFunction,
  getFunctionDTOTreeList,
  getFunctionTreeList,
  getFunctionsByUserId,
  postGetFunctionsByUserId,
  getSpecialTypeByFunction,
  insertSysFunction,
  isFunctionRecordByUserRole,
  isRepeatFunctionRecord,
  postIsRepeatFunctionRecord,
  queryFunctionListByRoleId,
  updateSysFunction,
};
