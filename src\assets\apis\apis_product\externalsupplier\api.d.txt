declare namespace defs {
  export namespace externalsupplier {
    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class DisabilityImp {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractCode */
      contractCode: string;

      /** contractName */
      contractName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** departmentName */
      departmentName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column PD_DISABILITY_IMP.DISABILITY_IMP_ID           ibatorgenerated Mon Mar 02 11:37:49 CST 2015 */
      disabilityImpId: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column PD_DISABILITY_IMP.E_AMT           ibatorgenerated Mon Mar 02 11:37:49 CST 2015 */
      eAmt: string;

      /** empCode */
      empCode: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column PD_DISABILITY_IMP.EMP_HIRE_SEP_ID           ibatorgenerated Mon Mar 02 11:37:49 CST 2015 */
      empHireSepId: string;

      /** empName */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column PD_DISABILITY_IMP.END_MONTH           ibatorgenerated Mon Mar 02 11:37:49 CST 2015 */
      endMonth: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** idCardNum */
      idCardNum: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column PD_DISABILITY_IMP.P_AMT           ibatorgenerated Mon Mar 02 11:37:49 CST 2015 */
      pAmt: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** providerCode */
      providerCode: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column PD_DISABILITY_IMP.PROVIDER_CODE           ibatorgenerated Mon Mar 02 11:37:49 CST 2015 */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupId */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** prvdGroupName */
      prvdGroupName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column PD_DISABILITY_IMP.START_MONTH           ibatorgenerated Mon Mar 02 11:37:49 CST 2015 */
      startMonth: string;

      /** status */
      status: string;

      /** statusName */
      statusName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** subcontractId */
      subcontractId: string;

      /** subcontractName */
      subcontractName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column PD_DISABILITY_IMP.TOTAL           ibatorgenerated Mon Mar 02 11:37:49 CST 2015 */
      total: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class DisabilityQuery {
      /** 大合同编号 */
      contractId: string;

      /** 大合同名称 */
      contractName: string;

      /** 客户id */
      custId: string;

      /** 客户姓名 */
      custName: string;

      /** 供应商名称 */
      departmentName: string;

      /** 唯一号 */
      empCode: string;

      /** 雇员姓名 */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** 身份证 */
      idCardNum: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商id */
      providerId: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** 供应商集团名称 */
      prvdGroupName: string;

      /** startIndex */
      startIndex: number;

      /** 小合同编号 */
      subcontractId: string;

      /** 小合同名称 */
      subcontractName: string;
    }

    export class EmployeeFee {
      /** add */
      add: boolean;

      /** 调整情况分类：1费用添加、2费用清空、3比例调整、4基数调整、5基数+比例调整、6其他调整 */
      adjSituType: string;

      /** 金额 */
      amount: number;

      /** 金额(不含税) */
      amtNoTax: string;

      /** 附加税费 */
      atr: string;

      /** 基数绑定级次 */
      baseBindingLevel: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单起始月 */
      billStartMonth: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
      calculationOrder: string;

      /** 类型 */
      category: string;

      /** 收费结束时间 */
      chargeEndDate: string;

      /** 缴费频率 */
      chargeRate: string;

      /** 收费起始时间 */
      chargeStartDate: string;

      /** cityId */
      cityId: number;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 套餐id */
      comboId: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 企业附加金额 */
      eAdditionalAmt: number;

      /** 企业金额 */
      eAmt: number;

      /** 企业基数 */
      eBase: number;

      /** eBillTemplt */
      eBillTemplt: string;

      /** 企业账单模板id */
      eBillTempltId: string;

      /** 企业计算方法:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
      eCalculationMethod: string;

      /** 企业收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      eFeeMonth: string;

      /** 收费模板名 */
      eFeeTemplt: string;

      /** 企业收费模板id */
      eFeeTempltId: string;

      /** 企业频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      eFrequency: string;

      /** 企业最高比例 */
      eMaxRatio: number;

      /** 企业最低比例 */
      eMinRatio: number;

      /** 企业提前几个月收,默认为0，选项0-3 */
      eMonthInAdvance: string;

      /** 企业精确值0：0位小数1：1位小数2：2位小数5： 精确值 */
      ePrecision: string;

      /** 企业比例 */
      eRatio: number;

      /** 企业比例步长 */
      eRatioStep: number;

      /** 费用段历史id */
      empFeeHisId: string;

      /** 费用段id */
      empFeeId: string;

      /** 费用段操作id */
      empFeeOprId: string;

      /** 员工入离职id */
      empHireSepId: string;

      /** 员工id */
      empId: string;

      /** 外部供应商账单起始月 */
      exBillStartMonth: string;

      /** 供应商收费月 */
      exFeeMonth: string;

      /** 供应商收费模板名 */
      exFeeTemplt: string;

      /** 外部供应商收费模板id */
      exFeeTempltId: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否一次性付费 */
      isOneTimePay: string;

      /** 是否显示 1:是0:否 */
      isShow: string;

      /** 是否更新月度表1:是0:否 */
      isUptFeeMon: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 原金额 */
      oldAmount: string;

      /** 原账单起始月 */
      oldBillStartMonth: string;

      /** 个人附加金额 */
      pAdditionalAmt: number;

      /** 个人金额 */
      pAmt: number;

      /** 个人基数 */
      pBase: number;

      /** pBillTemplt */
      pBillTemplt: string;

      /** 个人部分账单模板id */
      pBillTempltId: string;

      /** 个人计算方式:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
      pCalculationMethod: string;

      /** 个人收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      pFeeMonth: string;

      /** 收费模板名 */
      pFeeTemplt: string;

      /** 个人收费模板id */
      pFeeTempltId: string;

      /** 个人频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      pFrequency: string;

      /** 个人最高比例 */
      pMaxRatio: number;

      /** 个人最低比例 */
      pMinRatio: number;

      /** 个人提前几个月收,提前几个月收,默认为0，选项0-3 */
      pMonthInAdvance: string;

      /** 个人精度0：0位小数1：1位小数2：2位小数5： 精确值 */
      pPrecision: string;

      /** 个人比例 */
      pRatio: number;

      /** 个人比例步长 */
      pRatioStep: number;

      /** 支付频率1:月缴,2季度缴,3年缴(不足一年按年缴),4年缴(不足一年按月缴) */
      payFrequency: string;

      /** 支付最后服务年月 */
      payLastServiceMonth: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 产品id */
      productId: string;

      /** 产品名称 */
      productName: string;

      /** 产品比例id */
      productRatioId: string;

      /** 产品比例名称 */
      productRatioName: string;

      /** 产品类型id */
      productTypeId: number;

      /** 供应商id */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 报价单id */
      quotationId: string;

      /** 报价单子项id */
      quotationItemId: string;

      /** 应收金额 */
      receivableAmt: number;

      /** 应收几个月 */
      receivableMonth: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 社保组id */
      ssGroupId: string;

      /** 社保组名称 */
      ssGroupName: string;

      /** 社保福利包id */
      ssWelfarePkgId: string;

      /** 社保福利包名称 */
      ssWelfarePkgName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 标签 */
      tag: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 增值税 */
      vat: string;

      /** 增值税率 */
      vatr: string;

      /** 实收金额 */
      verifyAmt: number;

      /** 实收金额(不含税) */
      verifyAmtNoTax: string;

      /** 实收金额增值税 */
      verifyAmtVat: string;
    }

    export class EmployeeFeeMonth {
      /** add */
      add: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.AMOUNT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      amount: string;

      /** 金额(不含税) */
      amtNoTax: string;

      /** 附加税费 */
      atr: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.BILL_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 账单月止 */
      billEndMonth: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.BILL_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 账单月起 */
      billMonth: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** chargeEndDate */
      chargeEndDate: string;

      /** chargeStartDate */
      chargeStartDate: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 大合同ID */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_ADDITIONAL_AMT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eAdditionalAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_AMT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_BASE	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eBase: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_BILL_TEMPLT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eBillTempltId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_FEE_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eFeeMonth: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_FEE_TEMPLT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eFeeTempltId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_FREQUENCY	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eFrequency: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_MONTH_IN_ADVANCE	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eMonthInAdvance: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.E_RATIO	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      eRatio: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.EMP_FEE_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      empFeeId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.EMP_FEE_MONTH_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      empFeeMonthId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.EMP_HIRE_SEP_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      empHireSepId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.EMP_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      empId: string;

      /** 结束查询月份 */
      endMonth: string;

      /** exBillMonth */
      exBillMonth: string;

      /** 供应商账单起始月 */
      exBillStartMonth: string;

      /** exFeeTempltId */
      exFeeTempltId: string;

      /** 供应商收费模板Name */
      exFeeTempltName: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** frequencyName */
      frequencyName: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否是供应商费用信息月度信息 0,1 */
      isPdFlag: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.IS_VALID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      isValid: string;

      /** 模拟人 */
      mimicBy: string;

      /** 月份数 */
      monthNums: string;

      /** noChange */
      noChange: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_ADDITIONAL_AMT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pAdditionalAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_AMT	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_BASE	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pBase: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_BILL_TEMPLT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pBillTempltId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_FEE_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pFeeMonth: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_FEE_TEMPLT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pFeeTempltId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_FREQUENCY	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pFrequency: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_MONTH_IN_ADVANCE	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pMonthInAdvance: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.P_RATIO	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      pRatio: string;

      /** 供应商的报价单名称 */
      pdQuotationName: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.PRODUCT_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      productId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.PRODUCT_RATIO_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      productRatioId: string;

      /** productRatioName */
      productRatioName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.QUOTATION_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      quotationId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.QUOTATION_DETAIL_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      quotationItemId: string;

      /** quotationName */
      quotationName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.REMARK	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.SERVICE_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 收费月止 */
      serviceEndMonth: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.SERVICE_MONTH	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 收费月起 */
      serviceMonth: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.SS_GROUP_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      ssGroupId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_EMP_FEE_MONTH.SS_WELFARE_PKG_ID	  	  ibatorgenerated Wed Dec 14 16:34:27 CST 2011 */
      ssWelfarePkgId: string;

      /** 开始查询月份 */
      startMonth: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同ID */
      subcontractId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 增值税 */
      vat: string;

      /** 增值税率 */
      vatr: string;
    }

    export class EmployeeFeeMonthVO {
      /** 按钮查询状态 */
      buttonQueryStatus: string;

      /** 费用段月度 */
      employeeFeeMonth: defs.externalsupplier.EmployeeFeeMonth;

      /** num */
      num: number;

      /** 状态 */
      state: string;
    }

    export class EmployeeHireSep {
      /** 开户人姓名 */
      accountEmployeeName: string;

      /** 实际工作地 */
      actualWorkLoc: string;

      /** add */
      add: boolean;

      /** 增员确认人 */
      addConfirmBy: string;

      /** 增员确认时间 */
      addConfirmDate: string;

      /** 增员过程 */
      addConfirmPro: string;

      /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
      addConfirmStatus: string;

      /** 增员状态名称 */
      addConfirmStatusName: string;

      /** 增员接单确认时间 */
      addPerfectBy: string;

      /** 增员接单确认人 */
      addPerfectDate: string;

      /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
      addReason: string;

      /** 增员备注 */
      addRemark: string;

      /** 年龄 */
      age: string;

      /** 变更确认人 */
      alterConfirmBy: string;

      /** 变更确认时间 */
      alterConfirmDate: string;

      /** 变更确认过程 */
      alterConfirmPro: string;

      /** 变更接单确认人 */
      alterPerfectBy: string;

      /** 变更接单确时间 */
      alterPerfectDate: string;

      /** 变更备注 */
      alterRemark: string;

      /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
      alterStatus: string;

      /** 变更状态名称 */
      alterStatusName: string;

      /** 大区类型 */
      areaType: string;

      /** 大区类型名称 */
      areaTypeName: string;

      /** 接单城市id */
      assigneeCityId: string;

      /** 接单客服name */
      assigneeCs: string;

      /** 接单客服 */
      assigneeCsId: string;

      /** 接单方 */
      assigneeProvider: string;

      /** 接单方 */
      assigneeProviderId: string;

      /** 派单客服name */
      assignerCs: string;

      /** 派单客服 */
      assignerCsId: string;

      /** 派单方 */
      assignerProvider: string;

      /** 派单方 */
      assignerProviderId: string;

      /** 派单类型1 执行单2 协调单3 收集单 */
      assignmentType: string;

      /** 关联状态 */
      associationStatus: string;

      /** 银行卡号 */
      bankAcct: string;

      /** 银行卡更新人 */
      bankCardUpdateBy: string;

      /** 银行卡更新时间 */
      bankCardUpdateDt: string;

      /** baseInfo主键 */
      baseInfoId: string;

      /** 批次号,用于生成社保服务信息 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单模板id */
      billTempltId: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 类型 */
      category: string;

      /** 离职证明电子版本 */
      certificateSpId: string;

      /** certificateStatusName */
      certificateStatusName: string;

      /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
      changeMethod: string;

      /** 收费截至日期 */
      chargeEndDate: string;

      /** 城市id */
      cityId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 商保订单状态 */
      commInsurStatus: string;

      /** 商保订单状态name */
      commInsurStatusName: string;

      /** 确认备注 */
      confirmRemark: string;

      /** 联系电话1，电话 */
      contactTel1: string;

      /** 联系电话2，手机 */
      contactTel2: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同id */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** contractStartDate */
      contractStartDate: string;

      /** contractStopDate */
      contractStopDate: string;

      /** 法人单位id */
      corporationId: string;

      /** 法人单位名称 */
      corporationName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户方内部编号 */
      custInternalNum: string;

      /** 客户姓名 */
      custName: string;

      /** 缴费实体id */
      custPayEntityId: number;

      /** 缴费实体 */
      custPayEntityName: string;

      /** 客户类型 */
      custType: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** 客户规模 */
      customerSize: string;

      /** 类型: 1,正常 2,大客户  */
      dataType: number;

      /** 申报工资 */
      decSalary: string;

      /** del */
      del: boolean;

      /** email */
      email: string;

      /** 客户端增员ID */
      empAddId: string;

      /** 客户端变更ID */
      empAlterId: string;

      /** feeId数组 */
      empFeeIdArray: string;

      /** 历史表主键 */
      empHireSepHisId: string;

      /** 员工入离职id */
      empHireSepId: string;

      /** 入职主记录ID */
      empHiresepMainId: string;

      /** 员工id */
      empId: string;

      /** 停缴id */
      empStopId: string;

      /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
      empStopProcessState: string;

      /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
      empType: string;

      /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
      empTypeId: number;

      /** 人员分类 */
      empTypeName: string;

      /** 唯一号 */
      employeeCode: string;

      /** 费用段列表 */
      employeeFeeList: Array<defs.externalsupplier.EmployeeFee>;

      /** 雇员姓名 */
      employeeName: string;

      /** 雇员状态 */
      employeeStatus: string;

      /** endIndex */
      endIndex: number;

      /** enhancedAgent */
      enhancedAgent: string;

      /** enhancedAgentName */
      enhancedAgentName: string;

      /** 外部供应商收费模板 */
      exFeeTemplt: string;

      /** 外部供应商账单id */
      exFeeTempltId: string;

      /** exQuotationFeeList */
      exQuotationFeeList: Array<defs.externalsupplier.EmployeeFee>;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      feeMonth: string;

      /** 收费模板名称 */
      feeTemplt: string;

      /** 收费模板id */
      feeTempltId: string;

      /** 档案柜编号 */
      fileCabCode: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 文件夹编号 */
      folderCode: string;

      /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      frequency: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 是否关联订单 */
      hasAssociations: number;

      /** 入职竞争对手 */
      hireCompetitor: string;

      /** 入职时间 */
      hireDt: string;

      /** 入职时间止 */
      hireEndDt: string;

      /** 入职报价单 */
      hireQuotationId: string;

      /** 入职备注 */
      hireRemark: string;

      /** 入职时间起 */
      hireStartDt: string;

      /** 证件号码 */
      idCardNum: string;

      /** 证件类型 */
      idCardType: string;

      /** 接单客服name */
      idCardTypeName: string;

      /** inId */
      inId: string;

      /** 内外部类型(1内部2外部3全部) */
      innerType: string;

      /** 是否需要签订劳动合同 */
      isArchive: string;

      /** 是否归档名称 */
      isArchiveName: string;

      /** 银行卡是否上传 */
      isBankCardUpload: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否入职呼叫 */
      isHireCall: string;

      /** 是否入职呼叫名称 */
      isHireCallName: string;

      /** 身份证是否上传 */
      isIDCardUpload: string;

      /** 是否单立户1 是0 否 */
      isIndependent: string;

      /** 劳动合同是否上传 */
      isLaborContractUpload: string;

      /** 是否需要签订劳动合同 */
      isNeedSign: string;

      /** 是否退费 0否  1是 */
      isRefund: string;

      /** 是否集中一地投保 */
      isSameInsur: string;

      /** 是否集中一地投保中文 */
      isSameInsurName: string;

      /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
      isSepCall: string;

      /** 是否离职呼叫名 */
      isSepCallName: string;

      /** 是否有统筹医疗 */
      isThereACoordinateHealth: string;

      /** 是否有统筹医疗名称 */
      isThereACoordinateHealthText: string;

      /** 是否有社保卡 */
      isThereSsCard: string;

      /** 是否有社保卡名称 */
      isThereSsCardText: string;

      /** 离职动态模板json */
      jsonStr: Array<object>;

      /** 劳动关系单位 */
      laborRelationUnit: string;

      /** 责任客服 */
      liabilityCs: string;

      /** 材料列表 */
      materialList: Array<defs.externalsupplier.Material>;

      /** materialSignStatus */
      materialSignStatus: number;

      /** materialSignStatusName */
      materialSignStatusName: string;

      /** 离职材料电子版本id */
      materialSpId: string;

      /** materialStatusName */
      materialStatusName: string;

      /** 模拟人 */
      mimicBy: string;

      /** 操作方式  单立户1、大户2 */
      modeOfOperation: string;

      /** 提前几个月收,默认为0，选项0-3 */
      monthInAdvance: string;

      /** 后指针 */
      nextPointer: string;

      /** noChange */
      noChange: boolean;

      /** 非社保列表 */
      nonSsGroupList: Array<defs.externalsupplier.EmployeeFee>;

      /** 银行名称 */
      openBankName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 挂起原因 */
      pendingReason: string;

      /** 挂起原因中文 */
      pendingReasonName: string;

      /** 人员分类id */
      personCategoryId: string;

      /** 职位id */
      positionId: string;

      /** 前指针 */
      prevPointer: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 流程实例化id */
      processInsId: string;

      /** 供应商编码 */
      providerCode: string;

      /** 供应商客服 */
      providerCs: string;

      /** 供应商客服id */
      providerCsId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 代理人 */
      proxyBy: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 供应商集团 */
      prvdGroupName: string;

      /** 离职材料签订形式 */
      quitSignType: number;

      /** quitSignTypeName */
      quitSignTypeName: string;

      /** 电子离职合同任务主键 */
      quitTaskId: number;

      /** 报价单编码 */
      quotationCode: string;

      /** 报价单名称 */
      quotationName: string;

      /** 减少详细原因 */
      reduceDetailReason: string;

      /** 减原详细原因名称 */
      reduceDetailReasonName: string;

      /** 客户端减员ID */
      reduceId: string;

      /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
      reduceReason: string;

      /** 减员原因名称 */
      reduceReasonName: string;

      /** 参考日期，页面传入 */
      referDate: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** riskPremiumRatio */
      riskPremiumRatio: string;

      /** riskSharingRatio */
      riskSharingRatio: string;

      /** 报入职人员id */
      rptHireBy: string;

      /** 报入职人 */
      rptHireByName: string;

      /** 报入职时间 */
      rptHireDt: string;

      /** 报入职日期止 */
      rptHireEndDt: string;

      /** 报入职日期起 */
      rptHireStartDt: string;

      /** 报离职人员id */
      rptSepBy: string;

      /** 报离职人 */
      rptSepByName: string;

      /** 报离职日期 */
      rptSepDt: string;

      /** 报离职日期止 */
      rptSepEndDt: string;

      /** 报离职日期起 */
      rptSepStartDt: string;

      /** 用章对象 */
      sealObject: string;

      /** 用章类型 */
      sealType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 离职确认人 */
      sepConfirmBy: string;

      /** 离职确认日期 */
      sepConfirmDate: string;

      /** 离职确认历史 */
      sepConfirmHis: string;

      /** 离职确认进程 */
      sepConfirmPro: string;

      /** 离职确认状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
      sepConfirmStatus: string;

      /** 离职状态名称 */
      sepConfirmStatusName: string;

      /** 离职详细原因 */
      sepDetailReason: string;

      /** 离职详细原因名称 */
      sepDetailReasonName: string;

      /** 离职日期 */
      sepDt: string;

      /** 离职时间止 */
      sepEndDt: string;

      /** 离职接单确认人 */
      sepPerfectBy: string;

      /** 离职接单确认时间 */
      sepPerfectDate: string;

      /** 离职手续办理状态:0  未完成   1  完成 */
      sepProcessStatus: string;

      /** 离职报价单 */
      sepQuotationId: string;

      /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
      sepReason: string;

      /** 离职原因名称 */
      sepReasonName: string;

      /** 离职备注 */
      sepRemark: string;

      /** 离职时间止 */
      sepStartDt: string;

      /** 离职导出类型:1离职接单确认,2离职派单确认 */
      sepType: string;

      /** 签约方分公司抬头 */
      signBranchTitle: string;

      /** 签约方分公司抬头id */
      signBranchTitleId: string;

      /** 签约方分公司抬头name */
      signBranchTitleName: string;

      /** 签单供应商 */
      signProvider: string;

      /** 签单方 */
      signProviderId: string;

      /** signStatus */
      signStatus: number;

      /** 短信发送日期 */
      smsSendDt: string;

      /** 短信发送状态: 0未发送, 1成功, 2失败 */
      smsSendStatus: string;

      /** 短信发送状态中文: 未发送, 成功, 失败 */
      smsSendStatusStr: string;

      /** 分拆方分公司:分拆方客服 */
      splitServiceProviderCs: string;

      /** 社保列表 */
      ssGroupList: Array<defs.externalsupplier.EmployeeFee>;

      /** 员工社保参与地 */
      ssParticipateLocation: string;

      /** startIndex */
      startIndex: number;

      /** 状态 1入职未生效2在职3离职 */
      status: string;

      /** 状态名称 */
      statusName: string;

      /** 小类名称 */
      subTypeName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同编号 */
      subcontractCode: string;

      /** 小合同id */
      subcontractId: string;

      /** 小合同名称 */
      subcontractName: string;

      /** 大类名称 */
      superTypeName: string;

      /** 总收费日期 */
      totalFeeDt: string;

      /** eos转移id */
      transferId: number;

      /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
      type: number;

      /** 机动分类项目1 */
      type1: string;

      /** 机动分类项目2 */
      type2: string;

      /** 机动分类项目3 */
      type3: string;

      /** 机动分类项目4 */
      type4: string;

      /** 机动分类项目5 */
      type5: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** uuid */
      uuid: string;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class ExternalEmpHireSepVO {
      /** 订单列表 */
      employeeHireSepList: Array<defs.externalsupplier.EmployeeHireSep>;

      /** 非社保公积金 */
      nonSsGroupList: Array<defs.externalsupplier.EmployeeFee>;
    }

    export class FeeChangeDetail {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_DETAIL.EMP_HIRE_SEP_ID	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      empHireSepId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_DETAIL.EMP_ID	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      empId: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_DETAIL.PD_TASK_DETAIL_ID	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      pdTaskDetailId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_DETAIL.PD_TASK_ID	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      pdTaskId: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class FeeChangeTask {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** changeRateName */
      changeRateName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_TASK.CHANGE_START_MONTH	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      changeStartMonth: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_TASK.FEE_TEMPLT_ID	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      feeTempltId: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** providerId */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** providerName */
      providerName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_TASK.STATUS	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      status: string;

      /** statusName */
      statusName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_TASK.TASK_ID	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      taskId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column PD_FEE_CHANGE_TASK.TASK_NAME	  	  ibatorgenerated Wed Feb 06 15:50:36 CST 2013 */
      taskName: string;

      /** type */
      type: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class FeeChangeTaskQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商id */
      providerId: string;

      /** startIndex */
      startIndex: number;

      /** 任务id */
      taskId: string;

      /** 任务名称 */
      taskName: string;

      /** 类型，1，2 */
      type: string;
    }

    export class FeeChangeTaskVO {
      /** 个人订单ids */
      empHireSepIds: string;

      /** 费用变更任务 */
      feeChangeTask: defs.externalsupplier.FeeChangeTask;
    }

    export class FileCustSnap {
      /** add */
      add: boolean;

      /** 金额 */
      amt: string;

      /** 派单方供应商id(派单地) */
      assignerProviderId: string;

      /** assignerProviderName */
      assignerProviderName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** billEndMon */
      billEndMon: string;

      /** billStartMon */
      billStartMon: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractType */
      contractType: string;

      /** contractTypeName */
      contractTypeName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** 客户id */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** empnum */
      empnum: string;

      /** endIndex */
      endIndex: number;

      /** 收费结束月 */
      endMonth: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** ifUseBillData */
      ifUseBillData: string;

      /** ifUseMaintainData */
      ifUseMaintainData: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** maintainEndMon */
      maintainEndMon: string;

      /** maintainStartMon */
      maintainStartMon: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 支付审核ID */
      payAuditId: string;

      /** payeeId */
      payeeId: string;

      /** payeeName */
      payeeName: string;

      /** 主键 */
      ppfcdId: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商ID */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** providerName */
      providerName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** prvdPayType */
      prvdPayType: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 收费开始月 */
      startMonth: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 签单方抬头id */
      titleId: string;

      /** 签单方抬头名字 */
      titleName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class FileEmpSnap {
      /** add */
      add: boolean;

      /** 金额 */
      amt: string;

      /** 派单方供应商id(派单地) */
      assignerProviderId: string;

      /** assignerProviderName */
      assignerProviderName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** billEndMon */
      billEndMon: string;

      /** billStartMon */
      billStartMon: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同类型 */
      contractType: string;

      /** contractTypeName */
      contractTypeName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** 客户id */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** eAmt */
      eAmt: string;

      /** empCode */
      empCode: string;

      /** 员工入离职ID */
      empHireSepId: string;

      /** empName */
      empName: string;

      /** empnum */
      empnum: string;

      /** endIndex */
      endIndex: number;

      /** 收费结束月 */
      endMonth: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 档案id */
      fileId: string;

      /** fileNo */
      fileNo: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** idCardNum */
      idCardNum: string;

      /** ifUseBillData */
      ifUseBillData: string;

      /** ifUseMaintainData */
      ifUseMaintainData: string;

      /** 档案调入时间 */
      inDt: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** maintainEndMon */
      maintainEndMon: string;

      /** maintainStartMon */
      maintainStartMon: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 档案调出时间 */
      outDt: string;

      /** pAmt */
      pAmt: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 支付审核ID */
      payAuditId: string;

      /** payeeId */
      payeeId: string;

      /** payeeName */
      payeeName: string;

      /** 主键 */
      ppfcdId: string;

      /** 主键 */
      ppfedId: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商ID */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** providerName */
      providerName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** prvdPayType */
      prvdPayType: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** serviceMonth */
      serviceMonth: string;

      /** startIndex */
      startIndex: number;

      /** 收费开始月 */
      startMonth: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 签单方抬头id */
      titleId: string;

      /** 签单方抬头名字 */
      titleName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class FilterEntity {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 分公司id */
      branchId: string;

      /** 分公司名称 */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 文件名称 */
      fileName: string;

      /** 文件路径 */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 主键 */
      groupBranchId: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class Material {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      materialId: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_NAME           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      materialName: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.REMARK           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }

    export class PayAudit {
      /** acctBankName */
      acctBankName: string;

      /** acctId */
      acctId: string;

      /** acctIdBankName */
      acctIdBankName: string;

      /** acctIdName */
      acctIdName: string;

      /** acctIdNum */
      acctIdNum: string;

      /** activityNameEn */
      activityNameEn: string;

      /** activityStatus */
      activityStatus: string;

      /** add */
      add: boolean;

      /** allowanceDetailIds */
      allowanceDetailIds: Array<object>;

      /** allowanceFileId */
      allowanceFileId: string;

      /** allowanceFileName */
      allowanceFileName: string;

      /** 本次使用金额 */
      amount: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      amt: number;

      /** appendRemark */
      appendRemark: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLICANT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      applicant: number;

      /** applicantDepartment */
      applicantDepartment: number;

      /** applicantDepartmentName */
      applicantDepartmentName: string;

      /** applicantName */
      applicantName: string;

      /** 申请人数 */
      applyCount: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      applyDt: string;

      /** applyEndDt */
      applyEndDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLY_PAY_AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      applyPayAmt: number;

      /** applyPayAmtNoOne */
      applyPayAmtNoOne: string;

      /** applyPayAmtOne */
      applyPayAmtOne: string;

      /** applyStartDt */
      applyStartDt: string;

      /** 申请单抬头 */
      applyTitle: string;

      /** 审批通过时间小于等于 */
      approveEndDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPROVE_OPINION	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      approveOpinion: string;

      /** 审批通过时间大于等于 */
      approveStartDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPROVE_STATUS	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      approveStatus: number;

      /** approveStatusName */
      approveStatusName: string;

      /** 派单地申请单抬头 */
      assignerApplyTitle: string;

      /** 派单地抬头对应分公司 */
      assignerApplyTitleBranchId: string;

      /** 派单地抬头名称 */
      assignerApplyTitleName: string;

      /** 派单地 */
      assignerDepartmentId: string;

      /** 派单地name */
      assignerDepartmentName: string;

      /** auditId */
      auditId: string;

      /** 出款账号 */
      backBankAcct: string;

      /** 出款附言 */
      backPostscript: string;

      /** 交易日期 */
      backTradeDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.BANK_ACCT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      bankAcct: string;

      /** bank_acct_id */
      bankAcctId: string;

      /** bank_name */
      bankName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** batchPayResult */
      batchPayResult: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.BILL_TYPE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      billType: string;

      /** 财务大类 */
      bizCategory: string;

      /** bizmanType */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 类型1 写入 RECEIPTS_ID 类型2 写入 PAY_AUDIT_ID */
      bussId: string;

      /** cashAmount */
      cashAmount: string;

      /** cashDt */
      cashDt: string;

      /** cashId */
      cashId: string;

      /** cashUser */
      cashUser: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 确认发放完成时间 */
      confirmFinishDt: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同名称 */
      contractName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建人的查询条件 */
      createByInput: string;

      /** createByName */
      createByName: string;

      /** 创建日期 */
      createDt: string;

      /** createDtTo */
      createDtTo: string;

      /** custCode */
      custCode: string;

      /** 客户确认状态 */
      custConfirmStatus: string;

      /** 客户填写的到款金额 */
      custEstimatedAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.CUST_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      custId: number;

      /** custList */
      custList: Array<object>;

      /** custName */
      custName: string;

      /** 客户填写的预计到款时间 */
      custSendMonth: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 支付地抬头对应分公司 */
      depTitleBranchId: string;

      /** 支付地抬头id */
      depTitleId: string;

      /** 支付地抬头名称 */
      depTitleName: string;

      /** 明细表的初始化状态 */
      detailStatus: string;

      /** empCode */
      empCode: string;

      /** empName */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** expType */
      expType: string;

      /** exportType */
      exportType: string;

      /** 离职补偿金综合 */
      f10008Sum: string;

      /** fileId */
      fileId: string;

      /** fileName */
      fileName: string;

      /** fileProviderName */
      fileProviderName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** governingArea */
      governingArea: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** idCardNum */
      idCardNum: string;

      /** inId */
      inId: string;

      /** invoiceAmount */
      invoiceAmount: string;

      /** invoiceDate */
      invoiceDate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.INVOICE_QTY	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      invoiceQty: number;

      /** isActualPay */
      isActualPay: string;

      /** 系统数据调整，不实际支付 */
      isAdjustNoPay: string;

      /** 系统核查通过自动提交 1:是 0:否 */
      isAutoCommit: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** isDelayAmtFlag */
      isDelayAmtFlag: string;

      /** 删除标记 */
      isDeleted: string;

      /** 已获取标记 */
      isGet: string;

      /** 是否加入银企直连黑名单  0：否 1：是 */
      isJoinBlacklist: string;

      /** isOneTimePay */
      isOneTimePay: string;

      /** 传递到金蝶系统：是、否 */
      isPassKingdee: string;

      /** isPaySet */
      isPaySet: string;

      /** 供应商工资或其他 1:是 0:否 */
      isSupplier: string;

      /** 是否使用核查到款审批 */
      isUseCheckPayApprove: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.LAST_PAY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      lastPayDt: string;

      /** 最晚支付日期到 */
      lastPayDtTo: string;

      /** lastPayEndDt */
      lastPayEndDt: string;

      /** lastPayStartDt */
      lastPayStartDt: string;

      /** 项目客服姓名 */
      liabilityCsName: string;

      /** mapLockMonUpdateDt */
      mapLockMonUpdateDt: object;

      /** mapLockMonUpdateDtM */
      mapLockMonUpdateDtM: object;

      /** 模拟人 */
      mimicBy: string;

      /** newApproveOpinion */
      newApproveOpinion: string;

      /** noChange */
      noChange: boolean;

      /** 未自动提交原因 */
      notAutoCommitReason: string;

      /** 被冲抵的发放批次id */
      offsetPayBatchId: string;

      /** 外包风险金表id */
      orRiskFundId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** participant */
      participant: string;

      /** payAddress */
      payAddress: string;

      /** payAddressName */
      payAddressName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payAmt: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_AUDIT_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payAuditId: number;

      /** 发放批次名称(不展示，用于后台比较重复名称数据) */
      payAuditName: string;

      /** 发放批次编号 */
      payBatchCode: string;

      /** 发放批次id */
      payBatchId: string;

      /** 发放批次名称 */
      payBatchName: string;

      /** 批次类型：正常发放、虚拟发放 */
      payBatchType: string;

      /** class id集合 */
      payClassIds: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_DETAIL_METHOD	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payDetailMethod: string;

      /** payDetailType */
      payDetailType: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payDt: string;

      /** payEndDt */
      payEndDt: string;

      /** 同步到中间库的pay_id */
      payId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_METHOD	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payMethod: number;

      /** payMethodName */
      payMethodName: string;

      /** payObject */
      payObject: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_PURPOSE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payPurpose: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_REASON	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payReason: string;

      /** send id集合 */
      paySends: string;

      /** payStartDt */
      payStartDt: string;

      /** payStatus */
      payStatus: string;

      /** payStatusName */
      payStatusName: string;

      /** payTreatmentDetail */
      payTreatmentDetail: Array<defs.externalsupplier.PayTreatmentDetail>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_TYPE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payType: number;

      /** payTypeName */
      payTypeName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAYEE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payee: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAYEE_BANK	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payeeBank: string;

      /** payeeBankName */
      payeeBankName: string;

      /** 到账日期 */
      payeeDt: string;

      /** 附言 */
      postscript: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processDefId */
      processDefId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PROCESS_INS_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      processInsId: number;

      /** providerId */
      providerId: string;

      /** providerIdAlias */
      providerIdAlias: string;

      /** providerName */
      providerName: string;

      /** provinceName */
      provinceName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupId */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** prvdGroupName */
      prvdGroupName: string;

      /** prvdPayType */
      prvdPayType: string;

      /** rcvIds */
      rcvIds: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.REMARK	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      remark: string;

      /** remindUserIds */
      remindUserIds: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 本次总金额 */
      riskAmount: string;

      /** 风险分担比例% */
      riskSharingRatio: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.RPT_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      rptDt: string;

      /** sBatchId */
      sBatchId: string;

      /** secondBank */
      secondBank: string;

      /** secondBankAcct */
      secondBankAcct: string;

      /** secondBankBranch */
      secondBankBranch: string;

      /** secondCity */
      secondCity: string;

      /** secondPayee */
      secondPayee: string;

      /** secondProvince */
      secondProvince: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 发放日期 */
      sendDt: string;

      /** 发放通道 */
      sendWay: string;

      /** 服务费合计金额 */
      serviceFeeAmount: string;

      /** 签约方分公司抬头name */
      signBranchTitleName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.SS_GROUP_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      ssGroupId: number;

      /** ssGroupName */
      ssGroupName: string;

      /** 社保公积金合计金额 */
      ssPfAmount: string;

      /** startIndex */
      startIndex: number;

      /** subcontractAlias */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** userId */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.WELFARE_PROCESSOR	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      welfareProcessor: number;

      /** welfareProcessorName */
      welfareProcessorName: string;

      /** 扣缴义务人ID集合 */
      withholdAgentIds: string;

      /** 扣缴义务人名称集合 */
      withholdAgentNames: string;

      /** 扣缴义务人类型 */
      withholdAgentType: string;

      /** workitemId */
      workitemId: string;
    }

    export class PayQuery {
      /** applicant */
      applicant: string;

      /** 申请结束时间 */
      applyEndDt: string;

      /** 申请时间从 */
      applyStartDt: string;

      /** 审批状态 */
      approveStatus: string;

      /** assignerProviderId */
      assignerProviderId: string;

      /** assigner_PROVIDER_ID */
      assigner_PROVIDER_ID: string;

      /** 客户id */
      custId: string;

      /** 支付地抬头 */
      depTitleId: string;

      /** 唯一号 */
      empCode: string;

      /** endIndex */
      endIndex: number;

      /** isPayApplayPage */
      isPayApplayPage: string;

      /** 最晚支付时间小于等于 */
      lastPayEndDt: string;

      /** 最晚支付时间大于等于 */
      lastPayStartDt: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 申请人 */
      participant: string;

      /** 支付主记录id */
      payAuditId: string;

      /** 支付所属年月 */
      payDt: string;

      /** 支付方式 */
      payMethod: string;

      /** 支付类型 */
      payType: string;

      /** pay_AUDIT_ID */
      pay_AUDIT_ID: string;

      /** 收款方id */
      payeeId: string;

      /** payer_TYPE */
      payer_TYPE: string;

      /** pdEfBillMonthEnd */
      pdEfBillMonthEnd: string;

      /** pdEfBillMonthStart */
      pdEfBillMonthStart: string;

      /** pd_EF_BILL_MONTH_END */
      pd_EF_BILL_MONTH_END: string;

      /** pd_EF_BILL_MONTH_START */
      pd_EF_BILL_MONTH_START: string;

      /** 流程定义id */
      processDefId: string;

      /** 产品code */
      productCode: string;

      /** 供应商id */
      providerId: string;

      /** provider_ID */
      provider_ID: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** 供应商支付类型 */
      prvdPayType: string;

      /** prvd_GROUP_ID */
      prvd_GROUP_ID: string;

      /** ids */
      rcvIds: string;

      /** receivableAmtGthen */
      receivableAmtGthen: string;

      /** receivableAmtLthen */
      receivableAmtLthen: string;

      /** receivable_AMT_GTHEN */
      receivable_AMT_GTHEN: string;

      /** receivable_AMT_LTHEN */
      receivable_AMT_LTHEN: string;

      /** 查询权限 */
      selByAuth: string;

      /** 服务年月 */
      serviceMonth: string;

      /** 社保组id */
      ssGroupId: string;

      /** startIndex */
      startIndex: number;

      /** 抬头id */
      titleId: string;
    }

    export class PayTreatmentDetail {
      /** 账户名称 */
      accountEmployeeName: string;

      /** add */
      add: boolean;

      /** 银行卡号 */
      bankAcct: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 业务小类 */
      busSubtypeId: string;

      /** 业务大类 */
      busTypeId: string;

      /** businessId */
      businessId: string;

      /** cashId */
      cashId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custId */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户付款方名称 */
      custPayName: string;

      /** 客户付款方名称id */
      custPayerId: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 唯一号 */
      empCode: string;

      /** 姓名 */
      empName: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 办理凭证id */
      fileId: string;

      /** 办理凭证 */
      fileName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 证件号码 */
      idCardNum: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 开户行名称 */
      openBankName: string;

      /** payAuditId */
      payAuditId: string;

      /** 主键 */
      payTreatmentDetailId: number;

      /** 支付对象类型 */
      payType: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processInsId */
      processInsId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 实报金额 */
      realAmount: string;

      /** 提报金额 */
      reportAmount: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 入离职状态 */
      sepStatus: string;

      /** 社保状态 */
      ssStatus: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同名称 */
      subcontractName: string;

      /** transactTypeId */
      transactTypeId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ProviderBank {
      /**  账户类型 */
      accountType: string;

      /** add */
      add: boolean;

      /** 自动上传流水 */
      autoUploadFlow: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column BD_PROVIDER_BANK.BANK	  	  ibatorgenerated Wed Mar 23 09:35:02 CST 2011 */
      bank: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column BD_PROVIDER_BANK.BANK_ACCT	  	  ibatorgenerated Wed Mar 23 09:35:02 CST 2011 */
      bankAcct: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column BD_PROVIDER_BANK.BANK_ACCT_ID	  	  ibatorgenerated Wed Mar 23 09:35:02 CST 2011 */
      bankAcctId: string;

      /** 分行名银行维护 bd_base_date type=603 对应数据 */
      bankBranch: string;

      /** 分行名号 */
      bankBranchCode: string;

      /** 分行名号 */
      bankBranchName: string;

      /** 银行通道代码 */
      bankCode: string;

      /** bankId */
      bankId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column BD_PROVIDER_BANK.BANK_NAME	  	  ibatorgenerated Wed Mar 23 09:35:02 CST 2011 */
      bankName: string;

      /** 银行通道代码ID */
      bankPassageId: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** isDefaultAcct */
      isDefaultAcct: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column BD_PROVIDER_BANK.Column_13	  	  ibatorgenerated Wed Mar 23 09:35:02 CST 2011 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column BD_PROVIDER_BANK.PROVIDER_ID	  	  ibatorgenerated Wed Mar 23 09:35:02 CST 2011 */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column BD_PROVIDER_BANK.PROVIDER_TYPE	  	  ibatorgenerated Wed Mar 23 09:35:02 CST 2011 */
      providerType: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column BD_PROVIDER_BANK.REMARK	  	  ibatorgenerated Wed Mar 23 09:35:02 CST 2011 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ProviderCSAuth {
      /** add */
      add: boolean;

      /** 大区id */
      areaId: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 责任客服id */
      csId: string;

      /** csName */
      csName: string;

      /** 客服类型(1:前道,2:后道) */
      csType: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 结束时间 */
      endDt: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 是否当前段,查询使用 */
      isCurrentStmt: string;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** providerCode */
      providerCode: string;

      /** 供应商id */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** providerName */
      providerName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdCsAuthId */
      prvdCsAuthId: string;

      /** prvdGroupCode */
      prvdGroupCode: string;

      /** prvdGroupId */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** prvdGroupName */
      prvdGroupName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 开始时间 */
      startDt: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ProviderCSAuthQuery {
      /** 新责任客服 */
      newPrvdCSAuth: defs.externalsupplier.ProviderCSAuth;

      /** 分配得供应商list */
      toBeAssignedPrvdCSAuthList: Array<defs.externalsupplier.ProviderCSAuth>;
    }

    export class ProviderGroup {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 城市id */
      cityId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 联系人 */
      contact: string;

      /** 联系地址 */
      contactAddress: string;

      /** 联系电话1 */
      contactTel1: string;

      /** 联系电话2 */
      contactTel2: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractEndDate */
      contractEndDate: string;

      /** contractStartDate */
      contractStartDate: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** humanResourceLicence */
      humanResourceLicence: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isRestrictAuth */
      isRestrictAuth: string;

      /** laborDispatchLicence */
      laborDispatchLicence: string;

      /** latestPayDate */
      latestPayDate: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdCodeIn */
      prvdCodeIn: string;

      /** prvdGroupCode */
      prvdGroupCode: string;

      /** prvdGroupEName */
      prvdGroupEName: string;

      /** prvdGroupId */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** prvdGroupName */
      prvdGroupName: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** socialCreditCode */
      socialCreditCode: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 邮编 */
      zipCode: string;
    }

    export class ProviderGroupContract {
      /** activityNameEn */
      activityNameEn: string;

      /** add */
      add: boolean;

      /** 提交人 */
      approveCommitBy: string;

      /** 提交人名称 */
      approveCommitByName: string;

      /** 审批意见 */
      approveOpinion: string;

      /** 审批过程 */
      approveProcess: string;

      /** 审批状态 */
      approveStatus: string;

      /** approveStatusName */
      approveStatusName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 城市id */
      cityId: string;

      /** 城市名称 */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 联系人 */
      contact: string;

      /** 联系电话 */
      contactTel: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractCode */
      contractCode: string;

      /** 合同终止期 */
      contractEndDate: string;

      /** 合同名称 */
      contractName: string;

      /** 合同生效期 */
      contractStartDate: string;

      /** 合同状态 */
      contractStatus: string;

      /** contractStatusName */
      contractStatusName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 附件名称 */
      fileName: string;

      /** 合同附件 */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 正式合同名称 */
      formalContractName: string;

      /** 正式合同路径 */
      formalContractPath: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 人力资源许可证 */
      humanResourceLicence: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 劳务派遣许可证 */
      laborDispatchLicence: string;

      /** 最晚支付日 */
      latestPayDate: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 原供应商集团合同Id */
      oldContractId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 流程INSID */
      processInsId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** provinceId */
      provinceId: string;

      /** provinceName */
      provinceName: string;

      /** 代理人 */
      proxyBy: string;

      /** 供应商集团Code */
      prvdGroupCode: string;

      /** 供应商集团合同Id */
      prvdGroupContractId: string;

      /** 供应商集团Id */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 供应商集团名称 */
      prvdGroupName: string;

      /** remark */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** roleId */
      roleId: string;

      /** 保存0，提交1 */
      saveOrCommit: number;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 社会统一信用代码 */
      socialCreditCode: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** workItemId */
      workItemId: string;

      /** 流程id */
      workitemId: string;
    }

    export class PrvdGroupContractAgree {
      /** branchId */
      branchId: string;

      /** branchName */
      branchName: string;

      /** fileName */
      fileName: string;

      /** filePath */
      filePath: string;

      /** prvdGroupAgreeId */
      prvdGroupAgreeId: string;

      /** prvdGroupContractId */
      prvdGroupContractId: string;

      /** uploadBy */
      uploadBy: string;

      /** uploadByName */
      uploadByName: string;

      /** uploadDt */
      uploadDt: string;
    }

    export class PrvdPayDetail {
      /** add */
      add: boolean;

      /** afAmount */
      afAmount: string;

      /** applyPayAmt */
      applyPayAmt: string;

      /** archiveFee */
      archiveFee: string;

      /** 派单方供应商id(派单地) */
      assignerProviderId: string;

      /** assignerProviderName */
      assignerProviderName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单年月 */
      billMonth: string;

      /** 账单人数 */
      billNum: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractType */
      contractType: string;

      /** contractTypeName */
      contractTypeName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** 客户id */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** dataId */
      dataId: string;

      /** del */
      del: boolean;

      /** dgAmount */
      dgAmount: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** groupId */
      groupId: string;

      /** groupIdText */
      groupIdText: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** otherFee */
      otherFee: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 支付审核ID */
      payAuditId: string;

      /** 供应商申请支付id */
      payDetailId: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商ID */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** providerName */
      providerName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** receivableId */
      receivableId: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 服务费 */
      serviceFee: string;

      /** 社保合计 */
      siAmount: string;

      /** signBranchTitle */
      signBranchTitle: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** subtotal */
      subtotal: string;

      /** titleName */
      titleName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class PrvdPayItem {
      /** acctBankName */
      acctBankName: string;

      /** acctId */
      acctId: string;

      /** acctIdBankName */
      acctIdBankName: string;

      /** acctIdName */
      acctIdName: string;

      /** acctIdNum */
      acctIdNum: string;

      /** activityNameEn */
      activityNameEn: string;

      /** activityStatus */
      activityStatus: string;

      /** add */
      add: boolean;

      /** allowanceDetailIds */
      allowanceDetailIds: Array<object>;

      /** allowanceFileId */
      allowanceFileId: string;

      /** allowanceFileName */
      allowanceFileName: string;

      /** 本次使用金额 */
      amount: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      amt: number;

      /** appendRemark */
      appendRemark: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLICANT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      applicant: number;

      /** applicantDepartment */
      applicantDepartment: number;

      /** applicantDepartmentName */
      applicantDepartmentName: string;

      /** applicantName */
      applicantName: string;

      /** 申请人数 */
      applyCount: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      applyDt: string;

      /** applyEndDt */
      applyEndDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPLY_PAY_AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      applyPayAmt: number;

      /** applyPayAmtNoOne */
      applyPayAmtNoOne: string;

      /** applyPayAmtOne */
      applyPayAmtOne: string;

      /** applyStartDt */
      applyStartDt: string;

      /** 申请单抬头 */
      applyTitle: string;

      /** 审批通过时间小于等于 */
      approveEndDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPROVE_OPINION	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      approveOpinion: string;

      /** 审批通过时间大于等于 */
      approveStartDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.APPROVE_STATUS	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      approveStatus: number;

      /** approveStatusName */
      approveStatusName: string;

      /** 派单地申请单抬头 */
      assignerApplyTitle: string;

      /** 派单地抬头对应分公司 */
      assignerApplyTitleBranchId: string;

      /** 派单地抬头名称 */
      assignerApplyTitleName: string;

      /** 派单地 */
      assignerDepartmentId: string;

      /** 派单地name */
      assignerDepartmentName: string;

      /** assignerProviderAmt */
      assignerProviderAmt: string;

      /** 拍单方供应商id */
      assignerProviderId: string;

      /** assignerProviderName */
      assignerProviderName: string;

      /** auditId */
      auditId: string;

      /** 出款账号 */
      backBankAcct: string;

      /** 出款附言 */
      backPostscript: string;

      /** 交易日期 */
      backTradeDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.BANK_ACCT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      bankAcct: string;

      /** bank_acct_id */
      bankAcctId: string;

      /** bank_name */
      bankName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** batchPayResult */
      batchPayResult: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** billEndMon */
      billEndMon: string;

      /** billStartMon */
      billStartMon: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.BILL_TYPE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      billType: string;

      /** 财务大类 */
      bizCategory: string;

      /** bizmanType */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 类型1 写入 RECEIPTS_ID 类型2 写入 PAY_AUDIT_ID */
      bussId: string;

      /** cashAmount */
      cashAmount: string;

      /** cashDt */
      cashDt: string;

      /** cashId */
      cashId: string;

      /** cashUser */
      cashUser: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 确认发放完成时间 */
      confirmFinishDt: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同名称 */
      contractName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建人的查询条件 */
      createByInput: string;

      /** createByName */
      createByName: string;

      /** 创建日期 */
      createDt: string;

      /** createDtTo */
      createDtTo: string;

      /** custCode */
      custCode: string;

      /** 客户确认状态 */
      custConfirmStatus: string;

      /** 客户填写的到款金额 */
      custEstimatedAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.CUST_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      custId: number;

      /** custList */
      custList: Array<object>;

      /** custName */
      custName: string;

      /** 客户填写的预计到款时间 */
      custSendMonth: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 支付地抬头对应分公司 */
      depTitleBranchId: string;

      /** 支付地抬头id */
      depTitleId: string;

      /** 支付地抬头名称 */
      depTitleName: string;

      /** 明细表的初始化状态 */
      detailStatus: string;

      /** empCode */
      empCode: string;

      /** empName */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** expType */
      expType: string;

      /** exportType */
      exportType: string;

      /** 离职补偿金综合 */
      f10008Sum: string;

      /** failureReason */
      failureReason: string;

      /** fileId */
      fileId: string;

      /** fileName */
      fileName: string;

      /** fileProviderName */
      fileProviderName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** governingArea */
      governingArea: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** idCardNum */
      idCardNum: string;

      /** ifUseBillData */
      ifUseBillData: string;

      /** ifUseMaintainData */
      ifUseMaintainData: string;

      /** inId */
      inId: string;

      /** invoiceAmount */
      invoiceAmount: string;

      /** invoiceDate */
      invoiceDate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.INVOICE_QTY	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      invoiceQty: number;

      /** isActualPay */
      isActualPay: string;

      /** 系统数据调整，不实际支付 */
      isAdjustNoPay: string;

      /** 系统核查通过自动提交 1:是 0:否 */
      isAutoCommit: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** isDelayAmtFlag */
      isDelayAmtFlag: string;

      /** 是否删除0：否1：是，默认值是0 */
      isDeleted: string;

      /** 已获取标记 */
      isGet: string;

      /** 是否加入银企直连黑名单  0：否 1：是 */
      isJoinBlacklist: string;

      /** isOneTimePay */
      isOneTimePay: string;

      /** 传递到金蝶系统：是、否 */
      isPassKingdee: string;

      /** isPaySet */
      isPaySet: string;

      /** 供应商工资或其他 1:是 0:否 */
      isSupplier: string;

      /** 是否使用核查到款审批 */
      isUseCheckPayApprove: string;

      /** lastPayDay */
      lastPayDay: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.LAST_PAY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      lastPayDt: string;

      /** 最晚支付日期到 */
      lastPayDtTo: string;

      /** lastPayEndDt */
      lastPayEndDt: string;

      /** lastPayStartDt */
      lastPayStartDt: string;

      /** 项目客服姓名 */
      liabilityCsName: string;

      /** maintainEndMon */
      maintainEndMon: string;

      /** maintainStartMon */
      maintainStartMon: string;

      /** mapLockMonUpdateDt */
      mapLockMonUpdateDt: object;

      /** mapLockMonUpdateDtM */
      mapLockMonUpdateDtM: object;

      /** 模拟人 */
      mimicBy: string;

      /** newApproveOpinion */
      newApproveOpinion: string;

      /** noChange */
      noChange: boolean;

      /** 未自动提交原因 */
      notAutoCommitReason: string;

      /** 被冲抵的发放批次id */
      offsetPayBatchId: string;

      /** 外包风险金表id */
      orRiskFundId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** participant */
      participant: string;

      /** payAddress */
      payAddress: string;

      /** payAddressName */
      payAddressName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_AMT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payAmt: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_AUDIT_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payAuditId: number;

      /** 发放批次名称(不展示，用于后台比较重复名称数据) */
      payAuditName: string;

      /** 发放批次编号 */
      payBatchCode: string;

      /** 发放批次id */
      payBatchId: string;

      /** 发放批次名称 */
      payBatchName: string;

      /** 批次类型：正常发放、虚拟发放 */
      payBatchType: string;

      /** class id集合 */
      payClassIds: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_DETAIL_METHOD	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payDetailMethod: string;

      /** payDetailType */
      payDetailType: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payDt: string;

      /** payEndDt */
      payEndDt: string;

      /** 同步到中间库的pay_id */
      payId: string;

      /** 主键 */
      payItemId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_METHOD	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payMethod: number;

      /** payMethodName */
      payMethodName: string;

      /** payObject */
      payObject: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_PURPOSE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payPurpose: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_REASON	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payReason: string;

      /** send id集合 */
      paySends: string;

      /** payStartDt */
      payStartDt: string;

      /** payStatus */
      payStatus: string;

      /** payStatusName */
      payStatusName: string;

      /** payTreatmentDetail */
      payTreatmentDetail: Array<defs.externalsupplier.PayTreatmentDetail>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAY_TYPE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payType: number;

      /** payTypeName */
      payTypeName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAYEE	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payee: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PAYEE_BANK	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      payeeBank: string;

      /** payeeBankName */
      payeeBankName: string;

      /** 到账日期 */
      payeeDt: string;

      /** payeeId */
      payeeId: string;

      /** payeeName */
      payeeName: string;

      /** payerAcct */
      payerAcct: string;

      /** 附言 */
      postscript: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processDefId */
      processDefId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.PROCESS_INS_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      processInsId: number;

      /** providerId */
      providerId: string;

      /** providerIdAlias */
      providerIdAlias: string;

      /** providerName */
      providerName: string;

      /** provinceName */
      provinceName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupId */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** prvdGroupName */
      prvdGroupName: string;

      /** prvdPayType */
      prvdPayType: string;

      /** publicOrPrivate */
      publicOrPrivate: string;

      /** rcvIds */
      rcvIds: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.REMARK	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      remark: string;

      /** remindUserIds */
      remindUserIds: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 本次总金额 */
      riskAmount: string;

      /** 风险分担比例% */
      riskSharingRatio: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.RPT_DT	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      rptDt: string;

      /** sBatchId */
      sBatchId: string;

      /** secondBank */
      secondBank: string;

      /** secondBankAcct */
      secondBankAcct: string;

      /** secondBankBranch */
      secondBankBranch: string;

      /** secondCity */
      secondCity: string;

      /** secondPayee */
      secondPayee: string;

      /** secondProvince */
      secondProvince: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 发放日期 */
      sendDt: string;

      /** 发放通道 */
      sendWay: string;

      /** 服务费合计金额 */
      serviceFeeAmount: string;

      /** 签约方分公司抬头name */
      signBranchTitleName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.SS_GROUP_ID	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      ssGroupId: number;

      /** ssGroupName */
      ssGroupName: string;

      /** 社保公积金合计金额 */
      ssPfAmount: string;

      /** startIndex */
      startIndex: number;

      /** subcontractAlias */
      subcontractAlias: string;

      /** titleFeeList */
      titleFeeList: Array<defs.externalsupplier.PrvdPayDetail>;

      /** titleId */
      titleId: string;

      /** titleName */
      titleName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** useFor */
      useFor: string;

      /** userId */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SS_PAY_AUDIT.WELFARE_PROCESSOR	  	  ibatorgenerated Fri Mar 16 14:56:31 CST 2012 */
      welfareProcessor: number;

      /** welfareProcessorName */
      welfareProcessorName: string;

      /** 扣缴义务人ID集合 */
      withholdAgentIds: string;

      /** 扣缴义务人名称集合 */
      withholdAgentNames: string;

      /** 扣缴义务人类型 */
      withholdAgentType: string;

      /** workitemId */
      workitemId: string;
    }

    export class QueryPrvdPayDetail {
      /** add */
      add: boolean;

      /** applicant */
      applicant: string;

      /** applyDt */
      applyDt: string;

      /** approveStatus */
      approveStatus: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** billMonth */
      billMonth: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** departmentId */
      departmentId: string;

      /** departmentName */
      departmentName: string;

      /** empCode */
      empCode: string;

      /** empName */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** insuranceName */
      insuranceName: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** productCode */
      productCode: string;

      /** productName */
      productName: string;

      /** providerId */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupId */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** prvdPayType */
      prvdPayType: string;

      /** receivableAmt */
      receivableAmt: string;

      /** receivableId */
      receivableId: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** serviceMonth */
      serviceMonth: string;

      /** ssGroupId */
      ssGroupId: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class batchSaveOrUpdateProviderGroupVO {
      /** 新增集合 */
      insertList: Array<defs.externalsupplier.providerGroupVO>;

      /** 更新集合 */
      updateList: Array<defs.externalsupplier.providerGroupVO>;
    }

    export class pdEfTemplateDTO {
      /** 账单生成日 */
      agreedBillGenDt: string;

      /** 账单锁定日锁定日 */
      agreedBillLockDt: string;

      /** 收费频率 */
      chargeRate: string;

      /** 频率名称 */
      chargeRateName: string;

      /** 收费月 */
      feeMonth: string;

      /** 收费月名称 */
      feeMonthName: string;

      /** 是否默认 */
      isValid: string;

      /** 提前几月收 */
      monthInAdvance: string;

      /** 主键id */
      pdEfTemplateId: string;

      /** 供应商id */
      providerId: string;

      /** 备注 */
      remark: string;
    }

    export class pdEfTemplateQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商id */
      providerId: string;

      /** startIndex */
      startIndex: number;
    }

    export class pdEfTemplateVO {
      /** 账单生成日 */
      agreedBillGenDt: string;

      /** 约定账单锁定日 */
      agreedBillLockDt: string;

      /** 收费频率 */
      chargeRate: string;

      /** 频率名称 */
      chargeRateName: string;

      /** 收费月 */
      feeMonth: string;

      /** 提前几月收 */
      monthInAdvance: string;

      /** 主键id */
      pdEfTemplateId: string;

      /** 供应商id */
      providerId: string;

      /** 备注 */
      remark: string;
    }

    export class pdQuotationDTO {
      /** 状态(1:默认,0:非默认) */
      isValid: string;

      /** 1:单立户,2:大户 */
      optType: string;

      /** 主键id */
      pdQuotationId: string;

      /** 报价名称 */
      pdQuotationName: string;

      /** 价格 */
      pdQuotationPrice: string;

      /** 备注 */
      remark: string;
    }

    export class pdQuotationQuery {
      /** endIndex */
      endIndex: number;

      /** 是否删除 */
      isDeleted: string;

      /** 状态 */
      isValid: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商id */
      providerId: string;

      /** startIndex */
      startIndex: number;
    }

    export class pdQuotationVO {
      /** 操作方式：1:单立户,2:大户 */
      optType: string;

      /** 报价名称 */
      pdQuotationName: string;

      /** 价格 */
      pdQuotationPrice: string;

      /** 供应商ID */
      providerId: string;

      /** 备注 */
      remark: string;
    }

    export class providerAssignCSAuthHisQuery {
      /** 大区id */
      areaId: string;

      /** 责任客服id */
      csId: string;

      /** 客服类型 */
      csType: string;

      /** 结束时间 */
      endDt: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商id */
      providerId: string;

      /** 开始时间 */
      startDt: string;

      /** startIndex */
      startIndex: number;
    }

    export class providerBankDTO {
      /**  账户类型 */
      accountType: string;

      /** autoUploadFlow */
      autoUploadFlow: string;

      /** 开户行 */
      bank: string;

      /** 银行账号 */
      bankAcct: string;

      /** 银行账号ID */
      bankAcctId: string;

      /** 分行名银行维护 bd_base_date type=603 对应数据 */
      bankBranch: string;

      /** 分行名号 */
      bankBranchCode: string;

      /** 银行通道代码 */
      bankCode: string;

      /** 所属银行 */
      bankId: string;

      /** 开户名 */
      bankName: string;

      /** 银行通道代码ID */
      bankPassageId: string;

      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** isDefaultAcct */
      isDefaultAcct: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 供应商ID: PROVIDER_TYPE=1时存储分公司ID；PROVIDER_TYPE=2时存储外部供应商ID */
      providerId: string;

      /** 供应商类型: 1:分公司   2:外部供应商 */
      providerType: string;

      /** 备注 */
      remark: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class providerBankQuery {
      /** 银行账号 */
      bankAcct: string;

      /** 开户名 */
      bankName: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商ID: PROVIDER_TYPE=1时传分公司ID；PROVIDER_TYPE=2时传外部供应商ID */
      providerId: string;

      /** 供应商类型: 1:分公司   2:外部供应商 */
      providerType: string;

      /** startIndex */
      startIndex: number;
    }

    export class providerBankVO {
      /**  账户类型 */
      accountType: string;

      /** 自动上传流水 */
      autoUploadFlow: string;

      /** 开户行 */
      bank: string;

      /** 银行账号 */
      bankAcct: string;

      /** 银行账号ID */
      bankAcctId: string;

      /** 分行名银行维护 bd_base_date type=603 对应数据 */
      bankBranch: string;

      /** 银行通道代码 */
      bankCode: string;

      /** 所属银行: 0:其他银行,1:工商银行,2:农业银行,3:建设银行,4:中国银行,5:招商银行,6:浦发银行,7:交通银行 */
      bankId: string;

      /** 开户名 */
      bankName: string;

      /** 银行通道代码ID */
      bankPassageId: string;

      /** isDefaultAcct */
      isDefaultAcct: string;

      /** 供应商ID: PROVIDER_TYPE=1时存储分公司ID；PROVIDER_TYPE=2时存储外部供应商ID */
      providerId: string;

      /** 供应商类型: 1:分公司   2:外部供应商 */
      providerType: string;

      /** 备注 */
      remark: string;
    }

    export class providerContactVO {
      /** 联系人手机 */
      cellPhone: string;

      /** 联系人地址 */
      contactAddress: string;

      /** 联系人所属供应商id */
      contactBelongsId: string;

      /** 联系人部门 */
      contactDept: string;

      /** 联系人id */
      contactId: string;

      /** 联系人姓名 */
      contactName: string;

      /** 联系人职务 */
      contactPost: string;

      /** 联系人电话 */
      contactTel: string;

      /** 联系人类型,1:客户，2:外部供应商，3:分公司 */
      contactType: string;

      /** 联系人邮箱 */
      email: string;

      /** 联系人传真 */
      fax: string;

      /** 逻辑删除标识.  0 正常, 1 已删除，默认值是0 */
      isDeleted: string;

      /** 备注 */
      remark: string;
    }

    export class providerDTO {
      /** 申报截止日 */
      applicationDeadline: string;

      /** 账单地址 */
      billAddress: string;

      /** 账单生成日 */
      billGenDT: string;

      /** 账单锁定日 */
      billLockDT: string;

      /** 账单地址邮编 */
      billZipCode: string;

      /** 城市 */
      cityId: string;

      /** 联系人 */
      contact: string;

      /** 联系地址 */
      contactAddress: string;

      /** 联系人部门 */
      contactDept: string;

      /** 联系电话1 */
      contactTel1: string;

      /** 联系电话2 */
      contactTel2: string;

      /** 用户部门关系id */
      deptUserId: string;

      /** 客服电子邮件 */
      employeeEmail: string;

      /** 虚拟客服id */
      employeeId: string;

      /** 虚拟客服名称 */
      employeeName: string;

      /** 传真 */
      fax: string;

      /** 档案成本 */
      fileCost: string;

      /** 档案费价格 */
      filePrice: string;

      /** 五险合一成本 */
      fiveinoneInsurCost: string;

      /** 大区 */
      governingArea: string;

      /** 大区名称 */
      governingAreaName: string;

      /** 公积金成本 */
      providentFundCost: string;

      /** 外部供应商编码 */
      providerCode: string;

      /** 外部供应商英文名称 */
      providerEnglishNem: string;

      /** 主键id */
      providerId: string;

      /** 供应商名称 */
      providerName: string;

      /** 三险合一成本 */
      threeinoneInsurCost: string;

      /** 登录账号 */
      userName: string;

      /** 邮编 */
      zipCode: string;
    }

    export class providerGroupContactQuery {
      /** 所属供应商集团 */
      contactBelongsId: string;

      /** 联系人类型 */
      contactType: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class providerGroupContractQuery {
      /** 创建人 */
      approveCommitBy: string;

      /** 审批状态 */
      approveStatus: string;

      /** 城市id */
      cityId: string;

      /** 合同名称 */
      contractName: string;

      /** 合同状态 */
      contractStatus: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** provinceId */
      provinceId: string;

      /** 供应商集团编码 */
      prvdGroupCode: string;

      /** 合同编号 */
      prvdGroupContractId: string;

      /** 供应商集团名称 */
      prvdGroupName: string;

      /** roleId */
      roleId: string;

      /** startIndex */
      startIndex: number;
    }

    export class providerGroupDTO {
      /** 城市id */
      cityId: string;

      /** 联系人 */
      contact: string;

      /** 联系电话1 */
      contactTel1: string;

      /** 联系电话2 */
      contactTel2: string;

      /** 最终结束日期 */
      contractEndDate: string;

      /** 合同起始日期 */
      contractStartDate: string;

      /** 人力资源许可证 */
      humanResourceLicence: string;

      /** 删除标记 */
      isDeleted: string;

      /** 劳务派遣许可证 */
      laborDispatchLicence: string;

      /** 最晚支付日 */
      latestPayDate: string;

      /** 供应商集团编码 */
      prvdGroupCode: string;

      /** 主键id */
      prvdGroupId: string;

      /** 供应商集团名称 */
      prvdGroupName: string;

      /** 社会统一信用代码 */
      socialCreditCode: string;
    }

    export class providerGroupQuery {
      /** endIndex */
      endIndex: number;

      /** 是否删除，0否1是 */
      isDeleted: string;

      /** 是否展示符合登录人数据权限，1表示是，不传值表示否 */
      isRestrictAuth: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商集团编码 */
      prvdGroupCode: string;

      /** 供应商集团名称 */
      prvdGroupName: string;

      /** startIndex */
      startIndex: number;
    }

    export class providerGroupVO {
      /** 城市id */
      cityId: string;

      /** 联系人 */
      contact: string;

      /** 联系电话1 */
      contactTel1: string;

      /** 联系电话2 */
      contactTel2: string;

      /** 最终结束日期 */
      contractEndDate: string;

      /** 合同起始日期 */
      contractStartDate: string;

      /** 人力资源许可证 */
      humanResourceLicence: string;

      /** 劳务派遣许可证 */
      laborDispatchLicence: string;

      /** 最晚支付日 */
      latestPayDate: string;

      /** 供应商集团编码 */
      prvdGroupCode: string;

      /** 主键id */
      prvdGroupId: string;

      /** 供应商集团名称 */
      prvdGroupName: string;

      /** 社会统一信用代码 */
      socialCreditCode: string;
    }

    export class providerQuery {
      /** 城市 */
      cityId: string;

      /** endIndex */
      endIndex: number;

      /** 是否排除已所属供应商集团,有值表示是，无值表示否 */
      filterExistsPrvdId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商名称 */
      providerName: string;

      /** startIndex */
      startIndex: number;
    }

    export class providerVO {
      /** 申报截止日 */
      applicationDeadline: string;

      /** 账单地址 */
      billAddress: string;

      /** 账单生成日 */
      billGenDT: string;

      /** 账单锁定日 */
      billLockDT: string;

      /** 账单地址邮编 */
      billZipCode: string;

      /** 城市 */
      cityId: string;

      /** 联系人 */
      contact: string;

      /** 联系地址 */
      contactAddress: string;

      /** 联系人部门 */
      contactDept: string;

      /** 联系电话1 */
      contactTel1: string;

      /** 联系电话2 */
      contactTel2: string;

      /** 用户部门关系id */
      deptUserId: string;

      /** 电子邮件 */
      email: string;

      /** 虚拟客服 */
      employeeId: string;

      /** 传真 */
      fax: string;

      /** 档案成本 */
      fileCost: string;

      /** 档案费价格 */
      filePrice: string;

      /** 五险合一成本 */
      fiveinoneInsurCost: string;

      /** 大区 */
      governingBranch: string;

      /** 新虚拟客服 */
      newEmployeeId: string;

      /** 公积金成本 */
      providentFundCost: string;

      /** 外部供应商编码 */
      providerCode: string;

      /** 外部供应商英文名称 */
      providerEnglishNem: string;

      /** 主键id */
      providerId: string;

      /** 供应商名称 */
      providerName: string;

      /** 三险合一成本 */
      threeinoneInsurCost: string;

      /** 邮编 */
      zipCode: string;
    }

    export class prvdGroupRelationDTO {
      /** 城市id */
      cityId: string;

      /** 供应商编码 */
      providerCode: string;

      /** 供应商id */
      providerId: string;

      /** 供应商名称 */
      providerName: string;

      /** 供应商 */
      prvdGroupCode: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** 关系id */
      relationId: string;
    }

    export class prvdGroupRelationVO {
      /** 供应商id */
      providerId: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** 关系id */
      relationId: string;
    }
  }
}

declare namespace API {
  export namespace externalsupplier {
    /**
     * 残疾保障金数据维护
     */
    export namespace disabilityImp {
      /**
        * 导出
导出
        * /disabilityImp/exportExcel
        */
      export namespace exportExcel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询
查询
        * /disabilityImp/getDisabilityImp
        */
      export namespace getDisabilityImp {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.DisabilityQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.DisabilityQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询
查询
        * /disabilityImp/getDisabilityImpNew
        */
      export namespace getDisabilityImpNew {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.DisabilityImp,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.DisabilityImp,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存
保存
        * /disabilityImp/saveDisabilityImp
        */
      export namespace saveDisabilityImp {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.externalsupplier.DisabilityImp>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.externalsupplier.DisabilityImp>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 供应商订单管理
     */
    export namespace externalEmpHireSep {
      /**
        * 删除供应商费用段
删除供应商费用段
        * /externalEmpHireSep/deletePdHsEmpFee
        */
      export namespace deletePdHsEmpFee {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.EmployeeFee,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.EmployeeFee,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除外部供应商月度表数据
删除外部供应商月度表数据
        * /externalEmpHireSep/deletePdHsEmpFeeMonth
        */
      export namespace deletePdHsEmpFeeMonth {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.EmployeeFee,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.EmployeeFee,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出费用变更任务
导出费用变更任务
        * /externalEmpHireSep/exportFeeChangeTask
        */
      export namespace exportFeeChangeTask {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出增员文件
导出,增员接单确认type=1,增员派单确认type=null,个人订单导出type=35,离职确认导出sepType!=null,字段见页面
        * /externalEmpHireSep/exportFile
        */
      export namespace exportFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 生成外部供应商价费用段
生成外部供应商价费用段
        * /externalEmpHireSep/genExPdFeeMonth
        */
      export namespace genExPdFeeMonth {
        export class Params {
          /** empHireSepId */
          empHireSepId: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据输入的条件查询供应商里的个人订单详细页面的客户费用段月度表
根据输入的条件查询供应商里的个人订单详细页面的客户费用段月度表
        * /externalEmpHireSep/getEmployeeFeeMonthDetailList
        */
      export namespace getEmployeeFeeMonthDetailList {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.EmployeeFeeMonthVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.EmployeeFeeMonthVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询供应商里的个人订单的记录
查询供应商里的个人订单的记录
        * /externalEmpHireSep/getExEmployeeOrderList
        */
      export namespace getExEmployeeOrderList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取下拉框,exFeeTempltList供应商收费模板,exQuotationList供应商报价单
获取下拉框,exFeeTempltList供应商收费模板,exQuotationList供应商报价单
        * /externalEmpHireSep/getExternalDropdownList
        */
      export namespace getExternalDropdownList {
        export class Params {
          /** providerId */
          providerId?: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取exFeeTempltList供应商收费模板下拉框
获取exFeeTempltList供应商收费模板下拉框
        * /externalEmpHireSep/getExternalFrequencyDropdownList
        */
      export namespace getExternalFrequencyDropdownList {
        export class Params {
          /** providerId */
          providerId?: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取exQuotationList供应商报价单下拉框
获取exQuotationList供应商报价单下拉框
        * /externalEmpHireSep/getExternalQuotationDropdownList
        */
      export namespace getExternalQuotationDropdownList {
        export class Params {
          /** providerId */
          providerId?: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据员工上下岗id获取员工费用信息
根据员工上下岗id获取员工费用信息
        * /externalEmpHireSep/getPdEmpFeeByEmpHireSepId
        */
      export namespace getPdEmpFeeByEmpHireSepId {
        export class Params {
          /** empHireSepId */
          empHireSepId: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据输入的条件查询供应商里的个人订单详细页面的供应商费用段月度表
根据输入的条件查询供应商里的个人订单详细页面的供应商费用段月度表
        * /externalEmpHireSep/getPdEmployeeFeeMonthDetailList
        */
      export namespace getPdEmployeeFeeMonthDetailList {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.EmployeeFeeMonthVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.EmployeeFeeMonthVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取外部供应商客服
获取外部供应商客服
        * /externalEmpHireSep/getProviderCs
        */
      export namespace getProviderCs {
        export class Params {
          /** assigneeProviderId */
          assigneeProviderId: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入变更收费模板任务明细表
插入变更收费模板任务明细表
        * /externalEmpHireSep/insertPdFeeChangeDetail
        */
      export namespace insertPdFeeChangeDetail {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.FeeChangeDetail,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FeeChangeDetail,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新变更收费模板任务表
更新变更收费模板任务表
        * /externalEmpHireSep/insertPdFeeChangeTask
        */
      export namespace insertPdFeeChangeTask {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.FeeChangeTask,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FeeChangeTask,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增供应商费用段
新增供应商费用段
        * /externalEmpHireSep/insertPdHsEmpFee
        */
      export namespace insertPdHsEmpFee {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.EmployeeFee,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.EmployeeFee,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询可以变更的员工
查询可以变更的员工
        * /externalEmpHireSep/queryEmpListForChangeTemplt
        */
      export namespace queryEmpListForChangeTemplt {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FeeChangeTaskQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FeeChangeTaskQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 变更订单查询
变更订单查询
        * /externalEmpHireSep/queryExEmpOrderListForEdit
        */
      export namespace queryExEmpOrderListForEdit {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 接单完善查询
接单完善查询
        * /externalEmpHireSep/queryExEmpOrderListForPer
        */
      export namespace queryExEmpOrderListForPer {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 离职接单确认查询
离职接单确认查询
        * /externalEmpHireSep/queryExEmpOrderListForSepCon
        */
      export namespace queryExEmpOrderListForSepCon {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新变更收费模板任务明细表
更新变更收费模板任务明细表
        * /externalEmpHireSep/queryPdEfTemplateById
        */
      export namespace queryPdEfTemplateById {
        export class Params {
          /** templateId */
          templateId: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询变更任务
查询变更任务
        * /externalEmpHireSep/queryPdFeeChangeTaskList
        */
      export namespace queryPdFeeChangeTaskList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FeeChangeTaskQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FeeChangeTaskQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取供应商报价单费用段
获取供应商报价单费用段
        * /externalEmpHireSep/queryPdHsEmpFee
        */
      export namespace queryPdHsEmpFee {
        export class Params {
          /** empHireSepId */
          empHireSepId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.EmployeeFee;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 变更供应商报价单
变更供应商报价单
        * /externalEmpHireSep/updateExQuotation
        */
      export namespace updateExQuotation {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.ExternalEmpHireSepVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ExternalEmpHireSepVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新变更收费模板任务明细表
更新变更收费模板任务明细表
        * /externalEmpHireSep/updateExternalFeeFrequency
        */
      export namespace updateExternalFeeFrequency {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.FeeChangeTaskVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FeeChangeTaskVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新变更收费模板任务明细表
更新变更收费模板任务明细表
        * /externalEmpHireSep/updatePdFeeChangeDetail
        */
      export namespace updatePdFeeChangeDetail {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.FeeChangeDetail,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FeeChangeDetail,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入变更收费模板任务表
插入变更收费模板任务表
        * /externalEmpHireSep/updatePdFeeChangeTask
        */
      export namespace updatePdFeeChangeTask {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.FeeChangeTask,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FeeChangeTask,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新供应商费用段
更新供应商费用段
        * /externalEmpHireSep/updatePdHsEmpFee
        */
      export namespace updatePdHsEmpFee {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.EmployeeFee,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.EmployeeFee,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 分公司信息维护相关接口
     */
    export namespace externalSupplier {
      /**
        * 批量删除供应商银行账号
批量删除供应商银行账号
        * /externalsupplier/bank/batchDelete
        */
      export namespace providerBankBatchDelete {
        export class Params {
          /** 供应商银行账号ID，逗号分隔 */
          bankAcctIds: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 供应商银行账号列表
查询供应商银行账号列表
        * /externalsupplier/bank/list
        */
      export namespace providerBankList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.providerBankDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerBankQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerBankQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 银行编码
银行编码
        * /externalsupplier/bank/queryBankPassage
        */
      export namespace getCustPayerDorpDownList {
        export class Params {
          /** bankCode */
          bankCode: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增供应商银行账号
新增供应商银行账号
        * /externalsupplier/bank/save
        */
      export namespace providerBankSave {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerBankVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerBankVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 设置分公司默认出款账号
设置分公司默认出款账号
        * /externalsupplier/bank/setDefaultAcct
        */
      export namespace setDefaultAcct {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderBank,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderBank,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改供应商银行账号
修改供应商银行账号
        * /externalsupplier/bank/update
        */
      export namespace providerBankUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerBankVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerBankVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 档案/残障金支付
     */
    export namespace filePay {
      /**
       * getCoresFeeMonCount
       * /filePay/getCoresFeeMonCount
       */
      export namespace getCoresFeeMonCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取残障金金额
获取残障金金额
        * /filePay/getDisbSecurityPayAmt
        */
      export namespace getDisbSecurityPayAmt {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取供应商金额
获取供应商金额
        * /filePay/getPrvdOrGroupFilePayAmt
        */
      export namespace getPrvdOrGroupFilePayAmt {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 支付提交审批
支付提交审批
        * /filePay/postPayApprove
        */
      export namespace postPayApprove {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 客户级数据
客户级数据
        * /filePay/selectFileCustDetial
        */
      export namespace selectFileCustDetial {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FileCustSnap,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FileCustSnap,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 员工数据
员工数据
        * /filePay/selectFileEmpDetial
        */
      export namespace selectFileEmpDetial {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FileEmpSnap,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FileEmpSnap,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询
查询
        * /filePay/selectFilePayOfAssignerProvider
        */
      export namespace selectFilePayOfAssignerProvider {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 启动流程
启动流程
        * /filePay/startWorkFlow
        */
      export namespace startWorkFlow {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 支付审批通过
支付审批通过
        * /filePay/uptPayPass
        */
      export namespace uptPayPass {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 退回修改
退回修改
        * /filePay/uptPayReject
        */
      export namespace uptPayReject {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 终止
终止
        * /filePay/uptPayTerminal
        */
      export namespace uptPayTerminal {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 外部供应商报价单账单维护
     */
    export namespace outerPrvdManage {
      /**
        * 批量分配新的责任客服
批量分配新的责任客服
        * /outerProvider/manage/batchAssignedPrvdCSAuth
        */
      export namespace batchAssignedPrvdCSAuth {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderCSAuthQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderCSAuthQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 账单设置默认
账单设置默认
        * /outerProvider/manage/bill/default/{providerId}/{pdEfTemplateId}
        */
      export namespace takeEffectPrvdBill {
        export class Params {
          /** pdEfTemplateId */
          pdEfTemplateId: string;
          /** providerId */
          providerId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除账单
删除账单
        * /outerProvider/manage/bill/delete
        */
      export namespace deleteBill {
        export class Params {
          /** 主键id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 查询账单
       * /outerProvider/manage/bill/list
       */
      export namespace listBill {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.pdEfTemplateDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.pdEfTemplateQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.pdEfTemplateQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存账单
保存账单
        * /outerProvider/manage/bill/saveOrUpdate
        */
      export namespace saveBill {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.pdEfTemplateVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.pdEfTemplateVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询供应商分配客服权限
分页查询供应商分配客服权限
        * /outerProvider/manage/pageQueryPrvdAssignCSAuth
        */
      export namespace pageQueryPrvdAssignCSAuth {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderCSAuth,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderCSAuth,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询供应商分配客服权限历史
分页查询供应商分配客服权限历史
        * /outerProvider/manage/pageQueryPrvdAssignCSAuthHis
        */
      export namespace pageQueryPrvdAssignCSAuthHis {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerAssignCSAuthHisQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerAssignCSAuthHisQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询供应商集团联系人
查询供应商集团联系人
        * /outerProvider/manage/provider/group/contact
        */
      export namespace listProviderGroupContacts {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.providerGroupContactQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerGroupContactQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量删除供应商集团联系人
批量删除供应商集团联系人
        * /outerProvider/manage/provider/group/contact/delete
        */
      export namespace deleteProviderGroupContact {
        export class Params {
          /** 主键id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增或修改供应商联系人
新增或修改供应商联系人
        * /outerProvider/manage/provider/group/contact/saveOrUpdate
        */
      export namespace saveOrUpdateProviderGroupContact {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerContactVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerContactVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量删除供应商集团
批量删除供应商集团
        * /outerProvider/manage/provider/group/delete
        */
      export namespace deleteProviderGroup {
        export class Params {
          /** 主键id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除集团供应商分公司
删除集团供应商分公司
        * /outerProvider/manage/provider/group/deleteBranch
        */
      export namespace deleteBranch {
        export class Params {
          /** groupBranchId */
          groupBranchId: string;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出供应商集团Excel
导出供应商集团Excel
        * /outerProvider/manage/provider/group/exportExcel
        */
      export namespace exportExcel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询集团供应商分公司
查询集团供应商分公司
        * /outerProvider/manage/provider/group/getBranch
        */
      export namespace getBranch {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.FilterEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询供应商集团
查询供应商集团
        * /outerProvider/manage/provider/group/list
        */
      export namespace listProviderGroup {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.providerGroupDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerGroupQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerGroupQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存供应商关联关系
保存供应商关联关系
        * /outerProvider/manage/provider/group/relation/save
        */
      export namespace addPrvdGroupRelation {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.externalsupplier.prvdGroupRelationVO>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.externalsupplier.prvdGroupRelationVO>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询供应商关联关系
查询供应商关联关系
        * /outerProvider/manage/provider/group/relation/{prvdId}
        */
      export namespace queryPrvdGroupRelation {
        export class Params {
          /** prvdId */
          prvdId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.prvdGroupRelationDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 单个新增集团供应商
单个新增集团供应商
        * /outerProvider/manage/provider/group/save
        */
      export namespace savePrvdGroup {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderGroup,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderGroup,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存集团供应商分公司
保存集团供应商分公司
        * /outerProvider/manage/provider/group/saveBranch
        */
      export namespace saveBranch {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.externalsupplier.FilterEntity>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.externalsupplier.FilterEntity>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或修改集团供应商
批量新增或修改集团供应商
        * /outerProvider/manage/provider/group/saveOrUpdate
        */
      export namespace saveOrUpdateProviderGroup {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.batchSaveOrUpdateProviderGroupVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.batchSaveOrUpdateProviderGroupVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改集团供应商
修改集团供应商
        * /outerProvider/manage/provider/group/update
        */
      export namespace updatePrvdGroup {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderGroup,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderGroup,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 报价单设为默认
报价单设为默认
        * /outerProvider/manage/quotation/default/{providerId}/{quotationId}
        */
      export namespace takeEffectPrvdOffer {
        export class Params {
          /** providerId */
          providerId: string;
          /** quotationId */
          quotationId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除外部供应商报价单
删除外部供应商报价单
        * /outerProvider/manage/quotation/delete
        */
      export namespace deleteQuotation {
        export class Params {
          /** 主键id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询报价单
查询报价单
        * /outerProvider/manage/quotation/list
        */
      export namespace listQuotation {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.pdQuotationDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.pdQuotationQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.pdQuotationQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存报价单
保存报价单
        * /outerProvider/manage/quotation/save
        */
      export namespace saveQuotation {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.pdQuotationVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.pdQuotationVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新分配责任客服权限
更新分配责任客服权限
        * /outerProvider/manage/uptAssignCSAuth
        */
      export namespace uptAssignCSAuth {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderCSAuth,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderCSAuth,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 外部供应商维护
     */
    export namespace provider {
      /**
        * 批量删除外部供应商
批量删除外部供应商
        * /provider/delete
        */
      export namespace remove {
        export class Params {
          /** 主键id */
          ids: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出Excel
导出Excel
        * /provider/exportExcel
        */
      export namespace exportExcel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询外部供应商信息
查询外部供应商信息
        * /provider/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.providerDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存或修改外部供应商
saveOrUpdate
        * /provider/saveOrUpdate
        */
      export namespace saveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取外部供应商详情
获取外部供应商详情
        * /provider/{id}
        */
      export namespace get {
        export class Params {
          /** id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.providerDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 供应商集团合同维护
     */
    export namespace providerContract {
      /**
        * 添加供应商集团合同关联协议
添加供应商集团合同关联协议
        * /providerContract/addAgree
        */
      export namespace addAgree {
        export class Params {
          /** branchId */
          branchId: string;
          /** prvdGroupContractId */
          prvdGroupContractId: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 审批
审批
        * /providerContract/approve
        */
      export namespace approve {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderGroupContract,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderGroupContract,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 驳回
驳回
        * /providerContract/back
        */
      export namespace back {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderGroupContract,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderGroupContract,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除供应商集团合同关联协议
删除供应商集团合同关联协议
        * /providerContract/delAgree
        */
      export namespace delAgree {
        export class Params {
          /** prvdGroupAgreeId */
          prvdGroupAgreeId: string;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.PrvdGroupContractAgree;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出Excel
导出Excel
        * /providerContract/exportExcel
        */
      export namespace exportExcel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 添加正式合同附件
添加正式合同附件
        * /providerContract/formalContract
        */
      export namespace formalContract {
        export class Params {
          /** prvdGroupContractId */
          prvdGroupContractId: string;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增
新增
        * /providerContract/insert
        */
      export namespace insert {
        export class Params {
          /** activityNameEn */
          activityNameEn?: any;
          /** undefined */
          add?: any;
          /** 提交人 */
          approveCommitBy?: any;
          /** 提交人名称 */
          approveCommitByName?: any;
          /** 审批意见 */
          approveOpinion?: any;
          /** 审批过程 */
          approveProcess?: any;
          /** 审批状态 */
          approveStatus?: any;
          /** undefined */
          approveStatusName?: any;
          /** 批次号,用于备份 */
          batchId?: any;
          /** 账单表别名,控制客户权限用 */
          billAlias?: any;
          /** 财务大类 */
          bizCategory?: any;
          /** 业务类型,控制小合同权限用 */
          bizmanType?: any;
          /** sys_branch_title别名,控制合同权限用 */
          branchTitleAlias?: any;
          /** 城市id */
          cityId?: any;
          /** 城市名称 */
          cityName?: any;
          /** clientOperation */
          clientOperation?: any;
          /** flex是否行编号 */
          clientRowSeq?: any;
          /** flex是否选择 */
          clientSelected?: any;
          /** 联系人 */
          contact?: any;
          /** 联系电话 */
          contactTel?: any;
          /** 合同表别名,控制合同权限用 */
          contractAlias?: any;
          /** undefined */
          contractCode?: any;
          /** 合同终止期 */
          contractEndDate?: any;
          /** 合同名称 */
          contractName?: any;
          /** 合同生效期 */
          contractStartDate?: any;
          /** 合同状态 */
          contractStatus?: any;
          /** undefined */
          contractStatusName?: any;
          /** 创建人 */
          createBy?: any;
          /** createBy2 */
          createBy2?: any;
          /** 创建日期 */
          createDt?: any;
          /** 客户表别名,控制客户权限用 */
          customerAlias?: any;
          /** undefined */
          del?: any;
          /** undefined */
          endIndex?: any;
          /** 导入类型,扩充使用 */
          expType?: any;
          /** file */
          file?: File;
          /** 附件名称 */
          fileName?: any;
          /** 合同附件 */
          filePath?: any;
          /** filterByAuthNum */
          filterByAuthNum?: any;
          /** 提供查询是做为排除条件使用 */
          filterId?: any;
          /** 正式合同名称 */
          formalContractName?: any;
          /** 正式合同路径 */
          formalContractPath?: any;
          /** funBtnActiveStr */
          funBtnActiveStr?: any;
          /** 登录人所属分公司,控制小合同权限用 */
          governingBranch?: any;
          /** 人力资源许可证 */
          humanResourceLicence?: any;
          /** inId */
          inId?: any;
          /** 是否账单查询 */
          isBillQuery?: any;
          /** flex是否变化 */
          isChanged?: any;
          /** 删除标记 */
          isDeleted?: any;
          /** 劳务派遣许可证 */
          laborDispatchLicence?: any;
          /** 最晚支付日 */
          latestPayDate?: any;
          /** 模拟人 */
          mimicBy?: any;
          /** undefined */
          noChange?: any;
          /** 原供应商集团合同Id */
          oldContractId?: any;
          /** 页数 */
          pageNum?: any;
          /** 每页记录数,默认65536条 */
          pageSize?: any;
          /** 流程审批角色名字 */
          processAprRoleName?: any;
          /** 流程INSID */
          processInsId?: any;
          /** 供应商集团权限添加 */
          providerIdAlias?: any;
          /** undefined */
          provinceId?: any;
          /** undefined */
          provinceName?: any;
          /** 代理人 */
          proxyBy?: any;
          /** 供应商集团Code */
          prvdGroupCode?: any;
          /** 供应商集团合同Id */
          prvdGroupContractId?: any;
          /** 供应商集团Id */
          prvdGroupId?: any;
          /** prvdGroupIdAlias */
          prvdGroupIdAlias?: any;
          /** 供应商集团名称 */
          prvdGroupName?: any;
          /** undefined */
          remark?: any;
          /** 卡纯代发人员,默认过滤 */
          restrictPure?: any;
          /** 卡权限 */
          restrictType?: any;
          /** undefined */
          roleId?: any;
          /** 保存0，提交1 */
          saveOrCommit?: any;
          /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
          selByAuth?: any;
          /** 获取前台勾选key的字符串 */
          selectKeyStr?: any;
          /** 社会统一信用代码 */
          socialCreditCode?: any;
          /** undefined */
          startIndex?: any;
          /** 小合同表别名,控制小合同权限用 */
          subcontractAlias?: any;
          /** 修改人 */
          updateBy?: any;
          /** 修改日期 */
          updateDt?: any;
          /** undefined */
          upt?: any;
          /** 用户id,控制小合同权限用 */
          userId?: any;
          /** 用户管理用户表别名,控制客户权限用 */
          userManageUserAlias?: any;
          /** undefined */
          workItemId?: any;
          /** 流程id */
          workitemId?: any;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询供应商集团合同信息
查询供应商集团合同信息
        * /providerContract/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.ProviderGroupContract;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerGroupContractQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerGroupContractQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询供应商集团合同关联协议
查询供应商集团合同关联协议
        * /providerContract/listAgree
        */
      export namespace listAgree {
        export class Params {
          /** prvdGroupContractId */
          prvdGroupContractId: number;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.PrvdGroupContractAgree;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询供应商集团信息审批信息
查询供应商集团审批信息
        * /providerContract/listProviderApprove
        */
      export namespace listProviderApprove {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.ProviderGroup;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.providerGroupContractQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.providerGroupContractQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 终止
终止
        * /providerContract/terminal
        */
      export namespace terminal {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.ProviderGroupContract,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ProviderGroupContract,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改
修改
        * /providerContract/update
        */
      export namespace update {
        export class Params {
          /** activityNameEn */
          activityNameEn?: any;
          /** undefined */
          add?: any;
          /** 提交人 */
          approveCommitBy?: any;
          /** 提交人名称 */
          approveCommitByName?: any;
          /** 审批意见 */
          approveOpinion?: any;
          /** 审批过程 */
          approveProcess?: any;
          /** 审批状态 */
          approveStatus?: any;
          /** undefined */
          approveStatusName?: any;
          /** 批次号,用于备份 */
          batchId?: any;
          /** 账单表别名,控制客户权限用 */
          billAlias?: any;
          /** 财务大类 */
          bizCategory?: any;
          /** 业务类型,控制小合同权限用 */
          bizmanType?: any;
          /** sys_branch_title别名,控制合同权限用 */
          branchTitleAlias?: any;
          /** 城市id */
          cityId?: any;
          /** 城市名称 */
          cityName?: any;
          /** clientOperation */
          clientOperation?: any;
          /** flex是否行编号 */
          clientRowSeq?: any;
          /** flex是否选择 */
          clientSelected?: any;
          /** 联系人 */
          contact?: any;
          /** 联系电话 */
          contactTel?: any;
          /** 合同表别名,控制合同权限用 */
          contractAlias?: any;
          /** undefined */
          contractCode?: any;
          /** 合同终止期 */
          contractEndDate?: any;
          /** 合同名称 */
          contractName?: any;
          /** 合同生效期 */
          contractStartDate?: any;
          /** 合同状态 */
          contractStatus?: any;
          /** undefined */
          contractStatusName?: any;
          /** 创建人 */
          createBy?: any;
          /** createBy2 */
          createBy2?: any;
          /** 创建日期 */
          createDt?: any;
          /** 客户表别名,控制客户权限用 */
          customerAlias?: any;
          /** undefined */
          del?: any;
          /** undefined */
          endIndex?: any;
          /** 导入类型,扩充使用 */
          expType?: any;
          /** file */
          file?: File;
          /** 附件名称 */
          fileName?: any;
          /** 合同附件 */
          filePath?: any;
          /** filterByAuthNum */
          filterByAuthNum?: any;
          /** 提供查询是做为排除条件使用 */
          filterId?: any;
          /** 正式合同名称 */
          formalContractName?: any;
          /** 正式合同路径 */
          formalContractPath?: any;
          /** funBtnActiveStr */
          funBtnActiveStr?: any;
          /** 登录人所属分公司,控制小合同权限用 */
          governingBranch?: any;
          /** 人力资源许可证 */
          humanResourceLicence?: any;
          /** inId */
          inId?: any;
          /** 是否账单查询 */
          isBillQuery?: any;
          /** flex是否变化 */
          isChanged?: any;
          /** 删除标记 */
          isDeleted?: any;
          /** 劳务派遣许可证 */
          laborDispatchLicence?: any;
          /** 最晚支付日 */
          latestPayDate?: any;
          /** 模拟人 */
          mimicBy?: any;
          /** undefined */
          noChange?: any;
          /** 原供应商集团合同Id */
          oldContractId?: any;
          /** 页数 */
          pageNum?: any;
          /** 每页记录数,默认65536条 */
          pageSize?: any;
          /** 流程审批角色名字 */
          processAprRoleName?: any;
          /** 流程INSID */
          processInsId?: any;
          /** 供应商集团权限添加 */
          providerIdAlias?: any;
          /** undefined */
          provinceId?: any;
          /** undefined */
          provinceName?: any;
          /** 代理人 */
          proxyBy?: any;
          /** 供应商集团Code */
          prvdGroupCode?: any;
          /** 供应商集团合同Id */
          prvdGroupContractId?: any;
          /** 供应商集团Id */
          prvdGroupId?: any;
          /** prvdGroupIdAlias */
          prvdGroupIdAlias?: any;
          /** 供应商集团名称 */
          prvdGroupName?: any;
          /** undefined */
          remark?: any;
          /** 卡纯代发人员,默认过滤 */
          restrictPure?: any;
          /** 卡权限 */
          restrictType?: any;
          /** undefined */
          roleId?: any;
          /** 保存0，提交1 */
          saveOrCommit?: any;
          /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
          selByAuth?: any;
          /** 获取前台勾选key的字符串 */
          selectKeyStr?: any;
          /** 社会统一信用代码 */
          socialCreditCode?: any;
          /** undefined */
          startIndex?: any;
          /** 小合同表别名,控制小合同权限用 */
          subcontractAlias?: any;
          /** 修改人 */
          updateBy?: any;
          /** 修改日期 */
          updateDt?: any;
          /** undefined */
          upt?: any;
          /** 用户id,控制小合同权限用 */
          userId?: any;
          /** 用户管理用户表别名,控制客户权限用 */
          userManageUserAlias?: any;
          /** undefined */
          workItemId?: any;
          /** 流程id */
          workitemId?: any;
        }

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 供应商支付
     */
    export namespace providerPay {
      /**
        * 导出网银模板
导出网银模板
        * /providerPay/exportBankTemplate
        */
      export namespace exportEmpList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /providerPay/exportExcel
        */
      export namespace exportExcel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取供应商支付审批列表
获取供应商支付审批列表
        * /providerPay/getPayAuditList
        */
      export namespace getPayAuditList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 供应商支付查询(按照派单地)
供应商支付查询(按照派单地)
        * /providerPay/pageQueryPayItem
        */
      export namespace pageQueryPayItem {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 供应商支付汇总查询
供应商支付汇总查询
        * /providerPay/pageQueryPaySummary
        */
      export namespace pageQueryPaySummary {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询供应商支付记录
分页查询供应商支付记录
        * /providerPay/pageQueryPrvdBill
        */
      export namespace pageQueryPrvdBill {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 支付提交审批
支付提交审批
        * /providerPay/postPayApprove
        */
      export namespace postPayApprove {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 提交派单地支付记录到资金中间库
提交派单地支付记录到资金中间库
        * /providerPay/postPayment2BFS
        */
      export namespace postPayment2BFS {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据支付汇总记录id查询派单地支付汇总统计
根据支付汇总记录id查询派单地支付汇总统计
        * /providerPay/queryPayAssignStatByAuditId
        */
      export namespace queryPayAssignStatByAuditId {
        export class Params {
          /** payAuditId */
          payAuditId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询供应商支付明细
分页查询供应商支付明细
        * /providerPay/queryPayDetail
        */
      export namespace queryPayDetail {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayDetail,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayDetail,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 跟进应收id,统计供应商支付明细
跟进应收id,统计供应商支付明细
        * /providerPay/queryPayDetailStat
        */
      export namespace queryPayDetailStat {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * queryPrvdPayDetail
       * /providerPay/queryPrvdPayDetail
       */
      export namespace queryPrvdPayDetail {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.externalsupplier.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.QueryPrvdPayDetail,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.QueryPrvdPayDetail,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * syncData
       * /providerPay/syncData
       */
      export namespace syncData {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * updatePayAudit
       * /providerPay/updatePayAudit
       */
      export namespace updatePayAudit {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * uptPayItemStatus
       * /providerPay/uptPayItemStatus
       */
      export namespace uptPayItemStatus {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 支付审批通过
支付审批通过
        * /providerPay/uptPayPass
        */
      export namespace uptPayPass {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 退回修改
退回修改
        * /providerPay/uptPayReject
        */
      export namespace uptPayReject {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 终止
终止
        * /providerPay/uptPayTerminal
        */
      export namespace uptPayTerminal {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PayAudit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据tagObj的id到中间库抓取记录同步到hro库
根据tagObj的id到中间库抓取记录同步到hro库
        * /providerPay/uptSynBFSPayItemStatus
        */
      export namespace uptSynBFSPayItemStatus {
        export class Params {}

        export type Response<T> = defs.externalsupplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.externalsupplier.PrvdPayItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
