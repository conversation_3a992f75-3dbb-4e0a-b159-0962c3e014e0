<!--
 * @Author: 侯成
 * @since: 2019-07-24 13:41:41
 * @lastTime: 2019-08-28 10:28:37
 * @LastAuthor: 侯成
 * @message: 
 -->
# 下拉选择框组件
此组件用于通过 `Object` 和 `Map` 返回选择框。适合于各选项的代号和名称固定的情况，即可以在项目中硬编码。
对于选项的代号不固定的情形，请考虑使用[通用选择器](2-components/2.5-base-selectors.md)。

## 基础数据定义
鉴于此项目过于庞大，此前在个别文件中随意定义Object的方式已无法满足工程组织，遂在`src/utils`下开辟`settings`目录，专门用户数据定义：
示例：

```ts
// src\utils\settings\sales\contract.ts
// 合同类型代号
export const contractTypeCode = {
  proxy: 1,
  dispatch: 2,
  epiboly: 3,
};
// 合同类型代号名称映射
export const contractTypeMap = new Map<number, string>([
  [contractTypeCode.proxy, '代理'],
  [contractTypeCode.dispatch, '派遣'],
  [contractTypeCode.epiboly, '业务外包'],
])
// 不使用code的定义方式
// 缴费类型
export const payTypeMap = new Map<number, string>([
  [1, '月结'],
  [2, '账期'],
  [3, '垫款'],
  [4, '其他'],
])
```
目录结构同`pages`下的页面结构相同。

## 标准版下拉选择

![](../assets/images/2.1-basic-selectors.png)

`mapToSelectors`接收一个Map，返回一个下拉选择框。基本定义如下：

```tsx
// src/components/Selectors/Selectors.tsx

type PlainPropertyKey = number | string;

export const  mapToSelectors = <T extends PlainPropertyKey>(nameMap: Map<T, string>, options?: SelectProps) => JSX.Element
```
参数说明：
* `T`, Map中key的类型，`number`或`string`。
* `nameMap`, 待渲染的Map数据。类型为Map<T, string>。
* `options`，更多选项，用于给`Select`传递属性。

## 使用示例
```tsx
// src\pages\Sales\ContractManage\ContractForm.tsx
<Row gutter={\{ md: 24, lg: 24, xl: 48 }\}>
  <Col md={8} sm={12}>
    <FormItem label="合同小类">
      {getFieldDecorator('contractSubType', {
        rules: formRules.contractSubType,
        initialValue: zeroToNull(contractAddForm!.contractSubType),
      })(mapToSelectors(contractSubTypeMap))}
    </FormItem>
  </Col>
  {this.contractDate()}
</Row>
```

##  标准版布尔选择

在表单设计中，经常遇到仅有两个选项即【是】、【否】，的下拉选择框。在大量输入框整齐排列的场景，使用按钮控件`Switch`会影响影响表单设计的整体性，所以并不适用。因此，需要一个自定义组件，同时满足排列整齐和语义清晰两项要求。

![](../assets/images/2.6-switch-selectors.png)

`mapToSwitch` 是专用于二选一选择框的。基本定义如下：
```tsx
// src/components/Selectors/Selectors.tsx
export function mapToSwitch<T extends PlainPropertyKey>(
  nameMap: Map<T, string>,
  options?: SelectProps,
) => JSX.Element
```

参数说明：
* `T`, Map中key的类型，`number`或`string`。
* `nameMap`, 待渲染的Map数据。类型为Map<T, string>。
* `options`，更多选项，用于给`Select`传递属性。

## 使用示例

```tsx
// src\pages\emphiresep\SubcontractManage\AddForms\Settings.tsx
<FormItem label="是否单立户">
  {getFieldDecorator('isIh', { rules: formRules.isIh, initialValue: scTempData.isIh })(
    mapToSwitch(isIhMap),
  )}
</FormItem>
```

## 基于下拉选择框的数据展示组件

`mapToSelectView` 是用于展示下拉选择结果的组件。基本定义：
```tsx
// src\components\Selectors\Selectors.tsx
export const mapToSelectView = <T extends PlainPropertyKey>(
  nameMap: Map<T | undefined, string>,
  value?: T,
) => JSX.Element;
```
参数说明：
* `T`, Map中key的类型，`number`或`string`。
* `nameMap`, 待渲染的Map数据。类型为Map<T, string>。
* `value` 是待展示值，类型为 `T`。

使用示例：
```tsx
// src\pages\emphiresep\SubcontractManage\AddForms\Settings.tsx
<FormItem label="合同版本">
  {mapToSelectView(contractEditionMap, contractData.contractEdition)}
</FormItem>
```

`mapToSwitchView` 是用于展示下拉二选一结果的组件。基本定义：
```tsx
export const mapToSwitchView = <T extends PlainPropertyKey>(
  nameMap: Map<T | undefined, undefined | string>,
  status?: T,
): JSX.Element => JSX.Element
```
参数说明：
* `T`, Map中key的类型，`number`或`string`。
* `nameMap`, 待渲染的Map数据。类型为Map<T, string>。
* `status` 是待展示值，类型为 `T`。

使用示例：
```tsx
// src\pages\emphiresep\SubcontractManage\AddForms\Preview.tsx
<FormItem label="合同版本">
  {mapToSelectView(contractEditionMap, contractData.contractEdition)}
</FormItem>
```
