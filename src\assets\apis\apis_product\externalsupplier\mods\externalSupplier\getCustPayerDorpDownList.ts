import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/externalsupplier/bank/queryBankPassage
     * @desc 银行编码
银行编码
     * hasForm: true
     * hasBody: true
     */

export class Params {
  /** bankCode */
  bankCode: string;
}
export const init = new defs.externalsupplier.CommonResponse();
export const url =
  '/rhro-service-1.0/externalsupplier/bank/queryBankPassage:POST';
export const initialUrl =
  '/rhro-service-1.0/externalsupplier/bank/queryBankPassage';
export const cacheKey = '_externalsupplier_bank_queryBankPassage_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/externalsupplier/bank/queryBankPassage`;
  const fetchOption = {
    reqUrl,
    requestType: 'form',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/externalsupplier/bank/queryBankPassage`;
  const fetchOption = {
    reqUrl,
    requestType: 'form',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
