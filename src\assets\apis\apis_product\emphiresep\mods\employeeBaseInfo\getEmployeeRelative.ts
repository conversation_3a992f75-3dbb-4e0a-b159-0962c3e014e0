import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employeeBaseInfo/getEmployeeRelative
     * @desc 根据条件得到员工亲属的分页查询结果
根据条件得到员工亲属的分页查询结果
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** add */
  add?: boolean;
  /** 批次号,用于备份 */
  batchId?: string;
  /** 账单表别名,控制客户权限用 */
  billAlias?: string;
  /** 财务大类 */
  bizCategory?: string;
  /** 业务类型,控制小合同权限用 */
  bizmanType?: string;
  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias?: string;
  /** clientOperation */
  clientOperation?: number;
  /** flex是否行编号 */
  clientRowSeq?: number;
  /** flex是否选择 */
  clientSelected?: boolean;
  /** 合同表别名,控制合同权限用 */
  contractAlias?: string;
  /** countSqlName */
  countSqlName: string;
  /** 创建人 */
  createBy?: string;
  /** createBy2 */
  createBy2?: string;
  /** 创建日期 */
  createDt?: string;
  /** 客户表别名,控制客户权限用 */
  customerAlias?: string;
  /** del */
  del?: boolean;
  /** 员工id */
  empId?: string;
  /** 员工亲属id */
  empRelativesId?: string;
  /** 员工唯一号 */
  employeeCode?: string;
  /** 员工姓名 */
  employeeName?: string;
  /** endIndex */
  endIndex?: number;
  /** 导入类型,扩充使用 */
  expType?: string;
  /** filterByAuthNum */
  filterByAuthNum?: string;
  /** 提供查询是做为排除条件使用 */
  filterId?: string;
  /** funBtnActiveStr */
  funBtnActiveStr?: string;
  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch?: string;
  /** 证件号码 */
  idCardNum?: string;
  /** 证件类型 */
  idCardType?: string;
  /** inId */
  inId?: string;
  /** 是否账单查询 */
  isBillQuery?: string;
  /** flex是否变化 */
  isChanged?: boolean;
  /** 删除标记 */
  isDeleted?: string;
  /** 是否有效 */
  isValid?: string;
  /** limit */
  limit: number;
  /** 模拟人 */
  mimicBy?: string;
  /** noChange */
  noChange?: boolean;
  /** 页数 */
  pageNum?: number;
  /** pageQuerySqlName */
  pageQuerySqlName: string;
  /** 每页记录数,默认65536条 */
  pageSize?: number;
  /** 流程审批角色名字 */
  processAprRoleName?: string;
  /** 供应商集团权限添加 */
  providerIdAlias?: string;
  /** 代理人 */
  proxyBy?: string;
  /** prvdGroupIdAlias */
  prvdGroupIdAlias?: string;
  /** 生日 */
  relativeBirthday?: string;
  /** 性别0 女  1 男 */
  relativeGender?: string;
  /** 证件号码 */
  relativeIdCardNum?: string;
  /** 证件类型 */
  relativeIdCardType?: string;
  /** 亲属姓名 */
  relativeName?: string;
  /** 雇员信息备注 */
  relativeRemark?: string;
  /** 亲属类型1：配偶 2：子女 */
  relativeType?: string;
  /** 卡纯代发人员,默认过滤 */
  restrictPure?: string;
  /** 卡权限 */
  restrictType?: string;
  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth?: string;
  /** 获取前台勾选key的字符串 */
  selectKeyStr?: string;
  /** start */
  start: number;
  /** startIndex */
  startIndex?: number;
  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias?: string;
  /** 修改人 */
  updateBy?: string;
  /** 修改日期 */
  updateDt?: string;
  /** upt */
  upt?: boolean;
  /** 用户id,控制小合同权限用 */
  userId?: string;
  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias?: string;
}

export const init = new defs.emphiresep.Page();
export const url =
  '/rhro-service-1.0/employeeBaseInfo/getEmployeeRelative:POST';
export const initialUrl =
  '/rhro-service-1.0/employeeBaseInfo/getEmployeeRelative';
export const cacheKey = '_employeeBaseInfo_getEmployeeRelative_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employeeBaseInfo/getEmployeeRelative`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employeeBaseInfo/getEmployeeRelative`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
