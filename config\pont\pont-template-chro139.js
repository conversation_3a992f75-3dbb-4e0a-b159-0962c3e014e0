'use strict';
/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-08-09 18:36:55
 * @LastAuthor: 侯成
 * @LastTime: 2020-11-12 14:44:55
 * @message: message
 */
Object.defineProperty(exports, '__esModule', { value: true });
exports.FileStructures = void 0;
const pont_engine_1 = require('pont-engine');
const pont = require('pont-engine');
const contentTypeMap = {
  'application/x-www-form-urlencoded': 'form',
  'multipart/form-data': 'form-data',
};
const APIRequestConfig = `
type APIRequestConfig = {
  headers?: HeadersInit;
  charset?: 'utf8' | 'gbk';
  requestType?: 'json' | 'form';
  data?: any;
  params?: POVO;
  responseType?: "json" | "text" | "blob" | "arrayBuffer" | "formData";
  useCache?: boolean;
  ttl?: number;
  timeout?: number;
  errorHandler?: (error: any) => void;
  prefix?: string;
  suffix?: string;
}
`;
function urlResolve(url, paramsObj) {
  if (!url.includes('{')) return url;
  const urlArray = url.split(`/`).map((item) => {
    if (item.includes('{')) {
      const paramName = item.replace(/[{}\s]/g, '');
      return `\$\{${paramsObj}.${paramName}\}`;
    }
    return item;
  });
  return urlArray.join('/');
}
function initialUrlResolve(url, paramsObj) {
  if (!url.includes('{')) return url;
  const urlArray = [];
  const _urlArray = url.split(`/`).forEach((item) => {
    if (!item.includes('{')) {
      urlArray.push(item);
    }
  });
  return urlArray.join('/');
}
function urlWashFormat(url) {
  return url.replace('${', '').replace('}', '');
}
const getParamList = (parameters) => {
  const form = !!parameters.find((param) => param.in === 'formData');
  const paramList = [
    {
      paramKey: 'params',
      paramType: 'Params',
    },
    {
      paramKey: 'form',
      paramType: form ? 'FormData' : '',
    },
    {
      paramKey: 'body',
      paramType: 'any',
    },
    {
      paramKey: 'options',
      optional: true,
      paramType: 'any',
      initialValue: '{}',
    },
  ];
  return paramList;
};
const getRequestParams = (parameters) => {
  const paramList = getParamList(parameters).filter((param) => param.paramType);
  return paramList
    .map((param) => `${param.paramKey}${param.optional ? '?' : ''}: ${param.paramType}`)
    .join(', ');
};
const forgeParameters = (parameters) => {
  const params = parameters.map((param) => {
    const name = param.name;
    const description = param.description;
    const required = param.required;
    const typeName = param.dataType.typeName || 'any';
    // const defLine = required ? `            ${name}: ${typeName};` : `            ${name}?: ${typeName};`
    const defLine = required
      ? `            ${name}: ${typeName};`
      : `            ${name}?: ${typeName};`;
    return `            /** ${description} */\n${defLine}`;
  });
  return params;
};
class MyGenerator extends pont_engine_1.CodeGenerator {
  // ${this.getBaseClassesInDeclaration()}
  /** 获取总的类型定义代码 */
  getDeclaration() {
    return `
        ${this.getCommonDeclaration()}
        ${this.getBaseClassesInDeclaration()
          .replace('export class CommonResponse {', 'export class CommonResponse<T = object> {')
          .replace('data?: POVO;', 'data?: T;')}
        ${this.getModsDeclaration()}
      `;
  }
  /** 获取接口内容的类型定义代码 */
  getInterfaceContentInDeclaration(inter) {
    // const apiParams = inter.getRequestParams(this.surrounding);
    const apiParams = getRequestParams(inter.parameters);
    let paramsCode = inter.getParamsCode('Params', this.surrounding);
    // const contentsType =
    //   inter.consumes && inter.consumes.length ? inter.consumes[0] : 'application/json';
    // const consumeType = contentTypeMap[contentsType] || 'json';
    const hasForm = apiParams.includes('form');
    const hasBody = apiParams.includes('body');
    const requestMethod = inter.method.toLowerCase();
    const bodyParmas = inter.getBodyParamsCode();
    // const requestParams = bodyParmas ? `params: Params, bodyParams: ${bodyParmas}` : `params: Params`;
    const requestParamArr = [];
    const parameters = forgeParameters(inter.parameters);
    const isEmptyParams = paramsCode.replace(/(\n|\s)/g, '') === 'classParams{}';
    if (isEmptyParams && !bodyParmas && parameters.length > 0) {
      paramsCode = `class Params{\n${parameters.join('\n').replace('array', 'Array<any>')}
        }`;
    }
    const isGet = requestMethod === 'get';
    const needParams = !isEmptyParams && isGet;
    const needData = !isGet && (hasForm || hasBody || !isEmptyParams);
    needParams && requestParamArr.push('params: Params');
    needData && requestParamArr.push(`data: ${bodyParmas || 'Params'}`);
    requestParamArr.push('options?: APIRequestConfig');
    const requestParams = requestParamArr.join(', ');
    // export type Response = StdRes<${inter.responseType}>;
    const interResponseType = inter.responseType;
    return interResponseType.endsWith('.CommonResponse')
      ? `
      export ${paramsCode}

      export type Response<T> = ${interResponseType}<T>

      export const init: Response<object>;
      export const url: string;
      export const initialUrl: string;
      export const cacheKey: string;
      export function request<T = any>(${requestParams}): Promise<Response<T>>;
      export function requests<T = any>(${requestParams}): Promise<T>;
      `
      : `
        export ${paramsCode}
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: ${interResponseType};
          message: string;
        };

        export const init: Response;
        export function request<T = any>(${requestParams}): Promise<Response<T>>;
        export function requests<T = any>(${requestParams}): Promise<T>;
      `;
  }
  /** 旧实现, 获取接口实现内容的代码 */
  getInterfaceContent2(inter) {
    const ngixPreifx = 'rhro-service-1.0';
    const paramsCode = inter.getParamsCode();
    const requestMethod = inter.method.toLowerCase();
    const bodyParmas = inter.getBodyParamsCode();
    const contentType =
      inter.consumes && inter.consumes.length ? inter.consumes[0] : 'application/json';
    const requestType = contentTypeMap[contentType] || 'json';
    const isEmptyParams = paramsCode.replace(/(\n|\s)/g, '') === 'classParams{}';
    const isGet = requestMethod === 'get';
    const needParams = !isEmptyParams && isGet;
    const needData = !isGet && (bodyParmas || !isEmptyParams);
    const requestArgs = [];
    needParams && requestArgs.push('params: Params');
    // !isGet && requestArgs.push('data: Params');
    needData && requestArgs.push(`data: ${bodyParmas || 'Params'}`);
    requestArgs.push('options?: RequestConfig');
    const requestParams = requestArgs.join(', ');
    const originUrl = `/${ngixPreifx}/${this.dataSource.name}${inter.path}`;
    const paramsObj = isGet ? 'params' : 'data';
    const url = urlResolve(originUrl, paramsObj);
    const urlWashed = urlWashFormat(url);
    const initialUrl = urlWashFormat(initialUrlResolve(originUrl, paramsObj));
    const urlKey = `${urlWashed}:${requestMethod.toUpperCase()}`;
    const cacheKey =
      urlWashed.replace('/rhro-service-1.0/', '').replace(/\//g, '_') +
      '_' +
      requestMethod.toUpperCase();
    const common = `
    import * as defs from '../../baseClass';
    import { fetch, RequestConfig } from '@/utils/request'

    /**
     * @url ${url}
     * @desc ${inter.description}
     */

    export ${inter.getParamsCode()}
    export const init = ${inter.response.getInitialValue()};
    export const url = '${urlKey}';
    export const initialUrl = '${initialUrl}';
    export const cacheKey = '${cacheKey}';`;
    if (requestType === 'form-data') {
      return `
      ${common}
      export async function request(${requestParams}) {
        const formData = new FormData()
        Object.keys(data).forEach((item: any) => {
          formData.append(item, data[item])
        })
        const reqUrl = \`${url}\`
        const fetchOption = {
          reqUrl,
          requestType: '${requestType}',
          data: formData,
          ...options
        } as RequestConfig;

        return fetch.${requestMethod}(reqUrl, fetchOption);
      }
     `;
    } else {
      return `
      ${common}
      export async function request(${requestParams}) {
        const url = \`${url}\`
        const fetchOption = {
          reqUrl,
          requestType: '${requestType}',
          ${needParams ? 'params,' : ''}
          ${needData ? 'data,' : ''}
          ...options
        } as RequestConfig;
 
        return fetch.${requestMethod}(url, fetchOption);
      }
     `;
    }
  }
  /** 获取接口实现内容的代码 */
  getInterfaceContent(inter) {
    const ngixPreifx = 'rhro-service-1.0';
    // const apiParams = inter.getRequestParams(this.surrounding);
    const apiParams = getRequestParams(inter.parameters);
    let paramsCode = inter.getParamsCode('Params', this.surrounding);
    const _bodyParmas = inter.getBodyParamsCode();
    const bodyParmas = _bodyParmas ? _bodyParmas.replace('array', 'Array<any>') : _bodyParmas;
    const hasForm = apiParams.includes('form');
    const hasBody = apiParams.includes('body');
    const contentsType =
      inter.consumes && inter.consumes.length ? inter.consumes[0] : 'application/json';
    const consumeType = contentTypeMap[contentsType] || 'json';
    const requestMethod = inter.method.toLowerCase();
    let requestType = consumeType;
    if (hasForm) {
      const contentType = (inter.consumes || [])[0] || '';
      requestType = contentTypeMap[contentType] || 'form';
    }
    const parameters = forgeParameters(inter.parameters);
    const isEmptyParams = paramsCode.replace(/(\n|\s)/g, '') === 'classParams{}';
    if (isEmptyParams && !bodyParmas && parameters.length > 0) {
      paramsCode = `class Params{
        ${parameters.join('\n').replace('array', 'Array<any>')}
      }`;
    }
    const isGet = requestMethod === 'get';
    const needParams = !isEmptyParams && isGet;
    const needData = !isGet && (hasForm || hasBody || !isEmptyParams);
    const requestArgs = [];
    needParams && requestArgs.push('params: Params');
    needData && requestArgs.push(`data: ${bodyParmas || 'Params'}`);
    requestArgs.push('options?: RequestConfig');
    const requestParams = requestArgs.join(', ');
    const originUrl = `/${ngixPreifx}${inter.path}`;
    const paramsObj = isGet ? 'params' : 'data';
    const url = urlResolve(originUrl, paramsObj);
    const urlWashed = urlWashFormat(url);
    const initialUrl = urlWashFormat(initialUrlResolve(originUrl, paramsObj));
    const urlKey = `${urlWashed}:${requestMethod.toUpperCase()}`;
    const cacheKey =
      urlWashed.replace('/rhro-service-1.0', '').replace(/\//g, '_') +
      '_' +
      requestMethod.toUpperCase();
    const common = `
    import * as defs from '../../baseClass';
    import { fetch, RequestConfig, beforeFetch, afterFetch, checkResStatus,removeSpace } from '@/utils/request'

    /**
     * @url ${url}
     * @desc ${inter.description}
     * hasForm: ${hasForm}
     * hasBody: ${hasBody}
     */

    export ${paramsCode}
    export const init = ${inter.response.getInitialValue()};
    export const url = '${urlKey}';
    export const initialUrl = '${initialUrl}';
    export const cacheKey = '${cacheKey}';`;
    if (requestType === 'form-data') {
      return `
      ${common}
      export async function request(${requestParams}) {
        const formData = new FormData()
        Object.keys(data).forEach((item: any) => {
          formData.append(item, data[item])
        })
        const reqUrl = \`${url}\`
        const fetchOption = {
          reqUrl,
          requestType: '${requestType}',
          data: formData,
          ...options
        } as RequestConfig;

        return new Promise((reolve, reject) => {
          beforeFetch(cacheKey, fetchOption).then(() => {
            fetch
            .${requestMethod}(reqUrl, fetchOption)
            .then((res: StdRes | undefined) => {
              reolve(res)
            })
            .catch(e => reject(e))
            .finally(() => afterFetch(cacheKey));
          })
        });
      }
      export async function requests(${requestParams}) {
        const formData = new FormData()
        Object.keys(data).forEach((item: any) => {
          formData.append(item, data[item])
        })
        const reqUrl = \`${url}\`
        const fetchOption = {
          reqUrl,
          requestType: '${requestType}',
          data: formData,
          ...options
        } as RequestConfig;

        return new Promise((reolve, reject) => {
          beforeFetch(cacheKey, fetchOption).then(() => {
            fetch
            .${requestMethod}(reqUrl, fetchOption)
            .then((res: StdRes | undefined) => {
              if (checkResStatus(res)) return reolve(res ? res.data : res);
              reject(res ? res.message : res);
            })
            .catch(e => reject(e))
            .finally(() => afterFetch(cacheKey));
          })
        })
      }
     `;
    } else {
      return `
      ${common}
      export async function request(${requestParams}) {
        const reqUrl = \`${url}\`
        const fetchOption = {
          reqUrl,
          requestType: '${requestType}',
          ${needParams ? 'params:removeSpace(params),' : ''}
          ${needData ? 'data:removeSpace(data),' : ''}
          ...options
        } as RequestConfig;

        return new Promise((reolve, reject) => {
          beforeFetch(cacheKey, fetchOption).then(() => {
            fetch
            .${requestMethod}(reqUrl, fetchOption)
            .then((res: StdRes | undefined) => {
              reolve(res)
            })
            .catch(e => reject(e))
            .finally(() => afterFetch(cacheKey));
          })
        });
      }
      export async function requests(${requestParams}) {
        const reqUrl = \`${url}\`
        const fetchOption = {
          reqUrl,
          requestType: '${requestType}',
          ${needParams ? 'params:removeSpace(params),' : ''}
          ${needData ? 'data:removeSpace(data),' : ''}
          ...options
        } as RequestConfig;

        return new Promise((reolve, reject) => {
          beforeFetch(cacheKey, fetchOption).then(() => {
            fetch
            .${requestMethod}(reqUrl, fetchOption)
            .then((res: StdRes | undefined) => {
              if (checkResStatus(res)) return reolve(res ? res.data : res);
              reject(res ? res.message : res);
            })
            .catch(e => reject(e))
            .finally(() => afterFetch(cacheKey));
          })
        })
      }
     `;
    }
  }
}
exports.default = MyGenerator;
class FileStructures extends pont.FileStructures {
  getDataSourcesTs() {
    const dsNames = this.generators.map((ge) => ge.dataSource.name);
    return `
      ${dsNames
        .map((name) => {
          return `import { defs as ${name}Defs, ${name} } from './${name}';
          `;
        })
        .join('\n')}

      export const defs = {
        ${dsNames.map((name) => `${name}: ${name}Defs,`).join('\n')}
      };
      export const API = {
        ${dsNames.join(',\n')}
      };

      export function bindAPI(): void {
        (window as any).defs = defs;
        (window as any).API = API;
      };
    `;
  }
  getDataSourcesDeclarationTs() {
    const dsNames = this.generators.map((ge) => ge.dataSource.name);
    return `
    ${dsNames
      .map((name) => {
        return `/// <reference path="./${name}/api.d.ts" />`;
      })
      .join('\n')}

      type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
        [key in Key]: Value;
      }

      ${APIRequestConfig}
    `;
  }
}
exports.FileStructures = FileStructures;
