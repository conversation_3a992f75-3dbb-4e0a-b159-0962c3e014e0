/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-07-02 15:11:32
 * @LastAuthor: 侯成
 * @LastTime: 2020-12-10 16:11:22
 * @message: message
 */
export const USER_LOGIN = '/user/login';
export const USER_LOGIN_COMP = '/User/Login';
export const CUST_LOGIN = '/cust/login';
export const MINIC_USER_LOGIN = '/user/minicLogin';

const routerData = require('./routers/all.json');
export const routerMap = require('./routers/routerMap.json');
export const otherRoutes = require('./routers/otherRoutes.json');

export default [
  // {
  //   name: 'login',
  //   path: CUST_LOGIN,
  //   component: './OutUser/Login',
  // },
  {
    path: '/user',
    component: '../layouts/UserLayout',
    routes: [
      { path: '/user' },
      { path: USER_LOGIN, name: 'login', component: './User/Login' },
      { path: MINIC_USER_LOGIN, name: 'minicLogin', component: './User/MinicLogin' },
      { component: '404' },
    ],
  },
  // 给单页面使用
  {
    path: '/singlePage/:id',
    name: 'single',
    component: './singlePage/index',
  },
  {
    path: '/',
    component: '../layouts/SecurityLayout',
    routes: [
      {
        path: '/',
        component: '../layouts/BasicLayout',
        routes: [
          ...routerData,
          { path: '/', component: './Dashboard/Welcome' },
          {
            path: '/dashboard',
            name: 'dashboard',
            icon: 'dashboard',
            routes: [
              { path: '/dashboard/analysis', name: 'analysis', component: './Dashboard/Welcome' },
            ],
          },
          ...otherRoutes,
        ],
      },
      {
        component: './404',
      },
    ],
  },
];
