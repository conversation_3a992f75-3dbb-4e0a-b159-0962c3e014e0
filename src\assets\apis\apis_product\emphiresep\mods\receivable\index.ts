/**
 * @description 账单模板管理
 */
import * as checkSameReceivableNameByDiffentId from './checkSameReceivableNameByDiffentId';
import * as delReceivableTemplateInterList from './delReceivableTemplateInterList';
import * as deleteReceivableTemplate from './deleteReceivableTemplate';
import * as exportFile from './exportFile';
import * as exportImpHis from './exportImpHis';
import * as getBillQuotedBySubCount from './getBillQuotedBySubCount';
import * as getContractByCustWithState from './getContractByCustWithState';
import * as getImpDetail from './getImpDetail';
import * as getMaxBillLockingDay from './getMaxBillLockingDay';
import * as getProductDropdownListBySSGroup from './getProductDropdownListBySSGroup';
import * as getReceivableDropdownList from './getReceivableDropdownList';
import * as getReceivableFrequencyById from './getReceivableFrequencyById';
import * as getReceivableFrequencyDropdownList from './getReceivableFrequencyDropdownList';
import * as getReceivableTemplateById from './getReceivableTemplateById';
import * as getReceivableTemplateDropdownList from './getReceivableTemplateDropdownList';
import * as getReceivableTemplateInterList from './getReceivableTemplateInterList';
import * as getReceivableUpdateDropdownList from './getReceivableUpdateDropdownList';
import * as getTempltImpInfo from './getTempltImpInfo';
import * as insertAndUpdateOneTimeFrequency from './insertAndUpdateOneTimeFrequency';
import * as insertReceivableDisplay from './insertReceivableDisplay';
import * as insertReceivableFrequency from './insertReceivableFrequency';
import * as insertReceivablePrecision from './insertReceivablePrecision';
import * as insertReceivableTemplate from './insertReceivableTemplate';
import * as modifyDisplayAndPrecision from './modifyDisplayAndPrecision';
import * as queryOneTimeFrequencyList from './queryOneTimeFrequencyList';
import * as queryReceivableDisplayList from './queryReceivableDisplayList';
import * as queryReceivableFrequencyList from './queryReceivableFrequencyList';
import * as queryReceivablePrecisionAndDisplayList from './queryReceivablePrecisionAndDisplayList';
import * as queryReceivablePrecisionList from './queryReceivablePrecisionList';
import * as queryReceivableTemplateHisList from './queryReceivableTemplateHisList';
import * as queryReceivableTemplateList from './queryReceivableTemplateList';
import * as saveReceivableTemplateInterList from './saveReceivableTemplateInterList';
import * as setReceivableTemplate from './setReceivableTemplate';
import * as toolsDataByEntity from './toolsDataByEntity';
import * as updateReceivableDisplay from './updateReceivableDisplay';
import * as updateReceivableFrequency from './updateReceivableFrequency';
import * as updateReceivablePrecision from './updateReceivablePrecision';
import * as updateReceivableTemplate from './updateReceivableTemplate';

export {
  checkSameReceivableNameByDiffentId,
  delReceivableTemplateInterList,
  deleteReceivableTemplate,
  exportFile,
  exportImpHis,
  getBillQuotedBySubCount,
  getContractByCustWithState,
  getImpDetail,
  getMaxBillLockingDay,
  getProductDropdownListBySSGroup,
  getReceivableDropdownList,
  getReceivableFrequencyById,
  getReceivableFrequencyDropdownList,
  getReceivableTemplateById,
  getReceivableTemplateDropdownList,
  getReceivableTemplateInterList,
  getReceivableUpdateDropdownList,
  getTempltImpInfo,
  insertAndUpdateOneTimeFrequency,
  insertReceivableDisplay,
  insertReceivableFrequency,
  insertReceivablePrecision,
  insertReceivableTemplate,
  modifyDisplayAndPrecision,
  queryOneTimeFrequencyList,
  queryReceivableDisplayList,
  queryReceivableFrequencyList,
  queryReceivablePrecisionAndDisplayList,
  queryReceivablePrecisionList,
  queryReceivableTemplateHisList,
  queryReceivableTemplateList,
  saveReceivableTemplateInterList,
  setReceivableTemplate,
  toolsDataByEntity,
  updateReceivableDisplay,
  updateReceivableFrequency,
  updateReceivablePrecision,
  updateReceivableTemplate,
};
