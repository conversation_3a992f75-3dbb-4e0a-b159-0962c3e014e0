# 项目设计

##　系统功能菜单管理类型：独立脚本位置：｀ scripts/loadFuncs.ts ｀

### 概要

用于从配置文件生成路由配置。配置文件来源，'config/routers'下所有以`comp-`开头的 json 文件。一共生成三个文件：

```sh
src/locales/zh-CN/menu-fake.json
config/routers/routerMap.json
config/routers/all.json
```

分别是国际化命名配置，路由与组件映射关系配置，`umi`可识别的路由配置。

### `formatRouterMap`从配置文件生成映射关系大字典

```ts
const formatRouterMap = (files: Array<string>) => {
  const comps = files.filter((file: string) => file.startsWith(componentFile));
  const moduleMap: ModuleMap = comps.reduce((modMap: ModuleMap, comp: string) => {
    const moduleName = comp.replace(componentFile, '').replace('.json', '');
    modMap[moduleName] = require(`${componentFileDir}/${comp}`);
    return modMap;
  }, {});
  routerMap = Object.keys(moduleMap).reduce((routMap: RouterMap, module: string): RouterMap => {
    const routers = moduleMap[module];
    Object.keys(routers).forEach(
      (key: string) => (routMap[`/${module}/${key}`] = washComp(routers[key])),
    );
    return routMap;
  }, {});
};
```

### `extractRouters`从配置文件生成路由配置

```ts
export const extractRouters = (routers: SysFuncNode[]): RouterFunc[] =>
  routers.map((router: SysFuncNode) => extractChildren(router));

const extractChildren = (router: SysFuncNode) => {
  const newRout: RouterFunc = funcToRouter(router);
  if (router.subList && router.subList.length > 0) {
    newRout.routes = extractRouters(router.subList);
  }
  return newRout;
};

const funcToRouter = (func: SysFuncNode): RouterFunc => {
  const path = func.reactUrl || '';
  const name = func.funcName.trim();
  const newRout: RouterFunc = {
    path: func.reactUrl,
    name,
    icon: func.iconUrl,
  };
  if (func.funcType === 2) newRout.component = washComp(routerMap[path] || componentDefault);
  genLocales(name);
  return newRout;
};
```
