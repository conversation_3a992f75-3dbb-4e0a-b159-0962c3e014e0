import React, { useState, useEffect } from 'react';
import { Form, Input, Modal, Typography } from 'antd';
import { isEmpty } from 'lodash';
import { getUserId } from '@/utils/model';
import { CommonDropSelector } from '@/components/Selectors';
import { CachedPage, CachedTableOptionsType } from '@/components/CachedPage';
import { RowElement, ColElementButton } from '@/components/Forms/FormLayouts';
import { WritableColumnProps } from '@/utils/writable/types';
import { Writable, useWritable } from '@/components/Writable';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { QueryContractPop } from '@/components/StandardPop/QueryContractPop';
import { EditeFormProps } from '@/components/EditeForm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { isLockedMap, dataSources } from '@/utils/settings/finance/queryExBill';
import { msgErr, msgOk } from '@/utils/methods/message';
import dict from '@/locales/zh-CN/locale';
import LinkDetailForBill from '../../QueryBill/components/LinkDetailForBill';
import { stdMonthFormatMoDash } from '@/utils/methods/times';
import { formatAmount } from '@/utils/methods/format';
import { AsyncButton } from '@/components/Forms/Confirm';
import { Calculator } from '@/utils/methods/calculator';
interface OutsourcingOnechargesTaxProps {
  [props: string]: any;
}

export const yesOrNo = new Map<string, string>([
  ['1', dict.yes],
  ['0', dict.no],
]);
const service = API.finance.receivable.getCustOnCharges;
const oneChargesService = API.finance.receivable.getOneCharges;
let _options: CachedTableOptionsType;
const OutsourcingOnechargesTax: React.FC<OutsourcingOnechargesTaxProps> = () => {
  const [loading, setLoading] = useState(false);
  const [checkIsApproval, setCheckIsApproval] = useState(false);
  const [showDetailForBill, setShowDetailForBill] = useState(false);
  const [billTempMap, setBillTempMap] = useState<Map<number, string>>(new Map());
  const [contractList, setContractList] = useState<any>({});
  const [record, setRecord] = useState({});
  const [onechargesTaxAmt, setOnechargesTaxAmt] = useState<POJO>({});
  const [custId, setCustId] = useState();
  const [form] = Form.useForm();
  const [wriForm] = Form.useForm();
  const wriTable = useWritable({ service: oneChargesService });
  const userId = getUserId() || '';
  const initComboList = ['商业保险', '其他福利', '服务费', '福利', '存档费', '体检费', '岗位外包'];
  const { all: list } = wriTable.getList();
  const baseColumns: any[] = [
    {
      title: '客户',
      dataIndex: 'CUSTNAME',
    },
    { title: '客户账套', dataIndex: 'RECEIVABLETEMPLTNAME' },
    { title: '账单模板编号', dataIndex: 'RECEIVABLETEMPLTID' },
    {
      title: '账单状态',
      dataIndex: 'ISLOCKED',
      render: (text: any) => {
        return isLockedMap.get(text);
      },
    },
    { title: '账单年月', dataIndex: 'BILLYM' },
    { title: '财务应收年月', dataIndex: 'RECEIVABLEYM' },
    {
      title: '应收金额',
      dataIndex: 'RECEIVABLEAMT',
      render: (text: number, record: any) => {
        return text ? (
          <Typography.Link
            onClick={() => {
              setShowDetailForBill(true);
              setRecord(record);
            }}
          >
            {record.RECEIVABLEAMT}
          </Typography.Link>
        ) : null;
      },
    },
    { title: '账单附件', dataIndex: 'FILENAME' },
  ];
  const baseWritableColumns: WritableColumnProps<any>[] | any[] = [
    {
      title: '产品大类',
      dataIndex: 'productTypeId',
      inputProps: { disabled: true },
      inputRender: () => (
        <CommonDropSelector
          params={{ statementName: 'baseData.baseDataDropDownListByMap', type: '1008' }}
          code={'6'}
        />
      ),
    },
    {
      title: '产品名称',
      dataIndex: 'productId',
      inputProps: { disabled: true },
      inputRender: () => (
        <CommonDropSelector
          params={{ statementName: 'receive.getProLineDropList', id: '120' }}
          code={'37'}
        />
      ),
    },
    {
      title: '产品线',
      dataIndex: 'productLine',
      inputProps: { defaultValue: '8' },
      inputRender: ({ record, text }) =>
        record?.createBy ? (
          <CommonDropSelector
            params={{ statementName: 'receive.getProLineDropList', type: '1' }}
            disabled
            code={text}
          />
        ) : (
          <CommonDropSelector params={{ statementName: 'receive.getProLineDropList', type: '1' }} />
        ),
    },
    {
      title: '账单金额',
      dataIndex: 'amtNoTax',
      inputProps: { readOnly: true, defaultValue: onechargesTaxAmt?.dsdf },
      inputRender: 'string',
    },
    {
      title: '是否包含服务费',
      dataIndex: 'isIncludedService',
      inputRender: ({ record, serial, text }) =>
        mapToSelectors(yesOrNo, {
          value: text,
          allowClear: false,
          defaultValue: '0',
          disabled: record?.createBy,
          onChange: () => {
            calculateOnechargesTaxAmt(serial);
          },
        }),
    },
    {
      title: '是否包括存档费',
      dataIndex: 'isIncludedArchive',
      inputRender: ({ record, serial, text }) =>
        mapToSelectors(yesOrNo, {
          value: text,
          allowClear: false,
          defaultValue: '0',
          disabled: record?.createBy,
          onChange: () => {
            calculateOnechargesTaxAmt(serial);
          },
        }),
    },
    {
      title: '是否包括商保',
      dataIndex: 'isIncludedBusiness',
      inputRender: ({ record, serial, text }) =>
        mapToSelectors(yesOrNo, {
          value: text,
          allowClear: false,
          disabled: record?.createBy,
          defaultValue: '0',
          onChange: () => {
            calculateOnechargesTaxAmt(serial);
          },
        }),
    },
    {
      title: '是否包含客户一次性费用',
      dataIndex: 'isIncludedOnechares',
      inputRender: ({ record, serial, text }) =>
        mapToSelectors(yesOrNo, {
          value: text,
          allowClear: false,
          disabled: record?.createBy,
          defaultValue: '0',
          onChange: () => {
            calculateOnechargesTaxAmt(serial);
          },
        }),
    },
    {
      title: '总税率',
      dataIndex: 'totalTax',
      inputRender: (options) => {
        return options?.record?.createBy ? <Input readOnly /> : 'string';
      },
      rules: [
        { required: true, message: '' },
        {
          validator: (_: any, value: string) => {
            if (Number(value) < 0.06) {
              return Promise.reject('请输入正确百分制数值');
            }
            if (Number(value) > 1) {
              return Promise.reject('请输入正确百分制数值');
            }
            if (!/^(-?\d+)(\.\d+)?$/.test(value)) {
              return Promise.reject('请输入正确百分制数值');
            }
            return Promise.resolve();
          },
        },
      ],
      onGridChange: (e: any, { serial }: any) => {
        const value = e.currentTarget.value.replace(/\b(0+)/gi, '');
        if (isNaN(value) || value < 0.06 || value > 1 || !value) {
          wriTable.setFieldsValue(serial, {
            atr: ``,
          });
          return;
        }
        calculateOnechargesTaxAmt(serial);
      },
    },

    {
      title: '固定增值税',
      dataIndex: 'vatr',
      inputRender: 'string',
      inputProps: { readOnly: true, defaultValue: '0.06' },
    },
    {
      title: '附加税税率',
      dataIndex: 'atr',
      inputRender: 'string',
      inputProps: { readOnly: true },
    },
    { title: '金额', dataIndex: 'amount', inputRender: 'string', inputProps: { readOnly: true } },
    {
      title: '人数',
      dataIndex: 'headCount',
      inputRender: (options) => {
        const { dataFrom } = wriTable.getFieldsValue(options.serial, ['dataFrom']);
        return dataFrom === '2' || options?.record?.createBy ? <Input readOnly /> : 'string';
      },
      rules: [{ pattern: /^[0-9]+$/, required: true, message: '请输入人数' }],
    },
    {
      title: '大合同名称',
      dataIndex: 'contractName',
      inputProps: { readOnly: true },
      rules: [{ required: true, message: '请输入大合同名称' }],
      inputRender: (options) =>
        options?.record?.createBy ? (
          'string'
        ) : (
          <QueryContractPop
            rowValue="contractType-contractTypeName-contractSubType-contractSubTypeName-contractId-name-contractName"
            keyMap={{
              contractId: 'key',
              name: 'name',
              contractName: 'shortName',
              contractTypeName: 'contractTypeName',
              contractSubTypeName: 'contractSubTypeName',
            }}
            custId={custId}
            localSearch={true}
          />
        ),
    },
    {
      title: '数据来源',
      dataIndex: 'dataFrom',
      inputProps: { readOnly: true },
      inputRender: ({ record, serial, text }) =>
        mapToSelectors(dataSources, {
          value: text,
          disabled: true,
          defaultValue: '1',
        }),
    },
  ];
  const formColumns: EditeFormProps[] = [
    {
      label: '客户',
      fieldName: 'custName',
      rules: [{ required: true, message: '请选择客户' }],
      inputRender: () => (
        <CustomerPop
          rowValue="custId-custName"
          keyMap={{
            custId: 'custId',
            custName: 'custName',
          }}
          handdleConfirm={custIdChanged}
        />
      ),
    },
    {
      label: '客户账套',
      fieldName: 'templtCode',
      rules: [{ required: true, message: '请选择客户账套' }],
      inputRender: () => mapToSelectors(billTempMap, { showSearch: true }),
    },
    { label: '账单模板编号', fieldName: 'receivableTemplt', inputRender: 'string' },
    {
      label: '账单年月',
      fieldName: 'billYm',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '是否锁定',
      fieldName: 'isLocked',
      inputProps: { isLocked: '0' },
      inputRender: () => mapToSelectors(isLockedMap, { allowClear: true, disabled: true }),
    },
  ];
  useEffect(() => {
    if (!userId) return;
    checkUserDoOnechargesWithoutApproval();
  }, [userId]);

  useEffect(() => {
    if (!custId) return;
    wriTable.resetFields();
    wriTable.setNewData([]);
    const params = {
      statementName: 'receive.getbillTempDropDown',
      custId: custId,
    };
    const contractParams = {
      statementName: 'receive.getContractDropDownList',
      custId: custId,
    };
    const map = new Map<number, string>();
    try {
      API.basedata.baseDataCls.getDorpDownList.requests(params, { params }).then((data) => {
        data.list.forEach((e: any) => map.set(+e.key, e.shortName));
        setBillTempMap(map);
      });
      API.basedata.baseDataCls.getDorpDownList.requests(contractParams).then((data) => {
        setContractList(data);
      });
    } catch (e) {
      msgErr('获取数据失败');
    }
  }, [custId]);

  useEffect(() => {
    wriTable.resetFields();
    wriTable.setNewData([]);
  }, []);

  useEffect(() => {
    if (!_options?.selectedSingleRow?.RECEIVABLEID) return;
    API.finance.receivable.getOnechargesTaxAmt
      .requests({ receivableId: _options?.selectedSingleRow?.RECEIVABLEID })
      .then((res) => {
        setOnechargesTaxAmt(res);
      })
      .catch((err) => {
        msgErr('获取数据失败');
      });
  }, [_options?.selectedSingleRow]);

  // 客户名称改变
  const custIdChanged = (values: any) => {
    const { custId } = values;
    form.setFieldsValue({
      templtCode: '',
    });
    setCustId(custId);
  };
  const checkUserDoOnechargesWithoutApproval = () => {
    //如果是集团个别用户，则替换按钮功能。直接通过，不走审批
    try {
      API.finance.receivable.checkUserDoOnechargesWithoutApproval
        .requests({
          userId,
        })
        .then((data) => {
          setCheckIsApproval(data);
        });
    } catch {
      msgErr('获取数据失败');
    }
  };
  const getOneCharges = async (rows: { BILLYM: string; RECEIVABLETEMPLTID?: string }) => {
    setLoading(true);
    try {
      const res = await API.finance.receivable.getOneCharges.requests({
        billYm: rows?.BILLYM ?? '',
        receivableTempltId: rows?.RECEIVABLETEMPLTID ?? '',
        onechargesType: 1,
      });
      const list = res.list.map((item: any) => {
        const contractName = contractList.list.find((it: any) => it.key === item.contractId);
        return {
          ...item,
          contractName: contractName?.shortName,
          amtNoTax: formatAmount(item.amtNoTax),
          amount: formatAmount(item.amount),
          vatr: item.vatr,
          atr: item.atr,
        };
      });
      if (res.list.length < 1) {
        setLoading(false);
        wriTable.resetFields();
        wriTable.setNewData([]);
        return;
      }
      setLoading(false);
      wriTable.resetFields();
      wriTable.setNewData(list);
    } catch (e) {
      setLoading(false);
      msgErr('获取数据失败');
    }
  };

  /**
   * 删除按钮单击事件的处理方法
   */
  const deleteClickHandler = async () => {
    const { ISLOCKED, RECEIVABLEID, RECEIVABLEVERSIONID, BILLYM, RECEIVABLETEMPLTID } =
      _options?.selectedSingleRow || {};
    const { templtCode, receivableTemplt } = form.getFieldsValue() || {};
    let receivableTempltId;
    if (templtCode) {
      receivableTempltId = templtCode;
    } else {
      receivableTempltId = receivableTemplt ? receivableTemplt : '';
    }
    const { selectedRows } = wriTable;
    if (selectedRows.length === 0) {
      msgErr(dict.noDataSelected);
      return;
    }
    const onecharesIds = selectedRows
      .filter((item) => item.dataFrom !== '2')
      .map((item: any) => item.onecharesId);
    if (selectedRows.find((item) => item.dataFrom === '2')) {
      msgErr(dict.cannotdeleteOne);
      return;
    }
    if (!onecharesIds.toString()) {
      wriTable.deleteRows();
      return;
    }
    if (!isEmpty(_options?.selectedSingleRow)) {
      if (ISLOCKED === '0') {
        msgErr('如果账单已锁定,则不允许删除');
        return;
      }
      try {
        const res = await API.finance.receivable.getIsReceiveAudit.requests({
          billYm: BILLYM,
          receivableTempltId: RECEIVABLETEMPLTID,
        });
        if (res > 0) {
          msgErr('该账单存在客户一次性费用审批流程，请等待审批结束后操作');
          return;
        }
        const params = {
          ids: onecharesIds.toString(),
          receivableId: RECEIVABLEID,
          receivableVersionId: RECEIVABLEVERSIONID,
          billYm: BILLYM,
          receivableTempltId: RECEIVABLETEMPLTID,
        };
        try {
          await API.finance.receivable.deleteOneCharges.requests(params);
          msgOk(dict.delSuccess);
          _options.request({ ...form.getFieldsValue(), receivableTempltId });
        } catch {
          msgErr('删除失败');
        }
      } catch {
        msgErr('获取数据失败');
      }
    } else {
      msgErr(dict.selectMainRecord);
    }
  };
  /**
   * 删除按钮单击事件的处理方法_需审批
   */
  const deleteClickHandlerNeedApproval = async () => {
    const { selectedRows } = wriTable;
    if (selectedRows.length === 0) {
      msgErr(dict.noDataSelected);
      return;
    }
    if (selectedRows.find((item) => item.dataFrom === '2')) {
      msgErr(dict.cannotdeleteOne);
      return;
    }
    wriTable.deleteRows();
  };
  /**
   * 保存按钮单击事件的处理方法
   */
  const saveClickHandler = async () => {
    const { BILLYM, RECEIVABLETEMPLTID, APPORSTATUS, ISLOCKED, RECEIVABLEID } =
      (await _options?.selectedSingleRow) || {};
    const { billYm, templtCode, receivableTemplt, custId } = (await form.getFieldsValue()) || {};
    const receivableTempltId = templtCode ? templtCode : receivableTemplt ? receivableTemplt : '';
    const { all: list, added: added, updated: updated } = await wriTable.validateFields({
      validateAll: true,
    });
    const updateLen = [...updated, ...added];
    let chargeDTOs;
    let params: any;
    if (updateLen.length === 0) return msgErr(dict.noaddData);
    try {
      //选择了应收主记录
      if (_options.data?.list?.length > 0 && !isEmpty(_options?.selectedSingleRow)) {
        if (APPORSTATUS) {
          msgErr(dict.youCantAddOneChargesBesApproval);
          return;
        }
        if (ISLOCKED !== 0) {
          msgErr(dict.youCantAddOneChargesBesIsLocked);
          return;
        }
        chargeDTOs = [...added, ...updated]
          .filter((item: any) => item.clientOperation !== -1)
          .map((it: any) => ({
            ...it,
            billYm: BILLYM,
            amount: it.amount.replace(/,/g, ''),
            amtNoTax: String(it.amtNoTax).replace(/,/g, ''),
            onechargesType: 1,
            receivableTempltId: RECEIVABLETEMPLTID,
            receivableId: RECEIVABLEID,
            atr: `${parseFloat(it.atr)}`,
            vatr: `${parseFloat(it.vatr)}`,
            readOnlyForProductTypeId: initComboList.includes(it.productTypeName) ? false : true,
            productLineReadOnly: initComboList.includes(it.productTypeName) && false,
          }));
        params = {
          custId,
          billYm: BILLYM,
          receivableTempltId: RECEIVABLETEMPLTID,
          isoldData: 'T',
        };
      } else {
        //没有选择应收记录
        if (!billYm || !templtCode || !custId) {
          msgErr(dict.oceAlert);
          return;
        }
        chargeDTOs = [...added, ...updated]
          .filter((item: any) => item.clientOperation !== -1)
          .map((it: any) => ({
            ...it,
            billYm,
            amount: it.amount?.replace(/,/g, ''),
            amtNoTax: String(it.amtNoTax).replace(/,/g, ''),
            onechargesType: 1,
            receivableTempltId: templtCode,
            atr: `${parseFloat(it.atr)}`,
            vatr: `${parseFloat(it.vatr)}`,
            readOnlyForProductTypeId: initComboList.includes(it.productTypeName) ? false : true,
            productLineReadOnly: initComboList.includes(it.productTypeName) && false,
          }));
        params = {
          custId,
          billYm,
          receivableTempltId: templtCode,
          isoldData: 'F',
        };
      }
      if (updateLen.find((it: any) => it.totalTax < 0.0672)) {
        Modal.confirm({
          title: '提示',
          content: '总费率小于0.0672,需要提交审批',
          onOk: async () => {
            const getOnechargesInProcess = await API.finance.receivable.getOnechargesInProcess.requests(
              {
                receivableTempltId:
                  _options.data?.list?.length > 0 && RECEIVABLETEMPLTID
                    ? RECEIVABLETEMPLTID
                    : receivableTempltId,
              },
            );
            if (getOnechargesInProcess > 0)
              return msgErr('流程中还有未审批完成的一次性费用，请联系大区客服总监');
            const res1 = await API.finance.receivable.startOneCharges.requests({
              ...params,
              chargeDTOs,
            });
            if (!res1) {
              showInsertOnchargeError();
              return;
            }
            msgOk(dict.saveSuccess);
            wriTable.resetFields();
            wriTable.setNewData([]);
            _options.request({ ...form.getFieldsValue(), receivableTempltId });
          },
          onCancel: () => {},
        });
        return;
      }
      const res = await API.finance.receivable.insertOnChargesModel.requests({
        ...params,
        chargeDTOs,
      });
      if (!res) {
        showInsertOnchargeError();
        return;
      }
      msgOk(dict.saveSuccess);
      wriTable.resetFields();
      wriTable.setNewData([]);
      _options.request({ ...form.getFieldsValue(), receivableTempltId });
      getOneCharges({ BILLYM, RECEIVABLETEMPLTID });
    } catch {
      msgErr(dict.insertOnchargeError);
    }
  };

  /**
   * 保存按钮单击事件的处理方法_需审批
   */
  const saveClickHandlerNeedApproval = async () => {
    const { BILLYM, RECEIVABLETEMPLTID, APPORSTATUS, ISLOCKED } =
      (await _options?.selectedSingleRow) || {};
    const { billYm, templtCode, custId, receivableTemplt } = (await form.getFieldsValue()) || {};
    const receivableTempltId = templtCode ? templtCode : receivableTemplt ? receivableTemplt : '';
    const { all: list, updated: updated, added: added } = await wriTable.validateFields({
      validateAll: true,
    });
    const updateLen = [...updated, ...added];
    let chargeDTOs;
    let params: any;
    if (updateLen.length === 0) return msgErr(dict.DataNoChange);
    //审批流程————————选择了应收主记录
    if (_options.data?.list?.length > 0 && !isEmpty(_options?.selectedSingleRow)) {
      if (APPORSTATUS) {
        msgErr(dict.youCantAddOneChargesBesApproval);
        return;
      }
      if (ISLOCKED !== 0) {
        msgErr(dict.youCantAddOneChargesBesIsLocked);
        return;
      }
      chargeDTOs = [...list]
        .filter((item: any) => item.clientOperation !== -1)
        .map((it: any) => ({
          ...it,
          billYm: BILLYM,
          amount: it.amount?.replace(/,/g, ''),
          amtNoTax: String(it.amtNoTax).replace(/,/g, ''),
          onechargesType: 1,
          receivableTempltId: RECEIVABLETEMPLTID,
          atr: `${parseFloat(it.atr)}`,
          vatr: `${parseFloat(it.vatr)}`,
          readOnlyForProductTypeId: initComboList.includes(it.productTypeName) ? false : true,
          productLineReadOnly: initComboList.includes(it.productTypeName) && false,
        }));
      params = {
        custId,
        billYm: BILLYM,
        receivableTempltId: RECEIVABLETEMPLTID,
      };
    } else {
      //审批流程————————没有选择应收记录
      if (!billYm || !templtCode || !custId) {
        msgErr(dict.oceAlert);
        return;
      }
      chargeDTOs = [...list]
        .filter((item: any) => item.clientOperation !== -1)
        .map((it: any) => ({
          ...it,
          billYm,
          amount: it.amount?.replace(/,/g, ''),
          amtNoTax: String(it.amtNoTax).replace(/,/g, ''),
          onechargesType: 1,
          receivableTempltId: templtCode,
          atr: `${parseFloat(it.atr)}`,
          vatr: `${parseFloat(it.vatr)}`,
          readOnlyForProductTypeId: initComboList.includes(it.productTypeName) ? true : false,
          productLineReadOnly: initComboList.includes(it.productTypeName) && false,
        }));
      params = {
        custId,
        billYm,
        receivableTempltId: templtCode,
      };
    }
    if (updateLen.find((it: any) => it.totalTax < 0.0672)) {
      Modal.confirm({
        title: '提示',
        content: '总费率小于0.0672,需要提交审批',
        onOk: async () => {
          const getOnechargesInProcess = await API.finance.receivable.getOnechargesInProcess.requests(
            {
              receivableTempltId:
                _options.data?.list?.length > 0 && RECEIVABLETEMPLTID
                  ? RECEIVABLETEMPLTID
                  : receivableTempltId,
            },
          );
          if (getOnechargesInProcess > 0)
            return msgErr('流程中还有未审批完成的一次性费用，请联系大区客服总监');
          const res1 = await API.finance.receivable.startOneCharges.requests({
            ...params,
            chargeDTOs,
          });
          if (!res1) {
            showInsertOnchargeError();
            return;
          }
          msgOk(dict.saveSuccess);
          wriTable.resetFields();
          wriTable.setNewData([]);
          _options.request({ ...form.getFieldsValue(), receivableTempltId });
        },
        onCancel: () => {},
      });
      return;
    }
    const res = await API.finance.receivable.insertOnChargesModel.requests({
      ...params,
      chargeDTOs,
    });
    if (!res) {
      showInsertOnchargeError();
      return;
    }
    msgOk(dict.saveSuccess);
    wriTable.resetFields();
    wriTable.setNewData([]);
    _options.request({ ...form.getFieldsValue(), receivableTempltId });
    getOneCharges({ BILLYM, RECEIVABLETEMPLTID });
  };
  const showInsertOnchargeError = () => {
    Modal.confirm({
      title: '提示',
      content: '该账套下存在在审批中的一次性项目',
    });
  };
  /**
   * 添加按钮单击事件的处理方法
   */
  const addClickHandler = async () => {
    const { billYm, templtCode } = (await form.getFieldsValue()) || {};
    const { BILLYM, RECEIVABLETEMPLTID, RECEIVABLEAMT } = (await _options?.selectedSingleRow) || {};
    let params;
    if (!onechargesTaxAmt?.perCount) {
      msgErr('账单内有人员不属于外包1、3，不能添加');
      return;
    }
    if (onechargesTaxAmt?.dsdf <= 0) {
      msgErr('账单应收金额小于等于0时不能添加');
      return;
    }
    if (isEmpty(await _options?.selectedSingleRow)) return msgErr('请选择选择一条已存在的应收记录');
    wriTable.addRows();
  };

  /**
   * 添加按钮单击事件的处理方法_需审批
   */
  const addClickHandlerNeedApproval = async () => {
    if (!onechargesTaxAmt?.perCount) {
      msgErr('账单内有人员不属于外包1、3，不能添加');
      return;
    }
    if (onechargesTaxAmt?.dsdf <= 0) {
      msgErr('账单应收金额小于等于0时不能添加');
      return;
    }
    if (!isEmpty(_options?.selectedSingleRow)) {
      wriTable.addRows();
    } else {
      msgErr('请选择选择一条已存在的应收记录');
    }
  };
  const setVisibleFun = () => {
    setShowDetailForBill(false);
  };
  const renderButton = (options: CachedTableOptionsType) => {
    _options = options;
    return (
      <AsyncButton
        type="primary"
        htmlType="submit"
        onClick={() => {
          setOnechargesTaxAmt({});
          _options.setSelectedSingleRow({});
        }}
      >
        查询
      </AsyncButton>
    );
  };
  const onSuccess = (data: any) => {
    _options.setNewData([]);
    wriTable.setNewData([]);
    const { BILLYM, RECEIVABLETEMPLTID } = data.list[0];
    getOneCharges({ BILLYM, RECEIVABLETEMPLTID });
  };
  const handleQueries = (values: POJO) => {
    wriTable.resetFields();
    const { custId, billYm, receivableTemplt, templtCode, isLocked } = values || {};
    return {
      ...values,
      custId,
      billYm,
      receivableTempltId: receivableTemplt ? receivableTemplt : templtCode,
      isLocked,
    };
  };
  const calculateOnechargesTaxAmt = async (serial: number) => {
    let serviceAmt = 0;
    let archiveAmt = 0;
    let businessAmt = 0;
    let onechargesAmt = 0;

    const {
      totalTax,
      productLine,
      isIncludedService = '0',
      isIncludedArchive = '0',
      isIncludedBusiness = '0',
      isIncludedOnechares = '0',
    } = await wriTable.getFieldsValue(serial, [
      'productLine',
      'totalTax',
      'isIncludedService',
      'isIncludedArchive',
      'isIncludedBusiness',
      'isIncludedOnechares',
    ]);
    if (Number(isIncludedService)) {
      serviceAmt = onechargesTaxAmt?.serviceAmt;
    }
    if (Number(isIncludedArchive)) {
      archiveAmt = onechargesTaxAmt?.archiveAmt;
    }
    if (Number(isIncludedBusiness)) {
      businessAmt = onechargesTaxAmt?.businessAmt;
    }
    if (Number(isIncludedOnechares)) {
      onechargesAmt = onechargesTaxAmt?.onechargesAmt;
    }
    if (!totalTax) return;
    // 金额不可修改
    const amtNoTax = onechargesTaxAmt?.dsdf || 0;
    const amt = Calculator.add(
      Number(amtNoTax + serviceAmt),
      Number(archiveAmt + businessAmt + onechargesAmt),
    );
    await wriTable.setFieldsValue(serial, {
      productLine: productLine ?? '8',
      productTypeId: '6',
      productTypeName: '服务费',
      productId: '37',
      productName: '外包全额服务费税',
      dataFrom: '1',
      vatr: '0.06',
      amtNoTax: onechargesTaxAmt?.dsdf,
      isIncludedService,
      isIncludedArchive,
      isIncludedBusiness,
      isIncludedOnechares,
      atr: `${Calculator.round(Calculator.subtract(Number(totalTax), 0.06), 4)}`,
      amount: Calculator.multiply(amt, Number(totalTax)).toFixed(2),
    });
  };
  return (
    <>
      <CachedPage
        cardProps={{ bordered: false, bodyStyle: { padding: 0 } }}
        service={service}
        formColumns={formColumns}
        columns={baseColumns}
        form={form}
        renderButtons={renderButton}
        initialValues={{ isLocked: 0 }}
        handleQueries={handleQueries}
        onSelectSingleRow={(rows) => {
          _options.setSelectedSingleRow(rows);
          getOneCharges(rows as any);
        }}
        hooksProps={{
          onSuccess: onSuccess,
        }}
      />
      <RowElement>
        <ColElementButton>
          <AsyncButton
            type="primary"
            onClick={checkIsApproval ? addClickHandler : addClickHandlerNeedApproval}
          >
            添加
          </AsyncButton>
          <AsyncButton
            onClick={checkIsApproval ? deleteClickHandler : deleteClickHandlerNeedApproval}
            disabled={list?.length === 0}
          >
            删除
          </AsyncButton>
          <AsyncButton
            onClick={checkIsApproval ? saveClickHandler : saveClickHandlerNeedApproval}
            disabled={list?.length === 0}
          >
            {checkIsApproval ? '保存' : '提交审批'}
          </AsyncButton>
        </ColElementButton>
      </RowElement>
      <Writable
        loading={loading}
        wriForm={wriForm}
        service={oneChargesService}
        columns={baseWritableColumns}
        wriTable={wriTable}
        notShowPagination
        noDeleteButton
        noAddButton
      />
      <LinkDetailForBill
        showDetailForBill={showDetailForBill}
        setVisibleFun={setVisibleFun}
        dataTransObj={record}
      />
    </>
  );
};

export default OutsourcingOnechargesTax;
