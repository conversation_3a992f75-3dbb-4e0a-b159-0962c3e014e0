/**
 * @description 报价单
 */
import * as abandonQuotation from './abandonQuotation';
import * as abandonQuotationGroup from './abandonQuotationGroup';
import * as approveQuotation from './approveQuotation';
import * as approveQuotationEx from './approveQuotationEx';
import * as back from './back';
import * as createQuotation from './createQuotation';
import * as createQuotationGroup from './createQuotationGroup';
import * as delQuotation from './delQuotation';
import * as delQuotationGroup from './delQuotationGroup';
import * as deleteBpoProjectRule from './deleteBpoProjectRule';
import * as doApprove from './doApprove';
import * as toDownLoad from './toDownLoad';
import * as effectQuotation from './effectQuotation';
import * as effectQuotationGroup from './effectQuotationGroup';
import * as getApproveStatusEx from './getApproveStatusEx';
import * as getCurrentSales from './getCurrentSales';
import * as getDuplicateBpoProjectRuleCount from './getDuplicateBpoProjectRuleCount';
import * as getQuotation from './getQuotation';
import * as getQuotationCrm from './getQuotationCrm';
import * as getQuotationData from './getQuotationData';
import * as getQuotationDropdownList from './getQuotationDropdownList';
import * as getQuotationEx from './getQuotationEx';
import * as getQuotationGroup from './getQuotationGroup';
import * as getQuotationGroupData from './getQuotationGroupData';
import * as getQuotationTemplEx from './getQuotationTemplEx';
import * as getValidOuotation from './getValidOuotation';
import * as insertBpoProjectRule from './insertBpoProjectRule';
import * as queryBpoProjectRule from './queryBpoProjectRule';
import * as queryQuoteLadderList from './queryQuoteLadderList';
import * as revalidationQuotation from './revalidationQuotation';
import * as selectQuotationByPrimaryKey from './selectQuotationByPrimaryKey';
import * as selectUser from './selectUser';
import * as terminal from './terminal';
import * as updateApproveQuotation from './updateApproveQuotation';
import * as updateBpoProjectRule from './updateBpoProjectRule';
import * as updateQuotation from './updateQuotation';
import * as updateQuotationGroup from './updateQuotationGroup';

export {
  abandonQuotation,
  abandonQuotationGroup,
  approveQuotation,
  approveQuotationEx,
  back,
  createQuotation,
  createQuotationGroup,
  delQuotation,
  delQuotationGroup,
  deleteBpoProjectRule,
  doApprove,
  toDownLoad,
  effectQuotation,
  effectQuotationGroup,
  getApproveStatusEx,
  getCurrentSales,
  getDuplicateBpoProjectRuleCount,
  getQuotation,
  getQuotationCrm,
  getQuotationData,
  getQuotationDropdownList,
  getQuotationEx,
  getQuotationGroup,
  getQuotationGroupData,
  getQuotationTemplEx,
  getValidOuotation,
  insertBpoProjectRule,
  queryBpoProjectRule,
  queryQuoteLadderList,
  revalidationQuotation,
  selectQuotationByPrimaryKey,
  selectUser,
  terminal,
  updateApproveQuotation,
  updateBpoProjectRule,
  updateQuotation,
  updateQuotationGroup,
};
