import React, { useEffect, useState } from 'react';
import { Form, Button, message, FormInstance, Input, Select } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import {
  CommonBaseDataSelector,
  BusTypeDropdownListSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { 
  isOrNoMap, 
  handleAttributeMap, 
  handleObjectMap, 
  handleMethodMap,
  employmentStatusMap,
  actualStatusMap
} from '../index';

interface LocalBusinessContentFormProps {
  modal: [boolean, CallableFunction];
  listOptions: any;
  initialInfo?: any;
}

const LocalBusinessContentForm: React.FC<LocalBusinessContentFormProps> = ({
  modal,
  listOptions,
  initialInfo,
}) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;

  const [form] = Form.useForm();
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (visible) {
      if (initialInfo && Object.keys(initialInfo).length > 0) {
        setIsEdit(true);
        form.setFieldsValue(initialInfo);
      } else {
        setIsEdit(false);
        form.resetFields();
      }
    }
  }, [visible, initialInfo, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      // TODO: 调用API保存数据
      // console.log('各地业务内容提交:', values);
      message.success(isEdit ? '修改成功' : '新增成功');
      setVisible(false);
      form.resetFields();
      // 刷新列表
      listOptions.request(listOptions.queries);
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button key="submit" type="primary" onClick={handleSubmit}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'city',
      inputRender: 'string',
      inputProps: { disabled: isEdit },
      rules: [{ required: true, message: '请输入城市' }],
    },
    {
      label: '业务类型',
      fieldName: 'businessType',
      inputRender: (outerForm: FormInstance) => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
          onConfirm={() => {
            outerForm.setFieldsValue({ businessProject: undefined });
          }}
        />
      ),
      inputProps: { disabled: isEdit },
      rules: [{ required: true, message: '请选择业务类型' }],
    },
    {
      label: '业务项目',
      fieldName: 'businessProject',
      inputRender: (outerForm: FormInstance) => (
        <BusTypeDropdownListSelector
          allowClear
          params={{ categoryId: outerForm.getFieldValue('businessType') ?? '' }}
        />
      ),
      inputProps: { disabled: isEdit },
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.businessType !== curValues.businessType;
      },
      rules: [{ required: true, message: '请选择业务项目' }],
    },
    {
      label: '业务内容',
      fieldName: 'businessContent',
      inputRender: (outerForm: FormInstance) => (
        <Select showSearch placeholder="请选择业务内容">
          <Select.Option value="gongshang">工伤业务</Select.Option>
          <Select.Option value="shengyu">生育业务</Select.Option>
          <Select.Option value="shiye">失业业务</Select.Option>
          <Select.Option value="yiliao">医疗业务</Select.Option>
          <Select.Option value="gongjijin">公积金提取</Select.Option>
          <Select.Option value="tuixiu">退休业务</Select.Option>
          <Select.Option value="zhengming">员工证明开具</Select.Option>
        </Select>
      ),
      inputProps: { disabled: isEdit },
      rules: [{ required: true, message: '请选择业务内容' }],
    },
    {
      label: '办理属性',
      fieldName: 'handleAttribute',
      inputProps: { disabled: isEdit },
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择办理属性' }],
    },
    {
      label: '办理对象',
      fieldName: 'handleObject',
      inputProps: { disabled: isEdit },
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择办理对象' }],
    },
    {
      label: '办理方式',
      fieldName: 'handleMethod',
      inputProps: { disabled: isEdit },
      inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择办理方式' }],
    },
    {
      label: '是否微信显示',
      fieldName: 'isWechatShow',
      inputProps: { disabled: isEdit },
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择是否微信显示' }],
    },
    {
      label: '是否客户端显示',
      fieldName: 'isClientShow',
      inputProps: { disabled: isEdit },
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择是否客户端显示' }],
    },
    {
      label: '业务内容说明',
      fieldName: 'contentDesc',
      inputRender: 'text',
      inputProps: { rows: 4, maxLength: 2000 },
    },
    {
      label: '是否有政府性收费',
      fieldName: 'hasGovFee',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择是否有政府性收费' }],
    },
    {
      label: '政府性收费金额',
      fieldName: 'govFeeAmount',
      inputRender: 'string',
      rules: [{ required: true, message: '请输入政府性收费金额' }],
    },
    {
      label: '是否可以自助办理',
      fieldName: 'canSelfService',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择是否可以自助办理' }],
    },
    {
      label: '入离职状态',
      fieldName: 'employmentStatus',
      // inputRender: () => mapToSelectors(employmentStatusMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择入离职状态' }],
    },
    {
      label: '缴纳产品要求',
      fieldName: 'productRequirement',
      inputRender: 'string',
      rules: [{ required: true, message: '请输入缴纳产品要求' }],
    },
    {
      label: '实做状态',
      fieldName: 'actualStatus',
      // inputRender: () => mapToSelectors(actualStatusMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择实做状态' }],
    },
    {
      label: '员工自助办理途径',
      fieldName: 'selfServiceMethod',
      inputRender: 'text',
      inputProps: { rows: 4, maxLength: 2000 },
      rules: [{ required: true, message: '请输入员工自助办理途径' }],
    },
  ];

  // 设置只读字段
  const readOnlyFields = isEdit
    ? {
        city: true,
        businessType: true,
        businessProject: true,
        businessContent: true,
        handleAttribute: true,
        handleObject: true,
        handleMethod: true,
        isWechatShow: true,
        isClientShow: true,
      }
    : undefined;

  return (
    <Codal
      title={isEdit ? '修改各地业务' : '新增各地业务'}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        setVisible(false);
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="60%"
    >
      <FormElement3 form={form}>
        <EnumerateFields
          formColumns={formColumns}
          outerForm={form}
          colNumber={2}
          readOnlyFields={readOnlyFields}
        />
      </FormElement3>
    </Codal>
  );
};

export default LocalBusinessContentForm;