<!--
 * @Author: 严小强
 * @since: 2019-10-29 17:15:28
 * @严小强: Do not edit
 * @lastTime: 2019-10-29 17:21:53
 * @message:
 * @Email: <EMAIL>
 -->

# 按钮级权限

```tsx
// 组件位置
// src\components\Forms\RenderButtons.tsx
```

参数说明：

- `path`, 按钮权限所在页面，例如:‘/emphiresep/BillTemplate’。
- `permissionKey`, 按钮或按钮组对应后台的权限 key。

使用实例：

```tsx
// src\pages\emphiresep\BillTemplate\index.tsx
import { AuthButtons } from '@/components/Forms/RenderButtons';

// 按钮组
<RenderButtons path="/emphiresep/BillTemplate" permissionKey="20150001">
  <Button type="primary" htmlType="submit">
    查询
  </Button>
  <Button style={\{ marginLeft: 8 }\} onClick={this.showAddBillTemplate}>
    新增
  </Button>
  <Button
    style={\{ marginLeft: 8 }\}
    disabled={canEdite}
    onClick={this.showUpdateBillTemplate}
  >
    修改
  </Button>
  <Button style={\{ marginLeft: 8 }\} disabled={canEdite} onClick={this.showDetail}>
    查看
  </Button>
  <Button style={\{ marginLeft: 8 }\} disabled={canEdite} onClick={this.showSetPrintSort}>
    设置打印顺序
  </Button>
  <Button style={\{ marginLeft: 8 }\} disabled={canEdite} onClick={this.showSetPrecision}>
    设置产品精度
  </Button>
  <Button
    style={\{ marginLeft: 8 }\}
    disabled={selectedRows.length === 0}
    onClick={this.deleteBillTemplate}
  >
    删除
  </Button>
</AuthButtons>
//单个按钮
<RenderButtons path="/emphiresep/BillTemplate" permissionKey="20150002">
  <Button style={\{ marginLeft: 8 }\} onClick={this.exportBillTemplate}>
    导出
  </Button>
</AuthButtons>
```
