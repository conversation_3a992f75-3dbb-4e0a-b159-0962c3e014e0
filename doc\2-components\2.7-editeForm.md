<!--
 * @Author: 严小强
 * @since: 2019-08-29 16:35:28
 * @严小强: Do not edit
 * @lastTime: 2019-09-02 17:45:43
 * @message: 
 * @Email: <EMAIL>
 -->
 
# 新增修改表单组件
根据对象数组生成form表单弹窗

## 根据对象生成表单内的操作框
```tsx
// 组件目录
// src/components/EditeForm/index.tsx

// 类型定义
interface EditeFormProps {
  // 当前操作框的key,即后台所需参数
  fieldName: string;
  // 当前操作框的名称
  label: string;
  // 外部form
  outerForm?: WrappedFormUtils<any>;
  // 当前操作框的类型
  type?: string;
  // 配合autoSelectForm使用
  dataType?: string;
  // 自定义render返回jsx element。
  render?: Function;
  // getFieldDecorator 方法内的属性
  formOptions?: GetFieldDecoratorOptions;
  // input，select等antd组件的属性。
  elementOptions?: { [propName: string]: any };
  // 是否隐藏当前操作框，联动使用。
  shouldHidden?: boolean;
  // 配合type为select时使用的options数据源，用mapToSelectors方法生成。
  selectTypeMap?: Map<string | number, string>;
}
// 具体实现看代码

```

属性 | 说明 | 类型 | 默认值
----|------|----- | ----
fieldName | 必填。当前操作框的key,即后台所需参数 | `string` | -
label | 必填。当前操作框的名称 | `string` | -
outerForm | 必填。外部form | `WrappedFormUtils<any>` | -
type | 非必填。当前操作框的类型。若不自定义render次项则为必填项，否则无显示 | `string` | -
dataType | 非必填。当type为autoSelect时此项为必填，详情请看AutoSelectForm的使用 | `string` | -
render | 非必填。自定义render返回jsx element。 | `Function` | -
formOptions | 非必填。getFieldDecorator 方法内的属性。 | `GetFieldDecoratorOptions` | -
elementOptions | 非必填。input，select等antd组件的属性。 | `{ [propName: string]: any }` | -
shouldHidden | 非必填。是否隐藏当前操作框，联动使用。 | `boolean` | -
selectTypeMap | 非必填。当type为select是此项为必填项，配合type为select时使用的options数据源，用mapToSelectors方法生成。 | `Map<string OR number, string>` | -

type可选值为`input,inputNumber,textarea,select,autoSelect,autoComplete`,若不满足需求，可自行添加。


## 新增公用组件
```tsx
// 组件目录
// src/components/EditeForm/AddForm.tsx

// 类型定义
interface AddProps extends FormComponentProps {
  title: string;
  visible: boolean;
  hideHandle: Function;
  submitHandle: Function;
  formColumns: EditeFormProps[];
  modalOptions?: ModalProps;
}

// 具体实现看代码
```
属性 | 说明 | 类型 | 默认值
----|------|----- | ----
title | 必填。当前新增弹框名称 | `string` | -
visible | 必填。是否显示弹窗 | `boolean` | -
hideHandle | 必填。关闭弹窗事件 | `Function` | -
submitHandle | 必填。新增保存事件 | `Function` | -
formColumns | 必填。生成form操作框的数据源 | `EditeFormProps` | -
modalOptions | 非必填。modal自带的属性 | `ModalProps` | -

```tsx
// 使用实例
// src\pages\emphiresep\BillTemplate\index.tsx
import AddForm from '@/components/EditeForm/AddForm';

formColumns: EditeFormProps[] = [
  {
    fieldName: 'custId',
    label: '客户名称',
    render: (outerForm: WrappedFormUtils<any>, formOptions: GetFieldDecoratorOptions, elementOptions: any) => {
      const { onModalForm } = this.state;
      return <Col md={8} sm={24}>
        <CustomerPop
          label="客户名称"
          rowValue="custId-custName"
          onModal={onModalForm}
          handdleConfirm={this.changeCust}
          taggleModal={this.taggleFormModal}
          fixedValues={\{ queryType: 1 \}}
          outerForm={outerForm}
          fieldOptions={formOptions}
          disabled={elementOptions && elementOptions.disabled}
        />
      </Col>
    },
  },
  {
    fieldName: 'receivableTempltName',
    label: '账单模版名称',
    type: 'input',
    formOptions: {
      rules: formBillTemplate.receivableTempltName,
    },
  },
  {
    fieldName: 'agreedBillGenDt',
    label: '约定帐单生成日',
    type: 'inputNumber',
    formOptions: {
      rules: formBillTemplate.agreedBillGenDt,
    },
    elementOptions: {
      min: 1,
      max: 31,
      precision: 0,
    },
  },
  {
    fieldName: 'isSSInclued',
    label: '社保是否计入总额',
    type: 'select',
    formOptions: {
      rules: formBillTemplate.isSSInclued,
    },
    selectTypeMap: isTrueTypeMap,
  },
  {
    fieldName: 'custPayerId',
    label: '客户付款方',
    type: 'autoSelect',
    dataType: 'custPayer',
    formOptions: {
      rules: formBillTemplate.custPayerId,
    },
  },
]

addBillTemplate = (fieldsValue: any) => {
  API.order.custBill.save.request({ ...fieldsValue }).then(res => {
    if (!res || res.code !== CODE_SUCCESS) {
      return msgCall({ code: 404, msg: (res && res.message) || '新增失败' });
    }
    msgCall({ code: 200, msg: '新增成功' });
    this.hideAddBillTemplate();
    this.queryCommon();
  });
};

<AddForm
  title="新增模版"
  visible={onAddBillTemplate}
  hideHandle={this.hideAddBillTemplate}
  submitHandle={this.addBillTemplate}
  formColumns={this.formColumns}
/>
```


## 修改公用组件
```tsx
// 组件目录
// src/components/EditeForm/UpdateForm.tsx

// 类型定义
interface UpdateProps extends FormComponentProps {
  title: string;
  visible: boolean;
  hideHandle: Function;
  submitHandle: Function;
  formColumns: EditeFormProps[];
  updateitem: { [propName: string]: any };
  primaryKey: string;
  modalOptions?: ModalProps;
}

// 具体实现看代码
```
属性 | 说明 | 类型 | 默认值
----|------|----- | ----
title | 必填。当前新增弹框名称 | `string` | -
visible | 必填。是否显示弹窗 | `boolean` | -
hideHandle | 必填。关闭弹窗事件 | `Function` | -
submitHandle | 必填。新增保存事件 | `Function` | -
formColumns | 必填。生成form操作框的数据源 | `EditeFormProps` | -
updateitem | 必填。需要修改的数据源对象 | `{ [propName: string]: any }` | -
primaryKey | 必填。需要修改的数据源对象的主键 | `{ [propName: string]: any }` | -
modalOptions | 非必填。modal自带的属性 | `ModalProps` | -

```tsx
// 使用实例
// src\pages\emphiresep\BillTemplate\index.tsx
import UpdateForm from '@/components/EditeForm/UpdateForm';

formColumns: EditeFormProps[] = [
  {
    fieldName: 'custId',
    label: '客户名称',
    render: (outerForm: WrappedFormUtils<any>, formOptions: GetFieldDecoratorOptions, elementOptions: any) => {
      const { onModalForm } = this.state;
      return <Col md={8} sm={24}>
        <CustomerPop
          label="客户名称"
          rowValue="custId-custName"
          onModal={onModalForm}
          handdleConfirm={this.changeCust}
          taggleModal={this.taggleFormModal}
          fixedValues={\{ queryType: 1 \}}
          outerForm={outerForm}
          fieldOptions={formOptions}
          disabled={elementOptions && elementOptions.disabled}
        />
      </Col>
    },
  },
  {
    fieldName: 'receivableTempltName',
    label: '账单模版名称',
    type: 'input',
    formOptions: {
      rules: formBillTemplate.receivableTempltName,
    },
  },
  {
    fieldName: 'agreedBillGenDt',
    label: '约定帐单生成日',
    type: 'inputNumber',
    formOptions: {
      rules: formBillTemplate.agreedBillGenDt,
    },
    elementOptions: {
      min: 1,
      max: 31,
      precision: 0,
    },
  },
  {
    fieldName: 'isSSInclued',
    label: '社保是否计入总额',
    type: 'select',
    formOptions: {
      rules: formBillTemplate.isSSInclued,
    },
    selectTypeMap: isTrueTypeMap,
  },
  {
    fieldName: 'custPayerId',
    label: '客户付款方',
    type: 'autoSelect',
    dataType: 'custPayer',
    formOptions: {
      rules: formBillTemplate.custPayerId,
    },
  },
]

updateBillTemplate = (data: any) => {
  const { formValues } = this.state;
  API.order.custBill.update.request({ ...data }).then(res => {
    if (!res || res.code !== CODE_SUCCESS) {
      return msgCall({ code: 404, msg: (res && res.message) || '修改失败' });
    }
    msgCall({ code: 200, msg: '修改成功' });
    this.hideUpdateBillTemplate();
    this.queryCommon(formValues, formValues);
  });
};

// 此操作是为了修改formColumns内的属性，若无此需求可跳过
const updateColumns = this.formColumns.map((item: EditeFormProps) => {
  if (item.fieldName === 'custId' || item.fieldName === 'entityId') {
    return { ...item, elementOptions: { disabled: true } }
  }
  return item;
})
<UpdateForm
  title="修改模版"
  visible={onUpdateBillTemplate}
  hideHandle={this.hideUpdateBillTemplate}
  submitHandle={this.updateBillTemplate}
  formColumns={updateColumns}
  updateitem={updateitem}
  primaryKey="receivableTempltId"
/>
```
