 .eslintignore                                      |   2 +-
 config/browsers.json                               |   2 +-
 config/config.ts                                   |   6 +-
 config/defaultSettings.ts                          |   2 +-
 config/locales/types.ts                            |   2 +-
 config/routers/comp-basedata.json                  |   3 +-
 config/routers/comp-emphiresep.json                |   1 -
 config/routers/comp-payroll.json                   |  20 +-
 config/routers/otherRoutes.json                    |   7 -
 package.json                                       |   6 +-
 scripts/loadFuncs-pro.js                           |  12 +-
 scripts/loadFuncs.ts                               |  12 +-
 src/components/Codal/index.tsx                     |   5 +-
 src/components/ContentViewer/index.less            |   2 +-
 src/components/ContentViewer/index.tsx             |   2 +-
 src/components/DateComp/StrDatePicker.tsx          |  33 +-
 src/components/DateRange4/dateRange.tsx            |  19 +-
 src/components/EditeForm/AddForm.tsx               |   2 +-
 src/components/Forms/index.less                    |  13 +-
 src/components/GlobalHeader/NoticePending.tsx      |   4 +-
 src/components/PageHeaderWrapper/breadcrumb.tsx    |   4 +-
 src/components/Selectors/BaseDataSelectors.tsx     |  27 +-
 src/components/Selectors/BaseDropDown.tsx          |  11 +
 src/components/Selectors/BaseSelectors.tsx         |  42 +-
 src/components/Selectors/FuncSelectors.tsx         |  26 +-
 src/components/Selectors/index.tsx                 |   2 +-
 .../StandardPop/SelectWithholdAgentPop.tsx         |  23 +-
 src/components/StandardPop/SsGroupPop.tsx          |   5 +-
 src/components/StandardTable/Pagination.tsx        |  12 +-
 .../StandardTable/hooks/useStandardTable.tsx       |  12 +-
 src/components/StandardTable/index.less            |  50 +-
 src/components/StandardTable/index.tsx             | 100 ++--
 src/components/UploadForm/ImportForm.tsx           |  78 ++-
 src/components/UploadForm/ImportHistoryForm.tsx    |   2 -
 src/components/UploadForm/index.tsx                |  10 +-
 src/components/Writable/libs/GeneralInput.tsx      |   6 +-
 src/components/Writable/libs/Grid.tsx              |  13 +-
 src/components/Writable/libs/Writable.tsx          |   1 -
 src/global.less                                    |  17 +-
 src/layouts/BasicLayout.less                       |   9 +-
 src/layouts/BasicLayout.tsx                        |  34 +-
 src/layouts/HomeUser.tsx                           |  32 +-
 src/layouts/components/index.tsx                   |  20 +-
 src/models/cache.ts                                |   4 +-
 src/models/notice.ts                               | 356 +++++------
 src/pages/Dashboard/Welcome.tsx                    |  41 +-
 src/pages/Dashboard/index.less                     |  38 +-
 src/pages/Mrpay/index.tsx                          |  18 +
 .../Sales/Contract/Manage/ContractApproveWin.tsx   |  13 +-
 src/pages/Sales/Contract/Manage/ContractForm.tsx   |  10 +-
 src/pages/Sales/Contract/Query/index.tsx           |   8 +-
 .../CustomerQuery/components/AddCustMaterial.tsx   |  10 -
 .../CustomerQuery/components/CustomerDetail.tsx    |   6 +-
 .../CustomerQuery/components/LinkCustomer.tsx      |  25 +-
 .../CustomerQuery/components/SalaryCalculation.tsx |  28 +-
 .../Sales/CustomerManage/CustomerQuery/index.tsx   |   5 -
 .../QuotationManage/Forms/QuotationAddForm.tsx     |   7 +-
 .../QuotationTempManage/TablePop/index.tsx         |   2 +-
 src/pages/User/ForgotPwd.tsx                       |  66 --
 src/pages/User/Login.less                          | 187 +-----
 src/pages/User/Login.tsx                           |  30 +-
 src/pages/User/MinicLogin.tsx                      |   2 +-
 src/pages/User/clientLogin.less                    | 170 ++++++
 .../ProductRatio/Form/MaintainProductRatioForm.tsx |   4 +-
 .../components/AddModal.tsx                        | 139 -----
 .../basedata/SpecialSignerTitleMaintance/index.tsx | 129 ----
 src/pages/crm/cust/QueryIfmCustApprove/index.tsx   |   6 +-
 .../Forms/UpdateBatchAlterEmpOrderWin.tsx          | 315 +++++-----
 .../Forms/AddOrderForm.tsx                         | 132 ++--
 .../emporder/ConfirmEmpOrderForAssigner/index.tsx  |  34 +-
 .../emporder/EmpBankCardMaintainance/index.tsx     |   4 -
 .../emporder/EmployeeBaseInfoManage/index.tsx      |  11 +-
 .../emporder/NationWideEmpQuery/index.tsx          |  10 +-
 .../QueryAdjustment/Forms/DetailAdjustForms.tsx    | 149 ++---
 .../emphiresep/emporder/QueryAdjustment/index.tsx  |  46 +-
 .../Forms/AddBatchDelProduct.tsx                   | 236 ++++----
 .../QueryClientOrderForAdd/Forms/AddOrderForm.tsx  | 134 ++---
 .../Forms/BatchEmpAddPop.tsx                       |  35 +-
 .../emporder/QueryEmpDetailInfo/index.tsx          |  38 +-
 .../QueryEmpOrderListForCon/Forms/AddOrderForm.tsx |  46 +-
 .../emporder/QueryEmpOrderListForCon/index.tsx     |  34 +-
 .../Forms/EditEmpOrder.tsx                         | 147 +++--
 .../Forms/EditQuotationOnly.tsx                    |  28 +-
 .../emporder/QueryEmpOrderListForEdit/index.tsx    |  40 +-
 .../QueryEmpOrderListForGen/Forms/AddOrderForm.tsx | 185 +++---
 .../QueryEmpOrderListForPer/Forms/AddOrderForm.tsx | 133 +++--
 .../Forms/PerfectSearchQuery.ts                    |  78 +--
 .../emporder/QueryEmpOrderListForPer/index.tsx     |  32 +-
 .../Forms/TransferEmpOrder.tsx                     | 165 +++--
 .../emporder/QueryEmployeeOrder/index.tsx          |  50 +-
 .../emporder/QueryImpOrderLite/index.tsx           |   7 +-
 .../QueryImpWrongOrderPro/Forms/AddForms.tsx       | 300 ----------
 .../QueryImpWrongOrderPro/Forms/ImpOrderPro.tsx    | 137 -----
 .../Forms/ImportResultTable.tsx                    | 191 ------
 .../Forms/OrderProFormDetail.tsx                   | 188 ------
 .../emporder/QueryImpWrongOrderPro/index.tsx       | 221 -------
 .../Forms/DetailMinBaseAdjustment.tsx              |  41 +-
 .../emporder/QueryMinBaseAdjustment/index.tsx      |  20 +-
 .../hire/HireClassify/components/CustMaterial.tsx  |  16 +-
 src/pages/emphiresep/hire/HireClassify/index.tsx   |   2 +-
 .../emphiresep/quitManager/BatchQuitLite/index.tsx |  10 +-
 .../QueryClientOrderForReduce/index.tsx            |  47 --
 .../QueryEmpOrderListForSepApply/index.tsx         |  47 +-
 .../QueryEmpOrderListForSepCon/index.tsx           |   9 +-
 .../Forms/onConfirm.tsx                            |  50 +-
 .../QueryExEmpOrderListForSepCon/index.tsx         |   7 +-
 .../AddTransferAndSubcontract.tsx                  |  72 +--
 .../sendorder/CustomerSubcontract/ContractView.tsx |  16 +-
 .../sendorder/CustomerSubcontract/index.tsx        |  10 +-
 .../ManageTemplate/components/CustContract.tsx     |   2 +-
 .../sendorder/ManageTemplate/components/Detail.tsx |   6 +-
 .../QueryTransferAndSubcontract/index.tsx          |   5 +-
 .../HospitalTransact/Forms/ConfirmHpTransact.tsx   | 153 ++---
 .../HospitalTransact/Forms/addHpTransact.tsx       |  82 +--
 .../HospitalTransact/Forms/branchForms.tsx         |  14 +-
 .../Ebmtransact/HospitalTransact/index.tsx         |  23 +-
 .../components/UpdateLaborContract.tsx             |  26 +-
 .../LaborcontractManage/components/main.tsx        |   2 +-
 src/pages/empwelfare/LaborcontractManage/index.tsx |   4 +-
 .../LockManage/components/QuerySSLock.tsx          |  13 +-
 .../empwelfare/PayManage/components/index.tsx      |   8 -
 .../components/NewAddSIBackPayImp.tsx              |   5 -
 .../ProvidentFund/QueryPfAdjustment/index.tsx      |   4 +-
 .../QueryPfMinBaseAdjustment/index.tsx             |   4 +-
 .../components/NewAddSIBackPayImp.tsx              |   5 -
 .../QuerySocialMakeUp/components/index.tsx         |  10 +-
 .../QuerySocialSecurity/components/BaseInfo.tsx    |  15 +-
 .../QuerySocialSecurity/components/index.tsx       |  20 +-
 .../Socialmanage/QuerySsAdjustment/index.tsx       |   4 +-
 .../QuerySsMinBaseAdjustment/index.tsx             |   4 +-
 .../Socialmanage/SocialApply/SocialFundApply.tsx   |  40 +-
 .../Socialmanage/SocialApply/components/tools.tsx  |  23 -
 .../{SocialApply => }/SocialApplyReceivable.tsx    |   5 +-
 .../Socialmanage/SocialChange/SocialChange.tsx     |   8 +-
 .../Socialmanage/SocialChange/SocialFundChange.tsx |  29 +-
 .../SocialProcess/SocialFundProcess.tsx            | 184 ++----
 .../SocialProcess/SocialProcessBatch.tsx           |  36 +-
 .../SocialProcess/SocialProcessSingle.tsx          |  44 +-
 .../Socialmanage/SocialStop/SocialFundStop.tsx     |  21 +-
 .../SocialUpdateSelect/SocialFundUpdateSelect.tsx  |  21 +-
 .../SocialUpdateSelect/SocialUpdateAll.tsx         |  19 +-
 .../SocialUpdateSelect/SocialUpdateBasic.tsx       |   5 +-
 .../QueryExBill/components/LinkDetailForBill.tsx   |   8 +-
 .../Forms/ApplyChangeTaskList.tsx                  |  21 +-
 .../Forms/EditEmpOrder.tsx                         | 114 ++--
 .../emporder/QueryExEmpOrderListForEdit/index.tsx  |  40 +-
 .../Forms/AddOrderForm.tsx                         | 134 ++---
 .../emporder/QueryExEmpOrderListForPer/index.tsx   |  20 -
 .../Forms/onConfirm.tsx                            |  36 +-
 .../emporder/QueryExEmployeeOrder/index.tsx        |  14 +-
 .../prvdPayApply/components/SubFilePayBase.tsx     |   4 +-
 src/pages/finance/Gathering/UploadCash/Query.tsx   |   2 +-
 .../UploadCash/components/InvoiceEditModal.tsx     |  20 +-
 .../UploadCash/components/InvoiceEntry.tsx         |   3 -
 .../Gathering/UploadCash/components/Receipts.tsx   |  10 +
 .../UploadCash/components/VerifyDetail.tsx         |  16 +-
 .../finance/Receive/BillLockAndUnlock/index.tsx    |  57 +-
 .../finance/Receive/BillPrintReport/index.tsx      | 132 ++--
 src/pages/finance/Receive/CreateBill/index.tsx     |  19 +-
 .../Onecharges/components/OnechargesQuery.tsx      |  22 +-
 src/pages/finance/Receive/OrderBill/index.less     |   2 +-
 .../QueryBill/components/LinkDetailForBill.tsx     |  13 +-
 .../Policy/CalculationSsCust/index.tsx             | 206 +++++--
 .../Policy/CalculationSsSale/index.tsx             | 196 ++++--
 .../infopublication/Policy/SiteQuery/index.tsx     |  17 +-
 .../SurveySsPolicyCust/components/tabCom.tsx       |  11 +-
 .../Policy/components/PolicyViewCodal/index.tsx    |  14 +-
 .../Policy/components/SingleCityForm/index.tsx     |   4 +-
 .../CountPayManage/Forms/ChangeCountPayWin.tsx     |  16 +-
 .../CountPayManage/Forms/DetailCountPay.tsx        |  31 +-
 src/pages/payroll/CountPayManage/index.tsx         |  36 +-
 .../payroll/DataInterfaceManage/Forms/AddForms.tsx |  72 +--
 src/pages/payroll/EditEmpBankCard/index.tsx        |   3 -
 .../JSDJ/AttachPay/components/DetailModal.tsx      |  93 +++
 src/pages/payroll/JSDJ/AttachPay/index.tsx         | 156 +++++
 .../JSDJ/DonationdeducItemUpload/Detail.tsx        | 123 ++++
 .../payroll/JSDJ/DonationdeducItemUpload/index.tsx | 110 ++++
 .../payroll/JSDJ/EndowmentItemUpload/Detail.tsx    | 107 ++++
 .../payroll/JSDJ/EndowmentItemUpload/index.tsx     | 110 ++++
 src/pages/payroll/JSDJ/HealthItemUpload/Detail.tsx | 111 ++++
 src/pages/payroll/JSDJ/HealthItemUpload/index.tsx  | 110 ++++
 .../payroll/JSDJ/QueryDonationdeducItem/index.tsx  | 122 ++++
 .../payroll/JSDJ/QueryEmpSubmitInfo/Detail.tsx     | 113 ++++
 .../JSDJ/QueryEmpSubmitInfo/EmpSubmitHistory.tsx   | 196 ++++++
 .../payroll/JSDJ/QueryEmpSubmitInfo/index.tsx      | 349 +++++++++++
 .../payroll/JSDJ/QueryEmpSubmitTask/Detail.tsx     | 197 ++++++
 .../payroll/JSDJ/QueryEmpSubmitTask/index.tsx      | 148 +++++
 .../payroll/JSDJ/QueryEndowmentItem/index.tsx      | 108 ++++
 src/pages/payroll/JSDJ/QueryHealthItem/index.tsx   | 111 ++++
 src/pages/payroll/JSDJ/QueryReliefItem/index.tsx   | 107 ++++
 src/pages/payroll/JSDJ/ReliefItemUpload/Detail.tsx | 107 ++++
 src/pages/payroll/JSDJ/ReliefItemUpload/index.tsx  | 202 +++++++
 .../components/AddExportModal.tsx                  | 120 ++++
 .../components/MaintainExport.tsx                  |  99 +++
 .../components/SendHistory.tsx                     | 133 +++++
 .../components/ShowFailureModal.tsx                |  77 +++
 .../payroll/JSDJ/TaxDeclarationManages/index.tsx   | 221 +++++++
 src/pages/payroll/JSDJ/TaxPayQuery/Detail.tsx      | 197 ++++++
 src/pages/payroll/JSDJ/TaxPayQuery/index.tsx       | 148 +++++
 .../payroll/JSDJ/TaxPaymentDownload/Detail.tsx     | 197 ++++++
 .../payroll/JSDJ/TaxPaymentDownload/index.tsx      | 148 +++++
 src/pages/payroll/JSDJ/TriPartyQuery/Detail.tsx    | 197 ++++++
 src/pages/payroll/JSDJ/TriPartyQuery/index.tsx     | 148 +++++
 src/pages/payroll/JSDJ/UngenerateTaxPay/index.tsx  | 103 ++++
 .../JSDJ/empDeclareReduce/components/Detail.tsx    | 193 ++++++
 src/pages/payroll/JSDJ/empDeclareReduce/index.tsx  | 155 +++++
 src/pages/payroll/JSDJ/uptEmpInfoManages/index.tsx | 181 ++++++
 .../PayRollArchivesManage/Forms/MissionHistory.tsx | 122 ++++
 .../Forms/MissionHistoryDetail.tsx                 | 110 ++++
 src/pages/payroll/PayRollArchivesManage/index.tsx  | 374 +++++++++++-
 src/pages/payroll/PayTaxes/index.tsx               | 219 -------
 .../PureDataInterfaceManage/Forms/AddForms.tsx     |  57 +-
 .../components/AddModal.tsx                        | 160 -----
 .../payroll/WithholdAgentLimitMaintance/index.tsx  | 165 -----
 src/pages/payroll/XZDFS/SENDMAIL/index.tsx         |   4 +-
 .../FirstFeedBackSendManages/components/detail.tsx |  42 +-
 src/pages/payroll/XZFFPC/QueryPayResult/index.tsx  |  20 +-
 .../SetPayResult/components/SetBatchPayResult.tsx  |   3 +-
 src/pages/payroll/XZFFPC/XJFFPC/Create.tsx         |   2 +-
 src/pages/payroll/XZFFPC/XJFFPC/Query.tsx          |  29 +-
 .../components/UpdatePayRollSendBatchWin.tsx       |   5 +-
 .../XZFFPC/XJFFPC/components/WageApplyPay.tsx      |  18 +-
 .../SZFFJGXN/components/QueryThisTimeDetailWin.tsx |   3 +-
 .../components/SetBatchPayResultVirtual.tsx        |   3 +-
 src/pages/payroll/XZFFPCXN/XJFFPCXN/Query.tsx      |   9 +-
 .../components/DelayPayRollSendBatchWin.tsx        |  15 +-
 .../XZFFPCXN/XJFFPCXN/components/PaySendDetail.tsx |  11 +-
 .../XJFFPCXN/components/ShowPayBatchDetail.tsx     |   7 +-
 .../UpdatePayRollSendVirtualBatchWin.tsx           |  13 +-
 .../payroll/archives/CheckEmpTypeManages/index.tsx |  11 +-
 .../payroll/archives/CheckHrieDtManages/index.tsx  |  11 +-
 src/pages/payroll/bswjdc/index.tsx                 |  11 +-
 .../payroll/deduction/CustTaxReport/index.tsx      |  73 +--
 .../UploadTaxDeduction/components/ImpDeduction.tsx |  34 +-
 .../UploadTaxDeduction/components/UploadReslut.tsx |  10 +-
 .../UploadTaxDeduction/components/index.tsx        |   8 +-
 .../payroll/withholdAgent/QueryWithholdAgent.tsx   |  13 +
 .../withholdAgent/components/WithholdAgentPop.tsx  |  11 +
 .../components/WithholdAgentPopDanli.tsx           |  11 +
 .../BillingApproval/components/InvoiceDetail.tsx   |  15 +-
 .../components/OpenInvoiceDetail.tsx               |  33 +-
 .../BillingApproval/components/ReceiveDetail.tsx   |   2 +-
 .../pending/BillingApproval/utils/createHtml.ts    |  51 +-
 .../DSPay/components/SubFilePayCustDetail.tsx      |   3 +-
 src/pages/pending/DSPay/index.tsx                  |   9 +-
 src/pages/pending/EditEmpBankCardAdjust/index.tsx  | 663 ---------------------
 .../EditEmpBankCardAdjust/relatedCustomerPop.tsx   |  78 ---
 src/pages/pending/OnechargesApproval/index.tsx     |  26 +-
 .../pending/PaymentApply/components/Delivery.tsx   |  82 ---
 .../PaymentApply/components/DeliveryResult.tsx     |  42 --
 src/pages/pending/PaymentApply/index.tsx           | 199 -------
 src/pages/pending/PdFilePay/index.tsx              |   4 +-
 src/pages/pending/QueryPrvdPayApprove/index.tsx    |   3 +-
 src/pages/pending/QuotationSale/index.tsx          |   3 +-
 .../SocialPay/components/FilePayApprove.tsx        | 103 +---
 src/pages/pending/UnlockWfl/UnlockPopAudit.tsx     |   9 +-
 .../pending/WagePay/components/WagePayApprove.tsx  |  14 +-
 src/pages/pending/WagePay/index.tsx                |   4 +-
 src/pages/pending/WagePayVirtual/index.tsx         |   4 +-
 .../ClientEmpVisitedReport/index.tsx               |   2 +-
 .../ComplainAppSelect/components/AddFormWin.tsx    |  74 +--
 .../qualitycontrol/ComplainAppSelect/index.tsx     |   1 +
 src/pages/sysmanage/AllocateService/index.tsx      |  19 +-
 src/utils/forms/validate.ts                        |   8 +-
 src/utils/login/index.ts                           |   8 +-
 src/utils/methods/file.ts                          |   5 +-
 src/utils/methods/pagenation.ts                    |  11 -
 src/utils/methods/times.ts                         |   4 +-
 src/utils/model/index.tsx                          |   8 +-
 src/utils/settings/payroll/jsdj.ts                 |  34 ++
 .../settings/payroll/payRollArchivesManage.ts      |  25 +
 .../welfaremanage/Socialmanage/socialApply.ts      |  24 +-
 yarn.lock                                          | 259 +-------
 273 files changed, 8641 insertions(+), 7374 deletions(-)
