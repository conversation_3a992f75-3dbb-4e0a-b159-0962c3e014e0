import React, { useEffect, useState } from 'react';
import { Form, Button, message } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';

interface BusinessNodeFormProps {
  modal: [boolean, CallableFunction];
  listOptions: any;
  initialInfo?: any;
}
const service = API.welfaremanage.ebmBusinessConfig.insertOrUpdateBusinessNodeConfig;
const BusinessNodeForm: React.FC<BusinessNodeFormProps> = ({ modal, listOptions, initialInfo }) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;

  const [form] = Form.useForm();
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (visible) {
      if (initialInfo && Object.keys(initialInfo).length > 0) {
        setIsEdit(true);
        form.setFieldsValue(initialInfo);
      } else {
        setIsEdit(false);
        form.resetFields();
      }
    }
  }, [visible, initialInfo, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      // 确保包含业务内容ID
      const submitData = {
        ...initialInfo,
        ...values,
      };
      // TODO: 调用API保存数据
      // console.log('业务节点提交:', submitData);
      await service.requests(submitData);
      message.success(isEdit ? '修改成功' : '新增成功');
      setVisible(false);
      form.resetFields();
      // 刷新列表，传递当前的业务内容ID
      console.log(listOptions.queries);
      listOptions.request(listOptions.queries);
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button key="submit" type="primary" onClick={handleSubmit}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '业务节点',
      fieldName: 'busNodeConfigName',
      inputRender: 'string',
    },
  ];

  return (
    <Codal
      title={isEdit ? '修改业务节点' : '新增业务节点'}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        setVisible(false);
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="60%"
    >
      <FormElement3 form={form}>
        <EnumerateFields formColumns={formColumns} outerForm={form} colNumber={1} />
      </FormElement3>
    </Codal>
  );
};

export default BusinessNodeForm;
