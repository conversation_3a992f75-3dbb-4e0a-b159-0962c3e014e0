import React, { useState } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, FormInstance, Modal, Typography, message } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance, useWritable } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import {
  BusinessBigByBusTypeSelector,
  GetBaseBusnameClassDropdownList,
} from '@/components/Selectors/BaseDataSelectors';
import BusinessContentForm from './components/BusinessContentForm';
import BusinessNodeForm from './components/BusinessNodeForm';
import { isEmpty } from 'lodash';

export const isOrNoMap = new Map<number | string, string>([
  ['1', '是'],
  ['0', '否'],
]);

export const handleAttributeMap = new Map<number | string, string>([
  ['1', '流程业务'],
  ['2', '单次业务'],
]);

export const handleObjectMap = new Map<number | string, string>([
  ['1', '客户'],
  ['2', '员工'],
]);

export const handleMethodMap = new Map<number | string, string>([
  ['1', '员工办理'],
  ['2', '客户办理'],
]);

export const categoryMap = new Map<string, string>([
  ['1', '社保业务'],
  ['2', '公积金业务'],
  ['3', '人力资源收费服务'],
]);
const service1 = API.welfaremanage.ebmBusinessConfig.selectBusinessConfigPage;
const service2 = API.welfaremanage.ebmBusinessConfig.selectBusinessNodeConfigPage;
const deleteService1 = API.welfaremanage.ebmBusinessConfig.deleteBusinessConfig;
const deleteService2 = API.welfaremanage.ebmBusinessConfig.deleteBusinessNodeConfig;
const invalidateService = API.welfaremanage.ebmBusinessConfig.updateState;

const NationwideBusiness = () => {
  const [form] = Form.useForm();
  // 业务内容弹窗状态
  const [businessContentVisible, setBusinessContentVisible] = useState(false);
  const [businessContentInitialInfo, setBusinessContentInitialInfo] = useState<any>({});

  // 业务节点弹窗状态
  const [businessNodeVisible, setBusinessNodeVisible] = useState(false);
  const [businessNodeInitialInfo, setBusinessNodeInitialInfo] = useState<any>({});

  // 选中的业务内容记录
  const [selectedBusinessContent, setSelectedBusinessContent] = useState<any>(null);

  const writable = useWritable({
    service: service1,
    onSuccess(data) {
      if (isEmpty(data?.list)) {
        setSelectedBusinessContent(null);
        wriTableSub.setNewData([]);
        return;
      }
      wriTableSub.request({ busConfigId: data?.list?.[0]?.busConfigId });
      setSelectedBusinessContent(data?.list?.[0]);
    },
  });

  const wriTableSub = useWritable({
    service: service2,
  });

  // 处理业务内容选择
  const handleBusinessContentSelect = (record: any) => {
    setSelectedBusinessContent(record);
    if (record?.busConfigId === selectedBusinessContent?.busConfigId) return;
    if (record?.busConfigId) {
      wriTableSub.request({ busConfigId: record.busConfigId });
    }
  };

  // 处理新增业务内容
  const handleAddBusinessContent = () => {
    setBusinessContentInitialInfo({});
    setBusinessContentVisible(true);
  };

  // 处理修改业务内容
  const handleEditBusinessContent = (options: WritableInstance) => {
    const selectedRows = options.selectedRows;
    if (!selectedRows || selectedRows.length === 0) {
      message.error('请选择要修改的记录');
      return;
    }
    if (selectedRows.length > 1) {
      message.error('只能选择一条记录进行修改');
      return;
    }
    setBusinessContentInitialInfo(selectedRows[0]);
    setBusinessContentVisible(true);
  };

  // 处理删除业务内容
  const handleDeleteBusinessContent = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要删除的记录');
      return;
    }

    // 检查是否有被引用的记录
    const hasReferencedRecord = options.selectedRows.some((item) => item.refStr === '1');
    if (hasReferencedRecord) {
      message.error('所选记录中包含已被引用的业务内容，无法删除');
      return;
    }

    const busConfigIds = options.selectedRows.map((item) => item.busConfigId.toString());
    Modal.confirm({
      content: `将删除所选业务内容，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await deleteService1.requests(busConfigIds);
        message.success('删除成功');
        options.request();
      },
    });
  };

  // 处理发布业务内容
  const handlePublishBusinessContent = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要发布的记录');
      return;
    }

    const hasPublishedRecord = options.selectedRows.filter((item) => item.isValid === '0');
    if (hasPublishedRecord.length === 0) {
      message.error('所选记录中没有要发布的业务内容');
      return;
    }

    const publishData = hasPublishedRecord.map((item) => ({ ...item, isValid: '1' }));

    Modal.confirm({
      content: `将发布所选业务内容，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await invalidateService.requests(publishData);
        message.success('发布成功');
        options.request();
      },
    });
  };

  // 处理失效业务内容
  const handleInvalidateBusinessContent = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要失效的记录');
      return;
    }
    const validRecords = options.selectedRows.filter((item) => item.isValid === '1');

    if (validRecords.length === 0) {
      message.error('所选记录中没有要失效的业务内容');
      return;
    }

    const invalidateData = validRecords.map((item) => ({ ...item, isValid: '0' }));

    Modal.confirm({
      content: `将失效所选业务内容，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await invalidateService.requests(invalidateData);
        message.success('失效成功');
        options.request();
      },
    });
  };

  // 处理导出数据
  const handleExportData = (options: WritableInstance) => {
    options.handleExport(
      { service: API.welfaremanage.ebmBusinessConfig.exportExcel },
      {
        columns,
        condition: { ...options.queries },
        fileName: '全国业务维护.xlsx',
      },
    );
  };

  // 处理新增业务节点
  const handleAddBusinessNode = () => {
    if (selectedBusinessContent?.transactProperty === '2' && wriTableSub.data.list.length >= 1) {
      message.error('当前业务内容属于“单次业务”，只能新增一个节点。');
      return;
    }
    if (!selectedBusinessContent) {
      message.error('请先选择业务内容，再新增业务节点');
      return;
    }
    setBusinessNodeInitialInfo({ busConfigId: selectedBusinessContent?.busConfigId, type: 'add' });
    setBusinessNodeVisible(true);
  };

  // 处理修改业务节点
  const handleEditBusinessNode = (options: WritableInstance) => {
    const selectedRows = options.selectedRows;
    if (!selectedRows || selectedRows.length === 0) {
      message.error('请选择要修改的记录');
      return;
    }
    if (selectedRows.length > 1) {
      message.error('只能选择一条记录进行修改');
      return;
    }
    setBusinessNodeInitialInfo(selectedRows[0]);
    setBusinessNodeVisible(true);
  };

  // 处理删除业务节点
  const handleDeleteBusinessNode = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要删除的记录');
      return;
    }

    // 检查是否有被引用的记录
    const hasReferencedRecord = options.selectedRows.some((item) => item.refStr === '1');
    if (hasReferencedRecord) {
      message.error('所选记录中包含已被引用的业务节点，无法删除');
      return;
    }

    const busNodeConfigIds = options.selectedRows.map((item) => item.busNodeConfigId);
    Modal.confirm({
      content: `将删除所选业务节点，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await deleteService2.requests(busNodeConfigIds);
        message.success('删除成功');
        options.request();
      },
    });
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm: FormInstance) => {
        return mapToSelectors(categoryMap, {
          allowClear: true,
          placeholder: '请选择业务类型',
          onChange: () => {
            outerForm.setFieldsValue({ busnameClassId: undefined, busContent: undefined });
          },
        });
      },
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      inputRender: (outerForm: FormInstance) => (
        <GetBaseBusnameClassDropdownList
          allowClear
          params={{ categoryId: outerForm.getFieldValue('categoryId') ?? '' }}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
    },
    {
      label: '业务内容',
      fieldName: 'busContent',
      inputRender: (outerForm: FormInstance) => (
        <BusinessBigByBusTypeSelector
          params={{
            busnameClassId: outerForm.getFieldValue('busnameClassId') ?? '',
            categoryId: outerForm.getFieldValue('categoryId') ?? '',
          }}
          onChange={(value: any, option: any) => {
            outerForm.setFieldsValue({ busContent: option?.title });
          }}
          allowClear
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.categoryId !== curValues.categoryId ||
          prevValues.busnameClassId !== curValues.busnameClassId
        );
      },
    },
    {
      label: '办理属性',
      fieldName: 'transactProperty',
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'transactObject',
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    // {
    //   label: '办理方式',
    //   fieldName: 'transactTypeStr',
    //   inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    // },
    {
      label: '是否微信显示',
      fieldName: 'wxShowState',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'clientShowState',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '业务内容编号', dataIndex: 'busConfigId' },
    { title: '业务类型', dataIndex: 'categoryName' },
    { title: '业务项目', dataIndex: 'busnameClassName' },
    { title: '业务内容', dataIndex: 'busContent' },
    { title: '办理属性', dataIndex: 'transactPropertyName' },
    { title: '办理对象', dataIndex: 'transactObjectName' },
    // { title: '办理方式', dataIndex: 'transactTypeStrName' },
    {
      title: '是否微信显示',
      dataIndex: 'wxShowStateName',
    },
    {
      title: '是否客户端显示',
      dataIndex: 'clientShowStateName',
    },
    {
      title: '是否被引用',
      dataIndex: 'refStr',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
  ];

  const businessNodeColumns: WritableColumnProps<any>[] = [
    { title: '业务节点编号', dataIndex: 'busNodeConfigId' },
    { title: '业务节点', dataIndex: 'busNodeConfigName' },
    {
      title: '是否被引用',
      dataIndex: 'refStr',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
  ];

  const renderButtons = (options: WritableInstance) => (
    <>
      <Button type="primary" htmlType="submit">
        查询
      </Button>
      <AuthButtons funcId="nationwide_business_btn">
        <Button onClick={handleAddBusinessContent}>新增</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button onClick={() => handleEditBusinessContent(options)}>修改</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button onClick={() => handleDeleteBusinessContent(options)}>删除</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button onClick={() => handlePublishBusinessContent(options)}>发布</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button onClick={() => handleInvalidateBusinessContent(options)}>失效</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton onClick={() => handleExportData(options)}>导出数据</AsyncButton>
      </AuthButtons>
      {/* 业务内容弹窗 */}
      <BusinessContentForm
        modal={[businessContentVisible, setBusinessContentVisible]}
        listOptions={options}
        initialInfo={businessContentInitialInfo}
      />
    </>
  );

  const renderBusinessNodeButtons = (options: WritableInstance) => (
    <>
      <AuthButtons funcId="nationwide_business_btn">
        <Button disabled={!selectedBusinessContent} onClick={handleAddBusinessNode}>
          新增
        </Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button onClick={() => handleEditBusinessNode(options)}>修改</Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button onClick={() => handleDeleteBusinessNode(options)}>删除</Button>
      </AuthButtons>
      {/* 业务节点弹窗 */}
      <BusinessNodeForm
        modal={[businessNodeVisible, setBusinessNodeVisible]}
        listOptions={options}
        initialInfo={businessNodeInitialInfo}
      />
    </>
  );

  return (
    <>
      <CachedPage
        service={service1}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        rowKey="busConfigId"
        selectedSingleRow={selectedBusinessContent}
        onSelectSingleRow={handleBusinessContentSelect}
        wriTable={writable}
      />
      <CachedPage
        service={service2}
        formColumns={[]}
        columns={businessNodeColumns}
        renderButtons={renderBusinessNodeButtons}
        rowKey="busNodeConfigId"
        wriTable={wriTableSub}
      />
    </>
  );
};

export default NationwideBusiness;
