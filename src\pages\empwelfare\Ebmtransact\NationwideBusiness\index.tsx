import React, { useState } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, FormInstance, Typography, message } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance, useWritable } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import {
  CommonBaseDataSelector,
  BusTypeDropdownListSelector,
} from '@/components/Selectors/BaseDataSelectors';
import BusinessContentForm from './components/BusinessContentForm';
import BusinessNodeForm from './components/BusinessNodeForm';
import { isEmpty } from 'lodash';

export const isOrNoMap = new Map<number | string, string>([
  ['1', '是'],
  ['0', '否'],
]);

export const handleAttributeMap = new Map<number | string, string>([
  ['1', '流程业务'],
  ['2', '单次业务'],
]);

export const handleObjectMap = new Map<number | string, string>([
  ['1', '客户'],
  ['2', '员工'],
]);

export const handleMethodMap = new Map<number | string, string>([
  ['1', '员工办理'],
  ['2', '客户办理'],
]);
const service1 = API.welfaremanage.ebmBusinessConfig.selectBusinessConfigPage;
const service2 = API.welfaremanage.ebmBusinessConfig.selectBusinessNodeConfigPage;
const deleteService1 = API.welfaremanage.ebmBusinessConfig.deleteBusinessConfig;
const deleteService2 = API.welfaremanage.ebmBusinessConfig.deleteBusinessNodeConfig;
const NationwideBusiness = () => {
  const [form] = Form.useForm();
  // 业务内容弹窗状态
  const [businessContentVisible, setBusinessContentVisible] = useState(false);
  const [businessContentInitialInfo, setBusinessContentInitialInfo] = useState<any>({});

  // 业务节点弹窗状态
  const [businessNodeVisible, setBusinessNodeVisible] = useState(false);
  const [businessNodeInitialInfo, setBusinessNodeInitialInfo] = useState<any>({});

  // 选中的业务内容记录
  const [selectedBusinessContent, setSelectedBusinessContent] = useState<any>(null);

  const writable = useWritable({
    service: service1,
    onSuccess(data) {
      if (isEmpty(selectedBusinessContent)) {
        wriTableSub.request({ busConfigId: data?.list?.[0]?.busConfigId });
        setSelectedBusinessContent(data?.list?.[0]);
      } else {
        setSelectedBusinessContent(undefined);
      }
    },
  });

  const wriTableSub = useWritable({
    service: service2,
  });

  // 处理业务内容选择
  const handleBusinessContentSelect = (record: any) => {
    setSelectedBusinessContent(record);
    if (record?.busConfigId) {
      wriTableSub.request({ busConfigId: record.busConfigId });
    }
  };

  // 处理新增业务内容
  const handleAddBusinessContent = () => {
    setBusinessContentInitialInfo({});
    setBusinessContentVisible(true);
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm: FormInstance) => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
          onConfirm={() => {
            outerForm.setFieldsValue({ busnameClassId: undefined });
          }}
        />
      ),
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      inputRender: (outerForm: FormInstance) => (
        <BusTypeDropdownListSelector
          allowClear
          params={{ categoryId: outerForm.getFieldValue('categoryId') ?? '' }}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
    },
    {
      label: '业务内容',
      fieldName: 'busContent',
      inputRender: 'string',
    },
    {
      label: '办理属性',
      fieldName: ' transactProperty',
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'transactObject',
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'transactTypeStr',
      inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    },
    {
      label: '是否微信显示',
      fieldName: 'wxShowState',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'clientShowState',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '业务内容编号', dataIndex: 'busConfigId' },
    { title: '业务类型', dataIndex: 'categoryName' },
    { title: '业务项目', dataIndex: 'busnameClassName' },
    { title: '业务内容', dataIndex: 'busContent' },
    { title: '办理属性', dataIndex: 'transactPropertyName' },
    { title: '办理对象', dataIndex: 'transactObjectName' },
    { title: '办理方式', dataIndex: 'transactTypeStrName' },
    {
      title: '是否微信显示',
      dataIndex: 'wxShowState',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '是否客户端显示',
      dataIndex: 'clientShowState',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '是否被引用',
      dataIndex: 'refStr',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
  ];

  const businessNodeColumns: WritableColumnProps<any>[] = [
    { title: '业务节点编号', dataIndex: 'busNodeConfigId' },
    { title: '业务节点', dataIndex: 'busNodeConfigName' },
    {
      title: '是否被引用',
      dataIndex: 'refStr',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
  ];

  const renderButtons = (options: WritableInstance) => (
    <>
      <Button type="primary" htmlType="submit">
        查询
      </Button>
      <AuthButtons funcId="nationwide_business_btn">
        <Button
          onClick={() => {
            setBusinessContentInitialInfo({});
            setBusinessContentVisible(true);
          }}
        >
          新增
        </Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button
          onClick={() => {
            const selectedRows = options.selectedRows;
            if (!selectedRows || selectedRows.length === 0) {
              message.error('请选择要修改的记录');
              return;
            }
            if (selectedRows.length > 1) {
              message.error('只能选择一条记录进行修改');
              return;
            }
            setBusinessContentInitialInfo(selectedRows[0]);
            setBusinessContentVisible(true);
          }}
        >
          修改
        </Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton>删除</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton>发布</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton>失效</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton
          onClick={() => {
            options.handleExport(
              { service: API.welfaremanage.ebmBusinessConfig.exportExcel },
              {
                columns,
                condition: { ...options.queries },
                fileName: '全国业务维护.xlsx',
              },
            );
          }}
        >
          导出数据
        </AsyncButton>
      </AuthButtons>
      {/* 业务内容弹窗 */}
      <BusinessContentForm
        modal={[businessContentVisible, setBusinessContentVisible]}
        listOptions={options}
        initialInfo={businessContentInitialInfo}
      />
    </>
  );

  const renderBusinessNodeButtons = (options: WritableInstance) => (
    <>
      <AuthButtons funcId="nationwide_business_btn">
        <Button
          disabled={!selectedBusinessContent}
          onClick={() => {
            setBusinessNodeInitialInfo({ busConfigId: selectedBusinessContent?.busConfigId });
            setBusinessNodeVisible(true);
          }}
        >
          新增
        </Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <Button
          onClick={() => {
            const selectedRows = options.selectedRows;
            if (!selectedRows || selectedRows.length === 0) {
              message.error('请选择要修改的记录');
              return;
            }
            if (selectedRows.length > 1) {
              message.error('只能选择一条记录进行修改');
              return;
            }
            setBusinessNodeInitialInfo(selectedRows[0]);
            setBusinessNodeVisible(true);
          }}
        >
          修改
        </Button>
      </AuthButtons>
      <AuthButtons funcId="nationwide_business_btn">
        <AsyncButton
          onClick={async () => {
            if (!options.selectedRows || options.selectedRows.length === 0) {
              message.error('请选择要删除的记录');
              return;
            }
            await deleteService2.requests({
              busNodeConfigId: options.selectedRows.map((item) => item.busNodeConfigId).join(','),
            });
            message.success('删除成功');
            options.request();
          }}
        >
          删除
        </AsyncButton>
      </AuthButtons>
      {/* 业务节点弹窗 */}
      <BusinessNodeForm
        modal={[businessNodeVisible, setBusinessNodeVisible]}
        listOptions={options}
        initialInfo={businessNodeInitialInfo}
      />
    </>
  );

  return (
    <>
      <CachedPage
        service={service1}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        rowKey="busConfigId"
        selectedSingleRow={selectedBusinessContent}
        onSelectSingleRow={handleBusinessContentSelect}
        wriTable={writable}
      />
      <CachedPage
        service={service2}
        formColumns={[]}
        columns={businessNodeColumns}
        renderButtons={renderBusinessNodeButtons}
        rowKey="busNodeConfigId"
        wriTable={wriTableSub}
      />
    </>
  );
};

export default NationwideBusiness;
