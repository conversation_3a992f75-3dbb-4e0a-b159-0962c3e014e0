{"province": "赵煜灏 省份维护 | ./basedata/ProvinceInfo/index", "city": "刘双 城市维护 | ./basedata/CityInfo/index", "area-code": "刘双 地区码维护 | ./basedata/AreaCode/index", "country": "刘双 国家维护 | ./basedata/CountryInfo/index", "competitor": "刘双 竞争对手维护 | ./basedata/Competitor/index", "sys-branch-title": "刘双 签单方抬头 | ./basedata/SysBranchTitle/index", "socialsecurity.prodratio": "严小强 社保比例维护 | ./basedata/Socialsecurity/ProductRatio/index", "socialsecurity.maintenance": "严小强 社保组维护 | ./basedata/Socialsecurity/Maintenance/index", "socialsecurity.comboManage": "严小强 社保组维护 | ./basedata/Socialsecurity/ComboManage/index", "socialsecurity.PersonCategory": "严小强 城市人员分类维护 | ./basedata/Socialsecurity/PersonCategory/index", "taxRateTable": "严小强 税率表维护 | ./basedata/TaxRateTable/index", "baseDataXml": "赵煜颢 数据字典维护 | ./basedata/BaseDataXml/index", "jindieOrg": "赵煜颢 金蝶分公司维护 | ./basedata/JindieOrg/index", "branchInvoice": "赵煜颢 分公司开票维护 | ./basedata/BranchInvoice/index", "branchback": "赵煜颢 分公司银行账号维护 | ./basedata/BranchBack/index", "hire": "赵煜灏 入离职基础数据 | ./basedata/Hire/index", "welfareType": "刘夏梅 福利类型大类小类维护 | ./basedata/WelfareType/index", "businessType": "刘夏梅 业务大小类维护 | ./basedata/BusinessType/index", "businessnameType": "赵煜颢 业务大小类名称维护 | ./basedata/BusinessnameType/index", "emp-maintain": "孙尚阳 分公司用工方维护 | ./basedata/EmpMaintain/index", "PrintProcess": "孙尚阳 用印流程查询 | ./basedata/PrintProcess/index", "socialsecurity.servicePoint": "孙尚阳 服务网点维护 | ./basedata/Socialsecurity/ServicePoint/index", "type-definition": "刘夏梅 导入类型定义| ./basedata/TypeDefinition/index", "instance-definition": "刘夏梅 导入实例定义 | ./basedata/InstanceDefinition/index", "FinancialApprovalMaintain": "孙尚阳 财务审批节点配置 | ./basedata/FinancialApprovalMaintain/index", "serviceType": "刘夏梅 合同大小类维护 | ./basedata/ServiceType/index", "fileProvider": "赵煜颢 档案供应商维护 | ./basedata/FileProvider/index", "materialsInfo.materialsAssert": "赵煜颢 材料维护 | ./basedata/MaterialsInfo/MaterialsAssert/index", "materialsInfo.materialPackage": "赵煜颢 材料包维护 | ./basedata/MaterialsInfo/MaterialPackage/index", "medicalIns.MedicalAssert": "赵煜颢 医疗机构维护 | ./basedata/medicalIns/MedicalAssert/index", "medicalIns.ImportMedical": "赵煜颢 批量导入医疗机构 | ./basedata/medicalIns/ImportMedical/index", "payment": "赵煜颢 支付地账号同步 | ./basedata/Payment/index", "specialSignerTitleMaintance": "赵煜颢 特殊签单方抬头维护 | ./basedata/SpecialSignerTitleMaintance/index", "welfareTreatment": "赵煜颢 福利待遇办理维护 | ./basedata/WelfareTreatment/index", "FAQMaintenance": "刘夏梅 FAQ问答维护 | ./basedata/FAQMaintenance/index", "paymentCustSync": "赵煜颢 支付地抬头+客户同步 | ./basedata/PaymentCustSync/index", "disabilitySetRatio": "陈国祥 残障金法定安置比例 | ./basedata/DisabilitySetRatio/index", "publicFieldQuery": "严小强 公共入职字段查询 | ./basedata/PublicFieldQuery/index", "uploadAndDownload": "刘夏梅 常用模板上传及下载（客服）| ./basedata/UploadAndDownload/index", "threeInOneIntegration.branchOfficeRule": "陈国祥 分公司规则 | ./basedata/ThreeInOneIntegration/BranchOfficeRule/index"}