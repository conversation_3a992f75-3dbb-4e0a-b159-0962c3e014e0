<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-29 09:38:30
 * @lastTime: 2019-08-28 14:46:11
 * @LastAuthor: Do not edit
 * @message: 
 -->
# 分页公共方法

## `pagenation`模块

基本类型
```ts
export interface PageConfig {
  pageNum?: number;
  pageSize?: number;
}
export interface PageResponse {
  list: Array<any>;
  total: number;
}
export interface PaginationConfig {
    total?: number;
    current?: number;
    pageSize?: number;
    onChange?: (page: number, pageSize?: number) => void;
    // ... ...
}

interface PageResOptions<T> {
  unionKey?: string;
  indexKey?: string;
  processor?: PageProcessor<T>;
}
```
### `pageRequest` 分页请求参数转换
```ts
export function pageRequest<T = any>(query: T, pageInfo?: PaginationConfig): T {
  const { current, pageSize } = pageInfo || { current: 1, pageSize: 5 };
  const data: any = { pageNum: current || 1, pageSize: pageSize || 5 };
  const params = Object.keys(query).reduce((obj, key) => {
    obj[key] = query[key];
    return obj;
  }, data) as T;
  return params;
}
```
由于前后端对分页参数的定义不统一，在请求接口之前应对参数进行转换。`query` 是待发送的请求数据，不包含分页配置。`params`是完整的待发送数据，包含完整的分页参数。示例：
```ts
// 第一次调用，pageInfo没有数据
const query = { roleName: '休闲部长'， roleGrade： 2 }
const pageInfo = undefined
输出：
const params = { roleName: '休闲部长'， roleGrade： 2，pageNum： 1， pageSize： 5 }
// 第二次调用，pageInfo有数据
const query = { roleName: '休闲部长'， roleGrade： 2 }
const pageInfo = { current: 2, pageSize: 10 }
输出：
const params = { roleName: '休闲部长'， roleGrade： 2，pageNum： 2， pageSize： 10 }
```

### `pageResponse` 分页响应数据转换
```ts
export function pageResponse<T>(res: PageResponse<T>, req?: PageConfig, options?: PageResOptions<T>,
): { list: Array<T>; pagination: PaginationConfig } {
  const { total } = res;
  let { list } = res;
  const pagination: PaginationConfig = {};
  if (req) {
    pagination.current = req.pageNum;
    pagination.pageSize = req.pageSize;
  }
  if (total !== -1) pagination.total = total;
  if (options) {
    const { indexKey, unionKey, processor } = options;
    if (indexKey) list = addIndexKey<T>(indexKey, list);
    if (unionKey) list = addUnionId<T>(unionKey, list);
    if (processor) list = processor(list);
  }
  const response: TablePage<T> = {
    list,
    pagination,
  };
  return response;
}
```

由于前后端对分页响应数据的定义不统一，在请求接口之之后应对数据结构进行转换。`res`是后端返回的数据中的`data`属性，`req`是调用接口时传递给后端的请求参数，一般是由`pageRequest`返回的结果。示例：
```ts
const res = { list: Array<RoleTable>, total: 2190 }
const req = { roleName: '休闲部长'， roleGrade： 2，pageNum： 1， pageSize： 5 }
输出：
const response = {
  list: Array<RoleTable>,
  pagination: { current: 1, pageSize: 5, total: 2190 }
}
```

### `options`说明
`pageResponse`还接收了一个参数`options`，类型是`PageResOptions<T>`。
主要结构如下：
```ts
interface PageResOptions<T> {
  unionKey?: string;
  indexKey?: string;
  processor?: PageProcessor<T>;
}
```
`unionKey`， 在某些关系表中，多个字段联合起来构成唯一的key，此时可传入此属性，以横杠分隔各字段。
`indexKey`， 在某些既不提供唯一主键，已不提供联合主键的数据中，传入此属性，可以行序列作为key。
`processor`， 在某些特殊情况下，`unionKey, indexKey`均无法满足需求时，可自定义生成的key的方法并传入。
