declare namespace defs {
  export namespace admin {
    export class AccountManageDTO {
      /** 登陆账号 */
      account?: string;

      /** 账号名称 */
      accountName?: string;

      /** 帐号状态 */
      accountStatus?: string;

      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** 所在分公司 */
      branchName?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 分公司id */
      companyId?: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 所在部门 */
      departName?: string;

      /** 用户编号 */
      empCode?: string;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 员工状态是否有效 */
      isRelationDeleted?: string;

      /** 邮箱 */
      mail?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 在线状态 */
      onlineStatus?: string;

      /** 密码 */
      password?: string;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 状态 */
      status?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class AccountQuery {
      /** 账号 */
      account?: string;

      /** 账号名 */
      accountName?: string;

      /** 账号状态0无效1有效 */
      accountStatus?: string;

      /** endIndex */
      endIndex?: number;

      /** 在线状态0离线大于0在线 */
      onlineStatus?: string;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数 */
      pageSize?: number;

      /** startIndex */
      startIndex?: number;
    }

    export class AuthorityAllocation {
      /** add */
      add?: boolean;

      /** 接单客服id */
      assigneeCsId?: string;

      /** 派单客服id */
      assignerCsId?: string;

      /** 签单方派单人 */
      assignerId?: string;

      /** 派单方id */
      assignerProviderId?: string;

      /** 派单方 */
      assignerProviderName?: string;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.BIZ_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      bizId?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.BIZMAN_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      bizmanId?: string;

      /** 客服名称 */
      bizmanName?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.BIZMAN_TYPE	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      bizmanType?: string;

      /** 客服类型 */
      bizmanTypeName?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** contractId */
      contractId?: string;

      /** 大合同名称 */
      contractName?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.CUST_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      custId?: string;

      /** 客户名称 */
      custName?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** 客户规模 */
      customerSize?: string;

      /** del */
      del?: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.DEPARTMENT_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      departmentId?: string;

      /** 分公司名称 */
      departmentName?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.END_DT	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      endDt?: string;

      /** endIndex */
      endIndex?: number;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** 新客服id */
      newBizmanId?: string;

      /** 新分公司idid */
      newBranchId?: string;

      /** 生效日期 */
      newEffectiveDt?: string;

      /** noChange */
      noChange?: boolean;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 替换权限级别 */
      privilegeLevel?: string;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 责任客服id */
      signCsId?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.START_DT	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      startDt?: string;

      /** startIndex */
      startIndex?: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.SUBCONTRACT_BIZ_AUTH_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      subcontractBizAuthId?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.SUBCONTRACT_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
      subcontractId?: string;

      /** 小合同名称 */
      subcontractName?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class AuthorityAllocationVO {
      /** 权限分配 */
      authorityAllocation?: defs.admin.AuthorityAllocation;

      /** 权限分配列表 */
      list?: Array<defs.admin.AuthorityAllocation>;
    }

    export class BaseEntity {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 菜单编码 */
      functionCode?: string;

      /** 菜单id */
      functionId?: string;

      /** 菜单信息id */
      functionInfoId?: string;

      /** 菜单名称 */
      functionName?: string;

      /** 菜单类型 */
      functionType?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** 帮助url */
      helpUrl?: string;

      /** 图标url */
      iconUrl?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 是不是我工作 */
      isMyWork?: string;

      /** 新是否有效：1有效，0无效 */
      isValid?: string;

      /** 菜单层级 */
      level?: string;

      /** 链接url */
      linkUrl?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** rownum */
      num?: string;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 新菜单url */
      reactUrl?: string;

      /** 报表url */
      reportUrl?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 是否选中 */
      selected?: string;

      /** 特例类型 */
      specialType?: string;

      /** 状态 */
      status?: string;

      /** subMenuList */
      subMenuList?: Array<defs.admin.BaseEntity>;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 上级菜单id */
      superFunctionId?: string;

      /** 类型,用于区分操作类型参数 */
      type?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode?: number;

      /** code */
      code?: number;

      /** data */
      data: T0;

      /** message */
      message?: string;

      /** t */
      t: T0;
    }

    export class DeptDTO {
      /** add */
      add?: boolean;

      /** 联系地址 */
      address?: string;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单地址 */
      billAddress?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 账单邮编 */
      billZipCode?: string;

      /** 部门业务类别: 10  全部20  客服30  新产品销售31  大客户销售32  渠道部销售33  电销部销售34  BPO事业部销售35  福利事业部销售40  销售50  财务60  法务70  人事80  其他 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** children */
      children?: Array<defs.admin.DeptDTO>;

      /** 城市id */
      cityId?: string;

      /** 城市名称 */
      cityName?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 联系电话1，电话 */
      contactTel1?: string;

      /** 联系电话2，手机 */
      contactTel2?: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 部门英文名称 */
      departmentEnglishName?: string;

      /** 部门级别：1:集团 2:大区 3 :分公司 4:部门 */
      departmentGrade?: string;

      /** 部门id */
      departmentId?: string;

      /** 部门内部编号 */
      departmentInternalCode?: string;

      /** 部门名称 */
      departmentName?: string;

      /** 部门属性 是否是业务部门 */
      departmentProperty?: string;

      /** email */
      email?: string;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** 传真 */
      fax?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 所属大区 */
      governingArea?: string;

      /** 所属分公司 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 负责人 */
      personInCharge?: string;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 部门类型1 内部 2外部 */
      providerType?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 用户名 */
      realName?: string;

      /** 备注 */
      remark?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 是否选中 */
      selected?: string;

      /** 状态 */
      status?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 上级部门id */
      superDepartmentId?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;

      /** 邮编 */
      zipCode?: string;
    }

    export class DropdownList {
      /** 业务大类类型 */
      btType?: string;

      /** chargeRate */
      chargeRate?: string;

      /** cityId */
      cityId?: string;

      /** cityIdForParty */
      cityIdForParty?: string;

      /** contractAvgAmt */
      contractAvgAmt?: string;

      /** contractHeadcount */
      contractHeadcount?: string;

      /** contractName */
      contractName?: string;

      /** contractSubType */
      contractSubType?: string;

      /** contractSubTypeName */
      contractSubTypeName?: string;

      /** contractType */
      contractType?: string;

      /** contractTypeName */
      contractTypeName?: string;

      /** currentSalesName */
      currentSalesName?: string;

      /** departmentName */
      departmentName?: string;

      /** exFeeMonth */
      exFeeMonth?: string;

      /** 供应商收费模板 */
      exFeeTempltId?: string;

      /** governingArea */
      governingArea?: string;

      /** 所属大区 */
      governingAreaId?: string;

      /** governingBranch */
      governingBranch?: string;

      /** 所属分公司 */
      governingBranchId?: string;

      /** groupType */
      groupType?: string;

      /** 主键 */
      key?: string;

      /** liabilityCsName */
      liabilityCsName?: string;

      /** 全称 */
      name?: string;

      /** 拼音码 */
      pinYinCode?: string;

      /** productLineId */
      productLineId?: string;

      /** 供应商类型1内部2外部 */
      providerType?: string;

      /** 保留名字1 */
      reserveName1?: string;

      /** 保留名字2 */
      reserveName2?: string;

      /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
      reserveObj?: string;

      /** 缩写名 */
      shortName?: string;

      /** signBrachTitleId */
      signBrachTitleId?: string;

      /** signBranchTitleName */
      signBranchTitleName?: string;

      /** 社保组ID */
      ssGroupId?: string;

      /** svcSubtypeName */
      svcSubtypeName?: string;

      /** svcTypeName */
      svcTypeName?: string;
    }

    export class EmpInfoInter {
      /** add */
      add?: boolean;

      /** address */
      address?: string;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** empId */
      empId?: string;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** id */
      id?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** languageType */
      languageType?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class EmpRelative {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 生日 */
      birthday?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 雇员id */
      empId?: string;

      /** 亲属id */
      empRelativesId?: string;

      /** endIndex */
      endIndex?: number;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 性别 */
      gender?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** 证件号 */
      idCardNum?: string;

      /** 证件类型 */
      idCardType?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 亲属姓名 */
      relativeName?: string;

      /** 亲属类型 */
      relativeType?: string;

      /** 备注 */
      remark?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** startIndex */
      startIndex?: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class Employee {
      /** add */
      add?: boolean;

      /** 联系地址 */
      address?: string;

      /** 养老金帐号 */
      annuityAccount?: string;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 首次参加工作日期 */
      beginWorkDate?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 生日 */
      birthday?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 联系电话1，电话 */
      contactTel1?: string;

      /** 联系电话2，手机 */
      contactTel2?: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 国家id */
      countryId?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 残疾类型 */
      disabilityType?: string;

      /** 教育程度 */
      educationLevel?: string;

      /** email */
      email?: string;

      /** 员工编号 */
      employeeCode?: string;

      /** 员工id */
      employeeId?: string;

      /** 员工姓名 */
      employeeName?: string;

      /** 上岗状态 */
      employeeStatus?: string;

      /** endIndex */
      endIndex?: number;

      /** 民族 */
      ethnic?: string;

      /** 查询使用排除的雇员id */
      exceptEmpId?: string;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** 家属劳保卡号 */
      familyLaborCardNo?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** 外语等级 */
      foreignLanguageLevel?: string;

      /** 外语语种 */
      foreignLanguageType?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 性别 */
      gender?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** 是否有不良记录 */
      haveBadnessRecord?: string;

      /** 健康状况 */
      healthStatus?: string;

      /** 入职日期 */
      hireDate?: string;

      /** 户口所在地 */
      hukouAddress?: string;

      /** 户口类别 */
      hukouType?: string;

      /** 证件号 */
      idCardNum?: string;

      /** 证件类型 */
      idCardType?: string;

      /** inId */
      inId?: string;

      /** 初始化类别 */
      initType?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** 是否市级保健对象 */
      isCareObject?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 是否伤残军人 */
      isDisabledMilitary?: string;

      /** 参军日期 */
      joinMilitaryDate?: string;

      /** 婚姻状况 */
      marriageStatus?: string;

      /** 医疗卡 */
      medicalCardId?: string;

      /** 医疗保障类别 */
      medicareType?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** 兵役状况 */
      nationalServiceStatus?: string;

      /** noChange */
      noChange?: boolean;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 密码 */
      password?: string;

      /** 政治面貌 */
      politicalStatus?: string;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 职业工种发证日期 */
      professionAwardDate?: string;

      /** 职业工种发证单位 */
      professionAwardUnit?: string;

      /** 职业工种等级 */
      professionLevel?: string;

      /** 职业工种名称 */
      professionName?: string;

      /** 专业技术职称 */
      professionTitle?: string;

      /** 公积金账号 */
      providentFundAccount?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 省份id */
      provinceId?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 雇员信息备注 */
      remark?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 转业日期 */
      retireFromMilitaryDate?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 离职提起 */
      sepDate?: string;

      /** startIndex */
      startIndex?: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 补充公积金账号 */
      supProvidentFundAccount?: string;

      /** 职称发证日期 */
      techAwardDate?: string;

      /** 职称发证单位 */
      techAwardUnit?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;

      /** 用户名 */
      userName?: string;

      /** 邮政编码 */
      zipCode?: string;
    }

    export class EmployeeFee {
      /** add */
      add?: boolean;

      /** 调整情况分类：1费用添加、2费用清空、3比例调整、4基数调整、5基数+比例调整、6其他调整 */
      adjSituType?: string;

      /** 金额 */
      amount?: number;

      /** 金额(不含税) */
      amtNoTax?: string;

      /** 附加税费 */
      atr?: string;

      /** 基数绑定级次 */
      baseBindingLevel?: string;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 账单起始月 */
      billStartMonth?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
      calculationOrder?: string;

      /** 类型 */
      category?: string;

      /** 收费结束时间 */
      chargeEndDate?: string;

      /** 缴费频率 */
      chargeRate?: string;

      /** 收费起始时间 */
      chargeStartDate?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 套餐id */
      comboId?: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 企业附加金额 */
      eAdditionalAmt?: number;

      /** 企业金额 */
      eAmt?: number;

      /** 企业基数 */
      eBase?: number;

      /** eBillTemplt */
      eBillTemplt?: string;

      /** 企业账单模板id */
      eBillTempltId?: string;

      /** 企业计算方法:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
      eCalculationMethod?: string;

      /** 企业收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      eFeeMonth?: string;

      /** 收费模板名 */
      eFeeTemplt?: string;

      /** 企业收费模板id */
      eFeeTempltId?: string;

      /** 企业频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      eFrequency?: string;

      /** 企业最高比例 */
      eMaxRatio?: number;

      /** 企业最低比例 */
      eMinRatio?: number;

      /** 企业提前几个月收,默认为0，选项0-3 */
      eMonthInAdvance?: string;

      /** 企业精确值0：0位小数1：1位小数2：2位小数5： 精确值 */
      ePrecision?: string;

      /** 企业比例 */
      eRatio?: number;

      /** 企业比例步长 */
      eRatioStep?: number;

      /** 费用段历史id */
      empFeeHisId?: string;

      /** 费用段id */
      empFeeId?: string;

      /** 费用段操作id */
      empFeeOprId?: string;

      /** 员工入离职id */
      empHireSepId?: string;

      /** 员工id */
      empId?: string;

      /** 外部供应商账单起始月 */
      exBillStartMonth?: string;

      /** 供应商收费月 */
      exFeeMonth?: string;

      /** 供应商收费模板名 */
      exFeeTemplt?: string;

      /** 外部供应商收费模板id */
      exFeeTempltId?: string;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 是否一次性付费 */
      isOneTimePay?: string;

      /** 是否显示 1:是0:否 */
      isShow?: string;

      /** 是否更新月度表1:是0:否 */
      isUptFeeMon?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 原金额 */
      oldAmount?: string;

      /** 原账单起始月 */
      oldBillStartMonth?: string;

      /** 个人附加金额 */
      pAdditionalAmt?: number;

      /** 个人金额 */
      pAmt?: number;

      /** 个人基数 */
      pBase?: number;

      /** pBillTemplt */
      pBillTemplt?: string;

      /** 个人部分账单模板id */
      pBillTempltId?: string;

      /** 个人计算方式:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
      pCalculationMethod?: string;

      /** 个人收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      pFeeMonth?: string;

      /** 收费模板名 */
      pFeeTemplt?: string;

      /** 个人收费模板id */
      pFeeTempltId?: string;

      /** 个人频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      pFrequency?: string;

      /** 个人最高比例 */
      pMaxRatio?: number;

      /** 个人最低比例 */
      pMinRatio?: number;

      /** 个人提前几个月收,提前几个月收,默认为0，选项0-3 */
      pMonthInAdvance?: string;

      /** 个人精度0：0位小数1：1位小数2：2位小数5： 精确值 */
      pPrecision?: string;

      /** 个人比例 */
      pRatio?: number;

      /** 个人比例步长 */
      pRatioStep?: number;

      /** 支付频率1:月缴,2季度缴,3年缴(不足一年按年缴),4年缴(不足一年按月缴) */
      payFrequency?: string;

      /** 支付最后服务年月 */
      payLastServiceMonth?: string;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 产品id */
      productId?: string;

      /** 产品名称 */
      productName?: string;

      /** 产品比例id */
      productRatioId?: string;

      /** 产品比例名称 */
      productRatioName?: string;

      /** 产品类型id */
      productTypeId?: number;

      /** 供应商id */
      providerId?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 报价单id */
      quotationId?: string;

      /** 报价单子项id */
      quotationItemId?: string;

      /** 应收金额 */
      receivableAmt?: number;

      /** 应收几个月 */
      receivableMonth?: string;

      /** 备注 */
      remark?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 社保组id */
      ssGroupId?: string;

      /** 社保组名称 */
      ssGroupName?: string;

      /** 社保福利包id */
      ssWelfarePkgId?: string;

      /** 社保福利包名称 */
      ssWelfarePkgName?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 标签 */
      tag?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;

      /** 增值税 */
      vat?: string;

      /** 增值税率 */
      vatr?: string;

      /** 实收金额 */
      verifyAmt?: number;

      /** 实收金额(不含税) */
      verifyAmtNoTax?: string;

      /** 实收金额增值税 */
      verifyAmtVat?: string;
    }

    export class EmployeeHireSep {
      /** 实际工作地 */
      actualWorkLoc?: string;

      /** add */
      add?: boolean;

      /** 增员确认人 */
      addConfirmBy?: string;

      /** 增员确认时间 */
      addConfirmDate?: string;

      /** 增员过程 */
      addConfirmPro?: string;

      /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
      addConfirmStatus?: string;

      /** 增员状态名称 */
      addConfirmStatusName?: string;

      /** 增员接单确认时间 */
      addPerfectBy?: string;

      /** 增员接单确认人 */
      addPerfectDate?: string;

      /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
      addReason?: string;

      /** 增员备注 */
      addRemark?: string;

      /** 年龄 */
      age?: string;

      /** 变更确认人 */
      alterConfirmBy?: string;

      /** 变更确认时间 */
      alterConfirmDate?: string;

      /** 变更确认过程 */
      alterConfirmPro?: string;

      /** 变更接单确认人 */
      alterPerfectBy?: string;

      /** 变更接单确时间 */
      alterPerfectDate?: string;

      /** 变更备注 */
      alterRemark?: string;

      /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
      alterStatus?: string;

      /** 变更状态名称 */
      alterStatusName?: string;

      /** 大区类型 */
      areaType?: string;

      /** 大区类型名称 */
      areaTypeName?: string;

      /** 接单客服name */
      assigneeCs?: string;

      /** 接单客服 */
      assigneeCsId?: string;

      /** 接单方 */
      assigneeProvider?: string;

      /** 接单方 */
      assigneeProviderId?: string;

      /** 派单客服name */
      assignerCs?: string;

      /** 派单客服 */
      assignerCsId?: string;

      /** 派单方 */
      assignerProvider?: string;

      /** 派单方 */
      assignerProviderId?: string;

      /** 派单类型1 执行单2 协调单3 收集单 */
      assignmentType?: string;

      /** 关联状态 */
      associationStatus?: string;

      /** 批次号,用于生成社保服务信息 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 账单模板id */
      billTempltId?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** 类型 */
      category?: string;

      /** 离职证明电子版本 */
      certificateSpId?: string;

      /** certificateStatusName */
      certificateStatusName?: string;

      /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
      changeMethod?: string;

      /** 收费截至日期 */
      chargeEndDate?: string;

      /** 城市id */
      cityId?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 商保订单状态 */
      commInsurStatus?: string;

      /** 商保订单状态name */
      commInsurStatusName?: string;

      /** 确认备注 */
      confirmRemark?: string;

      /** 联系电话1，电话 */
      contactTel1?: string;

      /** 联系电话2，手机 */
      contactTel2?: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 合同编号 */
      contractCode?: string;

      /** 合同id */
      contractId?: string;

      /** 合同名称 */
      contractName?: string;

      /** 法人单位id */
      corporationId?: string;

      /** 法人单位名称 */
      corporationName?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户编号 */
      custCode?: string;

      /** 客户id */
      custId?: string;

      /** 客户方内部编号 */
      custInternalNum?: string;

      /** 客户姓名 */
      custName?: string;

      /** 缴费实体id */
      custPayEntityId?: number;

      /** 缴费实体 */
      custPayEntityName?: string;

      /** 客户类型 */
      custType?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** 客户规模 */
      customerSize?: string;

      /** 申报工资 */
      decSalary?: string;

      /** del */
      del?: boolean;

      /** 客户端增员ID */
      empAddId?: string;

      /** 客户端变更ID */
      empAlterId?: string;

      /** feeId数组 */
      empFeeIdArray?: string;

      /** 历史表主键 */
      empHireSepHisId?: string;

      /** 员工入离职id */
      empHireSepId?: string;

      /** 入职主记录ID */
      empHiresepMainId?: string;

      /** 员工id */
      empId?: string;

      /** 停缴id */
      empStopId?: string;

      /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
      empStopProcessState?: string;

      /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
      empType?: string;

      /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
      empTypeId?: number;

      /** 人员分类 */
      empTypeName?: string;

      /** 唯一号 */
      employeeCode?: string;

      /** 费用段列表 */
      employeeFeeList?: Array<defs.admin.EmployeeFee>;

      /** 雇员姓名 */
      employeeName?: string;

      /** 雇员状态 */
      employeeStatus?: string;

      /** endIndex */
      endIndex?: number;

      /** enhancedAgent */
      enhancedAgent?: string;

      /** enhancedAgentName */
      enhancedAgentName?: string;

      /** 外部供应商收费模板 */
      exFeeTemplt?: string;

      /** 外部供应商账单id */
      exFeeTempltId?: string;

      /** exQuotationFeeList */
      exQuotationFeeList?: Array<defs.admin.EmployeeFee>;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      feeMonth?: string;

      /** 收费模板名称 */
      feeTemplt?: string;

      /** 收费模板id */
      feeTempltId?: string;

      /** 档案柜编号 */
      fileCabCode?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** 文件夹编号 */
      folderCode?: string;

      /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      frequency?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** 是否关联订单 */
      hasAssociations?: number;

      /** 入职竞争对手 */
      hireCompetitor?: string;

      /** 入职时间 */
      hireDt?: string;

      /** 入职时间止 */
      hireEndDt?: string;

      /** 入职报价单 */
      hireQuotationId?: string;

      /** 入职备注 */
      hireRemark?: string;

      /** 入职时间起 */
      hireStartDt?: string;

      /** 证件号码 */
      idCardNum?: string;

      /** 证件类型 */
      idCardType?: string;

      /** 接单客服name */
      idCardTypeName?: string;

      /** inId */
      inId?: string;

      /** 内外部类型(1内部2外部3全部) */
      innerType?: string;

      /** 是否需要签订劳动合同 */
      isArchive?: string;

      /** 是否归档名称 */
      isArchiveName?: string;

      /** 银行卡是否上传 */
      isBankCardUpload?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 是否入职呼叫 */
      isHireCall?: string;

      /** 是否入职呼叫名称 */
      isHireCallName?: string;

      /** 身份证是否上传 */
      isIDCardUpload?: string;

      /** 是否单立户1 是0 否 */
      isIndependent?: string;

      /** 劳动合同是否上传 */
      isLaborContractUpload?: string;

      /** 是否需要签订劳动合同 */
      isNeedSign?: string;

      /** 是否退费 0否  1是 */
      isRefund?: string;

      /** 是否集中一地投保 */
      isSameInsur?: string;

      /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
      isSepCall?: string;

      /** 是否离职呼叫名 */
      isSepCallName?: string;

      /** 是否有统筹医疗 */
      isThereACoordinateHealth?: string;

      /** 是否有统筹医疗名称 */
      isThereACoordinateHealthText?: string;

      /** 是否有社保卡 */
      isThereSsCard?: string;

      /** 是否有社保卡名称 */
      isThereSsCardText?: string;

      /** 离职动态模板json */
      jsonStr?: string;

      /** 劳动关系单位 */
      laborRelationUnit?: string;

      /** 责任客服 */
      liabilityCs?: string;

      /** 材料列表 */
      materialList?: Array<defs.admin.Material>;

      /** materialSignStatus */
      materialSignStatus?: number;

      /** materialSignStatusName */
      materialSignStatusName?: string;

      /** 离职材料电子版本id */
      materialSpId?: string;

      /** materialStatusName */
      materialStatusName?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** 操作方式  单立户1、大户2 */
      modeOfOperation?: string;

      /** 提前几个月收,默认为0，选项0-3 */
      monthInAdvance?: string;

      /** 后指针 */
      nextPointer?: string;

      /** noChange */
      noChange?: boolean;

      /** 非社保列表 */
      nonSsGroupList?: Array<defs.admin.EmployeeFee>;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 挂起原因 */
      pendingReason?: string;

      /** 挂起原因中文 */
      pendingReasonName?: string;

      /** 人员分类id */
      personCategoryId?: string;

      /** 职位id */
      positionId?: string;

      /** 前指针 */
      prevPointer?: string;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 流程实例化id */
      processInsId?: string;

      /** 供应商编码 */
      providerCode?: string;

      /** 供应商客服 */
      providerCs?: string;

      /** 供应商客服id */
      providerCsId?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 供应商类型1内部2外部 */
      providerType?: string;

      /** 代理人 */
      proxyBy?: string;

      /** 供应商集团id */
      prvdGroupId?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 供应商集团 */
      prvdGroupName?: string;

      /** 离职材料签订形式 */
      quitSignType?: number;

      /** quitSignTypeName */
      quitSignTypeName?: string;

      /** 电子离职合同任务主键 */
      quitTaskId?: number;

      /** 报价单编码 */
      quotationCode?: string;

      /** 报价单名称 */
      quotationName?: string;

      /** 减少详细原因 */
      reduceDetailReason?: string;

      /** 减原详细原因名称 */
      reduceDetailReasonName?: string;

      /** 客户端减员ID */
      reduceId?: string;

      /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
      reduceReason?: string;

      /** 减员原因名称 */
      reduceReasonName?: string;

      /** 参考日期，页面传入 */
      referDate?: string;

      /** 备注 */
      remark?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 报入职人员id */
      rptHireBy?: string;

      /** 报入职人 */
      rptHireByName?: string;

      /** 报入职时间 */
      rptHireDt?: string;

      /** 报入职日期止 */
      rptHireEndDt?: string;

      /** 报入职日期起 */
      rptHireStartDt?: string;

      /** 报离职人员id */
      rptSepBy?: string;

      /** 报离职人 */
      rptSepByName?: string;

      /** 报离职日期 */
      rptSepDt?: string;

      /** 报离职日期止 */
      rptSepEndDt?: string;

      /** 报离职日期起 */
      rptSepStartDt?: string;

      /** 用章对象 */
      sealObject?: string;

      /** 用章类型 */
      sealType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 离职确认人 */
      sepConfirmBy?: string;

      /** 离职确认日期 */
      sepConfirmDate?: string;

      /** 离职确认历史 */
      sepConfirmHis?: string;

      /** 离职确认进程 */
      sepConfirmPro?: string;

      /** 离职确认状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
      sepConfirmStatus?: string;

      /** 离职状态名称 */
      sepConfirmStatusName?: string;

      /** 离职详细原因 */
      sepDetailReason?: string;

      /** 离职详细原因名称 */
      sepDetailReasonName?: string;

      /** 离职日期 */
      sepDt?: string;

      /** 离职时间止 */
      sepEndDt?: string;

      /** 离职接单确认人 */
      sepPerfectBy?: string;

      /** 离职接单确认时间 */
      sepPerfectDate?: string;

      /** 离职手续办理状态:0  未完成   1  完成 */
      sepProcessStatus?: string;

      /** 离职报价单 */
      sepQuotationId?: string;

      /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
      sepReason?: string;

      /** 离职原因名称 */
      sepReasonName?: string;

      /** 离职备注 */
      sepRemark?: string;

      /** 离职时间止 */
      sepStartDt?: string;

      /** 离职导出类型:1离职接单确认,2离职派单确认 */
      sepType?: string;

      /** 签约方分公司抬头 */
      signBranchTitle?: string;

      /** 签约方分公司抬头id */
      signBranchTitleId?: string;

      /** 签约方分公司抬头name */
      signBranchTitleName?: string;

      /** 签单供应商 */
      signProvider?: string;

      /** 签单方 */
      signProviderId?: string;

      /** signStatus */
      signStatus?: number;

      /** 短信发送日期 */
      smsSendDt?: string;

      /** 短信发送状态: 0未发送, 1成功, 2失败 */
      smsSendStatus?: string;

      /** 短信发送状态中文: 未发送, 成功, 失败 */
      smsSendStatusStr?: string;

      /** 分拆方分公司:分拆方客服 */
      splitServiceProviderCs?: string;

      /** 社保列表 */
      ssGroupList?: Array<defs.admin.EmployeeFee>;

      /** 员工社保参与地 */
      ssParticipateLocation?: string;

      /** startIndex */
      startIndex?: number;

      /** 状态 1入职未生效2在职3离职 */
      status?: string;

      /** 状态名称 */
      statusName?: string;

      /** 小类名称 */
      subTypeName?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 小合同编号 */
      subcontractCode?: string;

      /** 小合同id */
      subcontractId?: string;

      /** 小合同名称 */
      subcontractName?: string;

      /** 大类名称 */
      superTypeName?: string;

      /** 总收费日期 */
      totalFeeDt?: string;

      /** eos转移id */
      transferId?: number;

      /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
      type?: number;

      /** 机动分类项目1 */
      type1?: string;

      /** 机动分类项目2 */
      type2?: string;

      /** 机动分类项目3 */
      type3?: string;

      /** 机动分类项目4 */
      type4?: string;

      /** 机动分类项目5 */
      type5?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;

      /** uuid */
      uuid?: string;
    }

    export class EmployeeQuery {
      /** 业务层级 */
      bizCategory?: string;

      /** 雇员姓名 */
      employeeName?: string;

      /** endIndex */
      endIndex?: number;

      /** 所属分公司id */
      governingBranch?: string;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数 */
      pageSize?: number;

      /** 角色编码 */
      roleCode?: string;

      /** 角色层级 */
      roleGrade?: string;

      /** startIndex */
      startIndex?: number;
    }

    export class EmployeeVO {
      /** 部门id */
      departmentId?: string;

      /** 部门角色关系id */
      deptUserId?: string;

      /** 内部雇员亲属列表新增 */
      empRelativeAddList?: Array<defs.admin.EmpRelative>;

      /** 内部雇员亲属列表更新 */
      empRelativeUptList?: Array<defs.admin.EmpRelative>;

      /** 内部雇员 */
      employee?: defs.admin.Employee;

      /** 菜单列表 */
      functionList?: Array<string>;

      /** 原部门角色关系id */
      origDeptUserId?: string;

      /** 角色id */
      userId?: string;

      /** 人管人列表 */
      userManageList?: Array<defs.admin.UserManage>;
    }

    export class FilterEntity {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** 城市id */
      cityId?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 部门级别：1:集团 2:大区 3 :分公司 4:部门 */
      departmentGrade?: string;

      /** 部门id */
      departmentId?: string;

      /** 部门名称 */
      departmentName?: string;

      /** endIndex */
      endIndex?: number;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 功能编码 */
      functionCode?: string;

      /** 功能名称 */
      functionName?: string;

      /** 所属大区id */
      governingAreaId?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 供应商类型，1内部2外部 */
      providerType?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 特殊类型 */
      specialType?: string;

      /** startIndex */
      startIndex?: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class FunctionDept {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** 主键 */
      checkId?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 部门id */
      departmentId?: string;

      /** 部门名称 */
      departmentName?: string;

      /** endIndex */
      endIndex?: number;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 功能编码 */
      functionCode?: string;

      /** 功能id */
      functionId?: string;

      /** 功能名称 */
      functionName?: string;

      /** 所在分公司 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 备注 */
      remark?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 特例 */
      specialType?: string;

      /** 特例名称 */
      specialTypeName?: string;

      /** startIndex */
      startIndex?: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class LoginDTO {
      /** 菜单列表 */
      functionList?: Array<defs.admin.BaseEntity>;

      /** 是否需要更新密码 */
      isNeedUpdatePw?: boolean;

      /** 鉴权接口返回沐容url根地址 */
      murongServer?: string;

      /** needUpdatePw */
      needUpdatePw?: boolean;

      /** 推送服务 */
      pushServerIP?: string;

      /** 契约锁服务器 */
      qysServer?: string;

      /** 报表目录 */
      reportDir?: string;

      /** 报表目录sparkSql */
      reportDirBySparkSql?: string;

      /** 报表map */
      reportV4DirMap?: object;

      /** 角色列表 */
      roleList?: Array<defs.admin.RoleDTO>;

      /** token,给MR用 */
      token?: string;

      /** 用户对象 */
      user?: defs.admin.UserDTO;

      /** 用户id */
      userId?: string;
    }

    export class Map<T0 = any, T1 = any> {}

    export class Material {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      materialId?: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_NAME           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      materialName?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.REMARK           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      remark?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class RoleDTO {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 角色业务类别: 10  全部20  客服30  新产品销售31  大客户销售32  渠道部销售33  电销部销售34  BPO事业部销售35  福利事业部销售40  销售50  财务60  法务70  人事80  其他 */
      bizCategory?: string;

      /** 角色业务类别Name */
      bizCategoryName?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** endIndex */
      endIndex?: number;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 角色编号 */
      roleCode?: string;

      /** 角色描述 */
      roleDesc?: string;

      /** 角色级别:1:集团,2:大区,3:分公司,4:部门,5:自身 */
      roleGrade?: string;

      /** 角色级别Name */
      roleGradeName?: string;

      /** 角色id */
      roleId?: string;

      /** 角色名称 */
      roleName?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** startIndex */
      startIndex?: number;

      /** 状态 */
      status?: string;

      /** 状态Name */
      statusName?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class RoleUser {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 部门id */
      deptId?: string;

      /** 部门名称 */
      deptName?: string;

      /** 员工编号 */
      employeeCode?: string;

      /** endIndex */
      endIndex?: number;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 所属大区 */
      governingArea?: string;

      /** 所属分公司 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 姓名 */
      realName?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 角色id */
      roleId?: string;

      /** 角色名称 */
      roleName?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** startIndex */
      startIndex?: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;

      /** 用户角色id */
      userRoleId?: string;
    }

    export class RoleVO {
      /** 菜单id列表 */
      menuList: Array<string>;

      /** 角色id */
      roleId: string;

      /** 角色用户列表 */
      roleUserList: Array<defs.admin.RoleUser>;

      /** 用户id */
      userId: string;
    }

    export class UserDTO {
      /** add */
      add?: boolean;

      /** 地址 */
      address?: string;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** 手机 */
      cellphone?: string;

      /** 所属分公司城市 */
      cityId?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 联系电话 */
      contactTel1?: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** 默认部门 */
      defaultDept?: string;

      /** del */
      del?: boolean;

      /** 部门层级 */
      departmentGrade?: string;

      /** 部门id */
      departmentId?: string;

      /** 部门名称 */
      departmentName?: string;

      /** 部门列表 */
      deptList?: Array<object>;

      /** 用户部门关系id */
      deptUserId?: string;

      /** 电子邮件 */
      email?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.EMPLOYEE_ID	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
      employeeId?: string;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 功能按钮列表 */
      funcBtnList?: Array<object>;

      /** 功能列表 */
      functionList?: Array<object>;

      /** 所属大区 */
      governingArea?: string;

      /** governingAreaName */
      governingAreaName?: string;

      /** 所属分公司 */
      governingBranch?: string;

      /** 所属分公司名字 */
      governingBranchName?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 是否需要更新密码 */
      isNeedUpdatePw?: boolean;

      /** 模拟人 */
      mimicBy?: string;

      /** needUpdatePw */
      needUpdatePw?: boolean;

      /** noChange */
      noChange?: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.PASSWORD	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
      password?: string;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.REAL_NAME	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
      realName?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 角色列表 */
      rolesList?: Array<object>;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.STATUS	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
      status?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.USER_ID	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
      userId?: string;

      /** 下属用户列表 */
      userList?: Array<object>;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.USER_NAME	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
      userName?: string;
    }

    export class UserDept {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 部门id */
      departmentId?: string;

      /** 姓名 */
      departmentName?: string;

      /** 部门用户id */
      deptUserId?: string;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 是否默认部门 */
      isDefault?: string;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 选中标记 */
      selected?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;

      /** 用户名 */
      userName?: string;
    }

    export class UserManage {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 财务大类 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 被管人id */
      subordinateId?: string;

      /** 类型 */
      type?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 人管人id */
      userManageId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }

    export class roleQuery {
      /** add */
      add?: boolean;

      /** 批次号,用于备份 */
      batchId?: string;

      /** 账单表别名,控制客户权限用 */
      billAlias?: string;

      /** 业务类别 */
      bizCategory?: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType?: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias?: string;

      /** clientOperation */
      clientOperation?: number;

      /** flex是否行编号 */
      clientRowSeq?: number;

      /** flex是否选择 */
      clientSelected?: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias?: string;

      /** 创建人 */
      createBy?: string;

      /** createBy2 */
      createBy2?: string;

      /** createByEx */
      createByEx?: string;

      /** 创建日期 */
      createDt?: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias?: string;

      /** del */
      del?: boolean;

      /** endIndex */
      endIndex?: number;

      /** 导入类型,扩充使用 */
      expType?: string;

      /** filterByAuthNum */
      filterByAuthNum?: string;

      /** 提供查询是做为排除条件使用 */
      filterId?: string;

      /** funBtnActiveStr */
      funBtnActiveStr?: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch?: string;

      /** inId */
      inId?: string;

      /** 是否账单查询 */
      isBillQuery?: string;

      /** flex是否变化 */
      isChanged?: boolean;

      /** 删除标记 */
      isDeleted?: string;

      /** 模拟人 */
      mimicBy?: string;

      /** noChange */
      noChange?: boolean;

      /** 页数 */
      pageNum?: number;

      /** 每页记录数,默认65536条 */
      pageSize?: number;

      /** 流程审批角色名字 */
      processAprRoleName?: string;

      /** 供应商集团权限添加 */
      providerIdAlias?: string;

      /** 代理人 */
      proxyBy?: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias?: string;

      /** 姓名 */
      realName?: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure?: string;

      /** 卡权限 */
      restrictType?: string;

      /** 角色编码 */
      roleCode?: string;

      /** 角色层级 */
      roleGrade?: string;

      /** 角色id */
      roleId?: string;

      /** 角色名称 */
      roleName?: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth?: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr?: string;

      /** startIndex */
      startIndex?: number;

      /** 状态 */
      status?: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias?: string;

      /** 修改人 */
      updateBy?: string;

      /** 修改日期 */
      updateDt?: string;

      /** upt */
      upt?: boolean;

      /** 用户id,控制小合同权限用 */
      userId?: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias?: string;
    }
  }
}

declare namespace API {
  export namespace admin {
    /**
     * 账号管理
     */
    export namespace account {
      /**
        * 根据条件得到管理账号的分页查询结果
根据条件得到管理账号的分页查询结果
        * /account/queryAccountList
        */
      export namespace queryAccountList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.AccountManageDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.AccountQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.AccountQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询对应输入的旧密码是否正确
查询对应输入的旧密码是否正确
        * /account/queryCorrectRecord
        */
      export namespace queryCorrectRecord {
        export class Params {
          /** 新密码 */
          password: string;
          /** 用户id */
          userId: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新账号状态
更新账号状态
        * /account/updateAccountStatus
        */
      export namespace updateAccountStatus {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.admin.AccountManageDTO>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.admin.AccountManageDTO>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新单个账号
更新单个账号
        * /account/updateOneAccountStatus
        */
      export namespace updateOneAccountStatus {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.AccountManageDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.AccountManageDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改密码(账号管理界面)
修改密码(账号管理界面)
        * /account/updatePasswordFromAccountManage
        */
      export namespace updatePasswordFromAccountManage {
        export class Params {
          /** 新密码 */
          password: string;
          /** 用户id */
          userId: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 分配客服
     */
    export namespace allocateCustomer {
      /**
        * 查询客服权限转给哪个客服
查询客服权限转给哪个客服
        * /allocateCustomer/queryCbmAllList
        */
      export namespace queryCbmAllList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.EmployeeHireSep;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询客服权限列表
查询客服权限列表
        * /allocateCustomer/queryCbmList
        */
      export namespace queryCbmList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.EmployeeHireSep;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分配客服权限
分配客服权限
        * /allocateCustomer/updateCbm
        */
      export namespace updateCbm {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.admin.AuthorityAllocationVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.AuthorityAllocationVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 部门信息
     */
    export namespace department {
      /**
        * 区域下拉数据
区域下拉数据
        * /department/areaDropDownList
        */
      export namespace areaDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除部门
删除部门,1成功，0失败，有子部门
        * /department/deleteDepart
        */
      export namespace deleteDepart {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.DeptDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.DeptDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除用户特殊功能管理关系
删除用户特殊功能管理关系
        * /department/deleteFunctionDept
        */
      export namespace deleteFunctionDept {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得部门以及员工需要的所有下拉菜单
获得部门以及员工需要的所有下拉菜单
        * /department/getAllDropDownList
        */
      export namespace getAllDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 部门下拉列表
部门下拉列表
        * /department/getDepartmentDropdownList
        */
      export namespace getDepartmentDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取内部编码个数
获取内部编码个数
        * /department/getDeptInternalCodeCount
        */
      export namespace getDeptInternalCodeCount {
        export class Params {
          /** departmentGrades */
          departmentGrades: string;
          /** parentDeptId */
          parentDeptId: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 金蝶部门下拉列表
金蝶部门下拉列表
        * /department/getKingdeeOrgDropDownList
        */
      export namespace getKingdeeOrgDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取特殊化类型字符串
获取特殊化类型字符串
        * /department/getSpecialType
        */
      export namespace getSpecialType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.FunctionDept,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.FunctionDept,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 特例类型下拉框
特例类型下拉框
        * /department/getSpecialTypeDropDownList
        */
      export namespace getSpecialTypeDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取部门以及子部门列表
获取部门以及子部门列表
        * /department/getSubDepartment
        */
      export namespace getSubDepartment {
        export class Params {
          /** departmentGrades */
          departmentGrades: string;
          /** parentDeptId */
          parentDeptId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.DeptDTO>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取内部编码个数
获取内部编码个数
        * /department/getSubDepartmentList
        */
      export namespace getSubDepartmentList {
        export class Params {
          /** departmentGrades */
          departmentGrades: string;
          /** parentDeptId */
          parentDeptId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.DeptDTO>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取父部门
获取父部门
        * /department/getSuperDepartId
        */
      export namespace getSuperDepartId {
        export class Params {
          /** departId */
          departId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<defs.admin.ObjectMap<string, string>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得子部门以及下面的人数
获得子部门以及下面的人数
        * /department/getUserAndSubDeptCount
        */
      export namespace getUserAndSubDeptCount {
        export class Params {
          /** departId */
          departId: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 初始化用户以及部门树
初始化用户以及部门树
        * /department/getUserDepartment
        */
      export namespace getUserDepartment {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<
            defs.admin.ObjectMap<string, Array<ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取用户部门信息
获取用户部门信息
        * /department/getUserDeptInfo
        */
      export namespace getUserDeptInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.DeptDTO>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.DeptDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.DeptDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入同级部门
插入同级部门
        * /department/insertDeriveDept
        */
      export namespace insertDeriveDept {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.DeptDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.DeptDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入用户特殊功能管理关系
插入用户特殊功能管理关系
        * /department/insertFunctionDept
        */
      export namespace insertFunctionDept {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.FunctionDept,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.FunctionDept,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询分公司
查询分公司
        * /department/queryBranchList
        */
      export namespace queryBranchList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 初始化用户以及部门树
初始化用户以及部门树
        * /department/queryDepartmentById
        */
      export namespace queryDepartmentById {
        export class Params {
          /** departmentId */
          departmentId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DeptDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询用户以及所在部门id
查询用户以及所在部门id
        * /department/queryEmployeeForDept
        */
      export namespace queryEmployeeForDept {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.UserDept>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询角色列表
查询角色列表
        * /department/queryFunctionDeptList
        */
      export namespace queryFunctionDeptList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DeptDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新部门信息
更新部门信息
        * /department/updateByDeptIdSelective
        */
      export namespace updateByDeptIdSelective {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.DeptDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.DeptDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新用户特殊功能管理关系
更新用户特殊功能管理关系
        * /department/updateFunctionDept
        */
      export namespace updateFunctionDept {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.FunctionDept,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.FunctionDept,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 内部雇员信息
     */
    export namespace employee {
      /**
        * 删除员工国际化信息
删除员工国际化信息
        * /employee/delEmpInfoInter
        */
      export namespace delEmpInfoInter {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.EmpInfoInter;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除人员与部门的关系
删除人员与部门的关系
        * /employee/deleteEmpDeptRelation
        */
      export namespace deleteEmpDeptRelation {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除亲属信息
更新员工信息
        * /employee/deleteEmpRelative
        */
      export namespace deleteEmpRelative {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除员工
删除员工,部门departmentId,用户userId,部门用户deptUserId
        * /employee/deleteEmployee
        */
      export namespace deleteEmployee {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.Employee;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 判断合同是否转移完毕
判断合同是否转移完毕
        * /employee/getContractFinishedCount
        */
      export namespace getContractFinishedCount {
        export class Params {
          /** departmentId */
          departmentId: string;
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取有复选框的用户和部门菜单列表,显示用
获取有复选框的用户和部门菜单列表,部门列表deptList，用户列表userList
        * /employee/getDepartmentWithCheckBox
        */
      export namespace getDepartmentWithCheckBox {
        export class Params {
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.DeptDTO>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取员工在部门下的个数
获取员工在部门下的个数
        * /employee/getEmpDeptCount
        */
      export namespace getEmpDeptCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查看员工部门关系个数
查看员工部门关系个数
        * /employee/getEmpDeptCount
        */
      export namespace postGetEmpDeptCount {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.admin.UserDept,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.UserDept,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取员工国际化信息
获取员工国际化信息
        * /employee/getEmpInfoInter
        */
      export namespace getEmpInfoInter {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.EmpInfoInter;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取员工内部编码个数
获取员工内部编码个数
        * /employee/getEmployeeCodeCount
        */
      export namespace getEmployeeCodeCount {
        export class Params {
          /** empCode */
          empCode: string;
          /** empId */
          empId?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询用户下拉框
查询用户下拉框
        * /employee/getEmployeeDropdownList
        */
      export namespace getEmployeeDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.EmployeeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取员工身份证号码个数
获取员工身份证号码个数
        * /employee/getIdCardNumCount
        */
      export namespace getIdCardNumCount {
        export class Params {
          /** empId */
          empId?: string;
          /** idCardNum */
          idCardNum: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取其他部门下面用户列表
获取其他部门下面用户列表
        * /employee/getOtherUserList
        */
      export namespace getOtherUserList {
        export class Params {
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取负责人
获取负责人
        * /employee/getPersonInChargeDropdownList
        */
      export namespace getPersonInChargeDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取登录名的个数
获取登录名的个数
        * /employee/getUserCount
        */
      export namespace getUserCount {
        export class Params {
          /** userId */
          userId?: string;
          /** userName */
          userName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取有复选框的用户和部门菜单列表,显示用
获取有复选框的用户和部门菜单列表,部门列表deptList，用户列表userList
        * /employee/getUserDepartmentWithCheckBox
        */
      export namespace getUserDepartmentWithCheckBox {
        export class Params {
          /** userId */
          userId: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据用户名返回用户信息列表
根据用户名返回用户信息列表
        * /employee/getUserList
        */
      export namespace getUserList {
        export class Params {
          /** empName */
          empName?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.UserDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入部门用户关系
插入部门用户关系,部门departmentId，用户userId,是否默认isDefault
        * /employee/insertEmpDeptRelation
        */
      export namespace insertEmpDeptRelation {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.admin.UserDept,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.UserDept,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入亲属信息
插入亲属信息
        * /employee/insertEmpRelative
        */
      export namespace insertEmpRelative {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.EmpRelative,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmpRelative,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入员工信息
插入员工信息,返回userId
        * /employee/insertEmployee
        */
      export namespace insertEmployee {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.Employee,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.Employee,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增雇员和雇员亲属
新增雇员和雇员亲属,部门departmentId,用户employee，用户亲属列表empRelativeList
        * /employee/insertEmployeeAndRelatives
        */
      export namespace insertEmployeeAndRelatives {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取部门以及子部门列表
获取部门以及子部门列表
        * /employee/queryEmpRelative
        */
      export namespace queryEmpRelative {
        export class Params {
          /** empId */
          empId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.EmpRelative;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据员工id获得员工信息
根据员工id获得员工信息
        * /employee/queryEmployee
        */
      export namespace queryEmployee {
        export class Params {
          /** empId */
          empId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.Employee;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取用户和亲属
获取用户和亲属,用户employee，用户亲属列表empRelatives
        * /employee/queryEmployeeAndRelatives
        */
      export namespace queryEmployeeAndRelatives {
        export class Params {
          /** empId */
          empId: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除人员与部门的关系
删除人员与部门的关系
        * /employee/queryFunctionListByUserId
        */
      export namespace queryFunctionListByUserId {
        export class Params {
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存员工国际化信息
保存员工国际化信息
        * /employee/saveEmpInfoInter
        */
      export namespace saveEmpInfoInter {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.EmpInfoInter;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新亲属信息
更新亲属信息
        * /employee/updateEmpRelative
        */
      export namespace updateEmpRelative {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.EmpRelative,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmpRelative,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新员工信息
更新员工信息
        * /employee/updateEmployee
        */
      export namespace updateEmployee {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.Employee,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.Employee,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增部门和雇员关系,并且修改雇员信息
新增部门和雇员关系,并且修改雇员信息，部门departmentId,用户employee，用户亲属列表empRelativeList
        * /employee/updateEmployeeAndDept
        */
      export namespace updateEmployeeAndDept {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取用户和亲属
修改雇员和雇员亲属,用户employee，新增用户亲属列表empRelativeAddList，更新用户亲属列表empRelativeUptList
        * /employee/updateEmployeeAndRelatives
        */
      export namespace updateEmployeeAndRelatives {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 换这个人的其他部门做默认部门
换这个人的其他部门做默认部门,部门departmentId，用户userId，原部门人员origDeptUserId，部门人员deptUserId
        * /employee/updateToOtherUser
        */
      export namespace updateToOtherUser {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新用户特殊权限
更新用户特殊权限
        * /employee/updateUserFunction
        */
      export namespace updateUserFunction {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新用户与用户关联表
更新用户与用户关联表，传userId和userManageList
        * /employee/updateUserManage
        */
      export namespace updateUserManage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新用户与部门关联表
更新用户与部门关联表，传userId和userManageList
        * /employee/updateUserManageDept
        */
      export namespace updateUserManageDept {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.EmployeeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 登录
     */
    export namespace login {
      /**
        * 模拟登录
模拟登录
        * /adminLogin
        */
      export namespace adminLogin {
        export class Params {
          /** 模拟人用户名 */
          adminName: string;
          /** 被模拟人用户名 */
          mimicName: string;
          /** 密码 */
          password: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 模拟登录
模拟登录
        * /adminLogin
        */
      export namespace postAdminLogin {
        export class Params {
          /** 模拟人用户名 */
          adminName: string;
          /** 被模拟人用户名 */
          mimicName: string;
          /** 密码 */
          password: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * fetchMemberStatistics
       * /fetchMemberStatistics
       */
      export namespace fetchMemberStatistics {
        export class Params {
          /** endDate */
          endDate: string;
          /** startDate */
          startDate: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 忘记密码
忘记密码
        * /forgotPassword
        */
      export namespace forgotPassword {
        export class Params {
          /** 中文名 */
          realName: string;
          /** 用户名 */
          userName: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 忘记密码
忘记密码
        * /forgotPassword
        */
      export namespace postForgotPassword {
        export class Params {
          /** 中文名 */
          realName: string;
          /** 用户名 */
          userName: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取验证码
获取验证码
        * /generateVCode
        */
      export namespace generateVCode {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * getUser
       * /getUser
       */
      export namespace getUser {
        export class Params {
          /** userId */
          userId?: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * isLogin
       * /isLogin
       */
      export namespace isLogin {
        export class Params {
          /** userId */
          userId?: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * isLogin
       * /isLogin
       */
      export namespace postIsLogin {
        export class Params {
          /** userId */
          userId?: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 登录
登录
        * /login
        */
      export namespace login {
        export class Params {
          /** captcha */
          captcha: string;
          /** 密码 */
          password: string;
          /** 用户名 */
          username: string;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<defs.admin.LoginDTO>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 登出
登出
        * /logout
        */
      export namespace logout {
        export class Params {}

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改密码
修改密码
        * /modifyPassword
        */
      export namespace modifyPassword {
        export class Params {
          /** 密码 */
          password: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 验证token
验证token
        * /verifyToken
        */
      export namespace verifyToken {
        export class Params {
          /** app */
          app: string;
          /** 权限id */
          funcId: string;
          /** sign */
          sign: string;
          /** token */
          token: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 验证token
验证token
        * /verifyToken
        */
      export namespace postVerifyToken {
        export class Params {
          /** app */
          app: string;
          /** 权限id */
          funcId: string;
          /** sign */
          sign: string;
          /** token */
          token: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 菜单信息
     */
    export namespace menu {
      /**
        * 删除菜单
删除菜单
        * /menu/deleteSysFunction
        */
      export namespace deleteSysFunction {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 返回菜单树
返回菜单树,id-1，类型0,2
        * /menu/getFunctionDTOTreeList
        */
      export namespace getFunctionDTOTreeList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 返回菜单树
返回菜单树
        * /menu/getFunctionTreeList
        */
      export namespace getFunctionTreeList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据用户返回菜单
根据用户返回菜单
        * /menu/getFunctionsByUserId
        */
      export namespace getFunctionsByUserId {
        export class Params {
          /** userId */
          userId?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据用户返回菜单
根据用户返回菜单
        * /menu/getFunctionsByUserId
        */
      export namespace postGetFunctionsByUserId {
        export class Params {
          /** userId */
          userId?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据菜单id获取特例类型
根据菜单id获取特例类型
        * /menu/getSpecialTypeByFunction
        */
      export namespace getSpecialTypeByFunction {
        export class Params {
          /** functionId */
          functionId: string;
        }

        export type Response<T> = defs.admin.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入菜单
插入菜单
        * /menu/insertSysFunction
        */
      export namespace insertSysFunction {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 判断这个菜单可以被删除
判断这个菜单可以被删除
        * /menu/isFunctionRecordByUserRole
        */
      export namespace isFunctionRecordByUserRole {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 是否重复菜单
是否重复菜单
        * /menu/isRepeatFunctionRecord
        */
      export namespace isRepeatFunctionRecord {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 是否重复菜单
是否重复菜单
        * /menu/isRepeatFunctionRecord
        */
      export namespace postIsRepeatFunctionRecord {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取角色下的菜单树
获取角色下的菜单树,原本在角色下，因为增加字段改用menuDTO，放在menu下了
        * /menu/queryFunctionListByRoleId
        */
      export namespace queryFunctionListByRoleId {
        export class Params {
          /** roleId */
          roleId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新菜单
更新菜单
        * /menu/updateSysFunction
        */
      export namespace updateSysFunction {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 角色信息
     */
    export namespace role {
      /**
        * 删除菜单
删除菜单
        * /role/deleteRole
        */
      export namespace deleteRole {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除角色下的用户
删除角色下的用户
        * /role/deleteRoleUser
        */
      export namespace deleteRoleUser {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.RoleUser,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.RoleUser,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除角色下的用户list方式
删除角色下的用户list方式
        * /role/deleteRoleUserList
        */
      export namespace deleteRoleUserList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.admin.RoleUser>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.admin.RoleUser>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取角色业务类型下拉框
获取角色业务类型下拉框
        * /role/getBizCategoryDropDown
        */
      export namespace getBizCategoryDropDown {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得角色名个数
获得角色名个数,roleId,roleName,roleCode
        * /role/getRoleCount
        */
      export namespace getRoleCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.roleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.roleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取角色下面是否有用户
获取角色下面是否有用户
        * /role/getRoleExistsUserCount
        */
      export namespace getRoleExistsUserCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取角色层级下拉框
获取角色层级下拉框
        * /role/getRoleGradeDropDown
        */
      export namespace getRoleGradeDropDown {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取角色状态下拉框
获取角色状态下拉框
        * /role/getRoleStatusDropDown
        */
      export namespace getRoleStatusDropDown {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取角色的用户个数
获取角色的用户个数,roleId,userId
        * /role/getRoleUserCount
        */
      export namespace getRoleUserCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.RoleUser,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.RoleUser,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据用户返回角色
根据用户返回角色
        * /role/getRolesByUserId
        */
      export namespace getRolesByUserId {
        export class Params {
          /** userName */
          userName?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.RoleDTO>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据用户返回角色
根据用户返回角色
        * /role/getRolesByUserId
        */
      export namespace postGetRolesByUserId {
        export class Params {
          /** userName */
          userName?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<Array<defs.admin.RoleDTO>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新这个人的权限
更新这个人的权限
        * /role/getSubUserAuthority
        */
      export namespace getSubUserAuthority {
        export class Params {
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入角色
插入角色
        * /role/insertRole
        */
      export namespace insertRole {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.RoleDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.RoleDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入用户的角色
插入用户的角色
        * /role/insertRoleUser
        */
      export namespace insertRoleUser {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.admin.RoleUser>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.admin.RoleUser>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新组织架构用户的角色
更新组织架构用户的角色
        * /role/insertUserRole
        */
      export namespace insertUserRole {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.RoleVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.RoleVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询角色下拉列表
查询角色下拉列表，传入roleName
        * /role/queryRoleDropdownList
        */
      export namespace queryRoleDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.RoleDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.roleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.roleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询角色列表
查询角色列表
        * /role/queryRoleList
        */
      export namespace queryRoleList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.RoleDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.roleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.roleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 返回用户的角色列表
返回用户的角色列表
        * /role/queryUserRoleList
        */
      export namespace queryUserRoleList {
        export class Params {
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询角色下的用户
查询角色下的用户
        * /role/queryUsersByRole
        */
      export namespace queryUsersByRole {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.RoleUser,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.RoleUser,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询在这个角色下不存在的员工列表
查询在这个角色下不存在的员工列表
        * /role/queryUsersNotInRole
        */
      export namespace queryUsersNotInRole {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.roleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.roleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新角色
更新角色
        * /role/updateRole
        */
      export namespace updateRole {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.RoleDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.RoleDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新角色菜单表
更新角色菜单表
        * /role/updateRoleFunction
        */
      export namespace updateRoleFunction {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.admin.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.admin.RoleVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.admin.RoleVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
