import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/department/queryFunctionDeptList
     * @desc 查询角色列表
查询角色列表
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.DeptDTO();
export const url = '/rhro-service-1.0/department/queryFunctionDeptList:POST';
export const initialUrl = '/rhro-service-1.0/department/queryFunctionDeptList';
export const cacheKey = '_department_queryFunctionDeptList_POST';
export async function request(
  data: defs.admin.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/department/queryFunctionDeptList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/department/queryFunctionDeptList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
