/**
 * @description 部门信息
 */
import * as areaDropDownList from './areaDropDownList';
import * as deleteDepart from './deleteDepart';
import * as deleteFunctionDept from './deleteFunctionDept';
import * as getAllDropDownList from './getAllDropDownList';
import * as getDepartmentDropdownList from './getDepartmentDropdownList';
import * as getDeptInternalCodeCount from './getDeptInternalCodeCount';
import * as getKingdeeOrgDropDownList from './getKingdeeOrgDropDownList';
import * as getSpecialType from './getSpecialType';
import * as getSpecialTypeDropDownList from './getSpecialTypeDropDownList';
import * as getSubDepartment from './getSubDepartment';
import * as getSubDepartmentList from './getSubDepartmentList';
import * as getSuperDepartId from './getSuperDepartId';
import * as getUserAndSubDeptCount from './getUserAndSubDeptCount';
import * as getUserDepartment from './getUserDepartment';
import * as getUserDeptInfo from './getUserDeptInfo';
import * as insertDeriveDept from './insertDeriveDept';
import * as insertFunctionDept from './insertFunctionDept';
import * as queryBranchList from './queryBranchList';
import * as queryDepartmentById from './queryDepartmentById';
import * as queryEmployeeForDept from './queryEmployeeForDept';
import * as queryFunctionDeptList from './queryFunctionDeptList';
import * as updateByDeptIdSelective from './updateByDeptIdSelective';
import * as updateFunctionDept from './updateFunctionDept';

export {
  areaDropDownList,
  deleteDepart,
  deleteFunctionDept,
  getAllDropDownList,
  getDepartmentDropdownList,
  getDeptInternalCodeCount,
  getKingdeeOrgDropDownList,
  getSpecialType,
  getSpecialTypeDropDownList,
  getSubDepartment,
  getSubDepartmentList,
  getSuperDepartId,
  getUserAndSubDeptCount,
  getUserDepartment,
  getUserDeptInfo,
  insertDeriveDept,
  insertFunctionDept,
  queryBranchList,
  queryDepartmentById,
  queryEmployeeForDept,
  queryFunctionDeptList,
  updateByDeptIdSelective,
  updateFunctionDept,
};
