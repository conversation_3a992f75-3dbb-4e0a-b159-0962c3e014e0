import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/singlePolicy/getBaseProportionTrialTable
     * @desc 根据选择城市+人员类别+接单方，获取对应的大户的标准套餐
根据选择城市+人员类别+接单方，获取对应的大户的标准套餐
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** branchId */
  branchId: number;
  /** cityId */
  cityId: number;
  /** personCategory */
  personCategory: number;
}

export const init = new defs.information.Result();
export const url =
  '/rhro-service-1.0/singlePolicy/getBaseProportionTrialTable:GET';
export const initialUrl =
  '/rhro-service-1.0/singlePolicy/getBaseProportionTrialTable';
export const cacheKey = '_singlePolicy_getBaseProportionTrialTable_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/singlePolicy/getBaseProportionTrialTable`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/singlePolicy/getBaseProportionTrialTable`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
