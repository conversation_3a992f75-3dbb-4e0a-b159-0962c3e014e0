<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-07-09 14:24:33
 * @LastAuthor: 侯成
 * @LastTime: 2020-07-09 17:53:39
 * @message: message
-->

# 组件及方法汇总

本文包含本项目中所有公共组件和公共方法的汇总。

## 分类

1.组件

form, editForm, select, input, pop, excetable

2.方法及配置

settings, forms settings, types, methods, excetable

## 公共库详细列表

| 公共库 | 说明 | 场景 | 位置 |
| --- | --- | --- | --- |
| **表单版式** | 与表单版式相关的公共组件 | 用于表单排版 | components |
| FormLayouts | Form 的排列样式，包含 `FormElement`, `RowElement`, `ColElement3`, `ColElementButton` | 在表单排版中使用，选择正确的标签，无需手动传入样式配置。 | components\Forms4\FormLayouts |
| ConfirmButton | 自动 loading 按钮 | 用于按钮防止重复点击。 | components\Forms4\Confirm |
| FormItem | 输入控件的 lable 标签，不带冒号 | 在表单中使用。 | components\Forms4\FormItem |
| FormItemColon | 输入控件的 lable 标签，带冒号 | 在详情展示中使用。 | components\Forms4\FormItemColon |
| **输入控件** | 与输入相关的公共组件。 | components | - |
| InpurtDecimal | 小数输入控件。与 InpurtNumber 不同，此组件在末尾不会添加零。 将会显示 `100`，而不是`100.00` | 通常用与金额输入。 | components\Forms4\inputs |
| DateRange | 日期范围选择器。 | 选择日期范围。 | components\DateRange4\index |
| MonthRange | 月份范围选择器。 | 选择月份范围。 | components\DateRange4\index |
| BaseDataSelectors | 基础数据下拉选择，数据来源与后端接口，目前有各类选择器 30 多个。 | 用于动态数据下拉。 | components\Selectors\BaseDataSelectors |
| mapToSelectors | 下拉选择器，数据来源本地配置，数据格式为`Map`。 | 用于静态数据下拉。 | components\Selectors\FuncSelectors |
| mapToSwitch | 二选一下拉选择，数据格式为`Map`。其中 positive 选项为绿点，negetive 选项为红点 | 用于下拉时二选一的情况，应当尽量推广使用 | components\Selectors\FuncSelectors |
| posoToSelectors | 下拉选择器，数据来源本地配置，数据格式为`Object`。键为`string`，值为`string`。目前使用较少。 | 用于静态数据下拉。通常在静态数据必须存储为`Object`时才使用，例如数据需要存储于 dva 中。 | components\Selectors\FuncSelectors |
| pisoToSelectors | 与上面不同的是，此处的数据结构中，键为`number`，值为`string`。目前使用较少。 | 同上 | components\Selectors\FuncSelectors |
| **大型组件** | 大型表单公共组件 | 用于表单中的各种输入框 | - |
| AddForm | 通过 columns 配置生成表单。 | 用于各种新增表单。 | components\EditeForm\AddForm |
| UpdateForm | 通过 columns 配置生成表单。 | 用于各种更新表单。 | components\EditeForm\AddForm |
| DetailForm | 通过 columns 配置生成表单详情。 | 用于各种详情表单展示。 | components\EditeForm\AddForm |
| Excetable | 行内编辑展示。 | 用于 T 表格行内编辑。 | components\Excetable |
| **表单数据展示** | 表单中的数据展示 | 用于表单中的数据展示 | - |
| mapToSelectView | mapToSelectors 的结果展示，数据格式为`Map`。 | 用于静态数据下拉结果的展示。 | components\Selectors\FuncSelectors |
| mapToSwitchView | mapToSwitch 的结果展示，数据格式为`Map`。其中 positive 选项为绿点，negetive 选项为红点。 | 用于下拉时二选一的情况，应当尽量推广使用。 | components\Selectors\FuncSelectors |
| povoToSelectView | posoToSelectors 的结果展示，数据格式为`Object`。键为`string`，值为`string`。目前使用较少。 | 用于静态数据下拉结果的展示。 | components\Selectors\FuncSelectors |
| pisoToSelectView | pisoToSelectors 的结果展示，数据格式为`Object`。键为`number`，值为`string`。目前使用较少。 | 用于静态数据下拉结果的展示。 | components\Selectors\FuncSelectors |
| **表格数据展示** | 表格中的数据展示 | 用于表格中的数据展示 | - |
| mapToRender | mapToSelectView 的 table columns 的封装，参数类型为`Map` | 用于在 table 中使用 mapToSelectView。 | utils\methods\tables |
| mapSwitchToRender | mapToSwitchView 的 table columns 的封装，参数类型为`Map` | 用于在 table 中使用 mapToSwitchView。 | utils\methods\tables |
| **表头 filter** | 表格中表头的 filter 展示 | 用于通过静态数据生成表格中的 filter | - |
| mapToFilter | Map 类型数据到 Table 表头的转换器，数据类型为 `Map`，应当推广使用。 | 用于通过静态数据生成表格中的 filter，通常在数据必须存储为`Object`格式时，使用本方法。 | utils\methods\tables |
| pojoToFilter | object 类型数据到 Table 表头的转换器，数据类型为 `Object`，较少使用。 | 用于通过静态数据生成表格中的 filter，通常在数据必须存储为`Object`格式时，使用本方法。 | utils\methods\tables |

## 静态数据配置

| 公共库 | 说明 | 场景 | 位置 |
| --- | --- | --- | --- |
| stdBoolTrue | 标准值`true`，即 `1` | 用于在各种地方设置 bool 值，同各种 bool 值作比较判断。 | src\utils\settings\index.ts |
| stdBoolFalse | 标准值`false`，即 `0` | 用于在各种地方设置 bool 值，同各种 bool 值作比较判断。 | src\utils\settings\index.ts |
| stdNotDeleted | 标准值`未删除`，`有效`等等，即 `0` | 用于在各种地方设置删除状态，同各种删除状态作比较判断。 | src\utils\settings\index.ts |
| stdIsDeleted | 标准值`已删除`，`无效效`等等，即 `1` | 用于在各种地方设置删除状态，同各种删除状态作比较判断。 | src\utils\settings\index.ts |
| stdBooleanMap | 标准数据结构，`是/否`二选一 | 同 mapToSwitch 搭配使用，生成标准的`是/否`选择器。同 mapToSwitchView 搭配，生成数据展示器。 | src\utils\settings\index.ts |
| stdIsDeletedMap | 标准数据结构，`有效/无效`二选一 | 同 mapToSwitch 搭配使用，生成标准的`有效/无效`选择器。同 mapToSwitchView 搭配，生成数据展示器。 | src\utils\settings\index.ts |

**补充说明**

`src\utils\settings`下有大量的数据配置，与`pages`的目录结构相对应相对应，在工程中使用的数据，应当置于此处。

## 类型配置

| 公共库       | 说明 | 场景 | 位置 |
| ------------ | ---- | ---- | ---- |
| ConnectedFC  | -    | -    | -    |
| ReducerArgs  | -    | -    | -    |
| IServiceType | -    | -    | -    |
| StdRes       | -    | -    | -    |
| POJO         | -    | -    | -    |
| POVO         | -    | -    | -    |
| POSO         | -    | -    | -    |
| PISO         | -    | -    | -    |
| PINO         | -    | -    | -    |
