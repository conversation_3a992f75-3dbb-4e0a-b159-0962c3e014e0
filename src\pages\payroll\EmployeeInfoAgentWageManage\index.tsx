import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Typography } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { useWritable, WritableInstance } from '@/components/Writable';
import { msgErr, msgOk } from '@/utils/methods/message';
import {
  CommonBaseDataSelector,
  EmployeeFilesSelector,
  mapToSelectors,
} from '@/components/Selectors';
import CustomerDetail from '@/pages/Sales/CustomerManage/CustomerQuery/components/CustomerDetail';
import UpdateForm from '@/components/EditeForm/UpdateForm';
import { isEmpty } from 'lodash';
import QueryAgentEmpModifyHistoryWin from './components/QueryAgentEmpModifyHistoryWin';
import { stdDateFormat } from '@/utils/methods/times';
import { CountrySelector } from '@/components/Selectors/BaseDataSelectors';
import { validateTaxTelephone } from '@/utils/forms/validate';

interface EmployeeInfoAgentWageManageProps {
  [props: string]: any;
}

const service = API.payroll.employeeInfoAgentWageManage.getWageAgentEmployeeBaseInfo;
const empTableService = API.payroll.employeeInfoAgentWageManage.getAgentWageEmployeeBankInfo;
const EmployeeInfoAgentWageManage: React.FC<EmployeeInfoAgentWageManageProps> = () => {
  const [form] = Form.useForm();
  const [form1] = Form.useForm();
  const [idCardTypeMap, setIdType] = useState(new Map());
  const [detailVisible, setDetailVisible] = useState<boolean>(false); // 客户详情Modal
  const [customerObj, setCustomerObj] = useState<POJO>({});
  const [onShowUpdateEmployeeBaseInfo, setOnShowUpdateEmployeeBaseInfo] = useState<boolean>(false);
  const [detailData, setDetailData] = useState<POJO>({}); //人员详情
  const [disableMode, setDisableMode] = useState<boolean>(false);
  const [disabledId, setDisabledId] = useState<string>('');
  const historyModal = useState<boolean>(false);
  const writable = useWritable({ service });
  useEffect(() => {
    API.basedata.baseDataCls.getDropDownList.requests({ type: '45' }).then((result) => {
      const { list = [] } = result || {};
      const idType = new Map<string, string>();
      list.forEach((item: any) => {
        idType.set(item?.key, item?.shortName);
      });
      setIdType(idType);
    });
  }, []);
  const formColumnsUpdate = [
    {
      label: '唯一号',
      fieldName: 'employeeCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '雇员姓名',
      fieldName: 'employeeName',
      inputRender: 'string',
      rules: [{ required: true, message: '雇员姓名' }],
    },
    {
      label: '证件类型',
      fieldName: 'idCardType',
      inputRender: () => {
        return <CommonBaseDataSelector disabled params={{ type: '045' }} />;
      },
    },
    {
      label: '证件号码',
      fieldName: 'idCardNum',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '生日',
      fieldName: 'birthday',
      inputRender: 'date',
      inputProps: { formta: stdDateFormat },
    },
    {
      label: '性别',
      fieldName: 'gender',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="genderList" />;
      },
    },
    {
      label: '民族',
      fieldName: 'ethnic',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="ethnicList" />;
      },
    },
    {
      label: '国家',
      fieldName: 'countryId',
      inputRender: () => {
        return <CountrySelector />;
      },
    },
    {
      label: '政治面貌',
      fieldName: 'politicalStatus',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="politicalStatusList" />;
      },
    },
    {
      label: '教育程度',
      fieldName: 'educationLevel',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="eduLevelList" />;
      },
    },
    {
      label: '手机',
      fieldName: 'contactTel2',
      inputRender: 'string',
      rules: [
        {
          required: true,
          message: '请填写手机号',
        },
        {
          validator: validateTaxTelephone,
          message: '手机号码不符合校验规则',
        },
      ],
    },
    { label: '联系电话2', fieldName: 'contactTel1', inputRender: 'string' },
    { label: '居住地址', fieldName: 'residentAddress', inputRender: 'string' },
    { label: '邮编', fieldName: 'zipCode', inputRender: 'string' },
    {
      label: '户口类别',
      fieldName: 'hukouType',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="hukouList" />;
      },
    },
    { label: '户口所在地', fieldName: 'hukouAddress', inputRender: 'string' },
    { label: '所在街道', fieldName: 'empStreet', inputRender: 'string' },
    {
      label: '用工形式',
      fieldName: 'workType',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="workTypeList" />;
      },
    },
    { label: '户籍地址邮编', fieldName: 'hukouZipCode', inputRender: 'string' },
    { label: '籍贯', fieldName: 'personalRegCity', inputRender: 'string' },
    {
      label: '电子邮件',
      fieldName: 'email',
      inputRender: 'string',
      rules: [
        {
          type: 'email',
        },
      ],
    },
    {
      label: '婚姻状况',
      fieldName: 'marriageStatus',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="marriageList" />;
      },
    },
    {
      label: '子女情况',
      fieldName: 'childrenStatus',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="childrenStatusList" />;
      },
    },
    {
      label: '健康状态',
      fieldName: 'healthStatus',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="healthList" />;
      },
    },
    {
      label: '残疾类型',
      fieldName: 'disabilityType',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="disableList" />;
      },
    },
    {
      label: '残疾等级',
      fieldName: 'disabilityLevel',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="disabilityLevels" />;
      },
    },
    {
      label: '残疾证号',
      fieldName: 'disabledId',
      inputRender: () => {
        return (
          <Input
            style={{ width: '100%' }}
            maxLength={20}
            onBlur={(e) => {
              const value = e.currentTarget.value;
              setDisabledId(value ? value : '');
              if (value) {
                if (value.length == 20) {
                  const itemCode = value.slice(-1);
                  const itemCode1 = value.slice(-2, -1);
                  if (itemCode === '0') {
                    form1.setFieldsValue({ disabilityLevel: '10' });
                  } else {
                    form1.setFieldsValue({ disabilityLevel: itemCode });
                  }
                  form1.setFieldsValue({ disabilityType: itemCode1 });
                }
              }
            }}
          />
        );
      },
      rules: [
        {
          pattern: new RegExp(/^\d{20}$/),
          message: '只能输入20位的数字',
        },
      ],
    },
    {
      label: '发证日期',
      fieldName: 'dateOfIssue',
      inputRender: 'date',
      formOptions: {
        rules: [{ required: disabledId ? true : false, message: '请选择发证日期' }],
      },
    },
    {
      label: '残疾证有效期至',
      fieldName: 'disaCertValidUntil',
      inputRender: 'date',
    },
    {
      label: '残疾人类别',
      fieldName: 'disabledPeopleType',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="disabledPeopleTypes" />;
      },
    },
    {
      label: '监护人姓名',
      fieldName: 'guardianName',
      inputRender: 'string',
    },
    {
      label: '监护人联系方式',
      fieldName: 'guardianContact',
      inputRender: 'string',
    },
    {
      label: '申领补贴情况',
      fieldName: 'subsidyAppStatus',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="subsidyAppStatuses" />;
      },
    },
    {
      label: '医疗保障类别',
      fieldName: 'medicareType',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="medInsurList" />;
      },
    },
    {
      label: '是否伤残军人',
      fieldName: 'isDisabledMilitary',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="yesNoList" />;
      },
    },
    {
      label: '是否市级保健对象',
      fieldName: 'isCareObject',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="yesNoList" />;
      },
    },
    { label: '家属劳保卡号', fieldName: 'familyLaborCardNo', inputRender: 'string' },
    {
      label: '兵役状况',
      fieldName: 'nationalServiceStatus',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="yesNoList" />;
      },
    },
    {
      label: '参军日期',
      fieldName: 'joinMilitaryDate',
      inputRender: 'date',
      inputProps: { formta: stdDateFormat },
    },
    {
      label: '转业日期',
      fieldName: 'retireFromMilitaryDate',
      inputRender: 'date',
      inputProps: { formta: stdDateFormat },
    },
    {
      label: '外语语种',
      fieldName: 'foreignLanguageType',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="foreignLangList" />;
      },
    },
    {
      label: '外语等级',
      fieldName: 'foreignLanguageLevel',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="foreignLangLvList" />;
      },
    },
    {
      label: '专业技术职称',
      fieldName: 'professionTitle',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="professionalList" />;
      },
    },
    { label: '职称发证单位', fieldName: 'techAwardUnit', inputRender: 'string' },
    { label: '职业工种名称', fieldName: 'professionName', inputRender: 'string' },
    { label: '职业工种等级', fieldName: 'professionLevel', inputRender: 'string' },
    { label: '职业工种发证单位', fieldName: 'professionAwardUnit', inputRender: 'string' },
    {
      label: '职业工种发证日期',
      fieldName: 'professionAwardDate',
      inputRender: 'date',
      inputProps: { formta: stdDateFormat },
    },
    {
      label: '首次参加工作日期',
      fieldName: 'beginWorkDate',
      inputRender: 'date',
      inputProps: { formta: stdDateFormat },
    },
    {
      label: '是否有不良记录',
      fieldName: 'haveBadnessRecord',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="yesNoList" />;
      },
    },
    {
      label: '毕业日期',
      fieldName: 'graduationDt',
      inputRender: 'date',
      inputProps: { formta: stdDateFormat },
    },
    {
      label: '是否为集体户口',
      fieldName: 'isGroupResident',
      inputRender: () => {
        return <EmployeeFilesSelector nestedKey="yesNoList" />;
      },
    },
    { label: '紧急联系人', fieldName: 'emergencyContact', inputRender: 'string' },
    { label: '紧急联系人电话', fieldName: 'emergencyPhone', inputRender: 'string' },
  ];
  const formColumns: EditeFormProps[] = [
    { label: '唯一号', fieldName: 'employeeCode', inputRender: 'string' },
    { label: '雇员姓名', fieldName: 'employeeName', inputRender: 'string' },
    {
      label: '证件类型',
      fieldName: 'idCardType',
      inputRender: () => mapToSelectors(idCardTypeMap),
    },
    { label: '证件号码', fieldName: 'idCardNum', inputRender: 'string' },
    { label: '客户编号', fieldName: 'custCode', inputRender: 'string' },
    { label: '客户名称', fieldName: 'custName', inputRender: 'string' },
  ];
  const columns: WritableColumnProps<any>[] = [
    { title: '雇员姓名', dataIndex: 'employeeName', inputRender: 'string' },
    { title: '唯一号', dataIndex: 'employeeCode', inputRender: 'string' },
    {
      title: '证件类型',
      dataIndex: 'idCardType',
      inputRender: 'string',
      render(t) {
        return <>{idCardTypeMap.get(String(t))}</>;
      },
    },
    { title: '证件号码', dataIndex: 'idCardNum', inputRender: 'string' },
    { title: '客户编号', dataIndex: 'custCode', inputRender: 'string' },
    {
      title: '客户名称',
      dataIndex: 'custName',
      inputRender: 'string',
      render(t, r) {
        return (
          <Typography.Link
            onClick={() => {
              setDetailVisible(true);
              setCustomerObj(r);
            }}
          >
            {t}
          </Typography.Link>
        );
      },
    },
    { title: '派单方客服', dataIndex: 'assignerProviderName', inputRender: 'string' },
    { title: '接单方客服', dataIndex: 'assigneeProviderName', inputRender: 'string' },
    { title: '状态', dataIndex: 'status', inputRender: 'string' },
    { title: '客户方编号', dataIndex: 'internalCode', inputRender: 'string' },
    { title: '档案柜编号', dataIndex: 'fileCabCode', inputRender: 'string' },
    { title: '文件夹编号', dataIndex: 'folderCode', inputRender: 'string' },
  ];
  const empColumns: WritableColumnProps<any>[] = [
    { title: '员工姓名', dataIndex: 'employeeName', inputRender: 'string' },
    { title: '客户公司', dataIndex: 'custName', inputRender: 'string' },
    { title: '开户行名称', dataIndex: 'openBankName', inputRender: 'string' },
    { title: '账号', dataIndex: 'bankAcct', inputRender: 'string' },
    { title: '账号省名', dataIndex: 'provinceName', inputRender: 'string' },
    { title: '账号市区名', dataIndex: 'cityName', inputRender: 'string' },
    { title: '银行类别', dataIndex: 'bankName', inputRender: 'string' },
    { title: '账户名', dataIndex: 'accountEmployeeName', inputRender: 'date' },
    { title: '业务类型', dataIndex: 'busiType', inputRender: 'date' },
  ];
  // 修改or查看
  const showUpdateEmployeeBaseInfo = (type: 'update' | 'read') => {
    const { selectedSingleRow } = writable;
    API.emphiresep.employeeBaseInfo.getEmployeeBaseInfoById
      .requests({ employeeId: selectedSingleRow.employeeId })
      .then((res) => {
        setDisableMode(type !== 'update');
        setDetailData(res);
        setOnShowUpdateEmployeeBaseInfo(!onShowUpdateEmployeeBaseInfo);
      });
  };
  const renderButtons = (_options: WritableInstance) => {
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button
          disabled={isEmpty(writable.selectedSingleRow)}
          onClick={() => showUpdateEmployeeBaseInfo('update')}
        >
          修改
        </Button>
        <Button
          disabled={isEmpty(writable.selectedSingleRow)}
          onClick={() => showUpdateEmployeeBaseInfo('read')}
        >
          查看
        </Button>
        <Button
          disabled={isEmpty(writable.selectedSingleRow)}
          onClick={() => historyModal[1](true)}
        >
          查看更改历史
        </Button>
      </>
    );
  };
  const checkForm = () => {
    const { employeeCode, employeeName, idCardNum, custCode, custName } = form.getFieldsValue();
    if (employeeCode || employeeName || idCardNum || custCode || custName) {
      return Promise.resolve();
    }
    return Promise.reject('【唯一号、雇员姓名、证件号码、客户编号、客户名称至少填写一个】');
  };
  //修改callback
  const updateEmployeeBaseInfo = async (_employeeBaseInfo: POJO) => {
    if (
      (_employeeBaseInfo.contactTel1 == null || _employeeBaseInfo.contactTel1 == '') &&
      (_employeeBaseInfo.contactTel2 == null || _employeeBaseInfo.contactTel2 == '')
    ) {
      return msgErr('手机与联系电话至少要填一项');
    }
    await API.emphiresep.employeeBaseInfo.updateEmployeeBaseInfo.requests(_employeeBaseInfo);
    msgOk('修改成功');
    setOnShowUpdateEmployeeBaseInfo(!onShowUpdateEmployeeBaseInfo);
    setDetailData({});
    writable.request();
  };
  return (
    <>
      <CachedPage
        form={form}
        service={service}
        formColumns={formColumns}
        notShowRowSelection
        wriTable={writable}
        columns={columns}
        beforeSubmitEvent={checkForm}
        renderButtons={renderButtons}
      />

      <UpdateForm
        title="员工信息"
        formColumns={formColumnsUpdate}
        hideHandle={() => {
          setOnShowUpdateEmployeeBaseInfo(!onShowUpdateEmployeeBaseInfo);
          setDetailData({});
        }}
        form={form1}
        visible={onShowUpdateEmployeeBaseInfo}
        updateitem={detailData}
        submitHandle={updateEmployeeBaseInfo}
        disableMode={disableMode}
      >
        {disableMode && (
          <CachedPage
            service={empTableService}
            formColumns={[]}
            hooksProps={{ autoRequest: { ...detailData } }}
            notShowRowSelection
            handleQueries={() => ({ ...detailData })}
            columns={empColumns}
          />
        )}
      </UpdateForm>
      <QueryAgentEmpModifyHistoryWin modal={historyModal} info={writable?.selectedSingleRow} />
      <CustomerDetail
        visible={detailVisible}
        hideHandle={() => setDetailVisible(false)}
        extraObj={customerObj}
        action="COMMON"
        source="COMMON_USE"
      />
    </>
  );
};

export default EmployeeInfoAgentWageManage;
