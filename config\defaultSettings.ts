/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-06-04 14:21:47
 * @LastAuthor: 侯成
 * @LastTime: 2020-11-06 16:59:16
 * @message: message
 */

import { Settings as ProSettings } from '@ant-design/pro-layout';
const recomandBrowsers = require('./browsers.json');

type DefaultSettings = ProSettings & {
  pwa: boolean;
  year: number;
  recomandBrowsers: any[];
};

const proSettings: DefaultSettings = {
  recomandBrowsers,
  year: 2020,
  navTheme: 'dark',
  primaryColor: '#545ba4',
  layout: 'sidemenu',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: false,
  colorWeak: false,
  menu: {
    locale: true,
  },
  title: '',
  pwa: true,
  iconfontUrl: '//at.alicdn.com/t/font_1977187_ap1pjmwsxgc.js',
};

export type { DefaultSettings };

export default proSettings;
