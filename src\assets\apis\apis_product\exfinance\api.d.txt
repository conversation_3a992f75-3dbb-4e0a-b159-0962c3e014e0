declare namespace defs {
  export namespace exfinance {
    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: ObjectMap<any, Array<object>>;

      /** message */
      message: string;

      /** t */
      t: ObjectMap<any, Array<object>>;
    }

    export class DelBillQuery {
      /** applyerAreaId */
      applyerAreaId: string;

      /** applyerBranchId */
      applyerBranchId: string;

      /** billYm */
      billYm: string;

      /** 创建人 */
      createBy: string;

      /** 客户ID */
      custId: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商ID */
      pdProviderId: string;

      /** 帐套ID */
      receivableTempltId: string;

      /** startIndex */
      startIndex: number;
    }

    export class ExBillDTO {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** empIds */
      empIds: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** receivableVersionid */
      receivableVersionid: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** serviceMonth */
      serviceMonth: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** type */
      type: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ExOneCharges {
      /** add */
      add: boolean;

      /** 金额 */
      amount: number;

      /** areaId */
      areaId: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单年月 */
      billYm: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 财务应收年月 */
      finReceiableYm: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 人数 */
      headCount: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 金额 */
      oldamount: number;

      /** onecharesId */
      onecharesId: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 产品id */
      productId: string;

      /** 产品线 */
      productLine: string;

      /** 产品名称 */
      productName: string;

      /** 产品大类 */
      productTypeId: string;

      /** providerId */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** quotationId */
      quotationId: string;

      /** quotationItemId */
      quotationItemId: string;

      /** receivableId */
      receivableId: string;

      /** 帐套名称 */
      receivableTempltName: string;

      /** 注释 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** templtId */
      templtId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ExOneChargesDTO {
      /** add */
      add: boolean;

      /** 金额 */
      amount: number;

      /** areaId */
      areaId: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单年月 */
      billYm: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 财务应收年月 */
      finReceiableYm: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 人数 */
      headCount: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 金额 */
      oldamount: number;

      /** onecharesId */
      onecharesId: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 产品id */
      productId: string;

      /** 产品线 */
      productLine: string;

      /** 产品名称 */
      productName: string;

      /** 产品大类 */
      productTypeId: string;

      /** providerId */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** quotationId */
      quotationId: string;

      /** quotationItemId */
      quotationItemId: string;

      /** receivableId */
      receivableId: string;

      /** 帐套名称 */
      receivableTempltName: string;

      /** 注释 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** templtId */
      templtId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class GenerateBillContionDTO {
      /** 大区ID */
      areaId: string;

      /** 账单年月 */
      billYm: string;

      /** billYmEd */
      billYmEd: string;

      /** billYmSt */
      billYmSt: string;

      /** billtempId */
      billtempId: string;

      /** chargeDTOs */
      chargeDTOs: Array<defs.exfinance.OneChargesDTO>;

      /** chargesDTOs */
      chargesDTOs: Array<defs.exfinance.ExOneChargesDTO>;

      /** contractIds */
      contractIds: string;

      /** createDt */
      createDt: string;

      /** 生成人 */
      creater: string;

      /** custCode */
      custCode: string;

      /** 客户ID */
      custId: string;

      /** custName */
      custName: string;

      /** 部门ID */
      departmentId: string;

      /** empCode */
      empCode: string;

      /** 雇员ID */
      empId: string;

      /** empName */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** 应收年月 */
      finReceivableYm: string;

      /** finReceivableYmEd */
      finReceivableYmEd: string;

      /** finReceivableYmSt */
      finReceivableYmSt: string;

      /** genType */
      genType: string;

      /** ids */
      ids: string;

      /** invoiceStatus */
      invoiceStatus: string;

      /** 是否锁定 */
      isLocked: string;

      /** 是否支付申请 */
      isPayed: string;

      /** isoldData */
      isoldData: string;

      /** 0 客户一次性项目 1 外包一次性税费 */
      onechargesType: string;

      /** 帐套ID */
      pBillTempltId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** payeeId */
      payeeId: string;

      /** 账单方ID */
      payerId: string;

      /** 产品ID */
      productId: string;

      /** 大区ID */
      providerId: string;

      /** 报价类型 */
      quotationItemType: string;

      /** receivableAmt */
      receivableAmt: string;

      /** receivableAmtEd */
      receivableAmtEd: string;

      /** receivableAmtSt */
      receivableAmtSt: string;

      /** 应收主记录ID */
      receivableId: string;

      /** 帐套ID */
      receivableTempltId: string;

      /** 应收ID串 */
      receivableTempltIds: string;

      /** receivableTempltName */
      receivableTempltName: string;

      /** 账单版本ID */
      receivableVersionId: string;

      /** genType */
      sdr: string;

      /** 服务年月 */
      serviceMonth: string;

      /** 社保组ID */
      ssGroupId: string;

      /** startIndex */
      startIndex: number;

      /** status */
      status: string;

      /** 帐套ID */
      tempId: string;

      /** tempIdArray */
      tempIdArray: string;

      /** userId */
      userId: string;

      /** verifyStatus */
      verifyStatus: string;

      /** wageIds */
      wageIds: string;

      /** zdr */
      zdr: string;
    }

    export class HashMap<T0 = any, T1 = any> {}

    export class Map<T0 = any, T1 = any> {}

    export class OneChargesDTO {
      /** add */
      add: boolean;

      /** 金额 */
      amount: number;

      /** amtNoTax */
      amtNoTax: number;

      /** atr */
      atr: number;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单年月 */
      billYm: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractId */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** dataFrom */
      dataFrom: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 财务应收年月 */
      finReceiableYm: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 人数 */
      headCount: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否包括存档费 0否 1是 */
      isIncludedArchive: string;

      /** 是否包括商保0否1是 */
      isIncludedBusiness: string;

      /** 是否包含客户一次性费用  0 否 1是 */
      isIncludedOnechares: string;

      /** 是否包含服务费(0 否，1 是) */
      isIncludedService: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** oldVat */
      oldVat: number;

      /** 金额 */
      oldamount: number;

      /** onecharesId */
      onecharesId: string;

      /** 0 客户一次性项目 1 外包一次性税费 */
      onechargesType: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 产品id */
      productId: string;

      /** 产品线 */
      productLine: string;

      /** 产品名称 */
      productName: string;

      /** 产品大类 */
      productTypeId: string;

      /** productTypeName */
      productTypeName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** quotationId */
      quotationId: string;

      /** quotationItemId */
      quotationItemId: string;

      /** receivableId */
      receivableId: string;

      /** receivableTempltId */
      receivableTempltId: string;

      /** 帐套名称 */
      receivableTempltName: string;

      /** 注释 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** serviceType */
      serviceType: string;

      /** serviceTypeText */
      serviceTypeText: string;

      /** signBranchTitle */
      signBranchTitle: string;

      /** signBranchTitleName */
      signBranchTitleName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 总税率 */
      totalTax: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** vat */
      vat: number;

      /** vatr */
      vatr: number;
    }

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }
  }
}

declare namespace API {
  export namespace exfinance {
    /**
     * Ex Bill Controller
     */
    export namespace exBill {
      /**
        * 生成供应商账单
生成供应商账单
        * /exBill/createBill
        */
      export namespace createBill {
        export class Params {
          /** 大区 */
          areaId: string;
          /** 账单年月 */
          billYm: string;
          /** 生成方式1全部重算 2 差异重算 */
          genType: string;
          /** providerId */
          providerId: string;
          /** 账单模板名称 */
          tmpltId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 生成供应商账单
生成供应商账单
        * /exBill/createBill
        */
      export namespace postCreateBill {
        export class Params {
          /** 大区 */
          areaId: string;
          /** 账单年月 */
          billYm: string;
          /** 生成方式1全部重算 2 差异重算 */
          genType: string;
          /** providerId */
          providerId: string;
          /** 账单模板名称 */
          tmpltId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除一次性收费
删除一次性收费
        * /exBill/deleteOneCharges
        */
      export namespace deleteOneCharges {
        export class Params {
          /** ids */
          ids: string;
          /** receiveId */
          receiveId: string;
        }

        export type Response<T> = defs.exfinance.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除一次性收费
删除一次性收费
        * /exBill/deleteOneCharges
        */
      export namespace postDeleteOneCharges {
        export class Params {
          /** ids */
          ids: string;
          /** receiveId */
          receiveId: string;
        }

        export type Response<T> = defs.exfinance.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /exBill/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.exfinance.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.exfinance.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * getAreaDropdownList
       * /exBill/getAreaDropdownList
       */
      export namespace getAreaDropdownList {
        export class Params {
          /** 供应商名称 */
          providerId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            defs.exfinance.ObjectMap<string, Array>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * getAreaDropdownList
       * /exBill/getAreaDropdownList
       */
      export namespace postGetAreaDropdownList {
        export class Params {
          /** 供应商名称 */
          providerId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            defs.exfinance.ObjectMap<string, Array>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取动态列
获取动态列
        * /exBill/getColForDetail
        */
      export namespace getColForDetail {
        export class Params {
          /** type */
          type: string;
          /** versionId */
          versionId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            Array<defs.exfinance.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取动态列
获取动态列
        * /exBill/getColForDetail
        */
      export namespace postGetColForDetail {
        export class Params {
          /** type */
          type: string;
          /** versionId */
          versionId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            Array<defs.exfinance.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除账单信息列表
删除账单信息列表
        * /exBill/getDelBillList
        */
      export namespace getDelBillList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.exfinance.DelBillQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.exfinance.DelBillQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取员工数据
获取员工数据
        * /exBill/getDetailForShow
        */
      export namespace getDetailForShow {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            Array<defs.exfinance.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.exfinance.ExBillDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.exfinance.ExBillDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查看供应商账单日志
查看供应商账单日志
        * /exBill/getLogList
        */
      export namespace getLogList {
        export class Params {
          /** 大区ID */
          areaId: string;
          /** 账单年月 */
          billYm: string;
          /** 供应商名称 */
          providerId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            Array<defs.exfinance.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查看供应商账单日志
查看供应商账单日志
        * /exBill/getLogList
        */
      export namespace postGetLogList {
        export class Params {
          /** 大区ID */
          areaId: string;
          /** 账单年月 */
          billYm: string;
          /** 供应商名称 */
          providerId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            Array<defs.exfinance.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取客户一次性收费
获取客户一次性收费
        * /exBill/getOneCharges
        */
      export namespace getOneCharges {
        export class Params {
          /** receiveId */
          receiveId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            Array<defs.exfinance.ExOneCharges>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取客户一次性收费
获取客户一次性收费
        * /exBill/getOneCharges
        */
      export namespace postGetOneCharges {
        export class Params {
          /** receiveId */
          receiveId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<
            Array<defs.exfinance.ExOneCharges>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 供应商应收明细查询
供应商应收明细查询
        * /exBill/getRecListForLock
        */
      export namespace getRecListForLock {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.exfinance.GenerateBillContionDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.exfinance.GenerateBillContionDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增供应商一次性收费
新增供应商一次性收费
        * /exBill/insertOnChargesModel
        */
      export namespace insertOnChargesModel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.exfinance.GenerateBillContionDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.exfinance.GenerateBillContionDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * lockExBill
       * /exBill/lockExBill
       */
      export namespace lockExBill {
        export class Params {
          /** lockType */
          lockType: string;
          /** receiveId */
          receiveId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * lockExBill
       * /exBill/lockExBill
       */
      export namespace postLockExBill {
        export class Params {
          /** lockType */
          lockType: string;
          /** receiveId */
          receiveId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.exfinance.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
