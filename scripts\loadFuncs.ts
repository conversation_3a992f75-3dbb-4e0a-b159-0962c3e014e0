/*
 * @Author: zhujianghua
 * @Email: <EMAIL>
 * @Date: 2020-11-27 18:21:43
 * @LastAuthor: 侯成
 * @LastTime: 2021-03-03 16:49:14
 * @message:
 */
import fs from 'fs';

const BASE_DIR = __filename.replace(/\\/g, '/').split('/scripts/loadFuncs')[0];
const readDir = (path: string) => {
  const dirs = fs.readdirSync(path).filter((e) => e.startsWith('comp-'));
  let list: { [key: string]: string }[] = [];
  dirs.forEach((name: string) => {
    const p = `${path}/${name}`;
    const json = JSON.parse(fs.readFileSync(p).toString());
    const moduleName = name.replace('comp-', '').replace('.json', '');
    list = list.concat(processOne(json, moduleName));
  });
  const routeMap: { [key: string]: string } = {};
  list.forEach((item) => {
    const path = item.path;
    routeMap[path] = item.component;
  });
  fs.writeFileSync(`${path}/all.json`, JSON.stringify(list, null, 4));
  fs.writeFileSync(`${path}/routerMap.json`, JSON.stringify(routeMap, null, 4));
};

const processOne = (json: { [key: string]: string }, name: string) => {
  return Object.keys(json).map((key) => {
    const value = json[key].split('|');
    const splits = value[0].trim().split(' ');
    return {
      path: `/${name}/${key.replace(/\./gi, '/')}`,
      component: value[1].replace(/\s*/gi, ''),
      pageName: splits[splits.length - 1].trim(),
      author: splits[0].trim(),
    };
  });
};

readDir(`${BASE_DIR}/config/routers`);
const copyFile = () => {
  fs.copyFile(
    `${BASE_DIR}src/assets/apis/index_local.ts`,
    `${BASE_DIR}src/assets/apis/index.ts`,
    function (err) {
      if (err) {
        console.log('something wrong in apis/index_local.ts was happened');
      } else {
        console.log('copy apis/index_local.ts succeed');
      }
    },
  );
};
copyFile();
