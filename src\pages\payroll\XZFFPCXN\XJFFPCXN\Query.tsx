import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { AsyncButton } from '@/components/Forms/Confirm';
import { mapToSelectors } from '@/components/Selectors';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import ImportForm from '@/components/UploadForm/ImportForm';
import { WritableInstance } from '@/components/Writable';
import QuerySocialPayDetailWin from '@/pages/finance/Pay/Query/components/QuerySocialPayDetailWin';
import { formatAmount } from '@/utils/methods/format';
import { msgErr, msgOk } from '@/utils/methods/message';
import { getCurrentUser } from '@/utils/model';
import { WritableColumnProps } from '@/utils/writable/types';
import { Button, Typography, Form, Modal } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { write } from 'xlsx/types';
import AppendBatchSendVirtual from './components/AppendBatchSendVirtual';
import DelayPayRollSendBatchWin from './components/DelayPayRollSendBatchWin';
import SendBatchDetailWin from './components/SendBatchDetailWin';
import ShowPayBatchDetail from './components/ShowPayBatchDetail';
import WageApplyPayVirtual from './components/WageApplyPayVirtual';

interface QueryProps {
  fill: string;
}

let _options: WritableInstance | undefined = undefined;
const Query = (props: QueryProps) => {
  const [form] = Form.useForm();

  const currentUser = getCurrentUser();

  const service = API.payroll.payBatch.queryBatch;

  const [appendVisible, setAppendVisible] = useState<boolean>(false);
  const [singleRow, setSingleRow] = useState<POJO>({});
  const [laterVisible, setLaterVisible] = useState<boolean>(false);
  const [moneyVisible, setMoneyVisible] = useState<boolean>(false);
  const [detailVisible, setDetailVisible] = useState<boolean>(false); // 详细可见
  const [viewDetailVisible, setViewDetailVisible] = useState<boolean>(false); // 查看详细可见
  const [wageVisible, setWageVisible] = useState<boolean>(false); // 支付审核可见

  const batchStatusList = new Map<string, string>([
    ['0', '初始'],
    ['1', '退回修改'],
    ['2', '支付审批通过'],
    ['3', '终止'],
    ['10', '薪资主管审批'],
  ]);

  const formColumns: EditeFormProps[] = [
    {
      label: '发放批次号',
      fieldName: 'payBatchCode',
      inputRender: 'string',
      rules: [{ required: false, pattern: /^[0-9]+$/ }],
    },
    { label: '发放批次名称', fieldName: 'payBatchName', inputRender: 'string' },
    { label: '客户', fieldName: 'custId', inputRender: () => <CustomerPop /> },
    // RHRO-3376 因为服务端 createDtTo createDt 卡条件的字段用反了，现在是前段修正
    { label: '生成时间>=', fieldName: 'createDtTo', inputRender: 'date' },
    { label: '生成时间<=', fieldName: 'createDt', inputRender: 'date' },
    {
      label: '状态',
      fieldName: 'approveStatus',
      inputRender: () => mapToSelectors(batchStatusList),
    },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '发放批次名称', dataIndex: 'payBatchName' },
    { title: '发放批次号', dataIndex: 'payBatchCode' },
    {
      title: '申请金额',
      dataIndex: 'applyPayAmt',
      render: (val, r) => <Typography.Link onClick={() => onLinkMoney(r)}>{val}</Typography.Link>,
    },
    { title: '生成时间', dataIndex: 'createDt' },
    {
      title: '状态',
      dataIndex: 'approveStatus',
      render: (val: number) => batchStatusList.get(val.toString()),
    },
    { title: '备注', dataIndex: 'remark' },
    { title: '创建人', dataIndex: 'createByName' },
    {
      title: '详细',
      dataIndex: 'a',
      render: (_, r) => <Typography.Link onClick={() => onDetail(r)}>查看</Typography.Link>,
    },
    {
      title: '查看明细',
      dataIndex: 'b',
      render: (_, r) => <Typography.Link onClick={() => onViewDetail(r)}>查看</Typography.Link>,
    },
  ];

  useEffect(() => {
    form.setFieldsValue({ payBatchName: props.fill });
  }, [props.fill]);

  const onLinkMoney = (r: POJO) => {
    setSingleRow(r);
    setMoneyVisible(true);
  };

  const onDetail = (r: POJO) => {
    setSingleRow(r);
    setDetailVisible(true);
  };

  const onViewDetail = (r: POJO) => {
    setSingleRow(r);
    setViewDetailVisible(true);
  };

  const onAppendSend = () => {
    const selected = _options?.selectedSingleRow;
    if (isEmpty(selected)) return msgErr('请先选择数据');
    if (selected.approveStatus != '0') return msgErr('只有初始状态的发放批次才能进行追加操作');
    if (selected.createBy != currentUser.profile.userId)
      return msgErr('只能是创建人本人才能进行追加操作');
    setSingleRow(selected);
    setAppendVisible(true);
  };

  const onDelete = () => {
    const rows = _options?.selectedRows;
    if (isEmpty(rows)) return msgErr('请先选择数据');
    const index = rows!.findIndex((e) => e.approveStatus != '0');
    if (index > -1) return msgErr('发放状态只有为初始时才可以删除');
    Modal.confirm({
      content: `选中${rows?.length}条数据,将进行删除操作，是否继续?`,
      onOk: async () => {
        const ids = rows!.map((e) => e.payAuditId).join(',');
        await API.payroll.payBatch.delPaySendBatch.requests({ ids });
        msgOk('删除成功');
        _options?.request();
      },
    });
  };

  const onLater = () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据');
    if (row.approveStatus == '0' || row.approveStatus == '1') {
      setSingleRow(row);
      setLaterVisible(true);
    } else {
      msgErr('只有初始和驳回状态的批次才能设置缓发');
    }
  };

  const beforeUpload = async () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) {
      msgErr('请先选择数据');
      return false;
    }
    if (row.approveStatus == '0') return true;
    msgErr('只有初始状态的发放批次才能生成支付审核');
    return false;
  };

  const impHisHandleQuerues = (val?: POJO) => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) {
      msgErr('请先选择数据');
      return undefined;
    }
    return {
      ...val,
      currentRecordId: row.payAuditId,
      showQueryElement: true,
    };
  };

  const onPay = async () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据');
    if (row.approveStatus == 0) {
      if (row.createBy != currentUser.profile.userId) {
        msgErr('该按钮只允许批次创建者本人操作');
      } else {
        const r = await API.payroll.payBatch.queryPaySendBySendIds.requests({
          paySendIds: row.paySends,
        });
        const list = r.list as POJO[];
        if (!list.length) return msgErr('此行记录没有发放记录');
        let isNotSame = false;
        for (let i = 0; i < list.length; i++) {
          if (list[i].createUser != currentUser.profile.userId) {
            isNotSame = true;
          }
        }
        if (isNotSame) {
          Modal.confirm({
            content: '计算与发放非同一人，是否仍继续操作',
            onOk: () => afterHandle(row),
          });
        } else {
          afterHandle(row);
        }
      }
    } else {
      msgErr('只有初始状态的发放批次才能生成支付审核');
    }
  };

  const afterHandle = (row: POJO) => {
    if (row.approveStatus == '1') {
      if (row.createBy != currentUser.profile.userId) {
        msgErr('退回修改状态的发放批次只能有创建人才能生成支付审核');
        return;
      }
    }
    setSingleRow(row);
    setWageVisible(true);
  };

  const renderButtons = (options: WritableInstance) => {
    _options = options;
    return (
      <React.Fragment>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        {/* <Button onClick={onAppendSend}>追加薪资发放</Button> */}
        <Button onClick={onDelete}>删除</Button>
        <Button onClick={onLater}>设置缓发</Button>
        <ImportForm
          btnName="批量设置缓发"
          downBtnName="下载模板"
          btnEnable
          showImpHisBtn
          ruleId="900000220"
          serviceName="payRollBatchService"
          fileSuffix=".xls"
          beforeUpload={beforeUpload}
          impHisBtnName="批量缓发历史"
          showImpHisByAuthType="3"
          impHisHandleQuerues={impHisHandleQuerues}
          handleQueries={(val?: POJO) => ({
            ...val,
            parameters: { payAuditId: options.selectedSingleRow?.payAuditId?.toString() || '' },
          })}
        />
        <AsyncButton onClick={onPay}>生成支付审核</AsyncButton>
      </React.Fragment>
    );
  };

  const renderModals = () => {
    return (
      <React.Fragment>
        <AppendBatchSendVirtual
          visible={appendVisible}
          hideHandle={(refresh) => {
            setAppendVisible(false);
            if (refresh) _options?.request();
          }}
          data={singleRow}
        />
        <DelayPayRollSendBatchWin
          visible={laterVisible}
          hideHandle={() => setLaterVisible(false)}
          data={singleRow}
        />
        <QuerySocialPayDetailWin
          visible={moneyVisible}
          hideHandle={() => setMoneyVisible(false)}
          data={singleRow}
          currentState={singleRow.payType == '4' ? 'noSecond' : 'hasSecond'}
          cb={(v) => setSingleRow(v)}
        />
        <SendBatchDetailWin
          visible={detailVisible}
          hideHandle={() => setDetailVisible(false)}
          data={{ type: '0', payAuditId: singleRow.payAuditId }}
        />
        <ShowPayBatchDetail
          visible={viewDetailVisible}
          hideHandle={() => setViewDetailVisible(false)}
          data={{
            payAuditId: singleRow.payAuditId?.toString() || '',
            paySends: singleRow.paySends,
            paySendId: singleRow.paySends,
            payBatchName: singleRow.payBatchName,
            payBatchCode: singleRow.payBatchCode,
          }}
        />
        <WageApplyPayVirtual
          visible={wageVisible}
          data={singleRow}
          hideHandle={(refresh) => {
            setWageVisible(false);
            if (refresh) _options?.request();
          }}
        />
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      <CachedPage
        cardProps={{ bordered: false, bodyStyle: { padding: 0 } }}
        service={service}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        notShowPagination
        fixedValues={{ payBatchType: 2 }}
        cached
        form={form}
        scroll={{ x: 'max-content', y: 500 }}
      />
      {renderModals()}
    </React.Fragment>
  );
};

export default Query;
