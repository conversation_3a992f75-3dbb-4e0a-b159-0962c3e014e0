import React, { useState } from 'react';
import { Button } from 'antd';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { WritableColumnProps } from '@/utils/writable/types';
import { ConfirmButton } from '@/components/Forms/Confirm';
import { WritableInstance } from '@/components/Writable';
import AddCodal from './AddCodal';
import SubmitCodal from './SubmitCodal';
import { GetBaseBusnameClassDropdownList, mapToSelectors } from '@/components/Selectors';
import { busTypeMap } from '@/utils/settings/empwelfare/businessProcessing';
import { getCurrentUserCityId } from '@/utils/model';

const columns: WritableColumnProps<any>[] = [
  { title: '城市', dataIndex: 'cityName' },
  { title: '业务类型', dataIndex: 'bizmanType' },
  { title: '业务项目', dataIndex: 'areaCode' },
  { title: '业务内容', dataIndex: 'busCityConfigId' },
  { title: '办理属性', dataIndex: 'areaCode' },
  { title: '办理对象', dataIndex: 'areaCode' },
  { title: '姓名', dataIndex: 'empName' },
  { title: '证件号码', dataIndex: 'areaCode' },
  { title: '入离职状态', dataIndex: 'areaCode' },
  { title: '实做状态', dataIndex: 'areaCode' },
  { title: '是否收费业务', dataIndex: 'areaCode' },
  { title: '是否收取客户费用', dataIndex: 'areaCode' },
  { title: '收取金额', dataIndex: 'areaCode' },
  { title: '业务来源', dataIndex: 'areaCode' },
  { title: '业务状态', dataIndex: 'areaCode' },
  { title: '业务进度', dataIndex: 'areaCode' },
  { title: '办理结果', dataIndex: 'areaCode' },
  { title: '创建人', dataIndex: 'areaCode' },
  { title: '创建时间', dataIndex: 'areaCode' },
  { title: '派单地', dataIndex: 'areaCode' },
  { title: '项目客服', dataIndex: 'areaCode' },
  { title: '接单地', dataIndex: 'areaCode' },
  { title: '接单客服', dataIndex: 'areaCode' },
  { title: '后道客服', dataIndex: 'areaCode' },
];

interface BusinessProcessingProps {
  type: string;
  [props: string]: any;
}

const service = API.welfaremanage.ebmBusiness.searchApplications;
const delService = API.basedata.areaCode.remove;
const BusinessProcessing: React.FC<BusinessProcessingProps> = (props) => {
  const { type } = props;
  let options: WritableInstance;
  const [addVisible, setAddVisible] = useState<string | undefined>();
  const [submitVisible, setSubmitVisible] = useState<string | undefined>();
  const [singleRow, setSingleRow] = useState<POJO | undefined>({});

  const currentUserCityId = getCurrentUserCityId() || '';

  const formColumns: EditeFormProps[] = [
    { label: '城市', fieldName: 'cityId', inputRender: 'string', hidden: type !== 'receive' },

    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm) =>
        mapToSelectors(busTypeMap, {
          onChange: (value) => {
            outerForm.setFieldsValue({ busnameClassId: undefined });
          },
        }),
      //  rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
      inputRender: (outerForm) => {
        const { categoryId } = outerForm.getFieldsValue();
        return (
          <GetBaseBusnameClassDropdownList
            allowClear
            // keyMap={{ busnameClassName: 'busnameClassName', busnameClassId: 'busnameClassId' }}
            params={{
              pageNum: 1,
              pageSize: 2147483647,
              categoryId,
            }}
          />
        );
      },
      // rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务内容',
      fieldName: 'newRemark',
      inputRender: 'string',
    },
    { label: '办理属性', fieldName: 'areaName', inputRender: 'string' },
    { label: '办理对象', fieldName: 'areaName', inputRender: 'string' },
    { label: '办理方式', fieldName: 'areaName', inputRender: 'string' },
    { label: '客户名称', fieldName: 'areaName', inputRender: 'string' },
    { label: '客户规模', fieldName: 'areaName', inputRender: 'string' },
    { label: '唯一号', fieldName: 'areaName', inputRender: 'string' },
    { label: '姓名', fieldName: 'areaName', inputRender: 'string' },
    { label: '证件号码', fieldName: 'areaName', inputRender: 'string' },
    { label: '业务来源', fieldName: 'areaName', inputRender: 'string' },
    { label: '业务状态', fieldName: 'areaName', inputRender: 'string' },
    { label: '业务进度', fieldName: 'areaName', inputRender: 'string' },
    { label: '办理结果', fieldName: 'areaName', inputRender: 'string' },
  ];

  // 删除
  const handleDelete = (table: WritableInstance) => {
    table.handleDelete({
      service: delService,
      data: (value) => ({ areaIds: value.map((item: any) => item.areaId).join(',') }),
    });
  };

  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    const singleRow = options.selectedSingleRow;
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={() => setAddVisible(type)}>新增办理</Button>
        <Button
          onClick={() => {
            setSubmitVisible('submit');
            setSingleRow(singleRow);
          }}
        >
          进入办理
        </Button>
        <Button onClick={() => handleDelete(options)}>删除</Button>
        <Button onClick={() => handleDelete(options)}>导出数据</Button>
      </>
    );
  };
  return (
    <CachedPage
      service={service}
      columns={columns}
      formColumns={formColumns}
      renderButtons={renderButtons}
    >
      <AddCodal
        title="新增办理"
        visible={addVisible}
        hideHandle={(value: string) => {
          if (value) {
            options?.request(options.queries);
          }
          setAddVisible(undefined);
        }}
      />
      <SubmitCodal
        title="进入办理"
        visible={submitVisible}
        hideHandle={(value: string) => {
          if (value) {
            options?.request(options.queries);
          }
          setSubmitVisible(undefined);
          setSingleRow({});
        }}
        initialValues={singleRow}
      />
    </CachedPage>
  );
};

export default BusinessProcessing;
