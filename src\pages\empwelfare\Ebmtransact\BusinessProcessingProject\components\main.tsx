import React, { useState } from 'react';
import { Button } from 'antd';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { WritableColumnProps } from '@/utils/writable/types';
import { ConfirmButton } from '@/components/Forms/Confirm';
import { WritableInstance } from '@/components/Writable';
import AddCodal from './AddCodal';
import SubmitCodal from './SubmitCodal';

const columns: WritableColumnProps<any>[] = [
  { title: '城市', dataIndex: 'areaCode' },
  { title: '业务类型', dataIndex: 'areaCode' },
  { title: '业务项目', dataIndex: 'areaCode' },
  { title: '业务内容', dataIndex: 'areaCode' },
  { title: '办理属性', dataIndex: 'areaCode' },
  { title: '办理对象', dataIndex: 'areaCode' },
  { title: '姓名', dataIndex: 'areaCode' },
  { title: '证件号码', dataIndex: 'areaCode' },
  { title: '业务来源', dataIndex: 'areaCode' },
  { title: '业务状态', dataIndex: 'areaCode' },
  { title: '业务进度', dataIndex: 'areaCode' },
  { title: '办理结果', dataIndex: 'areaCode' },
  { title: '创建人', dataIndex: 'areaCode' },
  { title: '创建时间', dataIndex: 'areaCode' },
  { title: '派单地', dataIndex: 'areaCode' },
  { title: '项目客服', dataIndex: 'areaCode' },
  { title: '接单地', dataIndex: 'areaCode' },
  { title: '接单客服', dataIndex: 'areaCode' },
  { title: '后道客服', dataIndex: 'areaCode' },
];

interface BusinessProcessingProps {
  type: string;
  [props: string]: any;
}

const service = API.basedata.areaCode.list;
const delService = API.basedata.areaCode.remove;
const BusinessProcessing: React.FC<BusinessProcessingProps> = (props) => {
  const { type } = props;
  let options: WritableInstance;
  const [addVisible, setAddVisible] = useState<string | undefined>();
  const [submitVisible, setSubmitVisible] = useState<string | undefined>();
  const [singleRow, setSingleRow] = useState<POJO | undefined>({});

  const formColumns: EditeFormProps[] = [
    { label: '城市', fieldName: 'areaCode', inputRender: 'string', hidden: type !== 'receive' },

    { label: '业务类型', fieldName: 'areaCode', inputRender: 'string' },
    { label: '业务项目', fieldName: 'areaName', inputRender: 'string' },
    { label: '业务内容', fieldName: 'areaName', inputRender: 'string' },
    { label: '办理属性', fieldName: 'areaName', inputRender: 'string' },
    { label: '办理对象', fieldName: 'areaName', inputRender: 'string' },
    { label: '办理方式', fieldName: 'areaName', inputRender: 'string' },
    { label: '客户名称', fieldName: 'areaName', inputRender: 'string' },
    { label: '客户规模', fieldName: 'areaName', inputRender: 'string' },
    { label: '唯一号', fieldName: 'areaName', inputRender: 'string' },
    { label: '姓名', fieldName: 'areaName', inputRender: 'string' },
    { label: '证件号码', fieldName: 'areaName', inputRender: 'string' },
    { label: '业务来源', fieldName: 'areaName', inputRender: 'string' },
    { label: '业务状态', fieldName: 'areaName', inputRender: 'string' },
    { label: '业务进度', fieldName: 'areaName', inputRender: 'string' },
    { label: '办理结果', fieldName: 'areaName', inputRender: 'string' },
  ];

  // 删除
  const handleDelete = (table: WritableInstance) => {
    table.handleDelete({
      service: delService,
      data: (value) => ({ areaIds: value.map((item: any) => item.areaId).join(',') }),
    });
  };

  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    const singleRow = options.selectedSingleRow;
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={() => setAddVisible(type)}>新增办理</Button>
        <Button
          onClick={() => {
            setSubmitVisible('submit');
            setSingleRow(singleRow);
          }}
        >
          进入办理
        </Button>
        <Button onClick={() => handleDelete(options)}>删除</Button>
        <Button onClick={() => handleDelete(options)}>导出数据</Button>
      </>
    );
  };
  return (
    <CachedPage
      service={service}
      columns={columns}
      formColumns={formColumns}
      renderButtons={renderButtons}
    >
      <AddCodal
        title="新增办理"
        visible={addVisible}
        hideHandle={(value: string) => {
          if (value) {
            options?.request(options.queries);
          }
          setAddVisible(undefined);
        }}
      />
      <SubmitCodal
        title="进入办理"
        visible={submitVisible}
        hideHandle={(value: string) => {
          if (value) {
            options?.request(options.queries);
          }
          setSubmitVisible(undefined);
          setSingleRow({});
        }}
        initialValues={singleRow}
      />
    </CachedPage>
  );
};

export default BusinessProcessing;
