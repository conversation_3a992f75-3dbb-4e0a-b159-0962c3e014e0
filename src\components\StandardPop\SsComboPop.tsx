/*
 * @Author: tjj
 * @Email: <EMAIL>
 * @Date: 2025-07-22 17:24:37
 * @LastAuthor: tjj
 * @message:
 */
import React from 'react';
import { StandardPop, DerivedPopProps } from './libs/StdPop';
import { mapToSelectors } from '@/components/Selectors';
import { EditeFormProps } from '../CachedPage/EnumerateFields';

const columns = [
  {
    title: '所属城市',
    dataIndex: 'cityName',
  },
  {
    title: '社保套餐名称',
    dataIndex: 'ssComboName',
  },
  {
    title: '社保套餐编号',
    dataIndex: 'ssComboCode',
  },
  {
    title: '分公司/供应商',
    dataIndex: 'departmentName',
  },
  {
    title: '是否单立户',
    dataIndex: 'isIndependentName',
  },
  {
    title: '人员类型',
    dataIndex: 'categoryName',
  },
];

const SsComboPop: React.FC<DerivedPopProps<defs.commons.Page>> = (props) => {
  const { fixedValues } = props;

  const service = API.combo.comboHro.queryComboGroupList;

  const formColumns: EditeFormProps[] = [
    {
      label: '分公司/供应商',
      fieldName: 'branchId',
      inputProps: { disabled: true },
      inputRender: () =>
        mapToSelectors(new Map<any, any>([[fixedValues?.branchId, fixedValues?.branchName]])),
    },
    { label: '社保套餐名称', fieldName: 'ssComboName', inputRender: 'string' },
    { label: '社保套餐编号', fieldName: 'ssComboCode', inputRender: 'number' },
  ];

  return (
    <StandardPop
      columns={columns}
      service={service}
      formColumns={formColumns}
      fixedValues={fixedValues}
      modalwidth="70%"
      {...(props as any)}
    />
  );
};

export { SsComboPop };
