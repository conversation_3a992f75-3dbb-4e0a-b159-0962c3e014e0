declare namespace defs {
  export namespace information {
    export class AnnualPay {
      /** annualPayHisId */
      annualPayHisId: number;

      /** annualPayId */
      annualPayId: number;

      /** avgEndTime */
      avgEndTime: string;

      /** avgPayMon */
      avgPayMon: string;

      /** avgPayYear */
      avgPayYear: string;

      /** avgStartTime */
      avgStartTime: string;

      /** cityId */
      cityId: number;

      /** cityName */
      cityName: string;

      /** createBy */
      createBy: string;

      /** createDt */
      createDt: string;

      /** execEndTime */
      execEndTime: string;

      /** execStartTime */
      execStartTime: string;

      /** isDeleted */
      isDeleted: number;

      /** minHour1 */
      minHour1: number;

      /** minHour2 */
      minHour2: number;

      /** minHour3 */
      minHour3: number;

      /** minHour4 */
      minHour4: number;

      /** minHour5 */
      minHour5: number;

      /** minHour6 */
      minHour6: number;

      /** minHourCounty1 */
      minHourCounty1: string;

      /** minHourCounty2 */
      minHourCounty2: string;

      /** minHourCounty3 */
      minHourCounty3: string;

      /** minHourCounty4 */
      minHourCounty4: string;

      /** minHourCounty5 */
      minHourCounty5: string;

      /** minHourCounty6 */
      minHourCounty6: string;

      /** minMon1 */
      minMon1: number;

      /** minMon2 */
      minMon2: number;

      /** minMon3 */
      minMon3: number;

      /** minMon4 */
      minMon4: number;

      /** minMon5 */
      minMon5: number;

      /** minMon6 */
      minMon6: number;

      /** minMonCounty1 */
      minMonCounty1: string;

      /** minMonCounty2 */
      minMonCounty2: string;

      /** minMonCounty3 */
      minMonCounty3: string;

      /** minMonCounty4 */
      minMonCounty4: string;

      /** minMonCounty5 */
      minMonCounty5: string;

      /** minMonCounty6 */
      minMonCounty6: string;

      /** payIncludeCpf */
      payIncludeCpf: number;

      /** payIncludeCpfStr */
      payIncludeCpfStr: string;

      /** payIncludeSi */
      payIncludeSi: number;

      /** payIncludeSiStr */
      payIncludeSiStr: string;

      /** pcName */
      pcName: string;

      /** policyFileName */
      policyFileName: string;

      /** policyFileUrl */
      policyFileUrl: string;

      /** policyLink */
      policyLink: string;

      /** policyUser */
      policyUser: number;

      /** policyUserStr */
      policyUserStr: string;

      /** provinceId */
      provinceId: number;

      /** provinceName */
      provinceName: string;

      /** statisticsCaliber */
      statisticsCaliber: string;

      /** updateBy */
      updateBy: string;

      /** updateDt */
      updateDt: string;

      /** yearDate */
      yearDate: string;
    }

    export class CalculateDTO {
      /** comboId */
      comboId: number;

      /** pfBase */
      pfBase: number;

      /** pfRatio */
      pfRatio: string;

      /** pfRatioText */
      pfRatioText: string;

      /** spfBase */
      spfBase: number;

      /** spfRatio */
      spfRatio: string;

      /** spfRatioText */
      spfRatioText: string;

      /** ssBase */
      ssBase: number;
    }

    export class CityInfo {
      /** branchId */
      branchId: number;

      /** cityId */
      cityId: number;

      /** cityName */
      cityName: string;

      /** headCount */
      headCount: number;

      /** pfPayBase */
      pfPayBase: number;

      /** ssPayBase */
      ssPayBase: number;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class DropdownList {
      /** 业务大类类型 */
      btType: string;

      /** chargeRate */
      chargeRate: string;

      /** cityId */
      cityId: string;

      /** cityIdForParty */
      cityIdForParty: string;

      /** contractAvgAmt */
      contractAvgAmt: string;

      /** contractHeadcount */
      contractHeadcount: string;

      /** contractName */
      contractName: string;

      /** contractSubType */
      contractSubType: string;

      /** contractSubTypeName */
      contractSubTypeName: string;

      /** contractType */
      contractType: string;

      /** contractTypeName */
      contractTypeName: string;

      /** currentSalesName */
      currentSalesName: string;

      /** departmentName */
      departmentName: string;

      /** exFeeMonth */
      exFeeMonth: string;

      /** 供应商收费模板 */
      exFeeTempltId: string;

      /** governingArea */
      governingArea: string;

      /** 所属大区 */
      governingAreaId: string;

      /** governingBranch */
      governingBranch: string;

      /** 所属分公司 */
      governingBranchId: string;

      /** groupType */
      groupType: string;

      /** 主键 */
      key: string;

      /** liabilityCsName */
      liabilityCsName: string;

      /** 全称 */
      name: string;

      /** 拼音码 */
      pinYinCode: string;

      /** productLineId */
      productLineId: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 保留名字1 */
      reserveName1: string;

      /** 保留名字2 */
      reserveName2: string;

      /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
      reserveObj: string;

      /** 缩写名 */
      shortName: string;

      /** signBrachTitleId */
      signBrachTitleId: string;

      /** signBranchTitleName */
      signBranchTitleName: string;

      /** 社保组ID */
      ssGroupId: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;
    }

    export class Exception {
      /** cause */
      cause: defs.information.Throwable;

      /** localizedMessage */
      localizedMessage: string;

      /** message */
      message: string;

      /** stackTrace */
      stackTrace: Array<defs.information.StackTraceElement>;

      /** suppressed */
      suppressed: Array<defs.information.Throwable>;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class FilterEntity {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 城市id逗号分开 */
      cityIds: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 更新日期小于 */
      endDt: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 更新日期大于 */
      startDt: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class FringeBenefits {
      /** accordingFileNumber1 */
      accordingFileNumber1: string;

      /** accordingFileNumber2 */
      accordingFileNumber2: string;

      /** accordingFileNumber3 */
      accordingFileNumber3: string;

      /** benefitsId */
      benefitsId: number;

      /** busnameClassId */
      busnameClassId: number;

      /** busnameSubtypeId */
      busnameSubtypeId: number;

      /** busnameTypeId */
      busnameTypeId: number;

      /** categoryId */
      categoryId: number;

      /** cityId */
      cityId: number;

      /** cityName */
      cityName: string;

      /** createBy */
      createBy: number;

      /** createDt */
      createDt: string;

      /** crossCityHandle */
      crossCityHandle: number;

      /** crossCityHandleArea */
      crossCityHandleArea: string;

      /** crossProvinceHandle */
      crossProvinceHandle: number;

      /** crossProvinceHandleArea */
      crossProvinceHandleArea: string;

      /** crossRegionHandle */
      crossRegionHandle: number;

      /** crossRegionHandleArea */
      crossRegionHandleArea: string;

      /** effectiveDate1 */
      effectiveDate1: string;

      /** effectiveDate2 */
      effectiveDate2: string;

      /** effectiveDate3 */
      effectiveDate3: string;

      /** ehandleCondition */
      ehandleCondition: string;

      /** ehandleOfflineProcess */
      ehandleOfflineProcess: string;

      /** ehandleProcess1 */
      ehandleProcess1: string;

      /** ehandleProcess2 */
      ehandleProcess2: string;

      /** ehandleProcess3 */
      ehandleProcess3: string;

      /** fileName1 */
      fileName1: string;

      /** fileName2 */
      fileName2: string;

      /** fileName3 */
      fileName3: string;

      /** handleForm */
      handleForm: string;

      /** handleType */
      handleType: number;

      /** handleWindow1 */
      handleWindow1: string;

      /** handleWindow2 */
      handleWindow2: string;

      /** handleWindow3 */
      handleWindow3: string;

      /** isDeleted */
      isDeleted: number;

      /** makReservations */
      makReservations: number;

      /** orderStatus */
      orderStatus: number;

      /** otherHandleInfo1 */
      otherHandleInfo1: string;

      /** otherHandleInfo2 */
      otherHandleInfo2: string;

      /** otherHandleInfo3 */
      otherHandleInfo3: string;

      /** otherHandleInfo4 */
      otherHandleInfo4: string;

      /** otherHandleInfo5 */
      otherHandleInfo5: string;

      /** otherHandleInfo6 */
      otherHandleInfo6: string;

      /** otherPolicyInfo1 */
      otherPolicyInfo1: string;

      /** otherPolicyInfo2 */
      otherPolicyInfo2: string;

      /** otherPolicyInfo3 */
      otherPolicyInfo3: string;

      /** otherPolicyInfo4 */
      otherPolicyInfo4: string;

      /** otherPolicyInfo5 */
      otherPolicyInfo5: string;

      /** otherPolicyInfo6 */
      otherPolicyInfo6: string;

      /** otherPolicyInfo7 */
      otherPolicyInfo7: string;

      /** otherPolicyInfo8 */
      otherPolicyInfo8: string;

      /** otherPolicyInfo9 */
      otherPolicyInfo9: string;

      /** payee */
      payee: string;

      /** personCategoryId */
      personCategoryId: string;

      /** phandleCondition */
      phandleCondition: string;

      /** phandleOfflineProcess */
      phandleOfflineProcess: string;

      /** phandleProcess1 */
      phandleProcess1: string;

      /** phandleProcess2 */
      phandleProcess2: string;

      /** phandleProcess3 */
      phandleProcess3: string;

      /** policyFileId1 */
      policyFileId1: string;

      /** policyFileId2 */
      policyFileId2: string;

      /** policyFileId3 */
      policyFileId3: string;

      /** policyFileName1 */
      policyFileName1: string;

      /** policyFileName2 */
      policyFileName2: string;

      /** policyFileName3 */
      policyFileName3: string;

      /** policySource1 */
      policySource1: string;

      /** policySource2 */
      policySource2: string;

      /** policySource3 */
      policySource3: string;

      /** policyUrl1 */
      policyUrl1: string;

      /** policyUrl2 */
      policyUrl2: string;

      /** policyUrl3 */
      policyUrl3: string;

      /** processDifference */
      processDifference: string;

      /** ssStatus */
      ssStatus: number;

      /** statutoryDeadline */
      statutoryDeadline: string;

      /** supplementaryInfo1 */
      supplementaryInfo1: string;

      /** supplementaryInfo2 */
      supplementaryInfo2: string;

      /** supplementaryInfo3 */
      supplementaryInfo3: string;

      /** supplementaryInfo4 */
      supplementaryInfo4: string;

      /** supplementaryInfo5 */
      supplementaryInfo5: string;

      /** supplementaryInfo6 */
      supplementaryInfo6: string;

      /** termsContent1 */
      termsContent1: string;

      /** termsContent2 */
      termsContent2: string;

      /** termsContent3 */
      termsContent3: string;

      /** tollHandle */
      tollHandle: number;

      /** tollStandard */
      tollStandard: string;

      /** updateBy */
      updateBy: number;

      /** updateDt */
      updateDt: string;

      /** windowAddress */
      windowAddress: string;
    }

    export class FringeBenefitsQuery {
      /** benefitsId */
      benefitsId: number;

      /** busnameClassId */
      busnameClassId: number;

      /** busnameSubtypeId */
      busnameSubtypeId: number;

      /** busnameTypeId */
      busnameTypeId: number;

      /** categoryId */
      categoryId: number;

      /** cityId */
      cityId: string;

      /** custId */
      custId: number;

      /** custName */
      custName: string;

      /** empId */
      empId: number;

      /** isCustAdmin */
      isCustAdmin: number;

      /** length */
      length: number;

      /** name */
      name: string;

      /** pageNum */
      pageNum: number;

      /** pageSize */
      pageSize: number;

      /** personCategoryId */
      personCategoryId: string;

      /** start */
      start: number;

      /** subtypeId */
      subtypeId: number;

      /** typeId */
      typeId: number;

      /** userId */
      userId: number;
    }

    export class Map<T0 = any, T1 = any> {}

    export class OnceChargeDTO {
      /** 户口管理费 */
      accountManagementFee: string;

      /** 费用ID */
      chargeId: number;

      /** 城市 */
      cityId: number;

      /** 合同费 */
      contractFee: string;

      /** 生育卡证 */
      feCard: string;

      /** 档案费 */
      fileFee: string;

      /** 医疗卡证 */
      hcCard: string;

      /** 招工费 */
      hiringFee: string;

      /** 劳动年审 */
      laborYearCareful: string;

      /** 工会费 */
      labourUnionFee: string;

      /** 流动调配费 */
      mobileDeployFee: string;

      /** 工本费 */
      nominalFee: string;

      /** 养老卡证 */
      oasCard: string;

      /** 其他当地收取的一次性费用1 */
      otherFee1: string;

      /** 其他当地收取的一次性费用2 */
      otherFee2: string;

      /** 其他当地收取的一次性费用3 */
      otherFee3: string;

      /** 其他当地收取的一次性费用4 */
      otherFee4: string;

      /** 其他当地收取的一次性费用5 */
      otherFee5: string;

      /** 其他当地收取的一次性费用6 */
      otherFee6: string;

      /** 公积金卡证 */
      pfCard: string;

      /** 政策维护人:2自营分公司网点维护、3供应商网点维护 */
      policyUser: number;

      /** 社保卡证 */
      ssCard: string;

      /** 失业卡证 */
      ueCard: string;
    }

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }

    export class PolicyDTO {
      /** 分公司Id */
      branchId: string;

      /** 城市Id */
      cityId: string;

      /** 城市名称 */
      cityName: string;

      /** 客户ID */
      custId: string;
    }

    export class PolicyDisabled {
      /** advanceAmount */
      advanceAmount: string;

      /** cityId */
      cityId: number;

      /** cityName */
      cityName: string;

      /** collectionUnits */
      collectionUnits: number;

      /** collectionUnitsStr */
      collectionUnitsStr: string;

      /** createBy */
      createBy: string;

      /** createDt */
      createDt: string;

      /** declareTime */
      declareTime: string;

      /** disabledSecureHisId */
      disabledSecureHisId: number;

      /** disabledSecureId */
      disabledSecureId: number;

      /** estimate */
      estimate: string;

      /** isDeleted */
      isDeleted: number;

      /** levyFrequency */
      levyFrequency: number;

      /** levyFrequencyStr */
      levyFrequencyStr: string;

      /** levyScope */
      levyScope: string;

      /** levyStandard */
      levyStandard: string;

      /** levyTarget */
      levyTarget: number;

      /** levyTargetStr */
      levyTargetStr: string;

      /** overduePay */
      overduePay: string;

      /** payTime */
      payTime: string;

      /** policyFileName */
      policyFileName: string;

      /** policyFileUrl */
      policyFileUrl: string;

      /** policyLink */
      policyLink: string;

      /** policyUser */
      policyUser: number;

      /** policyUserStr */
      policyUserStr: string;

      /** updateBy */
      updateBy: string;

      /** updateDt */
      updateDt: string;

      /** yearDate */
      yearDate: string;
    }

    export class PolicyInfoApproveQuery {
      /** add */
      add: boolean;

      /** 审批通过日期到 */
      applyDtEnd: string;

      /** 审批通过日期从 */
      applyDtStart: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 是否可批量导出0否1是 */
      canBatchExport: number;

      /** 城市id */
      cityId: number;

      /** 城市属性（1:空,2:自营,3:供应商 ） */
      cityProperty: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 维护人 */
      creater: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 生效结束时间 */
      effectiveDateEnd: string;

      /** 生效开始时间 */
      effectiveDateStart: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 失效结束时间 */
      expirationDateDateEnd: string;

      /** 失效开始时间 */
      expirationDateDateStart: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 生效状态 0初始 1待生效 2 生效 3失效 */
      infoState: number;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 标签id集合 */
      labelIds: Array<string>;

      /** 一级分类id */
      level1Id: number;

      /** 二级分类id */
      level2Id: number;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 参与人 */
      participant: string;

      /** 政策审批权限（1政策管理部审批、2省份维护人审批、3无需审批 4大区政策负责人审批） */
      policyApprovalAuth: number;

      /** 政策详情编号 */
      policyInfoCode: string;

      /** 供应商政策审批权限(0无需审批、1供应商政策区域负责人审批) */
      policyPrvdApprovalAuth: number;

      /** 关联模板Id */
      policyTemplateId: string;

      /** 所属年份 */
      policyTemplateInfoYear: string;

      /** 政策标题Id */
      policyTitleId: string;

      /** 更新结束开始时间 */
      policyUpdateDateEnd: string;

      /** 更新日期开始时间 */
      policyUpdateDateStart: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 省份id */
      provinceId: number;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 发布日期结束时间 */
      publishDateEnd: string;

      /** 发布日期开始时间 */
      publishDateStart: string;

      /** 发布状态:0初始、1待发布、2历史发布、3最新发布 */
      publishStatus: number;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 服务类型 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
      serviceType: number;

      /** 特区id */
      specialAreaId: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 政策详情名称 */
      templateInfoName: string;

      /** 适用范围:1国家 2省份3城市4特区 */
      templateScope: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class PolicyLabelDto {
      /** 主键 */
      policyLabelId: number;

      /** 标签名称 */
      policyLabelName: string;

      /** 是否有效（0：失效 ，1：有效） */
      state: 0 | 1 | 0 | 1;
    }

    export class PolicyLabelQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 标签名称 */
      policyLabelName: string;

      /** startIndex */
      startIndex: number;

      /** 是否有效（0：失效 ，1：有效） */
      state: number;
    }

    export class PolicyLevelDto {
      /** 客户端是否显示 0：不显示,1：显示 */
      clientShowState: number;

      /** levelIndex */
      levelIndex: number;

      /** parentId */
      parentId: number;

      /** policyLevelId */
      policyLevelId: number;

      /** policyLevelName */
      policyLevelName: string;

      /** wx端是否显示 0：不显示,1：显示 */
      wxShowState: number;
    }

    export class PolicyLevelQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 父级id */
      parentId: number;

      /** 层级名称 */
      policyLevelName: string;

      /** startIndex */
      startIndex: number;
    }

    export class PolicyProvinceUserQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 省份id ,分隔 */
      provinceIds: string;

      /** startIndex */
      startIndex: number;

      /** 1有效 0无效 */
      state: string;
    }

    export class PolicySearchQuery {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 城市id集合 */
      cityIds: Array<string>;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户 */
      custId: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 生效日期到 */
      effectiveDateEnd: string;

      /** 生效日期从 */
      effectiveDateStart: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 失效日期到 */
      expirationDateEnd: string;

      /** 失效日期从 */
      expirationDateStart: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 生效状态 0初始 1待生效 2 生效 3失效 */
      infoState: number;

      /** 是否全部城市:0否，1是 */
      isAllCity: number;

      /** 是否全部省份:0否，1是 */
      isAllProvince: number;

      /** 是否全部特区i:0否，1是 */
      isAllSpecialArea: number;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 标签id集合 */
      labelIds: Array<string>;

      /** 一级分类id */
      level1Id: number;

      /** 二级分类id */
      level2Id: number;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 政策编号 */
      policyInfoCode: string;

      /** 所属年份 */
      policyTemplateInfoYear: string;

      /** 政策标题Id */
      policyTitleId: string;

      /** 更新日期到 */
      policyUpdateDateEnd: string;

      /** 更新日期从 */
      policyUpdateDateStart: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 省份id集合 */
      provinceIds: Array<string>;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 发布日期到 */
      publishDateEnd: string;

      /** 发布日期从 */
      publishDateStart: string;

      /** 适用范围:1国家 2省份3城市4特区 */
      queryScopes: Array<string>;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 服务类型 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
      serviceType: number;

      /** 特区id集合 */
      specialAreaIds: Array<string>;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class PolicySpecialArea {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** provinceId */
      provinceId: string;

      /** provinceName */
      provinceName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** specialAreaId */
      specialAreaId: string;

      /** specialAreaName */
      specialAreaName: string;

      /** specialAreaNumber */
      specialAreaNumber: string;

      /** startIndex */
      startIndex: number;

      /** state */
      state: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class PolicySpecialAreaDto {
      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** provinceId */
      provinceId: string;

      /** provinceName */
      provinceName: string;

      /** specialAreaId */
      specialAreaId: string;

      /** specialAreaName */
      specialAreaName: string;

      /** specialAreaNumber */
      specialAreaNumber: string;

      /** state */
      state: number;
    }

    export class PolicySpecialAreaQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** specialAreaName */
      specialAreaName: string;

      /** startIndex */
      startIndex: number;

      /** state */
      state: number;
    }

    export class PolicyTemplateInfoQuery {
      /** add */
      add: boolean;

      /** 审批通过日期到 */
      applyDtEnd: string;

      /** 审批通过日期从 */
      applyDtStart: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 是否可批量导出0否1是 */
      canBatchExport: number;

      /** 城市id */
      cityId: number;

      /** 城市属性（1:空,2:自营,3:供应商 ） */
      cityProperty: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 维护人 */
      creater: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 生效结束时间 */
      effectiveDateEnd: string;

      /** 生效开始时间 */
      effectiveDateStart: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 失效结束时间 */
      expirationDateDateEnd: string;

      /** 失效开始时间 */
      expirationDateDateStart: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 生效状态 0初始 1待生效 2 生效 3失效 */
      infoState: number;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 标签id集合 */
      labelIds: Array<string>;

      /** 一级分类id */
      level1Id: number;

      /** 二级分类id */
      level2Id: number;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 审批人 */
      participant: string;

      /** 政策审批权限（1政策管理部审批、2省份维护人审批、3无需审批 4大区政策负责人审批） */
      policyApprovalAuth: number;

      /** 政策详情编号 */
      policyInfoCode: string;

      /** 供应商政策审批权限(0无需审批、1供应商政策区域负责人审批) */
      policyPrvdApprovalAuth: number;

      /** 关联模板Id */
      policyTemplateId: string;

      /** 所属年份 */
      policyTemplateInfoYear: string;

      /** 政策标题Id */
      policyTitleId: string;

      /** 更新结束开始时间 */
      policyUpdateDateEnd: string;

      /** 更新日期开始时间 */
      policyUpdateDateStart: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 省份id */
      provinceId: number;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 发布日期结束时间 */
      publishDateEnd: string;

      /** 发布日期开始时间 */
      publishDateStart: string;

      /** 发布状态:0初始、1待发布、2历史发布、3最新发布 */
      publishStatus: number;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 服务类型 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
      serviceType: number;

      /** 特区id */
      specialAreaId: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 政策详情名称 */
      templateInfoName: string;

      /** 适用范围:1国家 2省份3城市4特区 */
      templateScope: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class PolicyTemplateQuery {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 是否可以批量导出0否 1是 */
      canBatchExport: number;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
      serviceType: number;

      /** startIndex */
      startIndex: number;

      /** 是否有效(0：失效,1:有效) */
      state: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 模板编号 */
      templateCode: string;

      /** 模板名称 */
      templateName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class PolicyTitleDto {
      /** aiServiceLocation */
      aiServiceLocation: string;

      /** busNameClsaaName */
      busNameClsaaName: string;

      /** busnameClassId */
      busnameClassId: number;

      /** clientShowState */
      clientShowState: number;

      /** hroShowState */
      hroShowState: number;

      /** isDownPdf */
      isDownPdf: number;

      /** isLock */
      isLock: number;

      /** isMailPdf */
      isMailPdf: number;

      /** level1Id */
      level1Id: number;

      /** level1Name */
      level1Name: string;

      /** level2Id */
      level2Id: number;

      /** level2Name */
      level2Name: string;

      /** policyApprovalAuth */
      policyApprovalAuth: number;

      /** policyLableIds */
      policyLableIds: string;

      /** policyLableNames */
      policyLableNames: string;

      /** policyPrvdApprovalAuth */
      policyPrvdApprovalAuth: number;

      /** policyTemplateId */
      policyTemplateId: number;

      /** policyTitle */
      policyTitle: string;

      /** policyTitleId */
      policyTitleId: number;

      /** policyTitleScope */
      policyTitleScope: number;

      /** serviceType */
      serviceType: number;

      /** state */
      state: number;

      /** templateName */
      templateName: string;

      /** updateNoticeDay */
      updateNoticeDay: number;

      /** updateNoticeType */
      updateNoticeType: number;

      /** wxShowState */
      wxShowState: number;
    }

    export class PolicyTitleQuery {
      /** aiServiceLocation */
      aiServiceLocation: string;

      /** busnameClassId */
      busnameClassId: number;

      /** endIndex */
      endIndex: number;

      /** isDownPdf */
      isDownPdf: number;

      /** isLock */
      isLock: number;

      /** isMailPdf */
      isMailPdf: number;

      /** level1Id */
      level1Id: number;

      /** level2Id */
      level2Id: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** policyApprovalAuth */
      policyApprovalAuth: number;

      /** policyLableIds */
      policyLableIds: string;

      /** policyPrvdApprovalAuth */
      policyPrvdApprovalAuth: number;

      /** policyTemplateId */
      policyTemplateId: number;

      /** policyTitle */
      policyTitle: string;

      /** policyTitleScope */
      policyTitleScope: number;

      /** serviceType */
      serviceType: number;

      /** startIndex */
      startIndex: number;

      /** state */
      state: number;

      /** updateNoticeType */
      updateNoticeType: number;
    }

    export class QueryAnnualPay {
      /** cityIds */
      cityIds: string;

      /** custId */
      custId: number;

      /** empId */
      empId: number;

      /** isCustAdmin */
      isCustAdmin: number;

      /** length */
      length: number;

      /** name */
      name: string;

      /** pageNum */
      pageNum: number;

      /** pageSize */
      pageSize: number;

      /** provinceId */
      provinceId: string;

      /** provinceIds */
      provinceIds: string;

      /** start */
      start: number;

      /** updateDtEnd */
      updateDtEnd: string;

      /** updateDtStr */
      updateDtStr: string;

      /** userId */
      userId: number;

      /** yearDateEnd */
      yearDateEnd: string;

      /** yearDateStr */
      yearDateStr: string;
    }

    export class QueryPolicyDisabled {
      /** cityIds */
      cityIds: string;

      /** custId */
      custId: number;

      /** empId */
      empId: number;

      /** isCustAdmin */
      isCustAdmin: number;

      /** length */
      length: number;

      /** name */
      name: string;

      /** pageNum */
      pageNum: number;

      /** pageSize */
      pageSize: number;

      /** start */
      start: number;

      /** updateDtEnd */
      updateDtEnd: string;

      /** updateDtStr */
      updateDtStr: string;

      /** userId */
      userId: number;

      /** yearDateEnd */
      yearDateEnd: string;

      /** yearDateStr */
      yearDateStr: string;
    }

    export class Result {
      /** code */
      code: number;

      /** error */
      error: string;

      /** exception */
      exception: defs.information.Exception;

      /** message */
      message: string;

      /** object */
      object: object;
    }

    export class ServicePointDTO {
      /** 增员材料 */
      addEmpMaterial: string;

      /** 大区 */
      area: string;

      /** 接单方id */
      assigneeProviderId: string;

      /** 账单收费规则:1 预付;2每月付 */
      billFeeRule: string;

      /** 城市ID */
      cityId: string;

      /** cityLevel */
      cityLevel: string;

      /** cityLevelCN */
      cityLevelCN: string;

      /** cityName */
      cityName: string;

      /** 联系人(客服) */
      contactName: string;

      /** 联系电话 */
      contactTel: string;

      /** 离职补差规则 */
      dismissMakeupRule: string;

      /** 离职补差起始月 */
      dismissMakeupSatartMon: string;

      /** 是否有效 */
      isDeleted: string;

      /** 是否离职补差 */
      isDismissMakeup: string;

      /** 大户所在区 */
      largeAccountArea: string;

      /** 机构类别:1 自营分公司; 2 供应商 */
      organizationType: string;

      /** 补交材料 */
      payInbackMaterial: string;

      /** provinceName */
      provinceName: string;

      /** 减员材料 */
      reduceEmpMaterial: string;

      /** 备注 */
      remark: string;

      /** 网点服务名称(接单方) */
      serviceAssigneeName: string;

      /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
      serviceBranchFlag: string;

      /** 网点服务名称(集团) */
      serviceGroupName: string;

      /** 主键 */
      servicePointId: string;

      /** 公积金申报频率 */
      sfApplicationFrequency: string;

      /** 公积基金中心当月可操作时间段 */
      sfOperateTime: string;

      /** 当地单立户可操作区县 */
      singleAccountArea: string;

      /** 社保申报频率 */
      ssApplicationFrequency: string;

      /** 社保当月可操作时间段 */
      ssOperateTime: string;
    }

    export class StackTraceElement {
      /** className */
      className: string;

      /** fileName */
      fileName: string;

      /** lineNumber */
      lineNumber: number;

      /** methodName */
      methodName: string;

      /** nativeMethod */
      nativeMethod: boolean;
    }

    export class StatutoryHolidayDTO {
      /** 怀孕满4个月流产 */
      after4Miscarriage: string;

      /** 年假 */
      annualVacation: string;

      /** 怀孕未满4个月流产 */
      before4Miscarriage: string;

      /** 哺乳假 */
      breastfeedingLeave: string;

      /** 城市 */
      cityId: number;

      /** 难产 */
      dystocia: string;

      /** 顺产 */
      eutocia: string;

      /** 丧假 */
      funeralLeave: string;

      /** 当地额外奖励假（男职工） */
      manBonusLeave: string;

      /** 育儿假（男职工） */
      manParentalLeave: string;

      /** 婚假 */
      marriageLeave: string;

      /** 生育多胞胎 */
      multipleBirths: string;

      /** 其他法定假期1 */
      otherLeave1: string;

      /** 其他法定假期2 */
      otherLeave2: string;

      /** 其他法定假期3 */
      otherLeave3: string;

      /** 其他法定假期4 */
      otherLeave4: string;

      /** 其他法定假期5 */
      otherLeave5: string;

      /** 其他法定假期6 */
      otherLeave6: string;

      /** 陪产假 */
      paternityLeave: string;

      /** 政策维护人:2自营分公司网点维护、3供应商网点维护 */
      policyUser: number;

      /** 病假 */
      sickLeave: string;

      /** 法定假日ID */
      statutoryHolidayId: number;

      /** 未婚员工探望父母 */
      unVisitParents: string;

      /** 已婚员工探望父母 */
      visitParents: string;

      /** 探望配偶 */
      visitsSpouse: string;

      /** 当地额外奖励假（女职工） */
      womanBonusLeave: string;

      /** 育儿假（女职工） */
      womanParentalLeave: string;
    }

    export class TemplateFieldResp {
      /** aiServiceLocation */
      aiServiceLocation: string;

      /** 是否可批量导出 0否 1是 */
      canBatchExport: number;

      /** 客户端是否显示 0：不显示,1：显示 */
      clientShowState: number;

      /** 字段编码 */
      fieldCode: string;

      /** 字段名称 */
      fieldName: string;

      /** HRO端是否显示 0：不显示,1：显示 */
      hroShowState: number;

      /** 是否删除 0否 1是 */
      isDeleted: string;

      /** 字段是否必填 0否 1是 */
      isMust: number;

      /** 字段选项 */
      items: string;

      /** 政策模板字段ID */
      policyTemplateFieldId: string;

      /** 政策模板分组ID */
      policyTemplateGroupId: string;

      /** remark */
      remark: string;

      /** 显示顺序 */
      seqNum: number;

      /** 模板状态  状态:0初始 1有效 2无效 */
      templateState: number;

      /** 字段类型:1.文本 2.多行文本 3.日期 4.下拉菜单 5.链接 6.附件 7.多选项 8.数字(整数) 9.数字(小数) */
      type: number;

      /** wx端是否显示 0：不显示,1：显示 */
      wxShowState: number;
    }

    export class TemplateFieldValueResp {
      /** 字段值 */
      fieldValue: string;

      /** 附件名称 */
      fileName: string;

      /** 是否删除 0否 1是 */
      isDeleted: string;

      /** 政策模板字段ID */
      policyTemplateFieldId: string;

      /** 政策模板字段值ID */
      policyTemplateFieldValueId: string;

      /** 政策模板详情ID */
      policyTemplateInfoId: string;

      /** 特区ID */
      specialAreaId: string;

      /** 模板版本 */
      tpVersion: string;

      /** 超链接名称 */
      urlName: string;
    }

    export class TemplateGroupResp {
      /** aiServiceLocation */
      aiServiceLocation: string;

      /** 客户端是否显示 0：不显示,1：显示 */
      clientShowState: number;

      /** 字段列表 */
      fields: Array<defs.information.TemplateFieldResp>;

      /** 分组编号 */
      groupCode: string;

      /** HRO端是否显示 0：不显示,1：显示 */
      hroShowState: number;

      /** 是否删除 0否 1是 */
      isDeleted: string;

      /** 政策模板分组ID */
      policyTemplateGroupId: string;

      /** 政策模板ID */
      policyTemplateId: string;

      /** 显示顺序 */
      seqNum: number;

      /** 分组名称 */
      templateGroupName: string;

      /** 模板状态  状态:0初始 1有效 2无效 */
      templateState: number;

      /** wx端是否显示 0：不显示,1：显示 */
      wxShowState: number;
    }

    export class TemplateInfoAddReq {
      /** 关联业务名称ID */
      busnameClassId: string;

      /** 关联业务名称 */
      busnameClassName: string;

      /** 城市ID */
      cityId: string;

      /** 生效日期 */
      effectiveDate: string;

      /** 邮件附件 */
      emailAttachment: string;

      /** 邮件附件名称 */
      emailAttachmentName: string;

      /** 邮件内容 */
      emailContent: string;

      /** 邮件标题 */
      emailTitle: string;

      /** 失效日期 */
      expirationDate: string;

      /** 链接地址 */
      infoUrl: string;

      /** 是否内部分公司 0否1是 */
      internalSubsidiary: number;

      /** 政策详情PDF */
      pdfUrl: string;

      /** 政策审批权限（1政策管理部审批、2省份维护人审批、3无需审批 4大区政策负责人审批） */
      policyApprovalAuth: number;

      /** 供应商政策审批权限(0无需审批、1供应商政策区域负责人审批) */
      policyPrvdApprovalAuth: number;

      /** 政策详情字段值list */
      policyTemplateFieldValueList: Array<defs.information.TemplateFieldValueResp>;

      /** 关联模板ID */
      policyTemplateId: string;

      /** 政策详情ID */
      policyTemplateInfoId: string;

      /** 所属年份 */
      policyTemplateInfoYear: number;

      /** 关联标题ID */
      policyTitleId: string;

      /** 政策更新时间 */
      policyUpdateDate: string;

      /** 省份ID */
      provinceId: string;

      /** 发布状态:0初始、1待发布、2历史发布、3最新发布 */
      publishStatus: number;

      /** 收件人邮箱(多个用逗号分割) */
      recipientEmail: string;

      /** 服务类型 */
      serviceType: number;

      /** 服务类型名称 */
      serviceTypeName: string;

      /** 特区ID */
      specialAreaId: string;

      /** 是否支持单位办理 */
      supportedCorporate: number;

      /** 是否支持个人办理 */
      supportedIndividual: number;

      /** 政策详情名称 */
      templateInfoName: string;

      /** 适用范围:1国家 2省份3城市4特区 */
      templateScope: number;

      /** 版本号 */
      tpVersion: string;

      /** 人工更新周期 */
      updateCycle: number;

      /** 更新说明 */
      updateNotes: string;
    }

    export class TemplateInfoApproveReq {
      /** 审批意见 */
      approveOpinion: string;

      /** 关联业务名称ID */
      busnameClassId: string;

      /** 关联业务名称 */
      busnameClassName: string;

      /** 城市ID */
      cityId: string;

      /** 生效日期 */
      effectiveDate: string;

      /** 邮件附件 */
      emailAttachment: string;

      /** 邮件附件名称 */
      emailAttachmentName: string;

      /** 邮件内容 */
      emailContent: string;

      /** 邮件标题 */
      emailTitle: string;

      /** 失效日期 */
      expirationDate: string;

      /** 链接地址 */
      infoUrl: string;

      /** 是否内部分公司 0否1是 */
      internalSubsidiary: number;

      /** 政策详情PDF */
      pdfUrl: string;

      /** 政策审批权限（1政策管理部审批、2省份维护人审批、3无需审批 4大区政策负责人审批） */
      policyApprovalAuth: number;

      /** 供应商政策审批权限(0无需审批、1供应商政策区域负责人审批) */
      policyPrvdApprovalAuth: number;

      /** 政策详情字段值list */
      policyTemplateFieldValueList: Array<defs.information.TemplateFieldValueResp>;

      /** 关联模板ID */
      policyTemplateId: string;

      /** 政策详情ID */
      policyTemplateInfoId: string;

      /** 所属年份 */
      policyTemplateInfoYear: number;

      /** 关联标题ID */
      policyTitleId: string;

      /** 政策更新时间 */
      policyUpdateDate: string;

      /** 省份ID */
      provinceId: string;

      /** 发布状态:0初始、1待发布、2历史发布、3最新发布 */
      publishStatus: number;

      /** 收件人邮箱(多个用逗号分割) */
      recipientEmail: string;

      /** 服务类型 */
      serviceType: number;

      /** 服务类型名称 */
      serviceTypeName: string;

      /** 特区ID */
      specialAreaId: string;

      /** 是否支持单位办理 */
      supportedCorporate: number;

      /** 是否支持个人办理 */
      supportedIndividual: number;

      /** 政策详情名称 */
      templateInfoName: string;

      /** 适用范围:1国家 2省份3城市4特区 */
      templateScope: number;

      /** 版本号 */
      tpVersion: string;

      /** 人工更新周期 */
      updateCycle: number;

      /** 更新说明 */
      updateNotes: string;

      /** workItemId */
      workItemId: string;
    }

    export class TemplateInfoPubReq {
      /** 邮件附件 */
      emailAttachment: string;

      /** 邮件内容 */
      emailContent: string;

      /** 邮件标题 */
      emailTitle: string;

      /** 链接地址 */
      infoUrl: string;

      /** 是否发送邮件0否、1是 */
      isSendEmail: number;

      /** 政策模板详情ID */
      policyTemplateInfoId: string;

      /** 收件人邮箱(多个用逗号分割) */
      recipientEmail: string;

      /** 更新说明 */
      updateNotes: string;
    }

    export class TemplateInfoUpdateReq {
      /** 政策详情ID */
      policyTemplateInfoId: string;

      /** 更新时间 */
      policyUpdateDate: string;
    }

    export class TemplateReq {
      /** 是否可以批量导出0否 1是 */
      canBatchExport: number;

      /** 模板ID 修改查询必填 */
      policyTemplateId: string;

      /** reminderTime */
      reminderTime: string;

      /** 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
      serviceType: number;

      /** 状态:0初始 1有效 2无效 修改选填 */
      state: number;

      /** 模板名称 新增必填 修改选填 */
      templateName: string;

      /** 模板版本 */
      tpVersion: string;
    }

    export class TemplateSortItem {
      /** 主键 */
      id: string;

      /** seqNum */
      seqNum: number;
    }

    export class TemplateSortReq {
      /** 排序内容 */
      items: Array<defs.information.TemplateSortItem>;

      /** 1分组排序 2字段排序 */
      type: number;
    }

    export class Throwable {
      /** cause */
      cause: defs.information.Throwable;

      /** localizedMessage */
      localizedMessage: string;

      /** message */
      message: string;

      /** stackTrace */
      stackTrace: Array<defs.information.StackTraceElement>;

      /** suppressed */
      suppressed: Array<defs.information.Throwable>;
    }

    export class servicePointQuery {
      /** 大区 */
      area: string;

      /** 接单方id */
      assigneeProviderId: string;

      /** 城市 */
      cityId: string;

      /** cityLevel */
      cityLevel: string;

      /** cityLevelCN */
      cityLevelCN: string;

      /** 城市名称 */
      cityName: string;

      /** 联系人(客服) */
      contactName: string;

      /** 联系电话 */
      contactTel: string;

      /** 网点分公司id */
      departmentId: string;

      /** 网点分公司id */
      departmentName: string;

      /** endIndex */
      endIndex: number;

      /** 是否有效 */
      isDeleted: string;

      /** 机构类别:1 自营分公司; 2 供应商 */
      organizationType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 省份 */
      provinceName: string;

      /** 网点服务名称(接单方) */
      serviceAssigneeName: string;

      /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
      serviceBranchFlag: string;

      /** 网点服务名称(集团) */
      serviceGroupName: string;

      /** startIndex */
      startIndex: number;
    }
  }
}

declare namespace API {
  export namespace information {
    /**
     * 年度工资标准一览(省份/城市)
     */
    export namespace annualPay {
      /**
        * 导出
导出
        * /annualPay/exporter
        */
      export namespace exporter {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.QueryAnnualPay,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.QueryAnnualPay,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增
新增
        * /annualPay/insert
        */
      export namespace insert {
        export class Params {
          /** undefined */
          annualPayHisId?: any;
          /** undefined */
          annualPayId?: any;
          /** undefined */
          avgEndTime?: any;
          /** undefined */
          avgPayMon?: any;
          /** undefined */
          avgPayYear?: any;
          /** undefined */
          avgStartTime?: any;
          /** undefined */
          cityId?: any;
          /** undefined */
          cityName?: any;
          /** undefined */
          createBy?: any;
          /** undefined */
          createDt?: any;
          /** undefined */
          execEndTime?: any;
          /** undefined */
          execStartTime?: any;
          /** file */
          file?: File;
          /** undefined */
          isDeleted?: any;
          /** undefined */
          minHour1?: any;
          /** undefined */
          minHour2?: any;
          /** undefined */
          minHour3?: any;
          /** undefined */
          minHour4?: any;
          /** undefined */
          minHour5?: any;
          /** undefined */
          minHour6?: any;
          /** undefined */
          minHourCounty1?: any;
          /** undefined */
          minHourCounty2?: any;
          /** undefined */
          minHourCounty3?: any;
          /** undefined */
          minHourCounty4?: any;
          /** undefined */
          minHourCounty5?: any;
          /** undefined */
          minHourCounty6?: any;
          /** undefined */
          minMon1?: any;
          /** undefined */
          minMon2?: any;
          /** undefined */
          minMon3?: any;
          /** undefined */
          minMon4?: any;
          /** undefined */
          minMon5?: any;
          /** undefined */
          minMon6?: any;
          /** undefined */
          minMonCounty1?: any;
          /** undefined */
          minMonCounty2?: any;
          /** undefined */
          minMonCounty3?: any;
          /** undefined */
          minMonCounty4?: any;
          /** undefined */
          minMonCounty5?: any;
          /** undefined */
          minMonCounty6?: any;
          /** undefined */
          payIncludeCpf?: any;
          /** undefined */
          payIncludeCpfStr?: any;
          /** undefined */
          payIncludeSi?: any;
          /** undefined */
          payIncludeSiStr?: any;
          /** undefined */
          pcName?: any;
          /** undefined */
          policyFileName?: any;
          /** undefined */
          policyFileUrl?: any;
          /** undefined */
          policyLink?: any;
          /** undefined */
          policyUser?: any;
          /** undefined */
          policyUserStr?: any;
          /** undefined */
          provinceId?: any;
          /** undefined */
          provinceName?: any;
          /** undefined */
          statisticsCaliber?: any;
          /** undefined */
          updateBy?: any;
          /** undefined */
          updateDt?: any;
          /** undefined */
          yearDate?: any;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询
查询
        * /annualPay/select
        */
      export namespace select {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<
            Array<defs.information.AnnualPay>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.QueryAnnualPay,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.QueryAnnualPay,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改
修改
        * /annualPay/update
        */
      export namespace update {
        export class Params {
          /** undefined */
          annualPayHisId?: any;
          /** undefined */
          annualPayId?: any;
          /** undefined */
          avgEndTime?: any;
          /** undefined */
          avgPayMon?: any;
          /** undefined */
          avgPayYear?: any;
          /** undefined */
          avgStartTime?: any;
          /** undefined */
          cityId?: any;
          /** undefined */
          cityName?: any;
          /** undefined */
          createBy?: any;
          /** undefined */
          createDt?: any;
          /** undefined */
          execEndTime?: any;
          /** undefined */
          execStartTime?: any;
          /** file */
          file?: File;
          /** undefined */
          isDeleted?: any;
          /** undefined */
          minHour1?: any;
          /** undefined */
          minHour2?: any;
          /** undefined */
          minHour3?: any;
          /** undefined */
          minHour4?: any;
          /** undefined */
          minHour5?: any;
          /** undefined */
          minHour6?: any;
          /** undefined */
          minHourCounty1?: any;
          /** undefined */
          minHourCounty2?: any;
          /** undefined */
          minHourCounty3?: any;
          /** undefined */
          minHourCounty4?: any;
          /** undefined */
          minHourCounty5?: any;
          /** undefined */
          minHourCounty6?: any;
          /** undefined */
          minMon1?: any;
          /** undefined */
          minMon2?: any;
          /** undefined */
          minMon3?: any;
          /** undefined */
          minMon4?: any;
          /** undefined */
          minMon5?: any;
          /** undefined */
          minMon6?: any;
          /** undefined */
          minMonCounty1?: any;
          /** undefined */
          minMonCounty2?: any;
          /** undefined */
          minMonCounty3?: any;
          /** undefined */
          minMonCounty4?: any;
          /** undefined */
          minMonCounty5?: any;
          /** undefined */
          minMonCounty6?: any;
          /** undefined */
          payIncludeCpf?: any;
          /** undefined */
          payIncludeCpfStr?: any;
          /** undefined */
          payIncludeSi?: any;
          /** undefined */
          payIncludeSiStr?: any;
          /** undefined */
          pcName?: any;
          /** undefined */
          policyFileName?: any;
          /** undefined */
          policyFileUrl?: any;
          /** undefined */
          policyLink?: any;
          /** undefined */
          policyUser?: any;
          /** undefined */
          policyUserStr?: any;
          /** undefined */
          provinceId?: any;
          /** undefined */
          provinceName?: any;
          /** undefined */
          statisticsCaliber?: any;
          /** undefined */
          updateBy?: any;
          /** undefined */
          updateDt?: any;
          /** undefined */
          yearDate?: any;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 残保金方案测算
     */
    export namespace disabilityBenefitsContrller {
      /**
       * calculate
       * /disabilityBenefits/calculate
       */
      export namespace calculate {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * del
       * /disabilityBenefits/del
       */
      export namespace del {
        export class Params {
          /** disabilityBenefitsId */
          disabilityBenefitsId: number;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * query
       * /disabilityBenefits/query
       */
      export namespace query {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * save
       * /disabilityBenefits/save
       */
      export namespace save {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 福利待遇办理
     */
    export namespace fringeBenefits {
      /**
       * 查询页面导出，或者客户
       * /fringeBenefits/exportCustFringeBenefits
       */
      export namespace exportCustFringeBenefits {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 材料导出
       * /fringeBenefits/exportCustMaterial
       */
      export namespace exportCustMaterial {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 导出多个城市
       * /fringeBenefits/exportMultFringeBenefits
       */
      export namespace exportMultFringeBenefits {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 导出
       * /fringeBenefits/exportSingleFringeBenefits
       */
      export namespace exportSingleFringeBenefits {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 业务小类
       * /fringeBenefits/getBusSubtypeDropdownList
       */
      export namespace getBusSubtypeDropdownList {
        export class Params {
          /** busTypeId */
          busTypeId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 业务大类
       * /fringeBenefits/getBusTypeDropdownList
       */
      export namespace getBusTypeDropdownList {
        export class Params {
          /** busnameClassId */
          busnameClassId: string;
          /** categoryId */
          categoryId?: string;
          /** cityId */
          cityId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 业务项目
       * /fringeBenefits/getBusnameClassDropdownList
       */
      export namespace getBusnameClassDropdownList {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 修改按钮，传小类id
       * /fringeBenefits/getDetail
       */
      export namespace getDetail {
        export class Params {
          /** busnameSubtypeId */
          busnameSubtypeId: number;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 查询列表
       * /fringeBenefits/getList
       */
      export namespace getList {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FringeBenefitsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 新增或修改
       * /fringeBenefits/save
       */
      export namespace save {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.FringeBenefits,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FringeBenefits,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 社保公积金多个
     */
    export namespace multPolicy {
      /**
        * 社保公积金政策一览 竖表（多个城市）按客户 	  根据城市ID获取数据
社保公积金政策一览 竖表（多个城市）按客户 	  根据城市ID获取数据
        * /multPolicy/createSurSsVerticalByCust
        */
      export namespace createSurSsVerticalByCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取BoE4IP地址
获取BoE4IP地址
        * /multPolicy/getBoE4IP
        */
      export namespace getBoE4IP {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取BoE4IP地址
获取BoE4IP地址
        * /multPolicy/getBoE4IP
        */
      export namespace postGetBoE4IP {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 公积金试算（多个城市）
公积金试算（多个城市）
        * /multPolicy/getCalSsByCity
        */
      export namespace getCalSsByCity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: number;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.information.CityInfo>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.information.CityInfo>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 公积金试算（客户）
公积金试算（客户）
        * /multPolicy/getCalSsByCust
        */
      export namespace getCalSsByCust {
        export class Params {
          /** custId */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: number;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.information.CityInfo>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.information.CityInfo>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 社保公积金政策一览 横表（多个城市）按城市  横表
社保公积金政策一览 横表（多个城市）按城市  横表
        * /multPolicy/getSurSsAcrossByCity
        */
      export namespace getSurSsAcrossByCity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: number;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.information.PolicyDTO>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.information.PolicyDTO>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 社保公积金政策一览 横表（多个城市）按客户
社保公积金政策一览 横表（多个城市）按客户
        * /multPolicy/getSurSsAcrossByCust
        */
      export namespace getSurSsAcrossByCust {
        export class Params {
          /** 客户ID */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: number;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 社保公积金政策一览 横表（多个城市）按客户
社保公积金政策一览 横表（多个城市）按客户
        * /multPolicy/getSurSsAcrossByCust
        */
      export namespace postGetSurSsAcrossByCust {
        export class Params {
          /** 客户ID */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: number;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 社保公积金政策一览 横表（多个城市）按城市  竖表
社保公积金政策一览 横表（多个城市）按城市  竖表
        * /multPolicy/getSurSsVerticalByCity
        */
      export namespace getSurSsVerticalByCity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 界面选择的城市列表
界面选择的城市列表
        * /multPolicy/getSurSsVerticalCityData
        */
      export namespace getSurSsVerticalCityData {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<
            Array<defs.information.CityInfo>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.information.PolicyDTO>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.information.PolicyDTO>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 社保公积金政策一览 竖表（多个城市）按客户    获取城市列表
社保公积金政策一览 竖表（多个城市）按客户    获取城市列表
        * /multPolicy/getSurSsVerticalCtiysByCust
        */
      export namespace getSurSsVerticalCtiysByCust {
        export class Params {
          /** 客户ID */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<
            Array<defs.information.CityInfo>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 社保公积金政策一览 竖表（多个城市）按客户    获取城市列表
社保公积金政策一览 竖表（多个城市）按客户    获取城市列表
        * /multPolicy/getSurSsVerticalCtiysByCust
        */
      export namespace postGetSurSsVerticalCtiysByCust {
        export class Params {
          /** 客户ID */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<
            Array<defs.information.CityInfo>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Once Charge Controller
     */
    export namespace onceCharge {
      /**
        * 导出
导出
        * /onceCharge/export
        */
      export namespace updateOnceCharge {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.FilterEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增
新增
        * /onceCharge/insert
        */
      export namespace insertOnceCharge {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.OnceChargeDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.OnceChargeDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.OnceChargeDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询结果
根据条件分页查询结果
        * /onceCharge/query
        */
      export namespace queryOnceCharge {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改
修改
        * /onceCharge/update
        */
      export namespace postUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.OnceChargeDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.OnceChargeDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.OnceChargeDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 残疾人保障金一览
     */
    export namespace policyDisabled {
      /**
        * 导出
导出
        * /policyDisabled/exporter
        */
      export namespace exporter {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.QueryPolicyDisabled,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.QueryPolicyDisabled,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增
新增
        * /policyDisabled/insert
        */
      export namespace insert {
        export class Params {
          /** undefined */
          advanceAmount?: any;
          /** undefined */
          cityId?: any;
          /** undefined */
          cityName?: any;
          /** undefined */
          collectionUnits?: any;
          /** undefined */
          collectionUnitsStr?: any;
          /** undefined */
          createBy?: any;
          /** undefined */
          createDt?: any;
          /** undefined */
          declareTime?: any;
          /** undefined */
          disabledSecureHisId?: any;
          /** undefined */
          disabledSecureId?: any;
          /** undefined */
          estimate?: any;
          /** file */
          file?: File;
          /** undefined */
          isDeleted?: any;
          /** undefined */
          levyFrequency?: any;
          /** undefined */
          levyFrequencyStr?: any;
          /** undefined */
          levyScope?: any;
          /** undefined */
          levyStandard?: any;
          /** undefined */
          levyTarget?: any;
          /** undefined */
          levyTargetStr?: any;
          /** undefined */
          overduePay?: any;
          /** undefined */
          payTime?: any;
          /** undefined */
          policyFileName?: any;
          /** undefined */
          policyFileUrl?: any;
          /** undefined */
          policyLink?: any;
          /** undefined */
          policyUser?: any;
          /** undefined */
          policyUserStr?: any;
          /** undefined */
          updateBy?: any;
          /** undefined */
          updateDt?: any;
          /** undefined */
          yearDate?: any;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询
查询
        * /policyDisabled/select
        */
      export namespace select {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<
            Array<defs.information.PolicyDisabled>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.QueryPolicyDisabled,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.QueryPolicyDisabled,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改
修改
        * /policyDisabled/update
        */
      export namespace update {
        export class Params {
          /** undefined */
          advanceAmount?: any;
          /** undefined */
          cityId?: any;
          /** undefined */
          cityName?: any;
          /** undefined */
          collectionUnits?: any;
          /** undefined */
          collectionUnitsStr?: any;
          /** undefined */
          createBy?: any;
          /** undefined */
          createDt?: any;
          /** undefined */
          declareTime?: any;
          /** undefined */
          disabledSecureHisId?: any;
          /** undefined */
          disabledSecureId?: any;
          /** undefined */
          estimate?: any;
          /** file */
          file?: File;
          /** undefined */
          isDeleted?: any;
          /** undefined */
          levyFrequency?: any;
          /** undefined */
          levyFrequencyStr?: any;
          /** undefined */
          levyScope?: any;
          /** undefined */
          levyStandard?: any;
          /** undefined */
          levyTarget?: any;
          /** undefined */
          levyTargetStr?: any;
          /** undefined */
          overduePay?: any;
          /** undefined */
          payTime?: any;
          /** undefined */
          policyFileName?: any;
          /** undefined */
          policyFileUrl?: any;
          /** undefined */
          policyLink?: any;
          /** undefined */
          policyUser?: any;
          /** undefined */
          policyUserStr?: any;
          /** undefined */
          updateBy?: any;
          /** undefined */
          updateDt?: any;
          /** undefined */
          yearDate?: any;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 政策标签3.0
     */
    export namespace policyLabel {
      /**
        * 获取所有政策标签下拉列表
政策标签下拉列表
        * /policyLabel/getAllPolicyLabelDropdownList
        */
      export namespace getAllPolicyLabelDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策标签下拉列表
政策标签下拉列表
        * /policyLabel/getPolicyLabelDropdownList
        */
      export namespace getPolicyLabelDropdownList {
        export class Params {
          /** state */
          state?: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增标签
新增标签时标签名字必填且在有效的标签内不重名
        * /policyLabel/insert
        */
      export namespace insertPolicyLabel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyLabelDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyLabelDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询结果
根据条件分页查询结果
        * /policyLabel/query
        */
      export namespace queryPolicyLabel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyLabelQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyLabelQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改状态
修改状态（生效->失效；生效->失效；生效变失效时验证在有效的标签内不重名）
        * /policyLabel/updateState
        */
      export namespace updateState {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyLabelDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyLabelDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 政策大全3.0
     */
    export namespace policyLevel {
      /**
       * 删除层级
       * /policyLevel/delete
       */
      export namespace deletePolicyLevel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyLevelDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyLevelDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策层级下拉选择
政策层级下拉选择
        * /policyLevel/getPolicyLevelDropdownList
        */
      export namespace getPolicyLevelDropdownList {
        export class Params {
          /** parentId */
          parentId?: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增层级
新增层级时层级名字必填且同层级内层级名称不允许重复
        * /policyLevel/insert
        */
      export namespace insertPolicyLevel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyLevelDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyLevelDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询结果
根据条件分页查询结果
        * /policyLevel/query
        */
      export namespace queryPolicyLevel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyLevelQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyLevelQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改层级
修改层级时层级名字必填且同层级内层级名称不允许重复
        * /policyLevel/update
        */
      export namespace updatePolicyLevel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyLevelDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyLevelDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 校验导出政策标题
校验导出政策标题
        * /policyTitle/checkDownLoad
        */
      export namespace checkDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 删除
       * /policyTitle/delete
       */
      export namespace remove {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyTitleDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyTitleDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出政策标题
导出政策标题
        * /policyTitle/exportTitleFile
        */
      export namespace exportTitleFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策关联项目下拉选择
项目下拉选择
        * /policyTitle/getBusnameClassDropdownList
        */
      export namespace getBusnameClassDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 查询详情
       * /policyTitle/getDetail
       */
      export namespace getDetail {
        export class Params {
          /** policyTitleId */
          policyTitleId: number;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策标题是否被引用
政策标题是否被引用
        * /policyTitle/getPolicyTitleIsUse
        */
      export namespace getPolicyTitleIsUse {
        export class Params {
          /** policyTitleId */
          policyTitleId?: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 新增标题
       * /policyTitle/insert
       */
      export namespace insertPolicyTitle {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyTitleDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyTitleDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询结果
根据条件分页查询结果
        * /policyTitle/query
        */
      export namespace queryPolicyTitle {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyTitleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyTitleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 修改标题
       * /policyTitle/update
       */
      export namespace updatePolicyTitle {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyTitleDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyTitleDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 修改状态（生效/失效）
       * /policyTitle/updateState
       */
      export namespace updateState {
        export class Params {
          /** policyTitleId */
          policyTitleId: number;
          /** state */
          state: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 省级政策维护人管理
     */
    export namespace policyProvinceUser {
      /**
       * 删除
       * /policyProvinceUser/delete
       */
      export namespace remove {
        export class Params {
          /** userId */
          userId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 查询详情
       * /policyProvinceUser/getDetail
       */
      export namespace getDetail {
        export class Params {
          /** userId */
          userId: number;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 省份下拉框
省份下拉框
        * /policyProvinceUser/getProvinceDropdownList
        */
      export namespace getProvinceDropdownList {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询省份列表
查询省份列表
        * /policyProvinceUser/getProvinceList
        */
      export namespace getProvinceList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 新增省份政策维护人
       * /policyProvinceUser/insert
       */
      export namespace insertPolicyProvinceUser {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询结果
根据条件分页查询结果
        * /policyProvinceUser/query
        */
      export namespace queryPolicyProvinceUser {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyProvinceUserQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyProvinceUserQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 修改省份政策维护人
       * /policyProvinceUser/update
       */
      export namespace updatePolicyProvinceUser {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 政策3.0-政策查询-不按客户
     */
    export namespace policyQuerySingle {
      /**
        * 政策标题下拉框
政策标题下拉框
        * /policyQuerySingle/getPolicyTitleDropdownList
        */
      export namespace getPolicyTitleDropdownList {
        export class Params {
          /** 是否包含国家1是0否 */
          hasCountry: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策标题ID查询省份城市特区
政策标题ID查询省份城市特区
        * /policyQuerySingle/getTemplateInfoByTitleId
        */
      export namespace getTemplateInfoByTitleId {
        export class Params {
          /** 标题id */
          policyTitleId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询政策（批量）
分页查询政策（批量）
        * /policyQuerySingle/queryPolicyBatch
        */
      export namespace queryPolicyBatch {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询政策（单个）
分页查询政策（单个）
        * /policyQuerySingle/queryPolicySingle
        */
      export namespace queryPolicySingle {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询政策（销售）
分页查询政策（销售）
        * /policyQuerySingle/queryPolicySingleSale
        */
      export namespace queryPolicySingleSale {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * pdf下载日志保存）
pdf下载日志保存
        * /policyQuerySingle/savePdfDownLog
        */
      export namespace savePdfDownLog {
        export class Params {
          /** 详情id */
          policyTemplateInfoId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 政策查询按照客户
     */
    export namespace policySearchCust {
      /**
        * 导出政策详情
导出政策详情
        * /policySearchCust/exportSearchPolicyInfo
        */
      export namespace exportSearchPolicyInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据标题id获取分组字段信息
根据标题id获取分组字段信息
        * /policySearchCust/getColForDetail
        */
      export namespace getColForDetail {
        export class Params {
          /** policyTitleId */
          policyTitleId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策查询省份城市特区下拉列表(该客户下合同大类为代理、派遣、外包项目的，合同小类不等于体检、补医保、雇主责任险、易薪税(单立户)、易薪税(大户)所有小合同对应接单城市对应的省份城市特区)
政策查询省份城市特区下拉列表
        * /policySearchCust/getPolicyDropdownList
        */
      export namespace getPolicyDropdownList {
        export class Params {
          /** custId */
          custId: string;
          /** type */
          type: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据政策详情ID获取pdf文件(记录日志的)
根据政策详情ID获取pdf文件(记录日志的)
        * /policySearchCust/getPolicyPdf
        */
      export namespace getPolicyPdf {
        export class Params {
          /** policyTemplateInfoId */
          policyTemplateInfoId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询政策（单个-按客户）
根据条件分页查询政策（单个-按客户）
        * /policySearchCust/query
        */
      export namespace queryPolicySearchCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询政策（批量-按客户）
根据条件分页查询政策（批量-按客户）
        * /policySearchCust/queryBatch
        */
      export namespace queryPolicySearchCustBatch {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicySearchQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 政策3.0-政策特区
     */
    export namespace policySpecialArea {
      /**
       * 通过主键删除数据
       * /policySpecialArea/deleteById
       */
      export namespace deleteById {
        export class Params {
          /** 特区id */
          specialAreaId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 生效数据
       * /policySpecialArea/enableData
       */
      export namespace enableData {
        export class Params {
          /** 特区id */
          specialAreaId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 通过ID查询单条数据
       * /policySpecialArea/getDetail
       */
      export namespace getDetail {
        export class Params {
          /** 特区id */
          specialAreaId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.PolicySpecialArea;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 获取特区下拉选项
       * /policySpecialArea/getDropdownList
       */
      export namespace getDropdownList {
        export class Params {
          /** state */
          state: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<defs.information.DropdownList>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 获取特区下拉选项-后道政策维护人
       * /policySpecialArea/getDropdownListByBranchId
       */
      export namespace getDropdownListByBranchId {
        export class Params {
          /** 生效状态 */
          state?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<defs.information.DropdownList>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 获取特区下拉默认选中值-后道政策维护人
       * /policySpecialArea/getDropdownListDefaultValue
       */
      export namespace getDropdownListDefaultValue {
        export class Params {
          /** 生效状态 */
          state?: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 根据ids返回列表
       * /policySpecialArea/getListByIds
       */
      export namespace getListByIds {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 特区id查询省份城市名称
特区id查询省份城市名称
        * /policySpecialArea/getProvinceCityNameByAreaId
        */
      export namespace getProvinceCityNameByAreaId {
        export class Params {
          /** 特区id */
          specialAreaId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 新增数据
       * /policySpecialArea/insert
       */
      export namespace insert {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicySpecialAreaDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicySpecialAreaDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 失效数据
       * /policySpecialArea/invalidData
       */
      export namespace invalidData {
        export class Params {
          /** 特区id */
          specialAreaId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 分页查询
       * /policySpecialArea/queryPage
       */
      export namespace queryPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<defs.information.Page>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicySpecialAreaQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicySpecialAreaQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 更新数据
       * /policySpecialArea/update
       */
      export namespace update {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicySpecialAreaDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicySpecialAreaDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 政策模板操作接口
     */
    export namespace policyTemplate {
      /**
        * 查询是否模板是否被引用
查询是否模板是否被引用
        * /policy3/template/checkDelTemplate
        */
      export namespace checkDelTemplate {
        export class Params {
          /** policyTemplateId */
          policyTemplateId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 发送模板更新提醒前置判断
发送模板更新提醒前置判断
        * /policy3/template/checkSendPolicyTemplateUpdateRemind
        */
      export namespace checkSendPolicyTemplateUpdateRemind {
        export class Params {
          /** policyTemplateId */
          policyTemplateId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据模板ID复制模板
根据模板ID复制模板
        * /policy3/template/copyTemplate
        */
      export namespace copyTemplate {
        export class Params {
          /** (canBatchExport */
          '(canBatchExport'?: number;
          /** policyTemplateId */
          policyTemplateId: string;
          /** serviceType */
          serviceType: number;
          /** templateName */
          templateName: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据模板分组ID查询字段
根据模板分组ID查询字段
        * /policy3/template/getTemplateFields
        */
      export namespace getTemplateFields {
        export class Params {
          /** templateGroupId */
          templateGroupId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据模板ID查询分组
根据模板ID查询分组
        * /policy3/template/getTemplateGroups
        */
      export namespace getTemplateGroups {
        export class Params {
          /** templateId */
          templateId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据分组ID修改字段信息
根据分组ID修改字段信息
        * /policy3/template/modifyFields
        */
      export namespace modifyFields {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateFieldResp,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateFieldResp,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据模板id修改分组信息
根据模板id修改分组信息
        * /policy3/template/modifyGroups
        */
      export namespace modifyGroups {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateGroupResp,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateGroupResp,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据ID 修改模板名称
根据ID 修改模板名称
        * /policy3/template/modifyNameById
        */
      export namespace modifyNameById {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据ID 修改模板状态
根据ID 修改模板状态
        * /policy3/template/modifyStateById
        */
      export namespace modifyStateById {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取政策模板预览分组和字段
获取政策模板预览分组和字段
        * /policy3/template/previewPolicyTemplate
        */
      export namespace previewPolicyTemplate {
        export class Params {
          /** 模板ID */
          policyTemplateId: string;
          /** 类型 1 hro 2 客户端 3 微信端 */
          type: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取政策模板预览分组和字段 同时返回模板的版本号
获取政策模板预览分组和字段 同时返回模板的版本号
        * /policy3/template/previewPolicyTemplateAndVer
        */
      export namespace previewPolicyTemplateAndVer {
        export class Params {
          /** 模板ID */
          policyTemplateId: string;
          /** 类型 1 hro 2 客户端 3 微信端 */
          type: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策模板列表查询
政策模板列表查询
        * /policy3/template/queryPolicyTemplateList
        */
      export namespace queryPolicyTemplateList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyTemplateQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyTemplateQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据分组ID保存字段
根据分组ID保存字段
        * /policy3/template/saveFields
        */
      export namespace saveFields {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateFieldResp,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateFieldResp,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据模板id 保存分组
根据模板id保存分组
        * /policy3/template/saveGroups
        */
      export namespace saveGroups {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateGroupResp,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateGroupResp,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据类型分别保存字段和分组排序
根据类型分别保存字段和分组排序
        * /policy3/template/saveSortSeqNum
        */
      export namespace saveSortSeqNum {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateSortReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateSortReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增政策模板
新增政策模板
        * /policy3/template/saveTemplate
        */
      export namespace savePolicyTemplate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.TemplateReq;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.TemplateReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 发送模板更新提醒
发送模板更新提醒
        * /policy3/template/sendPolicyTemplateUpdateRemind
        */
      export namespace sendPolicyTemplateUpdateRemind {
        export class Params {
          /** policyTemplateId */
          policyTemplateId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 政策详情接口
     */
    export namespace policyTemplateInfo {
      /**
        * 政策详情保存接口
政策详情保存接口
        * /policy3/templateInfo/addInfo
        */
      export namespace addInfo {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 提交审批（需要先调用：新增政策详情下一步校验接口）
提交审批（需要先调用：新增政策详情下一步校验接口）
        * /policy3/templateInfo/approve/commit
        */
      export namespace commitApproval {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 审批通过
审批通过
        * /policy3/templateInfo/approve/pass
        */
      export namespace approved {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoApproveReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoApproveReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 终止
终止）
        * /policy3/templateInfo/approve/terminal
        */
      export namespace terminal {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoApproveReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoApproveReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增政策详情下一步校验接口
新增政策详情下一步校验接口
        * /policy3/templateInfo/checkAddNext
        */
      export namespace checkAddNext {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 校验政策是否有可以发布
校验政策是否有可以发布
        * /policy3/templateInfo/checkPublicPolicy
        */
      export namespace checkPublicPolicy {
        export class Params {
          /** policyTemplateInfoId */
          policyTemplateInfoId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.CommonResponse<defs.information.TemplateInfoAddReq>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 校验模板是否发生变化
校验模板是否发生变化
        * /policy3/templateInfo/checkTemplateChange
        */
      export namespace checkTemplateChange {
        export class Params {
          /** policyTemplateInfoId */
          policyTemplateInfoId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除政策详情
删除政策详情
        * /policy3/templateInfo/delete
        */
      export namespace remove {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出政策详情
导出政策详情
        * /policy3/templateInfo/exportPolicyInfo
        */
      export namespace exportPolicyInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据模板政策ID 获取政策详情和字段值
根据模板政策ID 获取政策详情和字段值
        * /policy3/templateInfo/getByInfoId
        */
      export namespace getByInfoId {
        export class Params {
          /** policyTemplateInfoId */
          policyTemplateInfoId?: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 复制上年度政策
根据条件返回上一年的政策信息
        * /policy3/templateInfo/getCopyLastPolicyInfo
        */
      export namespace getCopyLastPolicyInfo {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 当选择有内部分公司的时候 城市的下拉框选项
当选择有内部分公司的时候 城市的下拉框选项
        * /policy3/templateInfo/getInternalCity
        */
      export namespace getInternalCity {
        export class Params {
          /** 类型：1省份政策维护人，2后道政策维护人，3全部  */
          type?: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 有内部分公司时城市下拉框-后道政策维护人专用
有内部分公司时城市下拉框-后道政策维护人专用
        * /policy3/templateInfo/getInternalCityByBranchId
        */
      export namespace getInternalCityByBranchId {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件返回最后一条政策详情
根据条件返回最后一条政策详情
        * /policy3/templateInfo/getLastPolicyInfo
        */
      export namespace getLastPolicyInfo {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据政策详情ID获取pdf文件
根据政策详情ID获取pdf文件
        * /policy3/templateInfo/getPdf
        */
      export namespace getPdf {
        export class Params {
          /** policyTemplateInfoId */
          policyTemplateInfoId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据当前登陆人获取省份政策维护人管理中对应的省份下拉框
根据当前登陆人获取省份政策维护人管理中对应的省份下拉框
        * /policy3/templateInfo/getProvinceDropdownList
        */
      export namespace getProvinceDropdownList {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 城市id查询省份名称
城市id查询省份名称
        * /policy3/templateInfo/getProvinceNameByCityId
        */
      export namespace getProvinceNameByCityId {
        export class Params {
          /** 城市id */
          cityId?: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据适用范围获取模板标题和模板
根据适用范围获取模板标题和模板
        * /policy3/templateInfo/getTemplateTitlesByScope
        */
      export namespace getTemplateTitlesByScope {
        export class Params {
          /** 适用范围：1国家，2省份，3城市  */
          templateScope?: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策详情修改接口
政策详情修改接口
        * /policy3/templateInfo/modifyInfo
        */
      export namespace modifyInfo {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 发布政策
发布政策
        * /policy3/templateInfo/publishPolicy
        */
      export namespace publishPolicy {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoPubReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoPubReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策详情审批列表查询
政策详情审批列表查询
        * /policy3/templateInfo/queryPolicyInfoApproveList
        */
      export namespace queryPolicyInfoApproveList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyInfoApproveQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyInfoApproveQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 政策详情列表查询
政策详情列表查询
        * /policy3/templateInfo/queryPolicyTemplateInfoList
        */
      export namespace queryPolicyTemplateInfoList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.PolicyTemplateInfoQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.PolicyTemplateInfoQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据政策详情ID 补发邮件
根据政策详情ID 补发邮件
        * /policy3/templateInfo/resendEmail
        */
      export namespace resendEmail {
        export class Params {
          /** policyTemplateInfoId */
          policyTemplateInfoId: string;
        }

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新政策详情pdf
更新政策详情pdf
        * /policy3/templateInfo/updatePdf
        */
      export namespace updatePdf {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoAddReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存更新日期
保存更新日期
        * /policy3/templateInfo/updatePolicyUpdateDate
        */
      export namespace updatePolicyUpdateDate {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.information.TemplateInfoUpdateReq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.TemplateInfoUpdateReq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 没有内部分公司对应的城市的下拉框选项
没有内部分公司对应的城市的下拉框选项
        * /policy3/templateInfo/withoutInternalCity
        */
      export namespace getWithoutInternalCity {
        export class Params {}

        export type Response<T> = defs.information.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 服务网点查询
     */
    export namespace servicePointPolicy {
      /**
        * 服务网点查询
服务网点查询
        * /servicePointPolicy/getServicePointPage
        */
      export namespace getServicePointPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.ServicePointDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 社保公积金单个
     */
    export namespace singlePolicy {
      /**
        * 开始计算
开始计算
        * /singlePolicy/calculateInsurance
        */
      export namespace calculateInsurance {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.CalculateDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.CalculateDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 开始计算
开始计算
        * /singlePolicy/exportCalculateInsurance
        */
      export namespace exportCalculateInsurance {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.CalculateDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.CalculateDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取社保公积金政策信息
获取社保公积金政策信息
        * /singlePolicy/exportPolicyDetail
        */
      export namespace exportPolicyDetail {
        export class Params {
          /** cityId */
          cityId: number;
          /** personCategory */
          personCategory: number;
          /** servicePointId */
          servicePointId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取社保公积金政策信息
获取社保公积金政策信息
        * /singlePolicy/exportPolicyDetail
        */
      export namespace postExportPolicyDetail {
        export class Params {
          /** cityId */
          cityId: number;
          /** personCategory */
          personCategory: number;
          /** servicePointId */
          servicePointId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据选择城市+人员类别+接单方，获取对应的大户的标准套餐
根据选择城市+人员类别+接单方，获取对应的大户的标准套餐
        * /singlePolicy/getBaseProportionTrialTable
        */
      export namespace getBaseProportionTrialTable {
        export class Params {
          /** branchId */
          branchId: number;
          /** cityId */
          cityId: number;
          /** personCategory */
          personCategory: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据选择城市+人员类别+接单方，获取对应的大户的标准套餐
根据选择城市+人员类别+接单方，获取对应的大户的标准套餐
        * /singlePolicy/getBaseProportionTrialTable
        */
      export namespace postGetBaseProportionTrialTable {
        export class Params {
          /** branchId */
          branchId: number;
          /** cityId */
          cityId: number;
          /** personCategory */
          personCategory: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取优选网点
获取优选网点
        * /singlePolicy/getFirstServicePointCity
        */
      export namespace getFirstServicePointCity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取优选网点
获取优选网点
        * /singlePolicy/getFirstServicePointCity
        */
      export namespace postGetFirstServicePointCity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取社保公积金政策信息
获取社保公积金政策信息
        * /singlePolicy/getPolicyDetail
        */
      export namespace getPolicyDetail {
        export class Params {
          /** cityId */
          cityId: number;
          /** personCategory */
          personCategory: number;
          /** servicePointId */
          servicePointId?: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取社保公积金政策信息
获取社保公积金政策信息
        * /singlePolicy/getPolicyDetail
        */
      export namespace postGetPolicyDetail {
        export class Params {
          /** cityId */
          cityId: number;
          /** personCategory */
          personCategory: number;
          /** servicePointId */
          servicePointId?: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 公积金，补充公积金比例id
公积金，补充公积金比例id
        * /singlePolicy/getProductRatio
        */
      export namespace getProductRatio {
        export class Params {
          /** comboId */
          comboId: number;
          /** productTypeId */
          productTypeId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 公积金，补充公积金比例id
公积金，补充公积金比例id
        * /singlePolicy/getProductRatio
        */
      export namespace postGetProductRatio {
        export class Params {
          /** comboId */
          comboId: number;
          /** productTypeId */
          productTypeId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据cityId 取服务网点
根据cityId 取服务网点
        * /singlePolicy/getServicePoint
        */
      export namespace getServicePoint {
        export class Params {
          /** cityId */
          cityId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据cityId 取服务网点
根据cityId 取服务网点
        * /singlePolicy/getServicePoint
        */
      export namespace postGetServicePoint {
        export class Params {
          /** cityId */
          cityId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Result;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Statutory Holiday Controller
     */
    export namespace statutoryHoliday {
      /**
        * 导出
导出
        * /statutoryHoliday/export
        */
      export namespace updateOnceCharge {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.FilterEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增
新增
        * /statutoryHoliday/insert
        */
      export namespace insertOnceCharge {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.OnceChargeDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.StatutoryHolidayDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.StatutoryHolidayDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询结果
根据条件分页查询结果
        * /statutoryHoliday/query
        */
      export namespace queryStatutoryHoliday {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改
修改
        * /statutoryHoliday/update
        */
      export namespace postUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.information.OnceChargeDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.information.StatutoryHolidayDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.information.StatutoryHolidayDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
