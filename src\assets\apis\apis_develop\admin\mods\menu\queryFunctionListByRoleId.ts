import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/menu/queryFunctionListByRoleId
     * @desc 获取角色下的菜单树
获取角色下的菜单树,原本在角色下，因为增加字段改用menuDTO，放在menu下了
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** roleId */
  roleId: string;
}

export const init = new defs.admin.CommonResponse();
export const url = '/rhro-service-1.0/menu/queryFunctionListByRoleId:GET';
export const initialUrl = '/rhro-service-1.0/menu/queryFunctionListByRoleId';
export const cacheKey = '_menu_queryFunctionListByRoleId_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/menu/queryFunctionListByRoleId`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/menu/queryFunctionListByRoleId`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
