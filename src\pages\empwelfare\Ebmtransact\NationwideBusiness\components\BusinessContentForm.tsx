import React, { useEffect, useState } from 'react';
import { Form, Button, message, FormInstance } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import {
  BusinessBigByBusTypeSelector,
  GetBaseBusnameClassDropdownList,
} from '@/components/Selectors/BaseDataSelectors';
import {
  isOrNoMap,
  handleAttributeMap,
  handleObjectMap,
  handleMethodMap,
  categoryMap,
} from '../index';

interface BusinessContentFormProps {
  modal: [boolean, CallableFunction];
  listOptions: any;
  initialInfo?: any;
}

const BusinessContentForm: React.FC<BusinessContentFormProps> = ({
  modal,
  listOptions,
  initialInfo,
}) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;

  const [form] = Form.useForm();
  const [busConfigId, setBusConfigId] = useState<string>('');
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (visible) {
      if (initialInfo && Object.keys(initialInfo).length > 0) {
        setIsEdit(true);
        form.setFieldsValue(initialInfo);
        setBusConfigId(initialInfo.busConfigId);
      } else {
        setIsEdit(false);
        form.resetFields();
        setBusConfigId('');
      }
    }
  }, [visible, initialInfo, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      // TODO: 调用API保存数据
      // console.log('业务内容提交:', values);
      const service = isEdit
        ? API.welfaremanage.ebmBusinessConfig.updateBusinessConfig
        : API.welfaremanage.ebmBusinessConfig.insertBusinessConfig;
      await service.requests({ ...values, busConfigId });
      message.success(isEdit ? '修改成功' : '新增成功');
      setVisible(false);
      form.resetFields();
      // 刷新列表
      listOptions.request(listOptions.queries);
    } catch (error) {
      // message.error('请检查表单填写是否正确');
    }
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button key="submit" type="primary" onClick={handleSubmit}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm: FormInstance) => {
        return mapToSelectors(categoryMap, {
          allowClear: true,
          placeholder: '请选择业务类型',
          onChange: () => {
            outerForm.setFieldsValue({ busnameClassId: undefined, busContent: undefined });
          },
        });
      },
      rules: [{ required: true, message: '请选择业务类型' }],
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      inputRender: (outerForm: FormInstance) => (
        <GetBaseBusnameClassDropdownList
          allowClear
          params={{ categoryId: outerForm.getFieldValue('categoryId') ?? '' }}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
      rules: [{ required: true, message: '请选择业务项目' }],
    },
    {
      label: '业务内容',
      fieldName: 'busContent',
      inputRender: (outerForm: FormInstance) => (
        <BusinessBigByBusTypeSelector
          params={{
            busnameClassId: outerForm.getFieldValue('busnameClassId') ?? '',
            categoryId: outerForm.getFieldValue('categoryId') ?? '',
          }}
          onChange={(value: any, option: any) => {
            outerForm.setFieldsValue({ busContent: option?.title });
          }}
          placeholder="请选择业务内容"
          allowClear
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.categoryId !== curValues.categoryId ||
          prevValues.busnameClassId !== curValues.busnameClassId
        );
      },
      rules: [{ required: true, message: '请输入业务内容' }],
    },
    {
      label: '办理属性',
      fieldName: 'transactProperty',
      inputProps: { disabled: isEdit },
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择办理属性' }],
    },
    {
      label: '办理对象',
      fieldName: 'transactObject',
      inputProps: { disabled: isEdit },
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择办理对象' }],
    },
    // {
    //   label: '办理方式',
    //   fieldName: 'transactTypeStr',
    //   inputProps: { disabled: isEdit },
    //   inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    //   rules: [{ required: true, message: '请选择办理方式' }],
    // },
    {
      label: '是否微信显示',
      fieldName: 'wxShowState',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择是否微信显示' }],
    },
    {
      label: '是否客户端显示',
      fieldName: 'clientShowState',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
      rules: [{ required: true, message: '请选择是否客户端显示' }],
    },
  ];

  // 设置只读字段
  const readOnlyFields = isEdit
    ? {
        handleAttribute: true,
        handleObject: true,
        handleMethod: true,
      }
    : undefined;

  return (
    <Codal
      title={isEdit ? '修改业务内容' : '新增业务内容'}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        setVisible(false);
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="60%"
    >
      <FormElement3 form={form}>
        <EnumerateFields
          formColumns={formColumns}
          outerForm={form}
          colNumber={2}
          readOnlyFields={readOnlyFields}
        />
      </FormElement3>
    </Codal>
  );
};

export default BusinessContentForm;
