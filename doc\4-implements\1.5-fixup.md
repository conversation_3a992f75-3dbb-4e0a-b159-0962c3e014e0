<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-01-02 18:00:12
 * @LastAuthor: 侯成
 * @LastTime: 2020-06-12 17:24:23
 * @message: message
 -->

# 页面更新

```tsx
import { FormElement4, RowElement, ColElement4, ColElementButton } from '@/components/Forms/FormLayouts';
import { FormElement3, RowElement, ColElement3, ColElementButton } from '@/components/Forms/FormLayouts';
import { FormElement2, RowElement, ColElement2, ColElementButton } from '@/components/Forms/FormLayouts';

import { FormItem } from '@/components/Forms/FormItem';
import { FormElement4, RowElement, ColElement4, ColElementButton } from '@/components/Forms/FormLayouts';
import { FormElement3, RowElement, ColElement3, ColElementButton } from '@/components/Forms/FormLayouts';
import { FormElement2, RowElement, ColElement2, ColElementButton } from '@/components/Forms/FormLayouts';

import Codal from '@/components/Codal';
import Confirm, { ConfirmLoading } from '@/components/Forms/Confirm'
import { getTableQueries, getTableParams, CacheLib } from '@/utils/methods/cache';
import { TablePage, pageCacheRequest, refreshRequest, pageInit } from '@/utils/methods/pagenation';
import { FormItemColon as FormItem } from '@/components/Forms/FormItem';
import { resError, resErrorMsg, resOkMsg } from '@/utils/methods/message';

// 创建时间≥
// title: '创建时间≤',
handleSearch = (event: React.FormEvent<any>) => {
  const { form } = this.props;
  event.preventDefault();
  // const { querysubcontracts } = this.props;
  form.validateFields((err, fieldsValue) => {
    if (err) return ConfirmLoading.clearLoading(service);
    this.querysubcontracts(refreshRequest(fieldsValue), pageInit);
  });
};


  comfirm = () => {
    if (resError(res)) return resErrorMsg(res, '请求列表错误');
    resOkMsg(res, '请求列表错误');
  }

 haddleSubcontractAdd = () => {
    // console.log('haddleSubcontractAdd is activated');
    const { dispatch } = this.props;
    dispatch!({
      type: 'subcontract/setAllEmpty',
    });
    const { onSubcontractAdd } = this.state;
    CacheLib.clearPageQueries(service);
    this.querysubcontracts(refreshRequest(), pageInit);
    this.setState({ onSubcontractAdd: !onSubcontractAdd });
  };

<ColElement4>
  <FormItem label="大合同">
    {getFieldDecorator('contractId', { initialValue: subcontractsQuery.contractId })(pisoToSelectors(contractNameMap))}
  </FormItem>
</ColElement4>

<ColElement4>
  <BranchPop
    modalwidth={800}
    label="接单分公司"
    viewKey="assigneeProviderName"
    rowValue="assigneeProviderId"
    keyMap={{ assigneeProviderId: 'branchId', assigneeProviderName: 'branchName' }}
    outerForm={this.props.form}
    rowValueData={{ assigneeProviderId: subcontractsQuery.assigneeProviderId, assigneeProviderName: subcontractsQuery.assigneeProviderName }}
  />
</ColElement4>

```
