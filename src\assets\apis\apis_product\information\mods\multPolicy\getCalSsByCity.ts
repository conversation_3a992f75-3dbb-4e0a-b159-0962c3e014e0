import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/multPolicy/getCalSsByCity
     * @desc 公积金试算（多个城市）
公积金试算（多个城市）
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = undefined;
export const url = '/rhro-service-1.0/multPolicy/getCalSsByCity:POST';
export const initialUrl = '/rhro-service-1.0/multPolicy/getCalSsByCity';
export const cacheKey = '_multPolicy_getCalSsByCity_POST';
export async function request(
  data: Array<defs.information.CityInfo>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/multPolicy/getCalSsByCity`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: Array<defs.information.CityInfo>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/multPolicy/getCalSsByCity`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
