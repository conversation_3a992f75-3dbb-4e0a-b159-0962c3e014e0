import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/department/getDepartmentDropdownList
     * @desc 部门下拉列表
部门下拉列表
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.DropdownList();
export const url =
  '/rhro-service-1.0/department/getDepartmentDropdownList:POST';
export const initialUrl =
  '/rhro-service-1.0/department/getDepartmentDropdownList';
export const cacheKey = '_department_getDepartmentDropdownList_POST';
export async function request(
  data: defs.admin.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/department/getDepartmentDropdownList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/department/getDepartmentDropdownList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
