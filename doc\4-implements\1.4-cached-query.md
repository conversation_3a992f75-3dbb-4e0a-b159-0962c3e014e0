<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-29 09:38:30
 * @lastTime: 2019-08-28 14:36:35
 * @LastAuthor   : 侯成
 * @message: 
 -->
# 缓存版查询
将基本查询升级为支持缓存的高级查询。以 `src/pages/sysmanage/Account/index.tsx` 为例。

## 外部引用
仅显示与基础版有区别的引用。
```tsx
// src\pages\sysmanage\Role\index.tsx
import { ConnectState } from '@/models/connect';
import { pageCacheRequest, pageInit } from '@/utils/methods/pagenation';
import { getTableQueries, getTableParams, CacheLib, ITableParams } from '@/utils/methods/cache';
```

### 基础类型定义

绑定dva

```tsx
// src\pages\sysmanage\Role\index.tsx

// 定义 `service` 对象

const service = API.admin.role.postRoleList

@connect(({ cache, sysrole, loading }: ConnectState & SystemConnectState) => ({
  roles: getTableQueries<RoleTable>(service, cache!.tableQueries),
  rolesParams: getTableParams<QueryInput>(service, cache!.tableParams),
  // 以下可选
  cacheLoading: loading.models.cache,
}))
class TableList extends Component<TableListProps, TableListState> {
  state: TableListState = {
    selectedRows: [],
  }
  // ... ...
}
```

### 给查询表单设置默认值
```tsx
// src\pages\sysmanage\Role\index.tsx

<Row gutter={\{ md: 24, lg: 24, xl: 48 }\}>
  <Col md={6} sm={12}>
    <FormItem label="角色名称">
      {getFieldDecorator('roleName', { initialValue: rolesParams.roleName })(<Input placeholder="请输入" />)}
    </FormItem>
  </Col>
  <Col md={6} sm={12}>
    <FormItem label="业务类型">
      {getFieldDecorator('bizCategory', { initialValue: rolesParams.bizCategory })(mapToSelectors(bizCategoryMap, {allowClear: true}))}
    </FormItem>
  </Col>
  <Col md={6} sm={12}>
    <FormItem label="角色层级">
      {getFieldDecorator('roleGrade', { initialValue: rolesParams.roleGrade })(mapToSelectors(deptGradeMap, { allowClear: true }))}
    </FormItem>
  </Col>
  <Col md={6} sm={12}>
    <FormItem label="角色状态">
      {getFieldDecorator('isDeleted', { initialValue: rolesParams.roleGrade })(mapToSwitch(isRoleDeleted, { allowClear: true }))}
    </FormItem>
  </Col>
</Row>
```

### 查询方法实现

三个核心方法

```tsx
// src\pages\sysmanage\Role\index.tsx

// 组件加载即查询，视需求而定。
componentDidMount() {
  this.queryRoles();
}

queryRoles = (queryInput?: QueryInput, pagination?: PaginationConfig) => {
  const { dispatch } = this.props;
  
  const values = { ...rolesParams, ...queryInput };
  // 基础版中pageRequest变为pageCacheRequest
  const params = pageCacheRequest<QueryInput>(values, pagination);
  // dispatch的type变为cache
  dispatch!({
    type: 'cache/getPageQueries',
    payload: { service, params },
  }).then(() => this.setState({ selectedRows: [] }));
};

handleStandardTableChange = (
  pagination: PaginationConfig,
  filtersArg: Record<keyof RoleTable, string[]>,
  sorter: SorterResult<RoleTable>,
) => {
  const filtered = filterToQuery(filtersArg)
  this.queryRoles(filtered, pagination);
};

handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault();
  const { form } = this.props;
  form.validateFields((err, fieldsValue) => {
    if (err) return;
    this.queryRoles(fieldsValue, pageInit);
  });
};
```

`queryRoles` 有更改，基础版中 `pageRequest` 变为 `pageCacheRequest`。

`handleSearch` 有更改，`this.queryRoles` 调用时，第二个参数需传入 `pageInit`。

请求数据时的 `dispatch` 指向 `cache/getPageQueries`，传入参数为{ service, params }，

升级至此完成。
