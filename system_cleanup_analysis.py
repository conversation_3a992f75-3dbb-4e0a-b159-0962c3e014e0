#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
System Cleanup Analysis Module

This module provides a structured analysis of files typically targeted by 
system optimization scripts, including categories, locations, size estimates,
impact assessments, and safety levels.
"""

# Analysis of files typically deleted by optimization scripts
CLEANUP_FILE_ANALYSIS = {
    "categories": [
        {
            "name": "Temporary Files",
            "description": "System and application temporary files",
            "file_types": ["*.tmp", "*.temp", "Temp*", "tmp*"],
            "typical_locations": [
                "C:\\Windows\\Temp\\",
                "C:\\Users\\<USER>\\AppData\\Local\\Temp\\",
                "C:\\Users\\<USER>\\AppData\\Roaming\\Temp\\"
            ],
            "size_estimate": "100MB - 2GB",
            "impact": "Low - Frees up disk space, may improve system performance slightly",
            "safety_level": "High"
        },
        {
            "name": "Log Files",
            "description": "System and application log files",
            "file_types": ["*.log", "*.log.*", "Logs"],
            "typical_locations": [
                "C:\\Windows\\Logs\\",
                "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\WER\\",
                "C:\\ProgramData\\Microsoft\\Windows\\WER\\",
                "C:\\Users\\<USER>\\AppData\\Local\\Temp\\",
                "C:\\Windows\\System32\\LogFiles\\"
            ],
            "size_estimate": "50MB - 500MB",
            "impact": "Low to Medium - Frees disk space, minimal impact on system functionality",
            "safety_level": "High"
        },
        {
            "name": "Browser Cache",
            "description": "Web browser cache files",
            "file_types": ["Cache", "GPUCache", "Media Cache", "Storage"],
            "typical_locations": [
                "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cache\\",
                "C:\\Users\\<USER>\\AppData\\Local\\Mozilla\\Firefox\\Profiles\\*\\cache2\\",
                "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Cache\\",
                "C:\\Users\\<USER>\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data\\Default\\Cache\\"
            ],
            "size_estimate": "100MB - 2GB",
            "impact": "Low - Frees disk space, may require websites to reload resources",
            "safety_level": "High"
        },
        {
            "name": "Download History",
            "description": "Browser download history and temporary files",
            "file_types": ["History", "Thumbnails", "Top Sites", "Visited Links"],
            "typical_locations": [
                "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\",
                "C:\\Users\\<USER>\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles\\*\\",
                "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\"
            ],
            "size_estimate": "10MB - 100MB",
            "impact": "Low - Clears browser history, may affect autocomplete suggestions",
            "safety_level": "Medium"
        },
        {
            "name": "Windows Update Files",
            "description": "Windows update temporary files and installation remnants",
            "file_types": ["$NTUninstall*", "*.cab", "*.msu", "*.msi"],
            "typical_locations": [
                "C:\\Windows\\SoftwareDistribution\\Download\\",
                "C:\\Windows\\Installer\\",
                "C:\\Windows\\Temp\\",
                "C:\\Windows\\WinSxS\\"
            ],
            "size_estimate": "500MB - 5GB",
            "impact": "Medium - Frees significant disk space, essential for Windows updates",
            "safety_level": "Medium"
        },
        {
            "name": "Recycle Bin",
            "description": "Deleted files stored in the Recycle Bin",
            "file_types": ["*"],
            "typical_locations": [
                "C:\\$Recycle.Bin\\"
            ],
            "size_estimate": "Variable - 100MB to several GB",
            "impact": "Low - Frees disk space, files are already deleted but retained for recovery",
            "safety_level": "High"
        },
        {
            "name": "Thumbnail Cache",
            "description": "Image and file thumbnail previews",
            "file_types": ["Thumbs.db", "thumbcache_*.db"],
            "typical_locations": [
                "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\Explorer\\",
                "C:\\Users\\<USER>\\Pictures\\",
                "C:\\Users\\<USER>\\Desktop\\"
            ],
            "size_estimate": "50MB - 300MB",
            "impact": "Low - Frees disk space, thumbnails will regenerate when needed",
            "safety_level": "High"
        },
        {
            "name": "Application Cache",
            "description": "Application-specific cache files",
            "file_types": ["Cache", "cache.db", "cache.tmp"],
            "typical_locations": [
                "C:\\Users\\<USER>\\AppData\\Local\\*\\Cache\\",
                "C:\\Users\\<USER>\\AppData\\Roaming\\*\\Cache\\",
                "C:\\ProgramData\\*\\Cache\\"
            ],
            "size_estimate": "100MB - 1GB",
            "impact": "Low - Frees disk space, applications may take longer to load initially",
            "safety_level": "High"
        },
        {
            "name": "System Restore Points",
            "description": "Windows system restore points",
            "file_types": ["*.sys", "*.bak", "RP*", "SP*"],
            "typical_locations": [
                "C:\\System Volume Information\\"
            ],
            "size_estimate": "1GB - 10GB",
            "impact": "Medium - Frees significant disk space, reduces ability to restore system to previous states",
            "safety_level": "Medium"
        },
        {
            "name": "Prefetch Files",
            "description": "Windows application prefetch data",
            "file_types": ["*.pf"],
            "typical_locations": [
                "C:\\Windows\\Prefetch\\"
            ],
            "size_estimate": "50MB - 200MB",
            "impact": "Low - Frees disk space, Windows may take slightly longer to start applications initially",
            "safety_level": "High"
        },
        {
            "name": "Memory Dump Files",
            "description": "System crash dump files",
            "file_types": ["*.dmp", "memory.dmp", "minidump"],
            "typical_locations": [
                "C:\\Windows\\Minidump\\",
                "C:\\Windows\\LiveKernelReports\\",
                "C:\\Windows\\MemoryDumps\\"
            ],
            "size_estimate": "100MB - 2GB",
            "impact": "Low - Frees disk space, removes diagnostic information for system crashes",
            "safety_level": "High"
        },
        {
            "name": "Old Driver Files",
            "description": "Previous versions of device drivers",
            "file_types": ["*.sys", "*.dll", "*.inf"],
            "typical_locations": [
                "C:\\Windows\\System32\\DriverStore\\FileRepository\\",
                "C:\\Windows\\inf\\"
            ],
            "size_estimate": "500MB - 3GB",
            "impact": "Medium - Frees disk space, removes ability to roll back to previous drivers",
            "safety_level": "Medium"
        }
    ],
    "summary": {
        "total_categories": 12,
        "total_estimated_space": "2.6GB - 21.4GB",
        "safety_distribution": {
            "High": 7,
            "Medium": 4,
            "Low": 1
        }
    }
}

# Example usage function
def print_cleanup_analysis():
    """
    Print a formatted analysis of cleanup categories
    """
    print("System Cleanup Analysis")
    print("=" * 50)
    
    for category in CLEANUP_FILE_ANALYSIS["categories"]:
        print(f"\nCategory: {category['name']}")
        print(f"Description: {category['description']}")
        print(f"File Types: {', '.join(category['file_types'])}")
        print(f"Typical Locations: {', '.join(category['typical_locations'])}")
        print(f"Size Estimate: {category['size_estimate']}")
        print(f"Impact: {category['impact']}")
        print(f"Safety Level: {category['safety_level']}")
        print("-" * 30)

if __name__ == "__main__":
    print_cleanup_analysis()