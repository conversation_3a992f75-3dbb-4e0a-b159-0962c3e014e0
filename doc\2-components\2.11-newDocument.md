<!--
 * @Author: 严小强
 * @since: 2019-08-29 16:35:28
 * @严小强: Do not edit
 * @lastTime: 2019-09-02 17:45:43
 * @message:
 * @Email: <EMAIL>
 -->

# 新增修改表单组件

```tsx
// src/components/EditeForm/AddForm.tsx  通用新增组件
// src/components/EditeForm/UpdateForm.tsx  通用修改组件
// src/components/EditeForm/DetailForm.tsx  通用详情组件
// 这三用法不复杂，具体用法点开文件看interface即可，也可全局搜索看以前写的页面

// src\components\EditeForm\Item\CheckboxItem.tsx
<RadioItem /> // 单选的Checkbox,默认选中1，未选中0
<CheckboxItem options={[
              { key: '8', shortName: '退回修改' },
              { key: '7', shortName: '撤回' },
              { key: '5', shortName: '完成' },
              { key: '9', shortName: '作废' },
            ]}/> //多选的Checkbox
// src/components/EditeForm/index.tsx  根据对象生成表单内的操作框
// 此组件一般不会单独使用，而是使用src\components\CachedPage\EnumerateFields.tsx的EnumerateFields组件根据数组直接生成一组表单
// 会在EnumerateFields种详解用法

```

## 根据对象生成表单内的操作框

```tsx
// 组件目录
// src/components/EditeForm/index.tsx

// 类型定义
interface EditeFormProps {
  // 当前操作框的key,即后台所需参数
  fieldName: string;
  // 当前操作框的名称
  label: string | ReactNode;
  // 外部form
  outerForm?: FormInstance;
  // 自定义render返回渲染所需的默认值。
  render?: (outerForm: FormInstance, record?: any) => JSX.Element | Primitive;
  // 自定义inputRender返回
  inputRender?: inputRender;
  // 禁用设置
  disableFilter?: (outerForm: FormInstance, options?: Partial<EditeFormProps>) => boolean;
  // getFieldDecorator 方法内的属性
  formOptions?: formOptions;
  // input，select等antd组件的属性。
  inputProps?: POJO & Partial<GeneralInputProps>;
  // 列数
  colNumber?: number;
  // 是否不需要默认包裹Col以及Form，默认包裹
  noCol?: boolean;
  colProps?: ColProps;
  // 表单验证规则
  rules?: Rule[];
  // 换行
  br?: boolean;
  record?: Store;
  hidden?: boolean; // 隐藏不同状态下的formItem,此装态不会保留form元素，最终form.validateFields()等取值不会取到该值
  hiddenForm?: boolean; // 隐藏不同状态下的formItem,此装态会保留form元素,可以取值，只是显示状态为隐藏
  noBorder?: boolean;
  shouldUpdate?:
    | boolean
    | ((
        prevValues: Store,
        nextValues: Store,
        info: {
          source?: string | undefined;
        },
      ) => boolean);
  // 去除校验空格的rule
  ignoreTrim?: boolean;
}
```

# 主页查询表单

```tsx
// src\components\CachedPage\index.tsx

// 类型定义
interface CachedPageProps
  extends Partial<CachedTableProps & CachedFormProps & useStandardTableProps> {
  // 生成查询form表单
  formColumns: EditeFormProps[];
  // 查询接口
  service: IServiceType;
  // 生成按钮
  renderButtons?: (options: WritableInstance) => React.ReactNode;
  // 处理查询数据
  handleQueries?: (values: Store) => Store | undefined | void;
  // 其他隐藏查询条件
  fixedValues?: POJO;
  hooksProps?: Partial<useStandardTableProps>;
  disabledFields?: POJO<boolean>;
  rules?: { [k: string]: Rule[] };
  //
  cardProps?: CardProps;
  onSuccess?: (data: any) => void;
  afterRequest?: (data: TablePage<any>) => TablePage<any>;
  colNumber?: number;
  newEditable?: boolean;
  tableTitle?: string | ReactNode;
}
// 继承了CachedForm以及CachedTable的类型定义，因为CachedPage是由这两组件组成

// CachedForm的类型定义
interface CachedFormProps extends FormProps {
  // 生成查询form表单
  formColumns: EditeFormProps[];
  // 查询接口
  service?: IServiceType;
  // StandardTable的配置
  table: POJO;
  // 生成按钮
  renderButtons?: (_options: WritableInstance) => React.ReactNode;
  // 处理查询数据，若想取消查询，就返回 undefined
  handleQueries?: (values: Store) => Store | undefined | void;
  // 其他隐藏查询条件
  fixedValues?: POJO;
  text?: string | (() => void); //为了头部搜索框上面添加说明文字
  // 每行显示几个FormItem，查询表单我们默认一般为4，新增修改一般默认为3
  colNumber?: number;
  disabledFields?: POJO<boolean>;
  rules?: { [k: string]: Rule[] };
  // 为class组件使用form控制
  formRef?: React.RefObject<FormInstance<any>>;
  // onsubmit前触发事件（form validator的校验的可拓展性之一（直接弹message框...））
  beforeSubmitEvent?: () => Promise<T>;
  // 为表格设置默认的pageSize
  initPageInfo?: PaginationConfig;
}
// 继承了antd的form表单的props

// CachedTable的类型定义
export interface CachedTableProps extends Partial<WritableProps> {
  // 查询接口
  service: IServiceType;
  // StandardTable的配置
  // table: POJO;
  // 可编辑Table
  wriForm?: FormInstance;
  // 是否有缓存,default:true
  cached?: boolean;
  // 是否可以编辑,default:false
  editable?: boolean | 'add';
}
// 继承了Writable的props类型定义
// 由editable来判断是可编辑table还是不可编辑table

// 用法
<CachedPage
  formColumns={formColumns}
  service={service}
  columns={columns}
  renderButtons={renderButton}
  initialValues={{ state: 1 }}
/>;

const renderButton = (options: WritableInstance) => {
  _options = options;
  return (
    <React.Fragment>
      <AuthButtons funcId="50301020">
        <Button type="primary" htmlType="submit">
          查询
        </Button>
      </AuthButtons>
      <AuthButtons funcId="50301010">
        <Button onClick={toVerifyAmt}>核销</Button>
      </AuthButtons>
      <AuthButtons funcId="50301020">
        <AsyncButton onClick={onSaveRemark}>保存备注</AsyncButton>
        <AsyncButton onClick={onExport} disabled={isEmpty(_options.queries)}>
          导出数据
        </AsyncButton>
      </AuthButtons>
      <ImportForm
        btnName="导入"
        downBtnName="下载模板"
        fileSuffix=".xls"
        ruleId="900000280"
        serviceName="socialManageService"
        btnEnable
        showImpHisBtn
        beforeUpload={beforeUpload}
        handleQueries={handleQueries}
        afterUpload={afterUpload}
        selectedRec={isEmpty(_options.selectedSingleRow) ? [] : [_options.selectedSingleRow]}
      />
    </React.Fragment>
  );
};
// renderButtons为查询表格下的按钮列表
// AuthButtons为权限按钮控制，funcId为菜单列表中按钮的funcId,可包裹一个或多个按钮
// AsyncButton为异步按钮onClick方法为async await用法时会自动调用接口时loading，防止多次点击，其他用法与Button一致
// ImportForm是公共的导入列表按钮
// ImportForm类型定义
interface ImportFormProps {
  // 按钮名称
  btnName: string;
  // 下载按钮名称
  downBtnName?: string;
  ruleId: string;
  // 上传文件所需的bizType
  bizType?: string;
  baseDataCode?: string;
  showImpHisByAuthType?: string;
  serviceName: string;
  // 按钮是否可用
  btnEnable?: boolean;
  // 查看导入历史的按钮名称，默认为查看导入历史
  impHisBtnName?: string;
  // 是否可以查看导入历史
  showImpHisBtn?: boolean;
  // 是否显示下载按钮
  showImpDownBtn?: boolean;
  // 是否显示导入结果
  showResultBtn?: boolean;
  fileSuffix?: string;
  // 选择的行数据
  selectedRec?: POJO[];
  exhParams?: POJO;
  // 是否显示导入结果页面的查询按钮
  showQueryElement?: boolean;
  // 导入前
  beforeUpload?: () => Promise<boolean>;
  // 对导入参数进行处理
  handleQueries?: (values?: POJO) => POJO | undefined;
  // 导入完成之后
  afterUpload?: (data?: POJO) => void;
  impHisHandleQuerues?: (values?: POJO) => POJO | undefined;
  formData?: POJO;
  parameters?: POJO;
  clearAddForm?: boolean;
  width?: number;
  funcCode?: string; //导入按钮权限设置
  checkValid?: boolean;
  linkType?: boolean; //导入按钮样式
}
// 有些不是公用的props，被改太多次了
```

## 根据列表生成 form 表单

```tsx
// src\components\CachedPage\EnumerateFields.tsx
// 类型定义
interface EnumerateFieldsProps {
  // 生成查询form表单,可以看上面的类型定义详情
  formColumns: EditeFormProps[];
  // 外部form
  outerForm: FormInstance;
  // 列数
  colNumber?: number;
  // 禁用的表单
  disabledFields?: POJO<boolean>;
  readOnlyFields?: POJO<boolean>;
  // 验证规则
  rules?: { [k: string]: Rule[] };
  // 全部禁用
  disabled?: boolean;
  readOnly?: boolean;
  // 去除校验空格的rule
  ignoreTrim?: boolean;

  // 统一设置FormItem
  itemOptions?: formOptions;
  record?: Store;
}
// 用法
const [form] = Form.useForm();
<EnumerateFields outerForm={form} colNumber={3} formColumns={formColumns} />;
const formColumns: EditeFormProps[] = [
  {
    label: '大区',
    fieldName: 'areaId',
    inputRender: () => <AreaSelector allowClear onChange={(val: any) => requestDepart(val)} />,
  },
  {
    label: '分公司',
    fieldName: 'providerIds',
    inputRender: () => mapToSelectors(deptmentMap, { allowClear: true, showSearch: true }),
  },
  {
    label: '',
    fieldName: '',
    inputRender: () => (
      <DateRange
        fields={[
          {
            title: '到款时间>=',
            dataIndex: 'cashDtStart',
          },
          {
            title: '到款时间<=',
            dataIndex: 'cashDtEnd',
          },
        ]}
        format={stdDateFormat}
      />
    ),
  },
  { label: '到款金额>=', fieldName: 'arriveAmtS', inputRender: 'number' },
  { label: '到款金额<=', fieldName: 'arriveAmtE', inputRender: 'number' },
  { label: '余额>=', fieldName: 'balanceSt', inputRender: 'number' },
  { label: '余额<=', fieldName: 'balanceEd', inputRender: 'number' },
  { label: '未核销金额>=', fieldName: 'unVerifyamtSt', inputRender: 'number' },
  { label: '未核销金额<=', fieldName: 'unVerifyamtEd', inputRender: 'number' },
  { label: '到款客户', fieldName: 'cashUser', inputRender: 'string' },
  {
    label: '是否有效',
    fieldName: 'state',
    inputRender: () => mapToSelectors(statusMap, { allowClear: true }),
  },
  {
    label: '',
    fieldName: '',
    inputRender: () => (
      <DateRange
        fields={[
          {
            title: '上传时间>=',
            dataIndex: 'createDtSt',
          },
          {
            title: '上传时间<=',
            dataIndex: 'createDtEd',
          },
        ]}
        format={stdDateFormat}
      />
    ),
  },
  {
    label: '冻结状态',
    fieldName: 'freeze',
    inputRender: () => mapToSelectors(freezeStatusMap, { allowClear: true }),
  },
  {
    label: '签单分公司抬头',
    fieldName: 'titleId',
    inputRender: () => (
      <GetBaseSelector
        params={{ statementName: 'OR_INVOICE_INFO.getSysBranchTitleList' }}
        showSearch
        allowClear
      />
    ),
  },
  {
    label: '核销人',
    fieldName: 'verifyBy',
    inputRender: () => (
      <SelectInnerEmpAmbPop
        title=""
        rowValue="verifyBy-verifyByName"
        keyMap={{
          verifyBy: 'EMPID',
          verifyByName: 'REALNAME',
        }}
      />
    ),
  },
];
```

# 下拉选项

```tsx
// src\components\Selectors
// src\components\Selectors\FuncSelectors.tsx
renderListToSelectors
mapToSelectors
// 这两个方法是用的最多的
// mapToSelectors接收两个参数
nameMap: Map<T, string | number> | undefined // 选项map
// 例如
const statusMap = new Map<number, string>([
  [1, '有效'],
  [2, '作废'],
]);
options?: SelectProps<T> & { useValue?: boolean } // antd的Select的props

// renderListToSelectors接收3个参数
list: POJO[] | undefined // 数组，后台接口返回的
keyPairs?: TListKeyPairs // 类似于['key', 'shortName']，key对应下拉选项选中后的id，shortName对应选中后的显示值，同时对应后台返回的list中的id跟value
options?: SelectProps<SelectValue> & Enhanced // antd的Select的props



// src\components\Selectors\BaseSelectors.tsx
// 类型定义
export interface StdSelectProps extends SelectProps<string> {
  // onConfirm?: <P extends typeof class>(data: P) => void;
  named?: boolean;
  skipEmptyParam?: boolean;
  // 在下拉框打开时才加载数据
  requestOnOpen?: boolean;
  // 既不不用加载数据，又有默认值的情况下，传入此值。将被渲染为一个默认选项。
  defaultdata?: { key: SelectValue; value: SelectValue };
  onValueFound?: (value: string | undefined, text: string | undefined, row: Store) => void;
  readOnly?: boolean;
  onChange?: (value: SelectValue, option: POVO | POVO[]) => void;
  onConfirm?: (data: any, options?: POJO) => void;
  advanceChange?: (data: POVO) => void;
  onMultiConfirm?: (data: POJO[], options?: POJO) => void;
  onLoaded?: (data?: POJO[], options?: POJO) => void;
  // 由于java DTO的默认行为，会把一些没数据的字段初始化默认控制，boolean => false, number => 0 等，但是前端不应显示这些值，在emptyValues中加入这些值可以过滤调。
  emptyValues?: any[];
  keyMap?: POJO<string>;
  editableDefaultValue?: string;
  // 筛选字段
  filterKeys?: string[];
  // 关联字段 如 城市需要与省份关联
  relation?: { key: string; value?: string };
}

interface CommonProps extends StdSelectProps {
  keyType?: TkeyType;
  dataDisabled?: number;
  params?: POJO;
  paramsData?: POJO;
  fixedParams?: POJO;
  code?: TOptionsValue;
  nestedKey?: string;
  mergedKeys?: string[];
  keyPairs?: TListKeyPairs;
  service: IServiceType;
  dataSource?: POJO[];
}

export interface BaseSelectorsViewProps extends CommonProps {
  listQueryMaps?: IListQueryMaps;
  undefinedParams?: boolean;
  cachedParams: string;
  cachedPoint: string;
  onSuccess?: (data: any) => void;
}

export interface BaseSelectorsProps<T extends TOptionsValue> extends CommonProps {
  listQueries?: IListQueries<T>;
  onSuccess?: (data: any) => void;
  ref?: TRef;
}
// 此组件过于复杂，只需去src\components\Selectors\BaseDataSelectors.tsx目录下复制粘贴修改接口以及新select组件名称即可
// 然后去src\components\Selectors\index.tsx该目录上添加导出即可使用
```

# 公共弹窗选项

```tsx
// src\components\StandardPop
// src\components\StandardPop\libs\StdPop.tsx
// 该组件是用于弹窗查询选择数据
// 类型定义
interface DerivedPopProps<T> {
  modalTitle?: string;
  title?: string;
  label?: string;
  modalwidth?: number;
  searchable?: boolean;
  notLableTitle?: string;
  readOnly?: boolean;
  distroyOnClose?: boolean;
  requestOnMounted?: boolean;
  outerForm?: FormInstance;
  fixedValues?: POVO;
  initData?: Partial<T>;
  editableDefaultValue?: POVO;
  formItemProps?: Partial<FormItemProps>;
  keyMap?: { [k: string]: string };
  rowKey?: string;
  // 横杠分隔多个值
  unionKey?: string;
  // 横杠分隔多个值，第一个值将作为rowKey, 最后一个值将作为rowValue。
  rowValue?: string;
  // 表单字段前缀，用Writable中。
  prefix?: string;
  // handdleConfirm中仅返回 rowValue 中的字段。
  rowValueOnly?: boolean;
  // handdleConfirm中仅返回 keyMap 中的字段。
  keyMapOnly?: boolean;
  rowValueData?: Record<string, unknown>;
  inputStyle?: React.CSSProperties;
  disabled?: boolean;
  multiple?: boolean;
  noDoubleSelect?: boolean;
  // 选择完的回调，会带出选择的数据
  handdleConfirm?: (value?: T) => void;
  advanceChange?: (value: POVO) => void;
  handdleMultiConfirm?: (values?: any[]) => void;
  // 查询数据处理
  haddleQueries?: (values: POJO) => POJO | null;
  onConfirm?: (value: T, options: IOnConfirmOptions<T>) => void;
  handleClear?: (value?: T) => void;
  handleCacheValue?: (value?: T) => void;
  buttonMode?: boolean;
  noClear?: boolean;
  tableProps?: EnhancedTableProps<any>;
  addonAfter?: React.ReactNode;
  addonBefore?: React.ReactNode;
  disabledFields?: boolean;
}

interface StandardPopProps<T extends unknown> extends DerivedPopProps<T> {
  nestedKey?: string;
  service: IServiceType;
  responseResolve?: (data: POJO | undefined, pageValues: POJO) => TablePage<T>;
  columns: ItemStruct[];
  renderQueryForm?: (props: QueryFormProps<T>) => JSX.Element;
  formColumns?: EditeFormProps[];
  beforeQuery?: (params: POJO) => boolean;
}
// 此组件同样太复杂，知道用法即可
<SelectInnerEmpAmbPop
  rowValue="verifyBy-verifyByName"
  keyMap={{
    verifyBy: 'EMPID',
    verifyByName: 'REALNAME',
  }}
  handdleConfirm={(data: any) => {
    //做你想要做的事
  }}
/>;
// rowValue第一个会作为列表的主键，必须唯一,最后一个作为选中后文本框的显示值
// 因为选中的列表值的字段可能跟你想要的不一样，keyMap就是用来转换用的，把列表的值的key转成你想要的
```

# 显示 table

```tsx
// src\components\StandardTable\index.tsx
// 类型定义
interface StandardTableProps<T = unknown> {
  /** 可编辑表格强制使用虚拟滚动 (非紧急不要使用) */
  forceEnterVirtualWithEditable?: boolean;
  //forceEnterVirtual===false时 强制不进入虚拟
  forceEnterVirtual?: boolean;
  enterVirtualLine?: number;
  overscanRowCount?: number;
  columns?: StandardTableColumnProps<T>[];
  // checkbox选择行
  onSelectRow?: (rows: T[]) => void;
  // 单次点击事件
  onSelectSingleRow?: (rows: T) => void;
  // 双击事件
  onSelectDoubleRow?: (rows: T) => void;
  // table数据源
  data: { list: T[]; pagination: PaginationConfig };
  // 主键
  rowKey?: string | ((record: T, index: number) => string);
  // 选中的行需配合onSelectRow使用
  selectedRows?: T[];
  //  单击选中的行需配合onSelectSingleRow使用
  selectedSingleRow?: T;
  defaultSelectedRowKeys?: string[];
  onChange?: (
    pagination: PaginationConfig,
    filters: Record<keyof T, string[]>,
    sorter: SorterResult<T>,
    extra?: TableCurrentDataSource<T>,
    tableConfig?: any,
  ) => void;
  loading?: boolean;
  dragable?: boolean;
  editable?: boolean;
  // New: 表格滚到指定的行
  classStyle?: string;
  positionIndex?: number;
  className?: string;
  rowClassName?: string | ((record: T, index: number) => string);
  tableOptions?: StandardTableHooksOptions;
  // 缓存key 默认是columns hash ,当columns动态可变时需要手动设置
  cacheKey?: string;
  // 锁定列数 默认值columns设置了fixed的索引或0
  lockNumber?: number;
}
interface EnhancedTableProps<T = any> extends StandardTableProps<T> {
  showAlert?: boolean;
  readOnly?: boolean;
  counter?: number;
  showLineSize?: number;
  onRow?: GetComponentProps<T>;
  onCellChange?: (nextSource: POTO) => void;
  rowSelectType?: 'checkbox' | 'radio';
  bordered?: boolean | undefined;
  scroll?: {
    x?: boolean | number | string;
    y?: boolean | number | string;
    scrollToFirstRowOnChange?: boolean;
  };
  // 是否竖向滚动，通常在数据多又不分页的表格中。
  scrollVertical?: boolean;
  // 新增是否显示选择框
  notShowRowSelection?: boolean;
  // 新增是否显示分页
  notShowPagination?: boolean;
  //前端分页
  sortByFrontend?: boolean;
}
// 基本用法
<StandardTable data={{ list: [], pagination: {} }} columns={columns} />;
const columns: StandardTableProps<defs.finance.OrUploadCashRecordDetail>[] = [
  { title: '到款编号', dataIndex: 'cashId' },
  { title: '到款客户', dataIndex: 'cashUser' },
  { title: '到款时间', dataIndex: 'cashDt' },
  {
    title: '已核销金额',
    dataIndex: 'verifyAmt',
    render: (value, r) => (
      <Typography.Link
        onClick={(e) => {
          e.preventDefault();
          setSingleRow(r);
          verifyAmtClick();
        }}
      >
        {formatAmount(value)}
      </Typography.Link>
    ),
  },
  { title: '费用属性', dataIndex: 'allowanceType', render: (value) => alllowMap.get(+value) },
];

// src\components\StandardTable\hooks\useStandardTable.tsx StandardTable的hook
const [table, options] = StandardTable.useStandardTable({
  service: API.commons.file.getFileInfo,
});
<StandardTable columns={columns} dragable notShowRowSelection {...(table as any)} />;
//  table中包含的类型定义
interface StandardTableHooksProps<T> {
  data: { list: T[]; pagination: PaginationConfig };
  selectedRows: T[];
  onSelectRow: (rows: T[]) => void;
  onChange: (
    pagination: PaginationConfig,
    filtersArg: Record<any, any>,
    sorter: SorterResult<any>,
    extra: { currentDataSource: []; action: 'paginate' | 'sort' | 'filter' },
  ) => void;
  loading: boolean;
  // 延时取消的loading，防止button连点用的
  loadingDelayed: boolean;
  selectedSingleRow?: T;
  onSelectSingleRow?: (rows: T) => void;
  onSelectDoubleRow?: (rows: T) => void;
  rowKey?: string | ((row: T) => string);
}
// options中包含的类型定义
interface StandardTableHooksOptions {
  queries: POJO;
  request: (
    values?: any,
    pagination?: PaginationConfig,
    requestNotShowPagination?: boolean,
    options?: RequestConfig,
  ) => Promise<TablePage<any>>;
  setQueries: (queries: any) => void;
  setData: (data: TablePage<any>) => void;
  // 设置新数据
  setNewData: (data: TablePage<any> | any[], options?: SetNewDataOptions) => TablePage<any>;
  setSelectedRows: (row: any[]) => void;
  setSelectedSingleRow: (row: any) => void;
  readonly size: number;
}
```

# 可编辑 table

```tsx
// src\components\Writable\index.tsx
// 类型定义
interface WritableProps<T = unknown> extends EnhancedTableProps<T> {
  service: IServiceType;
  data?: TablePage<T>;
  addButtons?: POJO[];
  noAddButton?: boolean;
  noDeleteButton?: boolean;
  readOnly?: boolean;
  disabled?: boolean;
  columns: (StandardTableColumnProps<T> & WritableColumnProps<T>)[];
  wriForm?: FormInstance;
  wriTable?: ReturnType<typeof useWritable>;
  editable?: 'add' | boolean;
  onBoxChange?: (
    index: number,
    key: string,
    value: Partial<T>,
    record?: T,
    options?: TValidateOptions,
  ) => void;
  onWriteFromChange?: (options: OnWriteFromChangeOptions) => void;
  selectedEdited?: boolean;
}

// WritableColumnProps类型定义
interface WritableColumnProps<T extends unknown> extends ColumnProps<T> {
  inputRender?: GeneralInputRender<T>;
  inputProps?: POJO;
  disabled?: boolean;
  readOnly?: boolean;
  rules?: Rule[];
  hidden?: boolean;
  advanceRules?: AdvanceRule[];
  formItemOptions?: FormItemProps;
  onGridChange?: (value: Partial<T>, options: GeneralInputRenderOption<any>) => void;
  onMounted?: (options: GeneralInputRenderOption<any>) => void;
  onBoxChange?: (
    serial: number,
    key: string,
    value: Partial<T>,
    record?: T,
    options?: GeneralInputRenderOption<any>,
  ) => void;
}

// 此组件太复杂，知道用法即可
const wriTable = useWritable({ service: service });

<Writable columns={columns2} service={service} wriTable={wriTable} />;

const columns: WritableColumnProps<defs.emphiresep.EmployeeFee>[] = [
  { title: '产品名称', dataIndex: 'productName' },
  {
    title: '收费起始月',
    dataIndex: 'chargeStartDate',
    inputRender: 'month',
    inputProps: { format: stdMonthFormatMoDash },
    rules: [{ required: true, message: '请输入收费起始月' }],
    onGridChange: (value: any, options: GeneralInputRenderOption<any>) => {
      const { chargeStartDate } = value;
      nonSsChargeStartDate_changeHandler(chargeStartDate, options);
    },
  },
  {
    title: '账单起始月',
    dataIndex: 'billStartMonth',
    inputRender: 'month',
    inputProps: {
      format: stdMonthFormatMoDash,
      disabledDate: (current: string | undefined) => {
        const nowDate = moment(new Date()).add(3, 'months').format(stdMonthFormatMoDash);
        const tooLate = current && moment(current).diff(nowDate, 'months') > 0;
        return tooLate;
      },
    },
    onGridChange: (value: any) => {
      const { billStartMonth } = value;
      billStartMonth_changeHandler(billStartMonth);
    },
  },
  {
    title: '企业金额',
    dataIndex: 'eAmt',
    inputRender: (options: GeneralInputRenderOption<any>) => {
      return <InputNumber onBlur={(e) => changeEAmt(e.target.value, options)} />;
    },
    rules: [{ required: true, message: '请输入企业金额' }],
  },
  {
    title: '个人比例',
    dataIndex: 'pRatio',
    inputRender: (options: GeneralInputRenderOption<any>) => {
      return <Input readOnly onClick={() => taggleOnShowRatio(options)} />;
    },
  },
  { title: '备注', dataIndex: 'remark', inputRender: 'text' },
];
// inputRender可以为sting也可以是一个方法
// 包括
//   string: stringInput,
//   password: passwordInput,
//   float: stringInput,
//   number: numberInput,
//   month: monthPicker,
//   datetime: datetimePicker,
//   date: datePicker,
//   text: textArea,
//   tableText: tableTextArea,
//   upload: upload,
//   multiupload: multiupload,
//  EnumerateFields里的inputRender跟这个一样
// onGridChange相当于onChange方法，返回两个参数，一个value当前更改后的值，一个options
// options 中有三个参数返回 record: T; text: string | number; serial: number;
// record是一行的值，text是当前的值，serial是一行的主键
```
