import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employee/saveEmpInfoInter
     * @desc 保存员工国际化信息
保存员工国际化信息
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.EmpInfoInter();
export const url = '/rhro-service-1.0/employee/saveEmpInfoInter:GET';
export const initialUrl = '/rhro-service-1.0/employee/saveEmpInfoInter';
export const cacheKey = '_employee_saveEmpInfoInter_GET';
export async function request(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/saveEmpInfoInter`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/saveEmpInfoInter`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
