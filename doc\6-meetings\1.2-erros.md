1. ERROR  Failed to compile with 9 errors                                                                         10:31:40
This dependency was not found:

* @/components/Selectors in ./src/pages/sysmanage/Account/index.tsx, ./src/pages/sysmanage/CustManageOrRegist/Comps/CustDetailInfoPage.tsx and 4 others

To install it, you can run: npm install --save @/components/Selectors


2. These relative modules were not found:

* ../QueryExBill/Form/Selectors in ./src/pages/finance/receive/RemoveBill/index.tsx
* ./Form/Selectors in ./src/pages/finance/receive/QueryExBill/index.tsx
* ./tableHeaders in ./src/pages/finance/receive/QueryExBill/Form/CustomerPop.tsx
 
 ERROR CODE  ERR_WEBPACK_MODULE_NOT_FOUND

 3.$ git pull
remote: Counting objects: 210, done.
remote: Compressing objects: 100% (93/93), done.
remote: Total 96 (delta 76), reused 0 (delta 0)
Unpacking objects: 100% (96/96), done.
From chro:chro_web
   e46d3a1..1bab9c0  dev        -> origin/dev
   9fa6e50..5582f07  huan       -> origin/huan
 * [new branch]      newdev     -> origin/newdev
   b5dccda..d17f7de  yxq        -> origin/yxq
Removing src/pages/finance/models/Receive/removeBills.tsx
Removing src/pages/finance/models/Receive/queryExBills.tsx
Removing src/pages/finance/cargo/models.ts
Auto-merging doc/2-components/2.5-base-selectors.md
CONFLICT (content): Merge conflict in doc/2-components/2.5-base-selectors.md
Auto-merging doc/2-components/1.2-autoSelectForm.md
Automatic merge failed; fix conflicts and then commit the result.

$ git merge rei
Auto-merging src/pages/Sales/ContractView/index.tsx
CONFLICT (content): Merge conflict in src/pages/Sales/ContractView/index.tsx
Auto-merging src/pages/Sales/ContractManage/index.tsx
CONFLICT (content): Merge conflict in src/pages/Sales/ContractManage/index.tsx
Auto-merging src/pages/Sales/ContractManage/ContractForm.tsx
CONFLICT (content): Merge conflict in src/pages/Sales/ContractManage/ContractForm.tsx
Auto-merging src/models/cache.ts
CONFLICT (content): Merge conflict in src/models/cache.ts
Auto-merging src/components/StandardPop/AllocateServicePop.tsx
CONFLICT (content): Merge conflict in src/components/StandardPop/AllocateServicePop.tsx
Auto-merging src/components/AutoSelectForm/CodeToViewDeprecated.tsx
CONFLICT (content): Merge conflict in src/components/AutoSelectForm/CodeToViewDeprecated.tsx
Automatic merge failed; fix conflicts and then commit the result.
