class AnnualPay {
  /** annualPayHisId */
  annualPayHisId = undefined;

  /** annualPayId */
  annualPayId = undefined;

  /** avgEndTime */
  avgEndTime = '';

  /** avgPayMon */
  avgPayMon = '';

  /** avgPayYear */
  avgPayYear = '';

  /** avgStartTime */
  avgStartTime = '';

  /** cityId */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** execEndTime */
  execEndTime = '';

  /** execStartTime */
  execStartTime = '';

  /** isDeleted */
  isDeleted = undefined;

  /** minHour1 */
  minHour1 = undefined;

  /** minHour2 */
  minHour2 = undefined;

  /** minHour3 */
  minHour3 = undefined;

  /** minHour4 */
  minHour4 = undefined;

  /** minHour5 */
  minHour5 = undefined;

  /** minHour6 */
  minHour6 = undefined;

  /** minHourCounty1 */
  minHourCounty1 = '';

  /** minHourCounty2 */
  minHourCounty2 = '';

  /** minHourCounty3 */
  minHourCounty3 = '';

  /** minHourCounty4 */
  minHourCounty4 = '';

  /** minHourCounty5 */
  minHourCounty5 = '';

  /** minHourCounty6 */
  minHourCounty6 = '';

  /** minMon1 */
  minMon1 = undefined;

  /** minMon2 */
  minMon2 = undefined;

  /** minMon3 */
  minMon3 = undefined;

  /** minMon4 */
  minMon4 = undefined;

  /** minMon5 */
  minMon5 = undefined;

  /** minMon6 */
  minMon6 = undefined;

  /** minMonCounty1 */
  minMonCounty1 = '';

  /** minMonCounty2 */
  minMonCounty2 = '';

  /** minMonCounty3 */
  minMonCounty3 = '';

  /** minMonCounty4 */
  minMonCounty4 = '';

  /** minMonCounty5 */
  minMonCounty5 = '';

  /** minMonCounty6 */
  minMonCounty6 = '';

  /** payIncludeCpf */
  payIncludeCpf = undefined;

  /** payIncludeCpfStr */
  payIncludeCpfStr = '';

  /** payIncludeSi */
  payIncludeSi = undefined;

  /** payIncludeSiStr */
  payIncludeSiStr = '';

  /** pcName */
  pcName = '';

  /** policyFileName */
  policyFileName = '';

  /** policyFileUrl */
  policyFileUrl = '';

  /** policyLink */
  policyLink = '';

  /** policyUser */
  policyUser = undefined;

  /** policyUserStr */
  policyUserStr = '';

  /** provinceId */
  provinceId = undefined;

  /** provinceName */
  provinceName = '';

  /** statisticsCaliber */
  statisticsCaliber = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';

  /** yearDate */
  yearDate = '';
}

class CalculateDTO {
  /** comboId */
  comboId = undefined;

  /** pfBase */
  pfBase = undefined;

  /** pfRatio */
  pfRatio = '';

  /** pfRatioText */
  pfRatioText = '';

  /** spfBase */
  spfBase = undefined;

  /** spfRatio */
  spfRatio = '';

  /** spfRatioText */
  spfRatioText = '';

  /** ssBase */
  ssBase = undefined;
}

class CityInfo {
  /** branchId */
  branchId = undefined;

  /** cityId */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** headCount */
  headCount = undefined;

  /** pfPayBase */
  pfPayBase = undefined;

  /** ssPayBase */
  ssPayBase = undefined;
}

class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = new DropdownList();

  /** message */
  message = '';

  /** t */
  t = new DropdownList();
}

class DropdownList {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** signBrachTitleId */
  signBrachTitleId = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class Exception {
  /** cause */
  cause = new Throwable();

  /** localizedMessage */
  localizedMessage = '';

  /** message */
  message = '';

  /** stackTrace */
  stackTrace = [];

  /** suppressed */
  suppressed = [];
}

class ExportQuery {
  /** 查询条件 */
  condition = undefined;

  /** 表头字段列表 */
  fieldArr = [];

  /** 表头字段中文拼接 */
  headStr = '';

  /** 表头字段类型列表 */
  typeArr = [];
}

class FilterEntity {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市id逗号分开 */
  cityIds = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 更新日期小于 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 更新日期大于 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class FringeBenefits {
  /** accordingFileNumber1 */
  accordingFileNumber1 = '';

  /** accordingFileNumber2 */
  accordingFileNumber2 = '';

  /** accordingFileNumber3 */
  accordingFileNumber3 = '';

  /** benefitsId */
  benefitsId = undefined;

  /** busnameClassId */
  busnameClassId = undefined;

  /** busnameSubtypeId */
  busnameSubtypeId = undefined;

  /** busnameTypeId */
  busnameTypeId = undefined;

  /** categoryId */
  categoryId = undefined;

  /** cityId */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** createBy */
  createBy = undefined;

  /** createDt */
  createDt = '';

  /** crossCityHandle */
  crossCityHandle = undefined;

  /** crossCityHandleArea */
  crossCityHandleArea = '';

  /** crossProvinceHandle */
  crossProvinceHandle = undefined;

  /** crossProvinceHandleArea */
  crossProvinceHandleArea = '';

  /** crossRegionHandle */
  crossRegionHandle = undefined;

  /** crossRegionHandleArea */
  crossRegionHandleArea = '';

  /** effectiveDate1 */
  effectiveDate1 = '';

  /** effectiveDate2 */
  effectiveDate2 = '';

  /** effectiveDate3 */
  effectiveDate3 = '';

  /** ehandleCondition */
  ehandleCondition = '';

  /** ehandleOfflineProcess */
  ehandleOfflineProcess = '';

  /** ehandleProcess1 */
  ehandleProcess1 = '';

  /** ehandleProcess2 */
  ehandleProcess2 = '';

  /** ehandleProcess3 */
  ehandleProcess3 = '';

  /** fileName1 */
  fileName1 = '';

  /** fileName2 */
  fileName2 = '';

  /** fileName3 */
  fileName3 = '';

  /** handleForm */
  handleForm = '';

  /** handleType */
  handleType = undefined;

  /** handleWindow1 */
  handleWindow1 = '';

  /** handleWindow2 */
  handleWindow2 = '';

  /** handleWindow3 */
  handleWindow3 = '';

  /** isDeleted */
  isDeleted = undefined;

  /** makReservations */
  makReservations = undefined;

  /** orderStatus */
  orderStatus = undefined;

  /** otherHandleInfo1 */
  otherHandleInfo1 = '';

  /** otherHandleInfo2 */
  otherHandleInfo2 = '';

  /** otherHandleInfo3 */
  otherHandleInfo3 = '';

  /** otherHandleInfo4 */
  otherHandleInfo4 = '';

  /** otherHandleInfo5 */
  otherHandleInfo5 = '';

  /** otherHandleInfo6 */
  otherHandleInfo6 = '';

  /** otherPolicyInfo1 */
  otherPolicyInfo1 = '';

  /** otherPolicyInfo2 */
  otherPolicyInfo2 = '';

  /** otherPolicyInfo3 */
  otherPolicyInfo3 = '';

  /** otherPolicyInfo4 */
  otherPolicyInfo4 = '';

  /** otherPolicyInfo5 */
  otherPolicyInfo5 = '';

  /** otherPolicyInfo6 */
  otherPolicyInfo6 = '';

  /** otherPolicyInfo7 */
  otherPolicyInfo7 = '';

  /** otherPolicyInfo8 */
  otherPolicyInfo8 = '';

  /** otherPolicyInfo9 */
  otherPolicyInfo9 = '';

  /** payee */
  payee = '';

  /** personCategoryId */
  personCategoryId = '';

  /** phandleCondition */
  phandleCondition = '';

  /** phandleOfflineProcess */
  phandleOfflineProcess = '';

  /** phandleProcess1 */
  phandleProcess1 = '';

  /** phandleProcess2 */
  phandleProcess2 = '';

  /** phandleProcess3 */
  phandleProcess3 = '';

  /** policyFileId1 */
  policyFileId1 = '';

  /** policyFileId2 */
  policyFileId2 = '';

  /** policyFileId3 */
  policyFileId3 = '';

  /** policyFileName1 */
  policyFileName1 = '';

  /** policyFileName2 */
  policyFileName2 = '';

  /** policyFileName3 */
  policyFileName3 = '';

  /** policySource1 */
  policySource1 = '';

  /** policySource2 */
  policySource2 = '';

  /** policySource3 */
  policySource3 = '';

  /** policyUrl1 */
  policyUrl1 = '';

  /** policyUrl2 */
  policyUrl2 = '';

  /** policyUrl3 */
  policyUrl3 = '';

  /** processDifference */
  processDifference = '';

  /** ssStatus */
  ssStatus = undefined;

  /** statutoryDeadline */
  statutoryDeadline = '';

  /** supplementaryInfo1 */
  supplementaryInfo1 = '';

  /** supplementaryInfo2 */
  supplementaryInfo2 = '';

  /** supplementaryInfo3 */
  supplementaryInfo3 = '';

  /** supplementaryInfo4 */
  supplementaryInfo4 = '';

  /** supplementaryInfo5 */
  supplementaryInfo5 = '';

  /** supplementaryInfo6 */
  supplementaryInfo6 = '';

  /** termsContent1 */
  termsContent1 = '';

  /** termsContent2 */
  termsContent2 = '';

  /** termsContent3 */
  termsContent3 = '';

  /** tollHandle */
  tollHandle = undefined;

  /** tollStandard */
  tollStandard = '';

  /** updateBy */
  updateBy = undefined;

  /** updateDt */
  updateDt = '';

  /** windowAddress */
  windowAddress = '';
}

class FringeBenefitsQuery {
  /** benefitsId */
  benefitsId = undefined;

  /** busnameClassId */
  busnameClassId = undefined;

  /** busnameSubtypeId */
  busnameSubtypeId = undefined;

  /** busnameTypeId */
  busnameTypeId = undefined;

  /** categoryId */
  categoryId = undefined;

  /** cityId */
  cityId = '';

  /** custId */
  custId = undefined;

  /** custName */
  custName = '';

  /** empId */
  empId = undefined;

  /** isCustAdmin */
  isCustAdmin = undefined;

  /** length */
  length = undefined;

  /** name */
  name = '';

  /** pageNum */
  pageNum = undefined;

  /** pageSize */
  pageSize = undefined;

  /** personCategoryId */
  personCategoryId = '';

  /** start */
  start = undefined;

  /** subtypeId */
  subtypeId = undefined;

  /** typeId */
  typeId = undefined;

  /** userId */
  userId = undefined;
}

class Map {}

class OnceChargeDTO {
  /** 户口管理费 */
  accountManagementFee = '';

  /** 费用ID */
  chargeId = undefined;

  /** 城市 */
  cityId = undefined;

  /** 合同费 */
  contractFee = '';

  /** 生育卡证 */
  feCard = '';

  /** 档案费 */
  fileFee = '';

  /** 医疗卡证 */
  hcCard = '';

  /** 招工费 */
  hiringFee = '';

  /** 劳动年审 */
  laborYearCareful = '';

  /** 工会费 */
  labourUnionFee = '';

  /** 流动调配费 */
  mobileDeployFee = '';

  /** 工本费 */
  nominalFee = '';

  /** 养老卡证 */
  oasCard = '';

  /** 其他当地收取的一次性费用1 */
  otherFee1 = '';

  /** 其他当地收取的一次性费用2 */
  otherFee2 = '';

  /** 其他当地收取的一次性费用3 */
  otherFee3 = '';

  /** 其他当地收取的一次性费用4 */
  otherFee4 = '';

  /** 其他当地收取的一次性费用5 */
  otherFee5 = '';

  /** 其他当地收取的一次性费用6 */
  otherFee6 = '';

  /** 公积金卡证 */
  pfCard = '';

  /** 政策维护人:2自营分公司网点维护、3供应商网点维护 */
  policyUser = undefined;

  /** 社保卡证 */
  ssCard = '';

  /** 失业卡证 */
  ueCard = '';
}

class Page {
  /** currentPage */
  currentPage = undefined;

  /** currentPageNo */
  currentPageNo = undefined;

  /** data */
  data = [];

  /** pageSize */
  pageSize = undefined;

  /** result */
  result = [];

  /** start */
  start = undefined;

  /** totalCount */
  totalCount = undefined;

  /** totalPage */
  totalPage = undefined;

  /** totalPageCount */
  totalPageCount = undefined;
}

class PolicyDTO {
  /** 分公司Id */
  branchId = '';

  /** 城市Id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 客户ID */
  custId = '';
}

class PolicyDisabled {
  /** advanceAmount */
  advanceAmount = '';

  /** cityId */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** collectionUnits */
  collectionUnits = undefined;

  /** collectionUnitsStr */
  collectionUnitsStr = '';

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** declareTime */
  declareTime = '';

  /** disabledSecureHisId */
  disabledSecureHisId = undefined;

  /** disabledSecureId */
  disabledSecureId = undefined;

  /** estimate */
  estimate = '';

  /** isDeleted */
  isDeleted = undefined;

  /** levyFrequency */
  levyFrequency = undefined;

  /** levyFrequencyStr */
  levyFrequencyStr = '';

  /** levyScope */
  levyScope = '';

  /** levyStandard */
  levyStandard = '';

  /** levyTarget */
  levyTarget = undefined;

  /** levyTargetStr */
  levyTargetStr = '';

  /** overduePay */
  overduePay = '';

  /** payTime */
  payTime = '';

  /** policyFileName */
  policyFileName = '';

  /** policyFileUrl */
  policyFileUrl = '';

  /** policyLink */
  policyLink = '';

  /** policyUser */
  policyUser = undefined;

  /** policyUserStr */
  policyUserStr = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';

  /** yearDate */
  yearDate = '';
}

class PolicyInfoApproveQuery {
  /** add */
  add = false;

  /** 审批通过日期到 */
  applyDtEnd = '';

  /** 审批通过日期从 */
  applyDtStart = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 是否可批量导出0否1是 */
  canBatchExport = undefined;

  /** 城市id */
  cityId = undefined;

  /** 城市属性（1:空,2:自营,3:供应商 ） */
  cityProperty = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 维护人 */
  creater = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 生效结束时间 */
  effectiveDateEnd = '';

  /** 生效开始时间 */
  effectiveDateStart = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 失效结束时间 */
  expirationDateDateEnd = '';

  /** 失效开始时间 */
  expirationDateDateStart = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 生效状态 0初始 1待生效 2 生效 3失效 */
  infoState = undefined;

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 标签id集合 */
  labelIds = [];

  /** 一级分类id */
  level1Id = undefined;

  /** 二级分类id */
  level2Id = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 参与人 */
  participant = '';

  /** 政策审批权限（1政策管理部审批、2省份维护人审批、3无需审批 4大区政策负责人审批） */
  policyApprovalAuth = undefined;

  /** 政策详情编号 */
  policyInfoCode = '';

  /** 供应商政策审批权限(0无需审批、1供应商政策区域负责人审批) */
  policyPrvdApprovalAuth = undefined;

  /** 关联模板Id */
  policyTemplateId = '';

  /** 所属年份 */
  policyTemplateInfoYear = '';

  /** 政策标题Id */
  policyTitleId = '';

  /** 更新结束开始时间 */
  policyUpdateDateEnd = '';

  /** 更新日期开始时间 */
  policyUpdateDateStart = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 省份id */
  provinceId = undefined;

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 发布日期结束时间 */
  publishDateEnd = '';

  /** 发布日期开始时间 */
  publishDateStart = '';

  /** 发布状态:0初始、1待发布、2历史发布、3最新发布 */
  publishStatus = undefined;

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 服务类型 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
  serviceType = undefined;

  /** 特区id */
  specialAreaId = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 政策详情名称 */
  templateInfoName = '';

  /** 适用范围:1国家 2省份3城市4特区 */
  templateScope = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class PolicyLabelDto {
  /** 主键 */
  policyLabelId = undefined;

  /** 标签名称 */
  policyLabelName = '';

  /** 是否有效（0：失效 ，1：有效） */
  state = 0;
}

class PolicyLabelQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 标签名称 */
  policyLabelName = '';

  /** startIndex */
  startIndex = undefined;

  /** 是否有效（0：失效 ，1：有效） */
  state = undefined;
}

class PolicyLevelDto {
  /** 客户端是否显示 0：不显示,1：显示 */
  clientShowState = undefined;

  /** levelIndex */
  levelIndex = undefined;

  /** parentId */
  parentId = undefined;

  /** policyLevelId */
  policyLevelId = undefined;

  /** policyLevelName */
  policyLevelName = '';

  /** wx端是否显示 0：不显示,1：显示 */
  wxShowState = undefined;
}

class PolicyLevelQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 父级id */
  parentId = undefined;

  /** 层级名称 */
  policyLevelName = '';

  /** startIndex */
  startIndex = undefined;
}

class PolicyProvinceUserQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 省份id ,分隔 */
  provinceIds = '';

  /** startIndex */
  startIndex = undefined;

  /** 1有效 0无效 */
  state = '';
}

class PolicySearchQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市id集合 */
  cityIds = [];

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户 */
  custId = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 生效日期到 */
  effectiveDateEnd = '';

  /** 生效日期从 */
  effectiveDateStart = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 失效日期到 */
  expirationDateEnd = '';

  /** 失效日期从 */
  expirationDateStart = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 生效状态 0初始 1待生效 2 生效 3失效 */
  infoState = undefined;

  /** 是否全部城市:0否，1是 */
  isAllCity = undefined;

  /** 是否全部省份:0否，1是 */
  isAllProvince = undefined;

  /** 是否全部特区i:0否，1是 */
  isAllSpecialArea = undefined;

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 标签id集合 */
  labelIds = [];

  /** 一级分类id */
  level1Id = undefined;

  /** 二级分类id */
  level2Id = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 政策编号 */
  policyInfoCode = '';

  /** 所属年份 */
  policyTemplateInfoYear = '';

  /** 政策标题Id */
  policyTitleId = '';

  /** 更新日期到 */
  policyUpdateDateEnd = '';

  /** 更新日期从 */
  policyUpdateDateStart = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 省份id集合 */
  provinceIds = [];

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 发布日期到 */
  publishDateEnd = '';

  /** 发布日期从 */
  publishDateStart = '';

  /** 适用范围:1国家 2省份3城市4特区 */
  queryScopes = [];

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 服务类型 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
  serviceType = undefined;

  /** 特区id集合 */
  specialAreaIds = [];

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class PolicySpecialArea {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** provinceId */
  provinceId = '';

  /** provinceName */
  provinceName = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** specialAreaId */
  specialAreaId = '';

  /** specialAreaName */
  specialAreaName = '';

  /** specialAreaNumber */
  specialAreaNumber = '';

  /** startIndex */
  startIndex = undefined;

  /** state */
  state = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class PolicySpecialAreaDto {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** provinceId */
  provinceId = '';

  /** provinceName */
  provinceName = '';

  /** specialAreaId */
  specialAreaId = '';

  /** specialAreaName */
  specialAreaName = '';

  /** specialAreaNumber */
  specialAreaNumber = '';

  /** state */
  state = undefined;
}

class PolicySpecialAreaQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** specialAreaName */
  specialAreaName = '';

  /** startIndex */
  startIndex = undefined;

  /** state */
  state = undefined;
}

class PolicyTemplateInfoQuery {
  /** add */
  add = false;

  /** 审批通过日期到 */
  applyDtEnd = '';

  /** 审批通过日期从 */
  applyDtStart = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 是否可批量导出0否1是 */
  canBatchExport = undefined;

  /** 城市id */
  cityId = undefined;

  /** 城市属性（1:空,2:自营,3:供应商 ） */
  cityProperty = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 维护人 */
  creater = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 生效结束时间 */
  effectiveDateEnd = '';

  /** 生效开始时间 */
  effectiveDateStart = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 失效结束时间 */
  expirationDateDateEnd = '';

  /** 失效开始时间 */
  expirationDateDateStart = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 生效状态 0初始 1待生效 2 生效 3失效 */
  infoState = undefined;

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 标签id集合 */
  labelIds = [];

  /** 一级分类id */
  level1Id = undefined;

  /** 二级分类id */
  level2Id = undefined;

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 审批人 */
  participant = '';

  /** 政策审批权限（1政策管理部审批、2省份维护人审批、3无需审批 4大区政策负责人审批） */
  policyApprovalAuth = undefined;

  /** 政策详情编号 */
  policyInfoCode = '';

  /** 供应商政策审批权限(0无需审批、1供应商政策区域负责人审批) */
  policyPrvdApprovalAuth = undefined;

  /** 关联模板Id */
  policyTemplateId = '';

  /** 所属年份 */
  policyTemplateInfoYear = '';

  /** 政策标题Id */
  policyTitleId = '';

  /** 更新结束开始时间 */
  policyUpdateDateEnd = '';

  /** 更新日期开始时间 */
  policyUpdateDateStart = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 省份id */
  provinceId = undefined;

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 发布日期结束时间 */
  publishDateEnd = '';

  /** 发布日期开始时间 */
  publishDateStart = '';

  /** 发布状态:0初始、1待发布、2历史发布、3最新发布 */
  publishStatus = undefined;

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 服务类型 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
  serviceType = undefined;

  /** 特区id */
  specialAreaId = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 政策详情名称 */
  templateInfoName = '';

  /** 适用范围:1国家 2省份3城市4特区 */
  templateScope = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class PolicyTemplateQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 是否可以批量导出0否 1是 */
  canBatchExport = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
  serviceType = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 是否有效(0：失效,1:有效) */
  state = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 模板编号 */
  templateCode = '';

  /** 模板名称 */
  templateName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class PolicyTitleDto {
  /** aiServiceLocation */
  aiServiceLocation = '';

  /** busNameClsaaName */
  busNameClsaaName = '';

  /** busnameClassId */
  busnameClassId = undefined;

  /** clientShowState */
  clientShowState = undefined;

  /** hroShowState */
  hroShowState = undefined;

  /** isDownPdf */
  isDownPdf = undefined;

  /** isLock */
  isLock = undefined;

  /** isMailPdf */
  isMailPdf = undefined;

  /** level1Id */
  level1Id = undefined;

  /** level1Name */
  level1Name = '';

  /** level2Id */
  level2Id = undefined;

  /** level2Name */
  level2Name = '';

  /** policyApprovalAuth */
  policyApprovalAuth = undefined;

  /** policyLableIds */
  policyLableIds = '';

  /** policyLableNames */
  policyLableNames = '';

  /** policyPrvdApprovalAuth */
  policyPrvdApprovalAuth = undefined;

  /** policyTemplateId */
  policyTemplateId = undefined;

  /** policyTitle */
  policyTitle = '';

  /** policyTitleId */
  policyTitleId = undefined;

  /** policyTitleScope */
  policyTitleScope = undefined;

  /** serviceType */
  serviceType = undefined;

  /** state */
  state = undefined;

  /** templateName */
  templateName = '';

  /** updateNoticeDay */
  updateNoticeDay = undefined;

  /** updateNoticeType */
  updateNoticeType = undefined;

  /** wxShowState */
  wxShowState = undefined;
}

class PolicyTitleQuery {
  /** aiServiceLocation */
  aiServiceLocation = '';

  /** busnameClassId */
  busnameClassId = undefined;

  /** endIndex */
  endIndex = undefined;

  /** isDownPdf */
  isDownPdf = undefined;

  /** isLock */
  isLock = undefined;

  /** isMailPdf */
  isMailPdf = undefined;

  /** level1Id */
  level1Id = undefined;

  /** level2Id */
  level2Id = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** policyApprovalAuth */
  policyApprovalAuth = undefined;

  /** policyLableIds */
  policyLableIds = '';

  /** policyPrvdApprovalAuth */
  policyPrvdApprovalAuth = undefined;

  /** policyTemplateId */
  policyTemplateId = undefined;

  /** policyTitle */
  policyTitle = '';

  /** policyTitleScope */
  policyTitleScope = undefined;

  /** serviceType */
  serviceType = undefined;

  /** startIndex */
  startIndex = undefined;

  /** state */
  state = undefined;

  /** updateNoticeType */
  updateNoticeType = undefined;
}

class QueryAnnualPay {
  /** cityIds */
  cityIds = '';

  /** custId */
  custId = undefined;

  /** empId */
  empId = undefined;

  /** isCustAdmin */
  isCustAdmin = undefined;

  /** length */
  length = undefined;

  /** name */
  name = '';

  /** pageNum */
  pageNum = undefined;

  /** pageSize */
  pageSize = undefined;

  /** provinceId */
  provinceId = '';

  /** provinceIds */
  provinceIds = '';

  /** start */
  start = undefined;

  /** updateDtEnd */
  updateDtEnd = '';

  /** updateDtStr */
  updateDtStr = '';

  /** userId */
  userId = undefined;

  /** yearDateEnd */
  yearDateEnd = '';

  /** yearDateStr */
  yearDateStr = '';
}

class QueryPolicyDisabled {
  /** cityIds */
  cityIds = '';

  /** custId */
  custId = undefined;

  /** empId */
  empId = undefined;

  /** isCustAdmin */
  isCustAdmin = undefined;

  /** length */
  length = undefined;

  /** name */
  name = '';

  /** pageNum */
  pageNum = undefined;

  /** pageSize */
  pageSize = undefined;

  /** start */
  start = undefined;

  /** updateDtEnd */
  updateDtEnd = '';

  /** updateDtStr */
  updateDtStr = '';

  /** userId */
  userId = undefined;

  /** yearDateEnd */
  yearDateEnd = '';

  /** yearDateStr */
  yearDateStr = '';
}

class Result {
  /** code */
  code = undefined;

  /** error */
  error = '';

  /** exception */
  exception = new Exception();

  /** message */
  message = '';

  /** object */
  object = undefined;
}

class ServicePointDTO {
  /** 增员材料 */
  addEmpMaterial = '';

  /** 大区 */
  area = '';

  /** 接单方id */
  assigneeProviderId = '';

  /** 账单收费规则:1 预付;2每月付 */
  billFeeRule = '';

  /** 城市ID */
  cityId = '';

  /** cityLevel */
  cityLevel = '';

  /** cityLevelCN */
  cityLevelCN = '';

  /** cityName */
  cityName = '';

  /** 联系人(客服) */
  contactName = '';

  /** 联系电话 */
  contactTel = '';

  /** 离职补差规则 */
  dismissMakeupRule = '';

  /** 离职补差起始月 */
  dismissMakeupSatartMon = '';

  /** 是否有效 */
  isDeleted = '';

  /** 是否离职补差 */
  isDismissMakeup = '';

  /** 大户所在区 */
  largeAccountArea = '';

  /** 机构类别:1 自营分公司; 2 供应商 */
  organizationType = '';

  /** 补交材料 */
  payInbackMaterial = '';

  /** provinceName */
  provinceName = '';

  /** 减员材料 */
  reduceEmpMaterial = '';

  /** 备注 */
  remark = '';

  /** 网点服务名称(接单方) */
  serviceAssigneeName = '';

  /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
  serviceBranchFlag = '';

  /** 网点服务名称(集团) */
  serviceGroupName = '';

  /** 主键 */
  servicePointId = '';

  /** 公积金申报频率 */
  sfApplicationFrequency = '';

  /** 公积基金中心当月可操作时间段 */
  sfOperateTime = '';

  /** 当地单立户可操作区县 */
  singleAccountArea = '';

  /** 社保申报频率 */
  ssApplicationFrequency = '';

  /** 社保当月可操作时间段 */
  ssOperateTime = '';
}

class StackTraceElement {
  /** className */
  className = '';

  /** fileName */
  fileName = '';

  /** lineNumber */
  lineNumber = undefined;

  /** methodName */
  methodName = '';

  /** nativeMethod */
  nativeMethod = false;
}

class StatutoryHolidayDTO {
  /** 怀孕满4个月流产 */
  after4Miscarriage = '';

  /** 年假 */
  annualVacation = '';

  /** 怀孕未满4个月流产 */
  before4Miscarriage = '';

  /** 哺乳假 */
  breastfeedingLeave = '';

  /** 城市 */
  cityId = undefined;

  /** 难产 */
  dystocia = '';

  /** 顺产 */
  eutocia = '';

  /** 丧假 */
  funeralLeave = '';

  /** 当地额外奖励假（男职工） */
  manBonusLeave = '';

  /** 育儿假（男职工） */
  manParentalLeave = '';

  /** 婚假 */
  marriageLeave = '';

  /** 生育多胞胎 */
  multipleBirths = '';

  /** 其他法定假期1 */
  otherLeave1 = '';

  /** 其他法定假期2 */
  otherLeave2 = '';

  /** 其他法定假期3 */
  otherLeave3 = '';

  /** 其他法定假期4 */
  otherLeave4 = '';

  /** 其他法定假期5 */
  otherLeave5 = '';

  /** 其他法定假期6 */
  otherLeave6 = '';

  /** 陪产假 */
  paternityLeave = '';

  /** 政策维护人:2自营分公司网点维护、3供应商网点维护 */
  policyUser = undefined;

  /** 病假 */
  sickLeave = '';

  /** 法定假日ID */
  statutoryHolidayId = undefined;

  /** 未婚员工探望父母 */
  unVisitParents = '';

  /** 已婚员工探望父母 */
  visitParents = '';

  /** 探望配偶 */
  visitsSpouse = '';

  /** 当地额外奖励假（女职工） */
  womanBonusLeave = '';

  /** 育儿假（女职工） */
  womanParentalLeave = '';
}

class TemplateFieldResp {
  /** aiServiceLocation */
  aiServiceLocation = '';

  /** 是否可批量导出 0否 1是 */
  canBatchExport = undefined;

  /** 客户端是否显示 0：不显示,1：显示 */
  clientShowState = undefined;

  /** 字段编码 */
  fieldCode = '';

  /** 字段名称 */
  fieldName = '';

  /** HRO端是否显示 0：不显示,1：显示 */
  hroShowState = undefined;

  /** 是否删除 0否 1是 */
  isDeleted = '';

  /** 字段是否必填 0否 1是 */
  isMust = undefined;

  /** 字段选项 */
  items = '';

  /** 政策模板字段ID */
  policyTemplateFieldId = '';

  /** 政策模板分组ID */
  policyTemplateGroupId = '';

  /** remark */
  remark = '';

  /** 显示顺序 */
  seqNum = undefined;

  /** 模板状态  状态:0初始 1有效 2无效 */
  templateState = undefined;

  /** 字段类型:1.文本 2.多行文本 3.日期 4.下拉菜单 5.链接 6.附件 7.多选项 8.数字(整数) 9.数字(小数) */
  type = undefined;

  /** wx端是否显示 0：不显示,1：显示 */
  wxShowState = undefined;
}

class TemplateFieldValueResp {
  /** 字段值 */
  fieldValue = '';

  /** 附件名称 */
  fileName = '';

  /** 是否删除 0否 1是 */
  isDeleted = '';

  /** 政策模板字段ID */
  policyTemplateFieldId = '';

  /** 政策模板字段值ID */
  policyTemplateFieldValueId = '';

  /** 政策模板详情ID */
  policyTemplateInfoId = '';

  /** 特区ID */
  specialAreaId = '';

  /** 模板版本 */
  tpVersion = '';

  /** 超链接名称 */
  urlName = '';
}

class TemplateGroupResp {
  /** aiServiceLocation */
  aiServiceLocation = '';

  /** 客户端是否显示 0：不显示,1：显示 */
  clientShowState = undefined;

  /** 字段列表 */
  fields = [];

  /** 分组编号 */
  groupCode = '';

  /** HRO端是否显示 0：不显示,1：显示 */
  hroShowState = undefined;

  /** 是否删除 0否 1是 */
  isDeleted = '';

  /** 政策模板分组ID */
  policyTemplateGroupId = '';

  /** 政策模板ID */
  policyTemplateId = '';

  /** 显示顺序 */
  seqNum = undefined;

  /** 分组名称 */
  templateGroupName = '';

  /** 模板状态  状态:0初始 1有效 2无效 */
  templateState = undefined;

  /** wx端是否显示 0：不显示,1：显示 */
  wxShowState = undefined;
}

class TemplateInfoAddReq {
  /** 关联业务名称ID */
  busnameClassId = '';

  /** 关联业务名称 */
  busnameClassName = '';

  /** 城市ID */
  cityId = '';

  /** 生效日期 */
  effectiveDate = '';

  /** 邮件附件 */
  emailAttachment = '';

  /** 邮件附件名称 */
  emailAttachmentName = '';

  /** 邮件内容 */
  emailContent = '';

  /** 邮件标题 */
  emailTitle = '';

  /** 失效日期 */
  expirationDate = '';

  /** 链接地址 */
  infoUrl = '';

  /** 是否内部分公司 0否1是 */
  internalSubsidiary = undefined;

  /** 政策详情PDF */
  pdfUrl = '';

  /** 政策审批权限（1政策管理部审批、2省份维护人审批、3无需审批 4大区政策负责人审批） */
  policyApprovalAuth = undefined;

  /** 供应商政策审批权限(0无需审批、1供应商政策区域负责人审批) */
  policyPrvdApprovalAuth = undefined;

  /** 政策详情字段值list */
  policyTemplateFieldValueList = [];

  /** 关联模板ID */
  policyTemplateId = '';

  /** 政策详情ID */
  policyTemplateInfoId = '';

  /** 所属年份 */
  policyTemplateInfoYear = undefined;

  /** 关联标题ID */
  policyTitleId = '';

  /** 政策更新时间 */
  policyUpdateDate = '';

  /** 省份ID */
  provinceId = '';

  /** 发布状态:0初始、1待发布、2历史发布、3最新发布 */
  publishStatus = undefined;

  /** 收件人邮箱(多个用逗号分割) */
  recipientEmail = '';

  /** 服务类型 */
  serviceType = undefined;

  /** 服务类型名称 */
  serviceTypeName = '';

  /** 特区ID */
  specialAreaId = '';

  /** 是否支持单位办理 */
  supportedCorporate = undefined;

  /** 是否支持个人办理 */
  supportedIndividual = undefined;

  /** 政策详情名称 */
  templateInfoName = '';

  /** 适用范围:1国家 2省份3城市4特区 */
  templateScope = undefined;

  /** 版本号 */
  tpVersion = '';

  /** 人工更新周期 */
  updateCycle = undefined;

  /** 更新说明 */
  updateNotes = '';
}

class TemplateInfoApproveReq {
  /** 审批意见 */
  approveOpinion = '';

  /** 关联业务名称ID */
  busnameClassId = '';

  /** 关联业务名称 */
  busnameClassName = '';

  /** 城市ID */
  cityId = '';

  /** 生效日期 */
  effectiveDate = '';

  /** 邮件附件 */
  emailAttachment = '';

  /** 邮件附件名称 */
  emailAttachmentName = '';

  /** 邮件内容 */
  emailContent = '';

  /** 邮件标题 */
  emailTitle = '';

  /** 失效日期 */
  expirationDate = '';

  /** 链接地址 */
  infoUrl = '';

  /** 是否内部分公司 0否1是 */
  internalSubsidiary = undefined;

  /** 政策详情PDF */
  pdfUrl = '';

  /** 政策审批权限（1政策管理部审批、2省份维护人审批、3无需审批 4大区政策负责人审批） */
  policyApprovalAuth = undefined;

  /** 供应商政策审批权限(0无需审批、1供应商政策区域负责人审批) */
  policyPrvdApprovalAuth = undefined;

  /** 政策详情字段值list */
  policyTemplateFieldValueList = [];

  /** 关联模板ID */
  policyTemplateId = '';

  /** 政策详情ID */
  policyTemplateInfoId = '';

  /** 所属年份 */
  policyTemplateInfoYear = undefined;

  /** 关联标题ID */
  policyTitleId = '';

  /** 政策更新时间 */
  policyUpdateDate = '';

  /** 省份ID */
  provinceId = '';

  /** 发布状态:0初始、1待发布、2历史发布、3最新发布 */
  publishStatus = undefined;

  /** 收件人邮箱(多个用逗号分割) */
  recipientEmail = '';

  /** 服务类型 */
  serviceType = undefined;

  /** 服务类型名称 */
  serviceTypeName = '';

  /** 特区ID */
  specialAreaId = '';

  /** 是否支持单位办理 */
  supportedCorporate = undefined;

  /** 是否支持个人办理 */
  supportedIndividual = undefined;

  /** 政策详情名称 */
  templateInfoName = '';

  /** 适用范围:1国家 2省份3城市4特区 */
  templateScope = undefined;

  /** 版本号 */
  tpVersion = '';

  /** 人工更新周期 */
  updateCycle = undefined;

  /** 更新说明 */
  updateNotes = '';

  /** workItemId */
  workItemId = '';
}

class TemplateInfoPubReq {
  /** 邮件附件 */
  emailAttachment = '';

  /** 邮件内容 */
  emailContent = '';

  /** 邮件标题 */
  emailTitle = '';

  /** 链接地址 */
  infoUrl = '';

  /** 是否发送邮件0否、1是 */
  isSendEmail = undefined;

  /** 政策模板详情ID */
  policyTemplateInfoId = '';

  /** 收件人邮箱(多个用逗号分割) */
  recipientEmail = '';

  /** 更新说明 */
  updateNotes = '';
}

class TemplateInfoUpdateReq {
  /** 政策详情ID */
  policyTemplateInfoId = '';

  /** 更新时间 */
  policyUpdateDate = '';
}

class TemplateReq {
  /** 是否可以批量导出0否 1是 */
  canBatchExport = undefined;

  /** 模板ID 修改查询必填 */
  policyTemplateId = '';

  /** reminderTime */
  reminderTime = '';

  /** 1派遣外包员工 2单位自有员工 3派遣外包员工、单位自有员工 */
  serviceType = undefined;

  /** 状态:0初始 1有效 2无效 修改选填 */
  state = undefined;

  /** 模板名称 新增必填 修改选填 */
  templateName = '';

  /** 模板版本 */
  tpVersion = '';
}

class TemplateSortItem {
  /** 主键 */
  id = '';

  /** seqNum */
  seqNum = undefined;
}

class TemplateSortReq {
  /** 排序内容 */
  items = [];

  /** 1分组排序 2字段排序 */
  type = undefined;
}

class Throwable {
  /** cause */
  cause = {};

  /** localizedMessage */
  localizedMessage = '';

  /** message */
  message = '';

  /** stackTrace */
  stackTrace = [];

  /** suppressed */
  suppressed = [];
}

class servicePointQuery {
  /** 大区 */
  area = '';

  /** 接单方id */
  assigneeProviderId = '';

  /** 城市 */
  cityId = '';

  /** cityLevel */
  cityLevel = '';

  /** cityLevelCN */
  cityLevelCN = '';

  /** 城市名称 */
  cityName = '';

  /** 联系人(客服) */
  contactName = '';

  /** 联系电话 */
  contactTel = '';

  /** 网点分公司id */
  departmentId = '';

  /** 网点分公司id */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否有效 */
  isDeleted = '';

  /** 机构类别:1 自营分公司; 2 供应商 */
  organizationType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 省份 */
  provinceName = '';

  /** 网点服务名称(接单方) */
  serviceAssigneeName = '';

  /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
  serviceBranchFlag = '';

  /** 网点服务名称(集团) */
  serviceGroupName = '';

  /** startIndex */
  startIndex = undefined;
}

export const information = {
  AnnualPay,
  CalculateDTO,
  CityInfo,
  CommonResponse,
  DropdownList,
  Exception,
  ExportQuery,
  FilterEntity,
  FringeBenefits,
  FringeBenefitsQuery,
  Map,
  OnceChargeDTO,
  Page,
  PolicyDTO,
  PolicyDisabled,
  PolicyInfoApproveQuery,
  PolicyLabelDto,
  PolicyLabelQuery,
  PolicyLevelDto,
  PolicyLevelQuery,
  PolicyProvinceUserQuery,
  PolicySearchQuery,
  PolicySpecialArea,
  PolicySpecialAreaDto,
  PolicySpecialAreaQuery,
  PolicyTemplateInfoQuery,
  PolicyTemplateQuery,
  PolicyTitleDto,
  PolicyTitleQuery,
  QueryAnnualPay,
  QueryPolicyDisabled,
  Result,
  ServicePointDTO,
  StackTraceElement,
  StatutoryHolidayDTO,
  TemplateFieldResp,
  TemplateFieldValueResp,
  TemplateGroupResp,
  TemplateInfoAddReq,
  TemplateInfoApproveReq,
  TemplateInfoPubReq,
  TemplateInfoUpdateReq,
  TemplateReq,
  TemplateSortItem,
  TemplateSortReq,
  Throwable,
  servicePointQuery,
};
