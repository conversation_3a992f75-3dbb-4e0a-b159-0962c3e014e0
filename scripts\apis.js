/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2021-03-03 09:37:13
 * @LastAuthor: 王正荣
 * @LastTime: 2021-03-09 18:48:11
 * @message: message
 * 用于处理apis目录
 */

'use strict';
const __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
exports.__esModule = true;

const fs_1 = __importDefault(require('fs'));
const path_1 = __importDefault(require('path'));
const fs = fs_1['default'];
const path = path_1['default'];
const SOURCE = 'src/apis';
const TARGET = 'src/assets/apis';

const BASE_DIR = __filename.replace(/\\/g, '/').split('/scripts/')[0];
const files = [];
const MODE = process.argv[2] || 'local';

const mkdirIfNotExists = function (dirPath) {
  if (fs.existsSync(dirPath)) {
    return;
  }
  const reletive = dirPath.replace(BASE_DIR, '');
  const dirs = reletive.split('/');
  let dir = '';
  for (const d of dirs) {
    if (!d) {
      continue;
    }
    dir = dir + '/' + d;
    const dirs = `${BASE_DIR}/${dir}`;
    if (!fs.existsSync(dirs)) {
      fs.mkdirSync(dirs);
    }
  }
};

const walkDir = function (filePath) {
  let state = fs.statSync(filePath);
  if (state.isFile()) {
    files.push(filePath);
  } else if (state.isDirectory()) {
    fs.readdirSync(filePath).forEach((file) => {
      walkDir(`${filePath}/${file}`);
    });
  }
};

const writeFile = (dir) => {
  const folder = `${dir}/apis_${MODE}`;
  const target = `${TARGET}/apis_${MODE}`;
  if (!fs.existsSync(folder)) {
    fs.mkdirSync(folder);
  }
  const baseDir = `${BASE_DIR}/`;
  const _files = files.map((file) => file.replace(baseDir, ''));
  fs.writeFileSync(`${folder}/all.json`, JSON.stringify(_files, null, 4));
  for (const file of files) {
    fs.readFile(file, function (err, data) {
      if (err) throw new Error(`读取文件 ${file} 时出现错误。`);
      const dist = file.replace(SOURCE, target).replace('.d.ts', '.d.txt');
      const distDir = path.dirname(dist);
      if (!fs.existsSync(distDir)) {
        mkdirIfNotExists(distDir);
      }
      fs.writeFile(dist, data, function (err) {
        if (err) throw new Error(`写入文件 ${dist} 时出现错误。`);
      });
    });
  }
};

const writeIndexFile = () => {
  const api_dir = MODE === 'local' ? '../../apis' : `./apis_${MODE}`;
  const content = `export { bindAPI } from "${api_dir}";`;
  const dist = `${BASE_DIR}/src/assets/apis/index_${MODE}.ts`;

  fs.writeFile(dist, content, function (err) {
    if (err) throw new Error(`写入文件 ${dist} 时出现错误。`);
  });
};

function main() {
  if (MODE !== 'local') {
    walkDir(`${BASE_DIR}/${SOURCE}`);
    writeFile(`${BASE_DIR}/${TARGET}`);
  }
  writeIndexFile();
}
main();
