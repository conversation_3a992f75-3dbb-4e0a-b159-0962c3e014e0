<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-09-17 14:49:57
 * @LastAuthor: 侯成
 * @LastTime: 2019-11-08 16:10:15
 * @message: 
 -->
# 公共组件汇总

术语释义：

* 一级组件：通常不依赖于其它公共组件，核心方法在组件内部实现，参数及方法较为抽象，理解难度最高。
* 二级组件：依赖于一级实现功能，没有自己的核心方法，通常是基于固定参数对一级的封装，服务于具体功能。
* 元组件：通常致力于解决某种类型的功能，不服务于某种特定的需求。参数和方法的命名通常较为抽象，一般不在业务中直接使用。
* 衍生组件：通常是对其它更为抽象组件进行封装，以特定的业务功能为目标。

## 下拉选择

基本选择框

![图2.1 基本选择框](../assets/images/2.1-basic-selectors.png)

 * [`mapToSelectors`](1.1-selectors.md): 将`Map` 映射为下拉选择。
 * [`mapToSwitch`](1.1-selectors.md):  将`Map` 映射为二选一选择框。

基本选择框数据展示

 * [`mapToSelectView`](1.1-selectors.md): 展示下拉选择结果。
 * [`mapToSwitchView`](1.1-selectors.md): 展示二选一选择框选择结果。

特殊选择框

![](../assets/images/2.2-auto-selectors.png)

 * [`AutoSelectForm`](1.2-autoSelectForm.md): 下拉选择 + 建议。
 * `CitySelector`: 衍生组件，专用于城市选择 。
 * `ProductSelector`: 衍生组件，专用于产品选择 。
 * `CustSelector`: 衍生组件，专用于客户选择 。
 * `ProvinceSelector`: 衍生组件，专用于省份选择 。
 * `CustPayerSelector`: 衍生组件，专用于客户付款方选择 。
 * `BranchSelector`: 衍生组件，专用于分公司选择 。
 * `CustPayerSelector`: 衍生组件，专用于客户付款方选择 。
 * `DownDownProductSelector`: 衍生组件，专用于下级产品选择 。

 特殊选择框数据展示
 * [`CodeToName`](1.2-autoSelectForm.md): 下拉选择建议组件的结果展示组件。
 * `CityName`: 衍生组件，专用于城市名称展示 。
 * `ProductName`: 衍生组件，专用于省份名称展示 。
 * `DownDownProductName`: 衍生组件，专用于子级产品展示 。
 * `CustName`: 衍生组件，专用于客户名称展示 。
 * `CustPayerName`: 衍生组件，专用于客户付款方展示 。
 * `BranchName`: 衍生组件，专用于分公司展示 。
 * `EntityName`: 衍生组件，专用于实体分公司展示 。

## 日期选择

![](../assets/images/2.3-date-range.png)

* [`DateRange`](2.4-date-range.md): 日期范围选择器。

## 弹窗选择

![](../assets/images/2.4-custpop.png)

 * [`StandardPop`](2.1-standard-pop.md): 标准弹窗选择，元组件，不直接在页内使用。
 * `BranchPop`: 衍生组件，用于分公司选择 。
 * `CustomerPop`: 衍生组件，用于客户选择 。
 * `EntityPop`: 衍生组件，用于实体分公司选择 。
 * `InnerUserPop`: 衍生组件，用于内部员工选择 。
 * `FrequencyPopPop`: 衍生组件，用于收费频率选择 。
 * `FristGradePop`: 衍生组件，用于一级供应商选择 。
 * `GroupProductPop`: 衍生组件，用于根据城市社保组选择产品 。
 * `RatioPop`: 衍生组件，用于比例选择 。

## 列表编辑

![](../assets/images/2.5-writable.png)

 * [`Writable`](2.6-writable.md): 可编辑表格。

## 新增修改表单
 * [`EditeForm`](2.7-editeForm.md): 新增修改表单。
