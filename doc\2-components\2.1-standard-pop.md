<!--
 * @Author: 侯成
 * @since: 2019-07-24 13:34:54
 * @lastTime: 2019-08-28 11:17:12
 * @LastAuthor: 侯成
 * @message: 
 -->
# 标准选择弹窗

在输入数据时，待选项过多，且没有明显规律，下拉列表选择就不再适用。此时，需要通过多重条件来查询，筛选，以缩小范围。通过弹窗表单查询，成为了一个可行的方案，本节将介绍一个可供调用的标准选择弹窗。

![](../assets/images/2.4-custpop.png)

## 基础声明

属性类型及默认值声明：
```tsx
// src/components/StandardPop/index.tsx

interface ItemStruct {
  title: string;
  dataIndex: string;
}

export interface FixedValues {
  [k: string]: string | number | boolean | undefined;
}

export interface StandardPopProps<T> extends FormComponentProps {
  label: string;
  outerForm?: WrappedFormUtils<any>;
  fixedValues?: FixedValues;
  fieldOptions?: GetFieldDecoratorOptions;
  keyMap?: { [k: string]: string };
  title?: string;
  viewKey?: string;
  // 横杠分隔多个值
  rowValue?: string;
  rowValueData?: Object;
  modalwidth?: number;
  inputStyle?: React.CSSProperties;
  disabled?: boolean;
  formItems?: ItemStruct[];
  rowLength?: number;
  rowKey?: string;
  // 横杠分隔多个值
  unionKey?: string;
  handdleConfirm?: (value?: T) => void;
  [k: string]: any;
}

interface StandardPopStates<T> {
  formValues: any;
  flag: Boolean;
  selectedRows: T[];
  nameMap: object;
  initialValue?: string | number;
  datas: TablePage<T>;
  loading: boolean;
  onModal: boolean;
  selectedRow?: T;
  [k: string]: any;
}

import FormItem from '@/components/Forms/FormItem';

class StandardPop<T> extends React.Component<StandardPopProps<T>, StandardPopStates<T>> {
  state: StandardPopStates<T> = {
    nameMap: {},
    formValues: {},
    selectedRows: [],
    initialValue: undefined,
    datas: {
      list: [],
      pagination: {},
    },
    loading: false,
  };
  columns: ItemStruct[] = [
    {
      title: '',
      dataIndex: '',
    },
    {
      title: '',
      dataIndex: '',
    },
  ];
  fieldOptions: GetFieldDecoratorOptions = this.props.fieldOptions || {};
  formItems: ItemStruct[] = this.props.formItems || this.columns;
  rowKey: string = this.props.rowKey || '';
  unionKey: string = this.props.unionKey || '';
  viewKey: string = this.props.viewKey || '';
  rowValue: string = this.viewKey || this.props.rowValue || '';
  rowLength: number | undefined = this.props.rowLength;
  detailService: ((data: { id: any; [k: string]: any }) => Promise<any>) | undefined = undefined;
  service: (data: any) => Promise<any> = (data: any): Promise<any> => request('');
}
```

属性说明：

### 外部输入属性
属性 | 说明 | 类型 | 默认值
----|------|----- | ----
label| 输入框的标题，由调用者手动传入。 | `string` | -
outerForm| 可选。需要时应当传入父组件的form属性，以提供表单验证功能。此属性为空时，将得到无验证功能的普通输入框。 | `WrappedFormUtils<any>` | `undefined`
fixedValues| 可选。固定参数，调用接口时，常需传入某些约定参数，而非用户填写。例如当前用户名，当前角色名。 | `FixedValues` | `this.props.label`
fieldOptions| 外部 +  内部属性。在继承组件设置实际值。用于输入框验证的配置，不传入`outerForm`属性，此属性无作用，可不设置。 | `GetFieldDecoratorOptions` | `{}`
keyMap| 可选。字段别名映射，在弹窗表格中，字段名与当前页面所需名称不同，例如表格中列名为`userName`，但在员工管理页面，所需的表单字段名`employeeName`。 | `string` | `this.props.label`
title| 可选。弹窗的标题 | `string` | `this.props.label`
viewKey| 可选。用于在输入框中显示信息的字段。若不传，将使用`rowValue` | `string` | `undefined`
rowValue| 可选。输入框中显示数据时，所依赖的`key`，将从被选中的行对象中依据此键取值。支持横杠分隔多个值。 | `string` | `undefined`
rowValueData| 可选。`rowValue`中个属性所对应的值。 | `Object` | `undefined`
modalwidth| 可选。弹窗弹窗的宽度。 | `number` | `520`
inputStyle| 可选。输入框的css样式属性。 | `React.CSSProperties` | `undefined`
formItems| 可选。弹窗中查询表单的所需项目，可传入各验证规则。 | `ItemStruct[]` | `this.columns`
handdleConfirm| 父组件传入的回调方法，确认列表选中结果后调用，将回传选中结果。 | `(value?: T) => void` | -
rowKey| 可选(不常用)。弹窗中表格`StandardTable`所要求的`rowKey`属性 | `string` | `undefined`
rowLength| 可选(不常用)。弹窗中表单每行中，输入框的个数。通常需配合`modalwidth`属性使用 | `number` | `undefined`
unionKey| 可选(不常用)。联合rowKey，横杠分隔多个值，在各种关系表中，通常多个ID合在一起作为一个唯一`key`值 | `string` | `undefined`

### 内部私有属性

属性 | 说明 | 类型 | 默认值
----|------|----- | ----
columns| 内部属性。在继承组件设置实际值。 `StandardTable`所要求的`columns`属性。 | `ItemStruct[]` | `[ { title: '', dataIndex: '' } ]`
fieldOptions| 外部 +  内部属性。在继承组件设置实际值。用于输入框验证的配置，不传入`outerForm`属性，此属性无作用，可不设置。 | `GetFieldDecoratorOptions` | `{}`
formItems| 外部 + 内部属性。弹窗中查询表单输入框的所需项目。在继承组件设置实际值。不设置时，将使用外部传入的属性，不传入时，将使用内部属性`columns`。 | `ItemStruct[]` | `this.props.formItems OR this.columns`
rowKey| 外部 + 内部属性。请参见外部输入属性说明。在继承组件设置实际值。不设置时，将使用外部传入的属性。 | `string` | `this.props.rowKey OR ''`
rowValue| 同上。 | `string` | `this.props.rowValue OR ''`
rowLength| 同上。 | `number OR undefined` | `this.props.rowLength`
detailService| 内部属性。 调用后端`API`的方法，获取当前传入的ID对应的数据详情，并取出`viewKey`对应的值，在继承组件设置实际方法体。 | `(data: { id: any; [k: string]: any }) => Promise<any>` | `undefined`
service| 内部属性。 调用后端`API`的方法，获取表格数据，在继承组件设置实际方法体。 | `(data: any): Promise<any>` | `(data: any): Promise<any> => request('')`

## 使用说明
标准弹窗 `StandardPopProps` 是元组件，无法直接使用，必须通过继承后，设置相关自定义属性，继承后的子组件方可供外部调用。
以下是通过继承元组件，实现的客户选择弹窗组件:
```tsx
// src/pages/Sales/Customer/CustomerOpts.tsx

import { Form } from 'antd';
import StandardPop, { StandardPopProps } from '@/components/StandardPop';
import { ICustomerOps } from '../cargo/types';

class CustomerOpsF extends StandardPop<ICustomerOps> {
  columns = [
    {
      title: '客户名称',
      dataIndex: 'custName',
    },
    {
      title: '客户编号',
      dataIndex: 'custId',
    },
  ];
  formItems = [
    {
      title: '客户名称',
      dataIndex: 'custName',
    },
    {
      title: '客户编号',
      dataIndex: 'custId',
    },
  ];
  rowKey = this.props.rowKey || 'custId';
  viewKey = this.props.viewKey || 'custName';
  rowValue = this.props.rowValue || 'custId';
  rowLength = this.props.rowLength || 2;
  service = API.query.cust.postDropDownList.request;
  detailService = API.sale.cust.getCustomer.request;
}

const CustomerOps = Form.create<StandardPopProps<ICustomerOps>>()(CustomerOpsF);
export default CustomerOps;
```
可以看到，通过继承元组件，大量简化了此共用组件的定义。`CustomerOps` 作为一个专门用于选择客户的弹窗，拥有一些确定的属性：
* 表格列配置`columns`。
* 查询表单配置`formItems`。此属性通常不是必须设置的，但是弹窗中表单项和表格列不一致，或弹窗表单需要额外配置时，此属性必须设置。
* 供`StatandardTable`作为表格每行唯一id的`rowKey`。
* 在选中表格中某行后，输入框中需展示相关，通常是各种`"name"`，在此组件中`rowValue`是`custName`。
* 弹窗表单中，每一行所包含的输入框个数，此组件中的值为 `2`。
* 在此组件的使用场景中，存在必选的情景。所以需配置相关验证规则，`fieldOptions`。需要指出的是，此属性即使已设置，也不代表输入框的验证生效，只用当外部输入属性`outerForm`被传入时，此属性才会产生应有的作用。
* `service` 是请求后端API的方法，定义在`pont`中。

继承元组件，私有属性定义完成后，便可作为公共组件，供外调用了，以下是示例：
```tsx
// src/pages/Sales/ContractManage/index.tsx
// ... ...
taggleModal = () => {
  const { onModal } = this.state;
  this.setState({ onModal: !onModal });
};
onCustomerOpsComfirm = (value?: ICustomerOps) => {
  // 在此接收被选中的数据
  console.log('value in onCustomerOpsComfirm:', value)
};
// ... ...

<Form
  onSubmit={this.handleSearch}
  layout="inline"
  {...this.formItemLayout}
  className={styles.popFormBase}
>
  <Row gutter={\{ md: 6, lg: 24, xl: 48 \}}>
    <Col md={6} sm={24}>
      <CustomerOps
        label="客户名称"
        handdleConfirm={this.onCustomerOpsComfirm}
        outerForm={this.props.form}
      />
    </Col>

    // ... ...

  </Row>
  
  // ... ...

</Form>
```
可以看到，客户弹窗选择的调用，也异常简单。
* `label` 是输入框的标签名字。`title`属性不传入时，`label`也将作为`Modal`弹窗的标题。
* `onModal` 是调用者在`state` 中维护的属性，控制`Modal`的显示与隐藏。
* `taggleModal` 是更新`onModal`属性的回调方法。
* `onCustomerOpsComfirm` 是`handdleConfirm`事件的回调函数。
* `outerForm` 是调用者的`form`属性，调用者被`Form.create`包装时将获得此值。

## 高级内容
此部分为高级内容。

### 具体实现
关于标准弹窗的具体实现。
