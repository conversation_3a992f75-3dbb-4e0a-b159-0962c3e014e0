import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employee/updateEmployeeAndDept
     * @desc 新增部门和雇员关系,并且修改雇员信息
新增部门和雇员关系,并且修改雇员信息，部门departmentId,用户employee，用户亲属列表empRelativeList
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.CommonResponse();
export const url = '/rhro-service-1.0/employee/updateEmployeeAndDept:POST';
export const initialUrl = '/rhro-service-1.0/employee/updateEmployeeAndDept';
export const cacheKey = '_employee_updateEmployeeAndDept_POST';
export async function request(
  data: defs.admin.EmployeeVO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employee/updateEmployeeAndDept`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.EmployeeVO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employee/updateEmployeeAndDept`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
