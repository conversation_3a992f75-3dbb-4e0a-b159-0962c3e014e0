<!--
 * @Author: 侯成
 * @since: 2019-07-25 16:11:41
 * @lastTime: 2019-08-02 19:27:17
 * @LastAuthor: Do not edit
 * @message: 
 -->
# 时间范围选择器

## 概况

时间选择器已有成熟组件，使用起来也异常简单。但时间范围选择，不能通过两个时间选择器的简单组合来实现。一个完整的时间范围选择器，至少应当包含几个功能：
* 开始时间的范围限制
* 结束时间的范围限制
* 开始时间不应晚于结束时间
* 结束时间不应早于开始时间
* 两个选择框之间的联动交互
在antd的组件库中，存在一个日期范围选器，不过它是用一个超长`input`输入框实现的，不符合当前需求，所以有必要实现一个范围选择器组件。

## 基本实现

类型声明：
```tsx
// /home/<USER>/ework/chro_web/src/components/DateRange/index.tsx
const backFormatter = 'YYYY/MM/DD'
export interface MomentRange {
  [x: string]: moment.Moment | undefined
}

export interface MomentBoundary {
  startBoundary: Array<number>;
  endBoundary: Array<number>;
}
interface DateRangeProps {
  fields: DateRangeFormItem[];
  showTime?: boolean;
  format?: string | string[];
  inputStyle?: React.CSSProperties;
  outerForm?: WrappedFormUtils<any>;
  handleConfirm?: (value: MomentRange) => void;
}

interface DateRangeStates extends MomentBoundary {
  startValue?: moment.Moment,
  endValue?: moment.Moment,
  endOpen: boolean,
}

class DateRange extends React.Component<DateRangeProps, DateRangeStates> {}
```
* `backFormatter`，默认日期格式。
* `MomentRange`，传入回调函数时，组件返回的数据格式。
* `MomentBoundary`，日期选择时，可选时间范围的配置的类型。

组件属性说明：

属性 | 说明 | 类型 | 默认值
----|------|----- | :----:
fields| 必选。控件表单的字段配置，详细说明见表格下方。 | `DateRangeFormItem[]` | -
showTime| 可选。是否显示时间。 | `boolean` | -
format| 可选。时间转字符串时的格式。 | `string OR string[]` | 'YYYY/MM/DD'
inputStyle| 可选。输入框的CSS属性。 | `React.CSSProperties` | -
outerForm| 可选。需要时应当传入父组件的form属性，以提供表单验证功能。此属性为空时，将得到无验证功能的普通输入框。 | `WrappedFormUtils<any>` | -
handleConfirm| 可选。获取选择结果的回调函数。需要说明的是，传入`outerForm`属性时，可以通过`form.validateFields`获取本控件的选中值，`handleConfirm` 是不必须的。若因各种情况无法使用`Form.create`包装控件，则传入本回调方法，亦可获得选中值。 | `React.CSSProperties` | -

**fields** 属性说明
`fields` 的类型是 `DateRangeFormItem[]`，这是一个在 `GetFieldDecoratorOptions` 的基础上的扩展类型。多接收了三个参数 `title`， `dataIndex`，`range`，说明如下
* `title` 是选择框的名称，通常显示与左侧。
* `dataIndex` 是这个选择器的字段的索引名，在返回值中将作为对象的键来使用。
* `range` 是这个选择器的日期可选范围，以半角波浪线分割下界与上界。`"2019/07/13"`, `"2019/07/13 ~"`, `"2019/07/13 ~ 2019/08/13"`, `"~ 2019/08/13"` 均是合法的范围标识。时间的格式与`format`属性的值相同。

使用示例：
```tsx
// src/pages/Sales/ContractManage/index.tsx

createDtRange = () => {
    const { form } = this.props
    const fields: DateRangeFormItem[] = [
      {
        title: "创建时间大于等于",
        dataIndex: "createDtStart",
        range: '2019/07/13~2019/08/13',
        rules: [{
          required: true,
          message: '请选择创建时间大于等于',
        }]
      },
      {
        title: "创建时间小于等于",
        dataIndex: "createDtEnd",
        range: '2019/08/13~2020/08/13'
      }
    ]
    return (
      <DateRange
      fields={fields}
      outerForm={form}
      // showTime={true}
      // format="YYYY/MM/DD HH:MM:SS"
      />
    )
  }
// ... ...
<Form
  onSubmit={this.handleSearch}
  layout="inline"
  {...this.formItemLayout}
>
  // ... ...

  <Row gutter={\{ md: 24, lg: 24, xl: 48 \}}>
    { this.createDtRange() }
  </Row>

  // ... ...
</Form>
```

接受日期数据后，将Moment转为字符串：
```tsx
// src\pages\Sales\ContractManage\index.tsx
handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault();
  const { form } = this.props;
  form.validateFields((err, fieldsValue) => {
    if (err) return;
    // console.log('fieldsValue in handleSearch:', fieldsValue);
    const dateValues = formatPickedDate(dateFields, fieldsValue);
    const values = { ...dateValues } as QueryInput;
    const { formValues } = this.state;

    this.setState({
      formValues: { ...formValues, ...values },
    });
    this.queryContracts(values);
  });
};
```
