import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/allocateCustomer/queryCbmAllList
     * @desc 查询客服权限转给哪个客服
查询客服权限转给哪个客服
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.EmployeeHireSep();
export const url = '/rhro-service-1.0/allocateCustomer/queryCbmAllList:POST';
export const initialUrl = '/rhro-service-1.0/allocateCustomer/queryCbmAllList';
export const cacheKey = '_allocateCustomer_queryCbmAllList_POST';
export async function request(
  data: defs.admin.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/allocateCustomer/queryCbmAllList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/allocateCustomer/queryCbmAllList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
