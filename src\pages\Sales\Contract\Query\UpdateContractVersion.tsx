import React, { useEffect, useState } from 'react';
import { Button, Form } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import Codal from '@/components/Codal';
import { WritableInstance } from '@/components/Writable';
import { FormElement2, FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { msgOk } from '@/utils/methods/message';
import { mapToSelectors } from '@/components/Selectors';
import {
  contractEndDateTypeMap,
  contractEndDateTypeNameMap,
} from '@/utils/settings/sales/contract';
import { RuleObject } from 'antd/lib/form';
import { StoreValue } from 'antd/lib/form/interface';
import { ConfirmLoading } from '@/utils/request';

const columns: WritableColumnProps<any>[] = [];
const renderButtons = (options: WritableInstance) => {
  return (
    <>
      <Button>保存</Button>
      <Button>取消</Button>
    </>
  );
};

interface UpdateContractVersionProps {
  [props: string]: any;
  modal: [boolean, CallableFunction];
  contract?: Partial<defs.sale.ContractDTO>;
  onConfirm: () => void;
}

const service = API.sale.contract.saveContractVersion;
const apis = [
  'contractService,saveContractVersion',
  'contractService,saveContractVersion',
  'contractVersionService,isDeletedContractVersion',
  'contractVersionService,setValidCustomerRecord',
  'serviceTypeService,getSub',
  'quotationService,getQuotation',
];
const codalNames = [];
const UpdateContractVersion: React.FC<UpdateContractVersionProps> = (props) => {
  const { contract, onConfirm, modal } = props;
  const [visible, setVisible] = modal;
  if (!visible) return null;
  if (!contract) return null;
  const [form] = Form.useForm();
  const { contractId } = contract;
  // 这个contract.contractEndDateType可能是中文
  const propsContractEndDateType =
    contractEndDateTypeNameMap.get(contract.contractEndDateType) || contract.contractEndDateType;
  const [contractEndDateType, setcontractEndDateType] = useState(propsContractEndDateType);

  useEffect(() => {
    if (!contractId) return;
    const contractVersion = {
      contractEndDateType: propsContractEndDateType,
      contractEndDate: contract.contractEndDate,
      contractVersion: contract.contractVersion,
    };
    form.setFieldsValue(contractVersion);
  }, [contractId]);

  const onOk = () => {
    form.validateFields().then((values) => {
      service.requests({ ...values, contractId }).then((data) => {
        msgOk('更新合同信息（质控）成功。');
        setVisible(false);
        onConfirm();
      });
    });
  };

  const oncontractEndDateTypeChange = (value: string) => {
    setcontractEndDateType(value);
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '合同最终结束日期类型',
      fieldName: 'contractEndDateType',
      inputRender: () =>
        mapToSelectors(contractEndDateTypeMap, { onChange: oncontractEndDateTypeChange }),
    },
    {
      label: '最终结束日期',
      fieldName: 'contractEndDate',
      inputRender: 'date',
      rules: [
        {
          required: contractEndDateType === '3',
          message: '请填写最终结束日期',
        },
      ],
    },
    {
      label: '合同版本',
      fieldName: 'contractVersion',
      rules: [
        { required: true, message: '请输入合同版本' },
        {
          validator: (_: RuleObject, value: StoreValue) => {
            if (!value) return Promise.resolve();
            if (value === '未匹配') return Promise.reject('不允许填写未匹配字样。');
            return Promise.resolve();
          },
        },
      ],
      inputRender: 'string',
    },
  ];

  return (
    <Codal
      title="合同版本号维护"
      visible={visible}
      service={service}
      onCancel={() => setVisible(false)}
      onOk={onOk}
      width={'70%'}
    >
      <FormElement2 form={form} onFinishFailed={() => ConfirmLoading.clearLoading(service)}>
        <EnumerateFields colNumber={2} outerForm={form} formColumns={formColumns} />
      </FormElement2>
    </Codal>
  );
};

export default UpdateContractVersion;
