import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/department/getSpecialType
     * @desc 获取特殊化类型字符串
获取特殊化类型字符串
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = '';
export const url = '/rhro-service-1.0/department/getSpecialType:POST';
export const initialUrl = '/rhro-service-1.0/department/getSpecialType';
export const cacheKey = '_department_getSpecialType_POST';
export async function request(
  data: defs.admin.FunctionDept,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/department/getSpecialType`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.FunctionDept,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/department/getSpecialType`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
