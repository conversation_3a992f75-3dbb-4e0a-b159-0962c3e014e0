/*
 * @Author: Veni_刘夏梅
 * @Email: <EMAIL>
 * @Date: 2020-12-01 16:10:24
 * @LastAuthor: Veni_刘夏梅
 * @LastTime: 2020-12-01 16:14:19
 * @message: 申报
 */

import React, { useEffect } from 'react';
import { Col, DatePicker, Form, InputNumber, Row } from 'antd';
import Codal from '@/components/Codal';

import { FormElement1, RowElementButton } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { mapToSelectors } from '@/components/Selectors';
import dayjs from 'dayjs';
import { stdYearFormat } from '@/utils/methods/times';
import moment from 'moment';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';

interface ConfirmCodalProps {
  visible: number;
  hideHandle: CallableFunction;
  submit: CallableFunction;
}

const declareWagesTypeMap = new Map<number, string>([
  [1, '按人'],
  [2, '按险种'],
]);

const SendCodal: React.FC<ConfirmCodalProps> = (props) => {
  const { visible, hideHandle, submit } = props;

  const [form] = Form.useForm();
  useEffect(() => {
    if (!visible) {
      form.resetFields();
      return;
    }
    if (visible === 1) {
      form.setFieldsValue({ declareWagesType: 1, isCompareEmptySalary: '1' });
    }
  }, [visible]);

  const formColumns = [
    {
      label: '年份',
      fieldName: 'operateYear',
      inputRender: () => (
        <DatePicker
          picker="year"
          format={stdYearFormat}
          disabledDate={(current) => {
            const beforeDate = moment(new Date()).subtract(1, 'years').format(stdYearFormat);
            const nowDate = moment(new Date()).add(2, 'years').format(stdYearFormat);

            const tooLate = current && moment(current).diff(nowDate, 'years') > 0;
            const beforeLate = current && moment(current).diff(beforeDate, 'years') < 0;
            // console.log('current',current,beforeLate,tooLate)
            return tooLate || beforeLate;
          }}
        />
      ),
      rules: [{ required: true, message: '请选择年份' }],
    },
    {
      label: '工资申报类型',
      fieldName: 'declareWagesType',
      inputRender: () =>
        mapToSelectors(declareWagesTypeMap, { disabled: visible === 1, allowClear: true }),
      rules: [{ required: true, message: '请选择工资申报类型' }],
    },
    {
      label: '是否仅比对缴费工资为空人员',
      fieldName: 'isCompareEmptySalary',
      inputRender: () => mapToSelectors(yesNoDataMap),
      hidden: visible !== 1,
      formOptions: {
        labelCol: {
          span: 12,
        },
        wrapperCol: { span: 12 },
      },
    },
  ];

  return (
    <Codal
      visible={!!visible}
      title={visible === 1 ? '税务比对' : '工资申报'}
      onCancel={() => {
        hideHandle();
      }}
      onOk={async () => {
        const values = await form.validateFields();
        submit(
          moment(values.operateYear).format('YYYY'),
          values.declareWagesType,
          values.isCompareEmptySalary,
        );
      }}
      width="60%"
    >
      <FormElement1 form={form}>
        <EnumerateFields formColumns={formColumns} outerForm={form} colNumber={2} />
        <>数据报送税务网站</>
      </FormElement1>
    </Codal>
  );
};

export default SendCodal;
