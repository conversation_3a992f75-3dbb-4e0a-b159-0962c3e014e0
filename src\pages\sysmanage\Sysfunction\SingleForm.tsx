import React, { useEffect, useState } from 'react';

import {
  FormElement2,
  RowElement,
  ColElement2,
  ColElementButton,
} from '@/components/Forms/FormLayouts';
import { useSelector, useDispatch } from 'umi';
import { shallowEqual } from 'react-redux';
import { Input, Select, Button, Radio, Checkbox, Row, Col } from 'antd';
import Confirm, { ConfirmLoading } from '@/components/Forms/Confirm';
import Form, { Rule, FormInstance, RuleObject } from 'antd/lib/form';
import { EnumerateFields, EditeFormProps } from '@/components/CachedPage/EnumerateFields';
import { StoreValue } from 'antd/lib/form/interface';
import {
  isMyWorkMap,
  sysFuncScene,
  menuTypeMap,
  IFuncInfo,
} from '@/utils/settings/sysmanage/sysfunc';
import { mapToRatio } from '@/components/Selectors/FuncRatio';
import { SysmanageConnectState } from '../models/connect';

import { mapToSelectors } from '@/components/Selectors';
import { resError, resErrorMsg, msgErr, msgOk } from '@/utils/methods/message';
import { posoToCheckbox } from '@/components/Selectors/FuncCheckbox';
import styles from './SysFuncForm.less';
import Icon, { StarOutlined } from '@ant-design/icons';
import { validateNaturalPositive } from '@/utils/forms/validate';
import { isUrl } from '@/utils/utils';
import IconFont from '@/components/IconFont';

interface SingleFormProps {
  scene: number;
  superFunctionId: string;
}

const formRules: { [key: string]: Rule[] } = {
  functionId: [
    {
      required: true,
      message: '请输入功能ID',
    },
    // {
    //   validator: validateNaturalPositive(),
    // },
  ],
  functionName: [
    {
      required: true,
      message: '请输入功能名称',
    },
    {
      max: 100,
      message: '功能名称长度应在100以内',
    },
  ],
  functionCode: [
    {
      required: true,
      message: '请输入功能编码',
    },
    {
      max: 50,
      message: '功能编码长度应在50以内',
    },
  ],
};

const services = [API.admin.menu.insertSysFunction, API.admin.menu.updateSysFunction];

const SingleForm: React.FC<SingleFormProps> = (props) => {
  const { scene, superFunctionId } = props;
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [icon, setIcon] = useState<string | undefined>();
  const [parentUrl, setParentUrl] = useState<string | undefined>();

  const { funcInfo, specialTypeMap } = useSelector(
    (state: SysmanageConnectState) => ({
      funcInfo: state!.sysfunction!.funcInfo,
      specialTypeMap: state!.sysfunction!.specialTypeMap,
    }),
    shallowEqual,
  );

  const formCols: EditeFormProps[] = [
    {
      fieldName: 'functionId',
      label: '功能Id',
      inputRender: 'string',
      inputProps: {
        disabled: scene === sysFuncScene.update,
      },
      rules: formRules.functionId,
    },
    {
      fieldName: 'iconUrl',
      label: '图标',
      inputRender: 'string',
      inputProps: {
        onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
          setIcon(event.currentTarget.value as string);
        },
      },
    },
    {
      fieldName: 'functionName',
      label: '功能名称',
      inputRender: 'string',
      formOptions: {
        rules: formRules.functionName,
      },
    },
    {
      fieldName: 'reactUrl',
      label: '链接地址',
      inputRender: 'string',
      inputProps: { placeholder: '请先确认父级地址存在' },
      rules: [
        {
          validator: (_: RuleObject, value: StoreValue) => {
            if (!value) return Promise.resolve();
            if (superFunctionId === '-1') return Promise.resolve();
            // if (scene === sysFuncScene.update) return Promise.resolve()
            if (!parentUrl) return Promise.reject('父级地址不存在，此处不可填写。');
            if (!value.startsWith(parentUrl))
              return Promise.reject(`链接地址必须以${parentUrl}开头。`);
            if (value === parentUrl) return Promise.reject('链接地址不可全等于父级菜单地址。');
            return Promise.resolve();
          },
        },
      ],
    },
    {
      fieldName: 'functionCode',
      label: '功能编码',
      inputRender: 'string',
      formOptions: {
        rules: formRules.functionCode,
      },
    },
    {
      fieldName: 'helpUrl',
      label: '帮助路径',
      inputRender: 'string',
    },
    {
      fieldName: 'functionType',
      label: '功能类型',
      inputRender: () => mapToSelectors(menuTypeMap),
    },
    {
      fieldName: 'reportUrl',
      label: '报表链接',
      inputRender: 'string',
    },
    {
      fieldName: 'isMyWork',
      label: '我的工作？',
      inputRender: (outerForm: FormInstance, options: Partial<EditeFormProps>) => {
        return mapToRatio(isMyWorkMap);
      },
    },
    {
      fieldName: 'specialType',
      label: '特例类型',
      inputRender: () => {
        return posoToCheckbox(specialTypeMap);
      },
    },
    {
      fieldName: 'inconExam',
      label: '图标示例',
      inputRender: () => {
        return (
          <ColElement2>
            {icon ? (
              <IconFont style={{ fontSize: '30px' }} type={icon} />
            ) : (
              <StarOutlined style={{ fontSize: '30px' }} />
            )}
          </ColElement2>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch({ type: 'sysfunction/getSpecialTypes' });
  }, []);

  useEffect(() => {
    let parentUrl;
    if (scene === sysFuncScene.addChild) {
      form.resetFields();
      parentUrl = funcInfo.reactUrl;
    } else if (scene === sysFuncScene.addSibling) {
      form.resetFields();
      parentUrl = funcInfo.parent?.reactUrl;
    } else if (scene === sysFuncScene.update) {
      form.setFieldsValue({ ...funcInfo });
      parentUrl = funcInfo.parent?.reactUrl;
      setIcon(funcInfo.iconUrl);
    }
    setParentUrl(parentUrl);
  }, [scene, funcInfo.functionId]);

  const onFinish = (values: IFuncInfo) => {
    if (!funcInfo.functionId) {
      ConfirmLoading.clearLoading(services);
      return msgErr('请选先择菜单。');
    }

    values.superFunctionId = superFunctionId;
    if (scene === sysFuncScene.update) {
      // 修改当前
      values.functionInfoId = funcInfo.functionInfoId;
      API.admin.menu.updateSysFunction.requests(values).then(() => {
        msgOk('更新成功');
        dispatch({ type: 'sysfunction/setSysFuncInfo', payload: { ...funcInfo, ...values } });
        dispatch({
          type: 'sysfunction/getSysFuncs',
        });
      });
      return;
    }
    let service: IServiceType;

    if (scene === sysFuncScene.addSibling) {
      // 新增同级
      service = API.admin.menu.insertSysFunction;
    } else {
      // 新增子级
      service = API.admin.menu.insertSysFunction;
    }

    service.requests(values).then((res: StdRes) => {
      msgOk('请求成功。');
      // dispatch({ type: 'sysfunction/setSysFuncInfo', payload: { ...funcInfo, ...values } });
      dispatch({
        type: 'sysfunction/getSysFuncs',
      });
    });
    // })
  };

  const onFinishFailed = (errorInfo: POJO) => {
    ConfirmLoading.clearLoading(services);
  };

  const handleFormReset = () => {};

  return (
    <FormElement2 form={form} onFinish={onFinish} onFinishFailed={onFinishFailed}>
      <EnumerateFields colNumber={2} formColumns={formCols} outerForm={form} />
      <RowElement>
        <ColElementButton offset={12}>
          <Confirm
            service={services}
            type="primary"
            htmlType="submit"
            disabled={!funcInfo.functionId}
          >
            保存
          </Confirm>
          <Button onClick={handleFormReset}>重置</Button>
        </ColElementButton>
      </RowElement>
    </FormElement2>
  );
};

export { SingleForm };
