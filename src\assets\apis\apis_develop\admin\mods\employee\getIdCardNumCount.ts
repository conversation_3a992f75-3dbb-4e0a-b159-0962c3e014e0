import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employee/getIdCardNumCount
     * @desc 获取员工身份证号码个数
获取员工身份证号码个数
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** empId */
  empId?: string;
  /** idCardNum */
  idCardNum: string;
}

export const init = '';
export const url = '/rhro-service-1.0/employee/getIdCardNumCount:GET';
export const initialUrl = '/rhro-service-1.0/employee/getIdCardNumCount';
export const cacheKey = '_employee_getIdCardNumCount_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/getIdCardNumCount`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/getIdCardNumCount`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
