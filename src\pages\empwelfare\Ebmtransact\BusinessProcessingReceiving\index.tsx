/*
 * @Author: <PERSON>eni_刘夏梅
 * @Email: <EMAIL>
 * @Date: 2020-12-02 15:24:46
 * @LastAuthor: Veni_刘夏梅
 * @LastTime: 2020-12-02 15:59:57
 * @message: 员工业务办理-HRO业务办理（接单）
 */

import React from 'react';
import BusinessProcessing from '../BusinessProcessingProject/components/main';

interface Props {
  [props: string]: any;
}

const BusinessProcessingProject: React.FC<Props> = () => {
  return <BusinessProcessing type="receive" />;
};

export default BusinessProcessingProject;
