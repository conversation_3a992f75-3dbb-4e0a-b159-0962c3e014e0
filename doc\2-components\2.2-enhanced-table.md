<!--
 * @Author: 侯成
 * @since: 2019-07-24 14:31:11
 * @lastTime: 2019-07-25 16:50:09
 * @LastAuthor: Do not edit
 * @message: 
 -->
# 增强版表格

## 具体实现
```tsx
// src/components/StandardTable/index.tsx

class EnhancedTable<T = any> extends StandardTable<T> {
  render() {
    const { selectedRowKeys, needTotalList } = this.state;
    const { data, rowKey, showAlert, rowSelectType, ...rest } = this.props;
    const { list = [], pagination } = data;

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    const rowSelection: TableRowSelection<T> = {
      selectedRowKeys,
      type: rowSelectType,
      onChange: this.handleRowSelectChange,
      getCheckboxProps: (record: T & { disabled?: boolean }) => ({
        disabled: record.disabled,
      }),
    };

    return (
      <div className={styles.standardTable}>
        {showAlert === false ? null : (
          <div className={styles.tableAlert}>
            <Alert
              // ... ...
            />
          </div>
        )}

        <Table<T>
          rowKey={rowKey || 'key'}
          rowSelection={rowSelection}
          dataSource={list}
          pagination={paginationProps}
          onChange={this.handleTableChange}
          {...rest}
        />
      </div>
    );
  }
}
export { EnhancedTable };
```

可以看到，`EnhancedTable`继承了`StandardTable`， 并且仅仅对`render` 做了少量更改。相比标准版--`StandardTable`，增强版在属性中多读取了两个属性`showAlert, rowSelectType`。
* `showAlert` 是控制是否先表格概况的属性。表格概况包括：表格总数，选中条数，清空选中的快捷按钮。这些内容，在主页页面中可能会有用。但是，对于弹窗中的表格，由于存在筛选条件，总数较少；通常只能选中1条，不需要快捷清空。这些概况便失去了指示意义，并且还挤占了弹窗的空间。所以在增强版中，此概况不显示，显式的传入`showAlert = true`时，才显示概况。
* `rowSelectType` 指示表格单选或多选的属性，接收的值为 `'checkbox' | 'radio'`。在弹窗表格中，通常只允许选择一项，通过传入 `'radio'`，表格将变只支持单选，省去了限制多条选中，多条提交所需做的工作。

调用示例：

```tsx
// src/components/StandardTable/index.tsx

<EnhancedTable
  rowKey={this.rowKey}
  loading={loading}
  showAlert={false}
  rowSelectType="radio"
  data={datas!}
  selectedRows={selectedRows}
  onSelectRow={this.handleSelectRows}
  columns={this.columns}
  onChange={this.handleStandardTableChange}
/>
```
可以看到，相比普通的`StandardTable`组件，增强版的`EnhancedTable`调用增加了两个属性传入分别是：
* `showAlert={false}`
* `rowSelectType="radio"`
在这个调用示例中，此表格将不显示概况，只支持单选。