[{"path": "/basedata/province", "component": "./basedata/ProvinceInfo/index", "pageName": "省份维护", "author": "侯成"}, {"path": "/basedata/city", "component": "./basedata/CityInfo/index", "pageName": "城市维护", "author": "刘双"}, {"path": "/basedata/area-code", "component": "./basedata/AreaCode/index", "pageName": "地区码维护", "author": "刘双"}, {"path": "/basedata/country", "component": "./basedata/CountryInfo/index", "pageName": "国家维护", "author": "刘双"}, {"path": "/basedata/competitor", "component": "./basedata/Competitor/index", "pageName": "竞争对手维护", "author": "刘双"}, {"path": "/basedata/sys-branch-title", "component": "./basedata/SysBranchTitle/index", "pageName": "签单方抬头", "author": "刘双"}, {"path": "/basedata/socialsecurity/prodratio", "component": "./basedata/Socialsecurity/ProductRatio/index", "pageName": "社保比例维护", "author": "严小强"}, {"path": "/basedata/socialsecurity/maintenance", "component": "./basedata/Socialsecurity/Maintenance/index", "pageName": "社保组维护", "author": "严小强"}, {"path": "/basedata/socialsecurity/comboManage", "component": "./basedata/Socialsecurity/ComboManage/index", "pageName": "社保组维护", "author": "严小强"}, {"path": "/basedata/socialsecurity/PersonCategory", "component": "./basedata/Socialsecurity/PersonCategory/index", "pageName": "城市人员分类维护", "author": "严小强"}, {"path": "/basedata/taxRateTable", "component": "./basedata/TaxRateTable/index", "pageName": "税率表维护", "author": "严小强"}, {"path": "/basedata/baseDataXml", "component": "./basedata/BaseDataXml/index", "pageName": "数据字典维护", "author": "赵煜颢"}, {"path": "/basedata/jindie<PERSON>rg", "component": "./basedata/JindieOrg/index", "pageName": "金蝶分公司维护", "author": "赵煜颢"}, {"path": "/basedata/branchInvoice", "component": "./basedata/BranchInvoice/index", "pageName": "分公司开票维护", "author": "赵煜颢"}, {"path": "/basedata/branchback", "component": "./basedata/BranchBack/index", "pageName": "分公司银行账号维护", "author": "赵煜颢"}, {"path": "/basedata/hire", "component": "./basedata/Hire/index", "pageName": "入离职基础数据", "author": "朱江华"}, {"path": "/basedata/welfareType", "component": "./basedata/WelfareType/index", "pageName": "福利类型大类小类维护", "author": "刘夏梅"}, {"path": "/basedata/businessType", "component": "./basedata/BusinessType/index", "pageName": "业务大小类维护", "author": "刘夏梅"}, {"path": "/basedata/emp-maintain", "component": "./basedata/EmpMaintain/index", "pageName": "分公司用工方维护", "author": "孙尚阳"}, {"path": "/basedata/socialsecurity/servicePoint", "component": "./basedata/Socialsecurity/ServicePoint/index", "pageName": "服务网点维护", "author": "孙尚阳"}, {"path": "/basedata/type-definition", "component": "./basedata/TypeDefinition/index", "pageName": "导入类型定义", "author": "沈彦霖"}, {"path": "/basedata/instance-definition", "component": "./basedata/InstanceDefinition/index", "pageName": "导入实例定义", "author": "沈彦霖"}, {"path": "/basedata/serviceType", "component": "./basedata/ServiceType/index", "pageName": "合同大小类维护", "author": "刘夏梅"}, {"path": "/basedata/fileProvider", "component": "./basedata/FileProvider/index", "pageName": "档案供应商维护", "author": "陈刚强"}, {"path": "/basedata/materialsInfo/materialsAssert", "component": "./basedata/MaterialsInfo/MaterialsAssert/index", "pageName": "材料维护", "author": "陈刚强"}, {"path": "/basedata/materialsInfo/materialPackage", "component": "./basedata/MaterialsInfo/MaterialPackage/index", "pageName": "材料包维护", "author": "陈刚强"}, {"path": "/basedata/medicalIns/MedicalAssert", "component": "./basedata/medicalIns/MedicalAssert/index", "pageName": "医疗机构维护", "author": "王正荣"}, {"path": "/basedata/medicalIns/ImportMedical", "component": "./basedata/medicalIns/ImportMedical/index", "pageName": "批量导入医疗机构", "author": "王正荣"}, {"path": "/basedata/specialSignerTitleMaintance", "component": "./basedata/SpecialSignerTitleMaintance/index", "pageName": "特殊签单方抬头维护", "author": "王正荣"}, {"path": "/emphiresep/sendorder/customerbubcontract", "component": "./emphiresep/sendorder/CustomerSubcontract/index", "pageName": "客户小合同管理", "author": "侯成"}, {"path": "/emphiresep/sendorder/querytransferandsubcontract", "component": "./emphiresep/sendorder/QueryTransferAndSubcontract/index", "pageName": "交接单录入和小合同派单提醒", "author": "侯成"}, {"path": "/emphiresep/sendorder/handover", "component": "./emphiresep/sendorder/QueryTransferInfo/index", "pageName": "客户交接单管理", "author": "侯成"}, {"path": "/emphiresep/sendorder/ManageTemplate", "component": "./emphiresep/sendorder/ManageTemplate/index", "pageName": "客户账单模板管理", "author": "朱江华"}, {"path": "/emphiresep/emporder/QueryEmpOrderListForGen", "component": "./emphiresep/emporder/QueryEmpOrderListForGen/index", "pageName": "生成个人订单", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryClientOrderForAdd", "component": "./emphiresep/emporder/QueryClientOrderForAdd/index", "pageName": "客户端增员", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryEmpOrderListForPer", "component": "./emphiresep/emporder/QueryEmpOrderListForPer/index", "pageName": "完善个人订单", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryEmpOrderListForCon", "component": "./emphiresep/emporder/QueryEmpOrderListForCon/index", "pageName": "确认个人订单", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryEmpOrderListForEdit", "component": "./emphiresep/emporder/QueryEmpOrderListForEdit/index", "pageName": "变更个人订单", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryEmployeeMonth", "component": "./emphiresep/emporder/QueryEmployeeMonth/index", "pageName": "个人订单月度查询", "author": "刘夏梅"}, {"path": "/emphiresep/emporder/EmpBankCardMaintainance", "component": "./emphiresep/emporder/EmpBankCardMaintainance/index", "pageName": "员工银行卡维护", "author": "王正荣"}, {"path": "/emphiresep/emporder/UpdateOderEmpIDC", "component": "./emphiresep/emporder/UpdateOderEmpIDC/index", "pageName": "证件号码修改", "author": "王正荣"}, {"path": "/emphiresep/emporder/ImportBankCard", "component": "./emphiresep/emporder/ImportBankCard/index", "pageName": "批量导入银行卡信息", "author": "王正荣"}, {"path": "/emphiresep/emporder/ConfirmEmpOrderForAssigner", "component": "./emphiresep/emporder/ConfirmEmpOrderForAssigner/index", "pageName": "变更订单确认", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryEmpOrderListForTransfer", "component": "./emphiresep/emporder/QueryEmpOrderListForTransfer/index", "pageName": "申报转移", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryEmployeeOrder", "component": "./emphiresep/emporder/QueryEmployeeOrder/index", "pageName": "个人订单查询", "author": "严小强"}, {"path": "/emphiresep/emporder/EmployeeBaseInfoManage", "component": "./emphiresep/emporder/EmployeeBaseInfoManage/index", "pageName": "员工信息查询", "author": "严小强"}, {"path": "/emphiresep/emporder/NationWideEmpQuery", "component": "./emphiresep/emporder/NationWideEmpQuery/index", "pageName": "全国雇员查询", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryImpOrderLite", "component": "./emphiresep/emporder/QueryImpOrderLite/index", "pageName": "跨客户批量导入个人订单", "author": "赵煜颢"}, {"path": "/emphiresep/emporder/QueryImpOrderPro", "component": "./emphiresep/emporder/QueryImpOrderPro/index", "pageName": "批量导入个人订单产品", "author": "赵煜颢"}, {"path": "/emphiresep/emporder/QueryImpWrongOrderPro", "component": "./emphiresep/emporder/QueryImpWrongOrderPro/index", "pageName": "非社保公积金批量导入个人订单", "author": "赵煜颢"}, {"path": "/emphiresep/emporder/BatchAlterEmpOrderManage", "component": "./emphiresep/emporder/BatchAlterEmpOrderManage/index", "pageName": "批量变更账单模板或收费模板", "author": "严小强"}, {"path": "/emphiresep/emporder/QueryBatchDelProduct", "component": "./emphiresep/emporder/QueryBatchDelProduct/index", "pageName": "批量删除个人订单产品", "author": "严小强"}, {"path": "/emphiresep/emporder/EmpFeeSearchReport", "component": "./emphiresep/emporder/EmpFeeSearchReport/index", "pageName": "雇员费用段横表查询", "author": "刘夏梅"}, {"path": "/emphiresep/emporder/QueryEmpDetailInfo", "component": "./emphiresep/emporder/QueryEmpDetailInfo/index", "pageName": "员工详细信息查询", "author": "刘夏梅"}, {"path": "/emphiresep/emporder/QueryAdjustment", "component": "./emphiresep/emporder/QueryAdjustment/index", "pageName": "社保公积金调整", "author": "赵煜颢"}, {"path": "/emphiresep/emporder/QueryMinBaseAdjustment", "component": "./emphiresep/emporder/QueryMinBaseAdjustment/index", "pageName": "社保公积金最低基数调整", "author": "赵煜颢"}, {"path": "/emphiresep/quitManager/QueryEmpOrderListForSepApply", "component": "./emphiresep/quitManager/QueryEmpOrderListForSepApply/index", "pageName": "申报员工离职", "author": "赵煜颢"}, {"path": "/emphiresep/quitManager/QueryExEmpOrderListForSepCon", "component": "./emphiresep/quitManager/QueryExEmpOrderListForSepCon/index", "pageName": "接单方确认离职", "author": "赵煜颢"}, {"path": "/emphiresep/quitManager/QueryEmpOrderListForSepCon", "component": "./emphiresep/quitManager/QueryEmpOrderListForSepCon/index", "pageName": "派单方确认离职", "author": "赵煜颢"}, {"path": "/emphiresep/quitManager/QueryClientOrderForReduce", "component": "./emphiresep/quitManager/QueryClientOrderForReduce/index", "pageName": "客户端减员", "author": "赵煜颢"}, {"path": "/emphiresep/quitManager/batchQuitLite", "component": "./emphiresep/quitManager/BatchQuitLite/index", "pageName": "批量离职申请", "author": "赵煜颢"}, {"path": "/emphiresep/empSMSInfo", "component": "./emphiresep/empSMSInfo/index", "pageName": "短信通知", "author": "刘夏梅"}, {"path": "/emphiresep/callcenter/QueryCcWorkOrder", "component": "./emphiresep/callcenter/QueryCcWorkOrder/index", "pageName": "工单查询", "author": "王正荣"}, {"path": "/emphiresep/hire/materialsCollection", "component": "./emphiresep/hire/MaterialsCollection/index", "pageName": "入职材料收缴", "author": "陈刚强"}, {"path": "/emphiresep/hire/search", "component": "./emphiresep/hire/MaterialsCollectionSearch/index", "pageName": "入职材料收缴查询", "author": "陈刚强"}, {"path": "/emphiresep/hire/queryEmpHireFromApp", "component": "./emphiresep/hire/QueryEmpHireFromApp/index", "pageName": "入职办理", "author": "陈刚强"}, {"path": "/emphiresep/hire/HireClassify", "component": "./emphiresep/hire/HireClassify/index", "pageName": "入职分类", "author": "刘夏梅"}, {"path": "/emphiresep/hire/HireClassifyAgain", "component": "./emphiresep/hire/HireClassifyAgain/index", "pageName": "入职重新分类", "author": "刘夏梅"}, {"path": "/emphiresep/hire/HireListType", "component": "./emphiresep/hire/HireListType/index", "pageName": "入职名单", "author": "刘夏梅"}, {"path": "/emphiresep/hire/queryCmpAccount", "component": "./emphiresep/hire/QueryCmpAccount/index", "pageName": "批量生成易智汇账号", "author": "陈刚强"}, {"path": "/emphiresep/hire/queryCmpAccountControl", "component": "./emphiresep/hire/QueryCmpAccountControl/index", "pageName": "入职附件上传监控", "author": "陈刚强"}, {"path": "/empwelfare/socialmanage/QuerySSLock", "component": "./empwelfare/LockManage/Social", "pageName": "社保锁定", "author": "沈彦霖"}, {"path": "/empwelfare/socialmanage/report/SocialSecReport", "component": "./empwelfare/Report/SocialSecReport", "pageName": "社保报表", "author": "沈彦霖"}, {"path": "/empwelfare/providentFund/QuerySSLock", "component": "./empwelfare/LockManage/Provident", "pageName": "公积金锁定", "author": "沈彦霖"}, {"path": "/empwelfare/providentFund/report/ProvidentFunReport", "component": "./empwelfare/Report/ProvidentFunReport", "pageName": "公积金报表", "author": "沈彦霖"}, {"path": "/empwelfare/socialmanage/socialapply", "component": "./empwelfare/Socialmanage/SocialApply", "pageName": "社保申请", "author": "侯成"}, {"path": "/empwelfare/socialmanage/SocialUpdateSelect", "component": "./empwelfare/Socialmanage/SocialUpdateSelect", "pageName": "社保修改", "author": "侯成"}, {"path": "/empwelfare/providentFund/UpdateSelect", "component": "./empwelfare/ProvidentFund/PfUpdateSelect", "pageName": "公积金修改", "author": "侯成"}, {"path": "/empwelfare/providentFund/SocialApplySelect", "component": "./empwelfare/ProvidentFund/ProvidentFundApply", "pageName": "公积申请", "author": "侯成"}, {"path": "/empwelfare/socialmanage/socialProcess", "component": "./empwelfare/Socialmanage/SocialProcess/index", "pageName": "社保办理", "author": "侯成"}, {"path": "/empwelfare/socialmanage/ssacct", "component": "./empwelfare/Socialmanage/Ssacct", "pageName": "社保账号管理", "author": "刘夏梅"}, {"path": "/empwelfare/socialmanage/QuerySsAdjustment", "component": "./empwelfare/Socialmanage/QuerySsAdjustment", "pageName": "社保调整", "author": "赵煜颢"}, {"path": "/empwelfare/socialmanage/QuerySsMinBaseAdjustment", "component": "./empwelfare/Socialmanage/QuerySsMinBaseAdjustment", "pageName": "社保最低基数调整", "author": "赵煜颢"}, {"path": "/empwelfare/providentFund/QueryPfAdjustment", "component": "./empwelfare/ProvidentFund/QueryPfAdjustment", "pageName": "公积金调整", "author": "赵煜颢"}, {"path": "/empwelfare/providentFund/QueryPfMinBaseAdjustment", "component": "./empwelfare/ProvidentFund/QueryPfMinBaseAdjustment", "pageName": "公积金最低基数调整", "author": "赵煜颢"}, {"path": "/empwelfare/providentFund/ssacct", "component": "./empwelfare/ProvidentFund/Ssacct", "pageName": "公积金账号管理", "author": "刘夏梅"}, {"path": "/empwelfare/socialmanage/SocialPay", "component": "./empwelfare/PayManage/SocialPay", "pageName": "社保支付", "author": "沈彦霖"}, {"path": "/empwelfare/providentFund/ProvidentFundPay", "component": "./empwelfare/PayManage/ProvidentFundPay", "pageName": "公积金支付", "author": "沈彦霖"}, {"path": "/empwelfare/socialmanage/ssImpBatch", "component": "./empwelfare/Socialmanage/QuerySSImpBatch", "pageName": "批量导入社保账号", "author": "刘夏梅"}, {"path": "/empwelfare/providentFund/ssImpBatch", "component": "./empwelfare/ProvidentFund/QuerySSImpBatch", "pageName": "批量导入公积金账号", "author": "刘夏梅"}, {"path": "/empwelfare/socialmanage/socialSecurity", "component": "./empwelfare/Socialmanage/QuerySocialSecurity", "pageName": "社保查询", "author": "刘夏梅"}, {"path": "/empwelfare/providentFund/socialSecurity", "component": "./empwelfare/ProvidentFund/QuerySocialSecurity", "pageName": "公积金查询", "author": "刘夏梅"}, {"path": "/empwelfare/socialmanage/socialMakeUp", "component": "./empwelfare/Socialmanage/QuerySocialMakeUp", "pageName": "社保补缴查询", "author": "刘夏梅"}, {"path": "/empwelfare/providentFund/socialMakeUp", "component": "./empwelfare/ProvidentFund/QuerySocialMakeUp", "pageName": "公积金补缴查询", "author": "刘夏梅"}, {"path": "/empwelfare/laborcontract", "component": "./empwelfare/LaborcontractManage", "pageName": "员工劳动合同管理", "author": "刘夏梅"}, {"path": "/empwelfare/providentFund/process", "component": "./empwelfare/ProvidentFund/ProvidentFundProcess", "pageName": "公积金办理", "author": "侯成"}, {"path": "/empwelfare/socialmanage/socialStop", "component": "./empwelfare/Socialmanage/SocialStop", "pageName": "社保停办", "author": "侯成"}, {"path": "/empwelfare/providentFund/fundStop", "component": "./empwelfare/ProvidentFund/ProvidentFundStop", "pageName": "公积金停办", "author": "侯成"}, {"path": "/empwelfare/socialmanage/SocialChangeSelect", "component": "./empwelfare/Socialmanage/SocialChange", "pageName": "社保变更", "author": "侯成"}, {"path": "/empwelfare/providentFund/ChangeSelect", "component": "./empwelfare/ProvidentFund/PfChange", "pageName": "公积金变更", "author": "侯成"}, {"path": "/empwelfare/ebmtransact/simpleBusiness/SimpleImpInterface", "component": "./empwelfare/Ebmtransact/SimpleBusiness/SimpleImpInterface", "pageName": "简易导入接口设置", "author": "严小强"}, {"path": "/empwelfare/ebmtransact/simpleBusiness/SimpleBusinessTransact", "component": "./empwelfare/Ebmtransact/SimpleBusiness/SimpleBusinessTransact", "pageName": "简易业务批量办理", "author": "严小强"}, {"path": "/empwelfare/ebmtransact/simpleBusiness/SimpleBusinessQuery", "component": "./empwelfare/Ebmtransact/SimpleBusiness/SimpleBusinessQuery", "pageName": "简易业务查询", "author": "严小强"}, {"path": "/empwelfare/ebmtransact/EbmTransactQuery", "component": "./empwelfare/Ebmtransact/EbmTransactQuery", "pageName": "员工业务查询", "author": "赵煜颢"}, {"path": "/empwelfare/ebmtransact/HospitalTransact", "component": "./empwelfare/Ebmtransact/HospitalTransact", "pageName": "定点医疗机构变更", "author": "赵煜颢"}, {"path": "/empwelfare/ebmtransact/QueryEmpBizBooking", "component": "./empwelfare/Ebmtransact/QueryEmpBizBooking", "pageName": "员工预约业务办理", "author": "刘夏梅"}, {"path": "/empwelfare/ebmtransact/SocialTransact", "component": "./empwelfare/Ebmtransact/SocialTransact", "pageName": "社保业务办理", "author": "刘夏梅"}, {"path": "/empwelfare/ebmtransact/PorvidentTransact", "component": "./empwelfare/Ebmtransact/PorvidentTransact", "pageName": "公积金业务办理", "author": "刘夏梅"}, {"path": "/empwelfare/ebmtransact/OnTransact", "component": "./empwelfare/Ebmtransact/OnTransact", "pageName": "在职业务办理", "author": "刘夏梅"}, {"path": "/empwelfare/socialmanage/QuerySIBackPayImp", "component": "./empwelfare/Socialmanage/QuerySIBackPayImp", "pageName": "社保管理->社保补缴导入", "author": "陈国祥"}, {"path": "/empwelfare/socialmanage/AdditionalSiQuery", "component": "./empwelfare/Socialmanage/AdditionalSiQuery", "pageName": "社保管理->社保补缴滞纳金导入", "author": "陈国祥"}, {"path": "/empwelfare/providentFund/QuerySIBackPayImp", "component": "./empwelfare/ProvidentFund/QuerySIBackPayImp", "pageName": "公积金补缴导入", "author": "陈国祥"}, {"path": "/empwelfare/providentFund/AdditionalSiQuery", "component": "./empwelfare/ProvidentFund/AdditionalSiQuery", "pageName": "公积金补缴滞纳金导入", "author": "陈国祥"}, {"path": "/empwelfare/socialmanage/QuerySsAndFundPayDetail", "component": "./empwelfare/Socialmanage/QuerySsAndFundPayDetail", "pageName": "社保/公积金支付明细查询", "author": "侯成"}, {"path": "/empwelfare/socialmanage/socialBudget", "component": "./empwelfare/Socialmanage/BudgetQuery", "pageName": "社保/公积金支付明细查询", "author": "侯成"}, {"path": "/empwelfare/filemanagement/QueryFileManagement", "component": "./empwelfare/filemanagement/QueryFileManagement", "pageName": "档案管理", "author": "沈彦霖"}, {"path": "/empwelfare/filemanagement/FilePay", "component": "./empwelfare/FilePay", "pageName": "档案支付", "author": "沈彦霖"}, {"path": "/externalSupplier/receive/QueryExBill", "component": "./externalSupplier/Receive/QueryExBill/index", "pageName": "应收查询", "author": "陈国祥"}, {"path": "/externalSupplier/receive/CreateExBillPrc", "component": "./externalSupplier/Receive/CreateExBillPrc/index", "pageName": "生成账单", "author": "陈国祥"}, {"path": "/externalSupplier/receive/ExOnecharges", "component": "./externalSupplier/Receive/ExOnecharges/index", "pageName": "客户一次性项目合并", "author": "陈国祥"}, {"path": "/externalSupplier/report/ExSupPayByDeptReport", "component": "./externalSupplier/report/ExSupPayByDeptReport", "pageName": "公司汇总表", "author": "陈国祥"}, {"path": "/externalSupplier/report/ExSupPayByPrvdReport", "component": "./externalSupplier/report/ExSupPayByPrvdReport", "pageName": "供应商汇总表", "author": "陈国祥"}, {"path": "/externalSupplier/emporder/QueryExEmpOrderListForPer", "component": "./externalSupplier/emporder/QueryExEmpOrderListForPer/index", "pageName": "完善个人订单", "author": "严小强"}, {"path": "/externalSupplier/emporder/QueryExEmpOrderListForEdit", "component": "./externalSupplier/emporder/QueryExEmpOrderListForEdit/index", "pageName": "变更个人订单", "author": "严小强"}, {"path": "/externalSupplier/emporder/QueryExEmployeeOrder", "component": "./externalSupplier/emporder/QueryExEmployeeOrder/index", "pageName": "个人订单查询", "author": "严小强"}, {"path": "/externalSupplier/emporder/QueryChangeFeeTemplate", "component": "./externalSupplier/emporder/QueryChangeFeeTemplate/index", "pageName": "变更收费模板", "author": "严小强"}, {"path": "/externalSupplier/emporder/QueryExEmpProOrderListForPer", "component": "./externalSupplier/emporder/QueryExEmpProOrderListForPer/index", "pageName": "接单方离职确认", "author": "赵煜颢"}, {"path": "/externalSupplier/emporder/QueryImpOrderProEx", "component": "./externalSupplier/emporder/QueryImpOrderProEx/index", "pageName": "批量导入个人订单产品", "author": "赵煜颢"}, {"path": "/externalSupplier/suppliermanage/QueryPrvdAssignCS", "component": "./externalSupplier/suppliermanage/QueryPrvdAssignCS/index", "pageName": "分配供应商客服", "author": "沈彦霖"}, {"path": "/externalSupplier/suppliermanage/Provider", "component": "./externalSupplier/suppliermanage/Provider/index", "pageName": "外部供应商维护", "author": "沈彦霖"}, {"path": "/externalSupplier/suppliermanage/QueryProviderGroup", "component": "./externalSupplier/suppliermanage/QueryProviderGroup/index", "pageName": "供应商集团设置", "author": "沈彦霖"}, {"path": "/externalSupplier/payApprove/prvdPayApprove", "component": "./externalSupplier/payApprove/prvdPayApprove/index", "pageName": "支付申请", "author": "朱江华"}, {"path": "/externalSupplier/payApprove/prvdPayApply", "component": "./externalSupplier/payApprove/prvdPayApply/index", "pageName": "档案外部供应商支付", "author": "朱江华"}, {"path": "/externalSupplier/payApprove/filePrvdPayApply", "component": "./externalSupplier/payApprove/filePrvdPayApply/index", "pageName": "残疾保障金外部供应商支付", "author": "朱江华"}, {"path": "/externalSupplier/receive/ExBillLockAndUnLock", "component": "./externalSupplier/Receive/ExBillLockAndUnLock/index", "pageName": "账单锁定解锁删除", "author": "孙尚阳"}, {"path": "/externalSupplier/receive/ExBillPrintReport", "component": "./externalSupplier/Receive/ExBillPrintReport/index", "pageName": "账单打印", "author": "孙尚阳"}, {"path": "/externalSupplier/payApprove/prvdPayQuery", "component": "./externalSupplier/payApprove/prvdPayQuery/index", "pageName": "档案外部供应商支付查询", "author": "朱江华"}, {"path": "/externalSupplier/payApprove/prvdPaySummaryQuery", "component": "./externalSupplier/payApprove/prvdPaySummaryQuery/index", "pageName": "档案外部供应商支付汇总查询", "author": "朱江华"}, {"path": "/externalSupplier/payApprove/filePrvdPayQuery", "component": "./externalSupplier/payApprove/filePrvdPayQuery/index", "pageName": "残疾保障金外部供应商支付查询", "author": "朱江华"}, {"path": "/externalSupplier/payApprove/filePrvdPaySummaryQuery", "component": "./externalSupplier/payApprove/filePrvdPaySummaryQuery/index", "pageName": "残疾保障金外部供应商支付汇总查询", "author": "朱江华"}, {"path": "/externalSupplier/payApprove/QueryDisabilityImp", "component": "./externalSupplier/payApprove/QueryDisabilityImp/index", "pageName": "残疾保障金数据维护", "author": "朱江华"}, {"path": "/externalSupplier/payApprove/QueryPrvdTotalPayment", "component": "./externalSupplier/payApprove/QueryPayment/QueryPrvdTotalPayment", "pageName": "支付汇总查询", "author": "沈彦霖"}, {"path": "/externalSupplier/payApprove/QueryPrvdPayment", "component": "./externalSupplier/payApprove/QueryPayment/QueryPrvdPayment", "pageName": "支付查询", "author": "沈彦霖"}, {"path": "/externalSupplier/payApprove/QueryPrvdPaymentDetail", "component": "./externalSupplier/payApprove/QueryPrvdPaymentDetail", "pageName": "支付明细查询", "author": "沈彦霖"}, {"path": "/finance/receive/QueryBill", "component": "./finance/Receive/QueryBill/index", "pageName": "应收查询", "author": "陈国祥"}, {"path": "/finance/receive/CreateBill", "component": "./finance/Receive/CreateBill/index", "pageName": "生成账单", "author": "陈国祥"}, {"path": "/finance/receive/Onecharges", "component": "./finance/Receive/Onecharges/index", "pageName": "客户一次性项目合并", "author": "陈国祥"}, {"path": "/finance/receive/BillLockAndUnlock", "component": "./finance/Receive/BillLockAndUnlock/index", "pageName": "账单锁定解锁删除", "author": "孙尚阳"}, {"path": "/finance/receive/BillPrintReport", "component": "./finance/Receive/BillPrintReport/index", "pageName": "账单打印", "author": "孙尚阳"}, {"path": "/finance/receive/QueryBillUnlockInfo", "component": "./finance/Receive/QueryBillUnlockInfo/index", "pageName": "账单解锁查询", "author": "孙尚阳"}, {"path": "/finance/receive/SpecialBillPrintReport", "component": "./finance/Receive/SpecialBillPrintReport/index", "pageName": "个性化账单打印", "author": "孙尚阳"}, {"path": "/finance/receive/BillGroupManage", "component": "./finance/Receive/BillGroupManage/index", "pageName": "账单分组维护", "author": "孙尚阳"}, {"path": "/finance/receive/EmpBillQuery", "component": "./finance/Receive/EmpBillQuery/index", "pageName": "员工应收账单查询", "author": "孙尚阳"}, {"path": "/finance/receive/OrderBill", "component": "./finance/Receive/OrderBill/index", "pageName": "预约生成账单", "author": "孙尚阳"}, {"path": "/finance/receive/OrderBillLog", "component": "./finance/Receive/OrderBillLog/index", "pageName": "预约生成账单日志", "author": "孙尚阳"}, {"path": "/finance/receive/QueryBillPc", "component": "./finance/Receive/QueryBillPc/index", "pageName": "账单政策对比表", "author": "陈刚强"}, {"path": "/finance/gathering/UploadCash", "component": "./finance/Gathering/UploadCash/index", "pageName": "上传网银到款", "author": "朱江华"}, {"path": "/finance/gathering/InvalidReceipt", "component": "./finance/Gathering/InvalidReceipt", "pageName": "到款核销作废", "author": "沈彦霖"}, {"path": "/finance/gathering/InvalidInvoice", "component": "./finance/Gathering/Invoice/InvalidInvoice/index", "pageName": "实收开票作废", "author": "沈彦霖"}, {"path": "/finance/gathering/ReceiptsQuery", "component": "./finance/Gathering/ReceiptsQuery", "pageName": "应收核销查询", "author": "沈彦霖"}, {"path": "/finance/gathering/ArrivalGathering", "component": "./finance/Gathering/ArrivalGathering", "pageName": "到款核销查询", "author": "沈彦霖"}, {"path": "/finance/gathering/QueryInvoice", "component": "./finance/Gathering/Invoice/QueryInvoice/index", "pageName": "实收发票查询", "author": "沈彦霖"}, {"path": "/finance/gathering/InvoiceEntry", "component": "./finance/Gathering/InvoiceEntry/index", "pageName": "实收开票录入", "author": "朱江华"}, {"path": "/finance/gathering/Receipts", "component": "./finance/Gathering/Receipts/index", "pageName": "批量核销应收", "author": "朱江华"}, {"path": "/finance/pay/OtherPay", "component": "./finance/Pay/OtherPay/index", "pageName": "其他支付", "author": "朱江华"}, {"path": "/finance/pay/Query", "component": "./finance/Pay/Query/index", "pageName": "支付查询", "author": "朱江华"}, {"path": "/finance/pay/QuerySocialAssignerProviderPay", "component": "./finance/Pay/QuerySocialAssignerProviderPay/index", "pageName": "派单支付查询", "author": "朱江华"}, {"path": "/infopublication/policy/siteQuery", "component": "./infopublication/Policy/SiteQuery/index", "pageName": "服务网点查询", "author": "王正荣"}, {"path": "/infopublication/policy/calculationSsCust", "component": "./infopublication/Policy/CalculationSsCust/index", "pageName": "社保公积金试算表（单个城市）客服", "author": "刘夏梅"}, {"path": "/infopublication/policy/calculationSsSale", "component": "./infopublication/Policy/CalculationSsSale/index", "pageName": "社保公积金试算表（单个城市）销售", "author": "刘夏梅"}, {"path": "/infopublication/policy/calculationSsCustMult", "component": "./infopublication/Policy/CalculationSsCustMult/index", "pageName": "客户", "author": "刘夏梅"}, {"path": "/infopublication/policy/calculationSsCityMult", "component": "./infopublication/Policy/CalculationSsCityMult/index", "pageName": "按城市", "author": "刘夏梅"}, {"path": "/infopublication/policy/surveySsPolicyCust", "component": "./infopublication/Policy/SurveySsPolicyCust/index", "pageName": "社保公积金政策一览（单个城市）客服", "author": "陈刚强"}, {"path": "/infopublication/policy/surveySsPolicySale", "component": "./infopublication/Policy/SurveySsPolicySale/index", "pageName": "社保公积金政策一览（单个城市）销售", "author": "陈刚强"}, {"path": "/infopublication/policy/surveySsPolicyCustMult", "component": "./infopublication/Policy/SurveySsPolicyCustMult/index", "pageName": "社保公积金政策一览（多个城市）客户", "author": "陈刚强"}, {"path": "/infopublication/policy/surveySsPolicyCityMult", "component": "./infopublication/Policy/SurveySsPolicyCityMult/index", "pageName": "社保公积金政策一览（多个城市）城市", "author": "陈刚强"}, {"path": "/payroll/agentWageIdCardNumManage", "component": "./payroll/AgentWageIdCardNumManage/index", "pageName": "纯代发人员证件号码修改", "author": "王正荣"}, {"path": "/payroll/editEmpBankCard", "component": "./payroll/EditEmpBankCard/index", "pageName": "纯代发人员银行卡信息维护", "author": "王正荣"}, {"path": "/payroll/ndhsqj/QueryCalAnnuaData", "component": "./payroll/CalAnnual/QueryCalAnnuaData", "pageName": "汇算清缴数据查询", "author": "沈彦霖"}, {"path": "/payroll/ndhsqj/CalAnnualDeclareImp", "component": "./payroll/CalAnnual/CalAnnualDeclareImp", "pageName": "申报结果导入", "author": "沈彦霖"}, {"path": "/payroll/ndhsqj/CalAnnualResultImp", "component": "./payroll/CalAnnual/CalAnnualResultImp", "pageName": "汇算结果导入", "author": "沈彦霖"}, {"path": "/payroll/ndhsqj/CalAnnualImportEmp", "component": "./payroll/CalAnnual/CalAnnualImportEmp", "pageName": "汇算清缴人员导入", "author": "沈彦霖"}, {"path": "/payroll/ndhsqj/KJYWRGL", "component": "./payroll/ndhsqj/KJYWRGL/index", "pageName": "汇算扣缴义务人管理", "author": "朱江华"}, {"path": "/payroll/ndhsqj/hsqjkhgl", "component": "./payroll/ndhsqj/hsqjkhgl/index", "pageName": "汇算扣缴义务人管理", "author": "朱江华"}, {"path": "/payroll/deduction/queryUploadDeductionDetail", "component": "./payroll/deduction/QueryUploadDeductionDetail/index", "pageName": "雇员专项抵扣月度查询", "author": "陈刚强"}, {"path": "/payroll/deduction/taxReportHugeCust", "component": "./payroll/deduction/TaxReportHugeCust/index", "pageName": "薪资档案增员报表（大户）", "author": "王正荣"}, {"path": "/payroll/deduction/taxReportProvider", "component": "./payroll/deduction/TaxReportProvider/index", "pageName": "薪资档案增员报表（供应商）", "author": "王正荣"}, {"path": "/payroll/deduction/taxReportIndependent", "component": "./payroll/deduction/TaxReportIndependent/index", "pageName": "薪资档案增员报表（单立户）", "author": "王正荣"}, {"path": "/payroll/deduction/custTaxReport", "component": "./payroll/deduction/CustTaxReport/index", "pageName": "雇员专项抵扣报表（按客户）", "author": "王正荣"}, {"path": "/payroll/deduction/taxReportSupply", "component": "./payroll/deduction/TaxReportSupply/index", "pageName": "增员补充报表", "author": "王正荣"}, {"path": "/payroll/deduction/uploadTaxDeductionLarge", "component": "./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionLarge", "pageName": "上传专项抵扣数据（大户）", "author": "陈刚强"}, {"path": "/payroll/deduction/uploadTaxDeductionSingle", "component": "./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionSingle", "pageName": "上传专项抵扣数据（单立户）", "author": "陈刚强"}, {"path": "/payroll/deduction/uploadTaxDeductionSupplier", "component": "./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionSupplier", "pageName": "上传专项抵扣数据（供应商）", "author": "陈刚强"}, {"path": "/payroll/deduction/specialDeductionReport", "component": "./payroll/deduction/SpecialDeductionReport/index", "pageName": "专项抵扣上传报表", "author": "陈刚强"}, {"path": "/payroll/queryPayRollClass", "component": "./payroll/QueryPayRollClass/index", "pageName": "薪资类别", "author": "孙尚阳"}, {"path": "/payroll/payRollClassItemManage", "component": "./payroll/PayRollClassItemManage/index", "pageName": "薪资项目维护", "author": "孙尚阳"}, {"path": "/payroll/queryPayRollClassItem", "component": "./payroll/QueryPayRollClassItem/index", "pageName": "薪资项目查询", "author": "孙尚阳"}, {"path": "/payroll/queryTaxRate", "component": "./payroll/taxrate/QueryTaxRate", "pageName": "税率表查看", "author": "陈国祥"}, {"path": "/payroll/queryWithholdAgent", "component": "./payroll/withholdAgent/QueryWithholdAgent", "pageName": "扣缴义务人维护", "author": "陈国祥"}, {"path": "/payroll/bankcardQuery", "component": "./payroll/bankcardqry/BankcardQuery", "pageName": "银行卡及小合同信息查询", "author": "陈国祥"}, {"path": "/payroll/XZFFPC/HFRYBB", "component": "./payroll/XZFFPC/HFRYBB/index", "pageName": "缓发人员报表", "author": "朱江华"}, {"path": "/payroll/XZFFPC/YBFFRYBB", "component": "./payroll/XZFFPC/YBFFRYBB/index", "pageName": "永不发放人员报表", "author": "朱江华"}, {"path": "/payroll/XZFFPCXN/XJFFPCXN", "component": "./payroll/XZFFPCXN/XJFFPCXN/index", "pageName": "新建发放批次(虚拟)", "author": "朱江华"}, {"path": "/payroll/bswjdc", "component": "./payroll/bswjdc/index", "pageName": "报税文件导出", "author": "朱江华"}, {"path": "/payroll/BatchFeedBackSendResult", "component": "./payroll/BatchFeedBackSendResult", "pageName": "网银包生成情况汇总表", "author": "沈彦霖"}, {"path": "/payroll/EmployeeInfoAgentWageManage", "component": "./payroll/EmployeeInfoAgentWageManage", "pageName": "纯代发人员信息维护", "author": "沈彦霖"}, {"path": "/payroll/QueryPayRollResult", "component": "./payroll/QueryPayRollResult", "pageName": "薪资结果查询", "author": "沈彦霖"}, {"path": "/payroll/QueryPaySendStage", "component": "./payroll/QueryPaySendStage", "pageName": "薪资发放阶段查询", "author": "沈彦霖"}, {"path": "/payroll/rptPsnSetFailureManages", "component": "./payroll/rptPsnSetFailureManages", "pageName": "自动失效薪资档案报表", "author": "沈彦霖"}, {"path": "/payroll/XZDFS/SENDMAIL", "component": "./payroll/XZDFS/SENDMAIL/index", "pageName": "发送薪资单", "author": "朱江华"}, {"path": "/payroll/distributePayrollSpecialiat", "component": "./payroll/DistributePayrollSpecialiat/index", "pageName": "分配薪资专员", "author": "王正荣"}, {"path": "/payroll/queryEosWageData", "component": "./payroll/QueryEosWageData/index", "pageName": "易OS薪资数据", "author": "王正荣"}, {"path": "/payroll/XZDFS/SENDMAILRESULT", "component": "./payroll/XZDFS/SENDMAILRESULT/index", "pageName": "发送历史查询", "author": "朱江华"}, {"path": "/payroll/XZFFPCXN/SZFFJGXN", "component": "./payroll/XZFFPCXN/SZFFJGXN/index", "pageName": "设置发放结果(虚拟)", "author": "朱江华"}, {"path": "/payroll/XZFFPCXN/XZZFCXXN", "component": "./payroll/XZFFPCXN/XZZFCXXN/index", "pageName": "薪资支付查询(虚拟)", "author": "朱江华"}, {"path": "/payroll/XZFFPCXN/HFRYBBXN", "component": "./payroll/XZFFPCXN/HFRYBBXN/index", "pageName": "缓发人员报表(虚拟)", "author": "朱江华"}, {"path": "/payroll/archives/CheckHrieDtManages", "component": "./payroll/archives/CheckHrieDtManages/index", "pageName": "核查受雇日期", "author": "孙尚阳"}, {"path": "/payroll/archives/QueryCheckHireDt", "component": "./payroll/archives/QueryCheckHireDt/index", "pageName": "核查受雇日期查询", "author": "孙尚阳"}, {"path": "/payroll/archives/CheckEmpTypeManages", "component": "./payroll/archives/CheckEmpTypeManages/index", "pageName": "核查从业类型", "author": "孙尚阳"}, {"path": "/payroll/archives/QueryEmpType", "component": "./payroll/archives/QueryEmpType/index", "pageName": "核查从业类型查询", "author": "孙尚阳"}, {"path": "/payroll/DataInterfaceManage", "component": "./payroll/DataInterfaceManage", "pageName": "数据导入", "author": "赵煜颢"}, {"path": "/payroll/PureDataInterfaceManage", "component": "./payroll/PureDataInterfaceManage", "pageName": "纯代发导入接口", "author": "赵煜颢"}, {"path": "/payroll/PayRollArchivesManage", "component": "./payroll/PayRollArchivesManage/index", "pageName": "薪资档案", "author": "严小强"}, {"path": "/payroll/CountPayManage", "component": "./payroll/CountPayManage/index", "pageName": "薪资计算", "author": "严小强"}, {"path": "/payroll/XZFFPC/XJFFPC", "component": "./payroll/XZFFPC/XJFFPC/index", "pageName": "新建发放批次", "author": "朱江华"}, {"path": "/payroll/XZFFPC/QueryPayResult", "component": "./payroll/XZFFPC/QueryPayResult/index", "pageName": "薪资支付查询", "author": "朱江华"}, {"path": "/payroll/XZFFPC/firstFeedBackSendManages", "component": "./payroll/XZFFPC/FirstFeedBackSendManages/index", "pageName": "首次反馈发放结果", "author": "陈刚强"}, {"path": "/payroll/XZFFPC/secondFeedBackSendManages", "component": "./payroll/XZFFPC/SecondFeedBackSendManages/index", "pageName": "二次发放结果反馈", "author": "王正荣"}, {"path": "/payroll/XZFFPC/updateSuccessManages", "component": "./payroll/XZFFPC/UpdateSuccessManages/index", "pageName": "发放成功记录调整", "author": "陈刚强"}, {"path": "/payroll/XZFFPC/updateBatchPayRollSpecialiat", "component": "./payroll/XZFFPC/UpdateBatchPayRollSpecialiat/index", "pageName": "发放批次变更薪资专员", "author": "王正荣"}, {"path": "/payroll/XZFFPC/SetPayResult", "component": "./payroll/XZFFPC/SetPayResult/index", "pageName": "设置发放结果", "author": "朱江华"}, {"path": "/payroll/XZFFPC/QueryWageAgainSend", "component": "./payroll/XZFFPC/QueryWageAgainSend/index", "pageName": "二次发放查询", "author": "刘夏梅"}, {"path": "/payroll/send/PayTaxBatchManage", "component": "./payroll/send/PayTaxBatchManage", "pageName": "报税批次管理", "author": "侯成"}, {"path": "/payroll/withholdAgentLimitMaintance", "component": "./payroll/WithholdAgentLimitMaintance", "pageName": "扣缴义务人限额维护", "author": "王正荣"}, {"path": "/payroll/payTaxes", "component": "./payroll/PayTaxes", "pageName": "税优惠人员信息维护（6W）", "author": "陈刚强"}, {"path": "/report/finance/finReceivableReport", "component": "./report/finance/FinReceivableReport/index", "pageName": "财务应收报表", "author": "陈刚强"}, {"path": "/report/finance/arrearInfoReport", "component": "./report/finance/ArrearInfoReport/index", "pageName": "欠款查询", "author": "陈刚强"}, {"path": "/report/finance/internalSettlementReport", "component": "./report/finance/InternalSettlementReport/index", "pageName": "内部社保、公积金结算表（应付）", "author": "王正荣"}, {"path": "/report/finance/finEfOrReport", "component": "./report/finance/FinEfOrReport/index", "pageName": "财务应收实收汇总表", "author": "王正荣"}, {"path": "/report/finance/finReceiptDetailRpt", "component": "./report/finance/FinReceiptDetailRpt/index", "pageName": "集团客户实付明细报表", "author": "王正荣"}, {"path": "/report/finance/finReceivableDetailRpt", "component": "./report/finance/FinReceivableDetailRpt/index", "pageName": "集团客户应收明细报表", "author": "王正荣"}, {"path": "/report/finance/paymentByGroupRpt", "component": "./report/finance/PaymentByGroupRpt/index", "pageName": "集团客户供应商实付明细报表", "author": "王正荣"}, {"path": "/report/finance/investorBillReport", "component": "./report/finance/InvestorBillReport/index", "pageName": "投资人账单报表", "author": "陈刚强"}, {"path": "/report/finance/investorCountReport", "component": "./report/finance/InvestorCountReport/index", "pageName": "投资人人数统计报表", "author": "陈刚强"}, {"path": "/report/finance/finReceiptInvoiceBill", "component": "./report/finance/FinReceiptInvoiceBill/index", "pageName": "账单核销开票统计汇总查询表", "author": "陈刚强"}, {"path": "/report/finance/customerDailyVersion", "component": "./report/finance/CustomerDailyVersion/index", "pageName": "每日首版账单客户", "author": "陈刚强"}, {"path": "/report/finance/servfeeReturnRptQuery", "component": "./report/finance/ServfeeReturnRptQuery/index", "pageName": "服务费退费报表", "author": "陈刚强"}, {"path": "/report/finance/noInvoiceBill", "component": "./report/finance/NoInvoiceBill/index", "pageName": "未开票报表", "author": "陈刚强"}, {"path": "/report/sales/contractApprovalStepTimeReport", "component": "./report/sales/ContractApprovalStepTimeReport/index", "pageName": "合同审批时长报表", "author": "王正荣"}, {"path": "/report/sales/customerServiceStatus", "component": "./report/sales/CustomerServiceStatus/index", "pageName": "客户服务状况", "author": "王正荣"}, {"path": "/report/customerserv/clientCustReport", "component": "./report/customerserv/ClientCustReport/index", "pageName": "客户端客户级统计报表", "author": "陈刚强"}, {"path": "/report/customerserv/clientGroupReport", "component": "./report/customerserv/ClientGroupReport/index", "pageName": "客户端集团公司级统计报表", "author": "陈刚强"}, {"path": "/report/customerserv/decreaseMemberReport", "component": "./report/customerserv/DecreaseMemberReport/index", "pageName": "减员查询", "author": "陈刚强"}, {"path": "/report/customerserv/increaseMemberReport", "component": "./report/customerserv/IncreaseMemberReport/index", "pageName": "增员查询", "author": "陈刚强"}, {"path": "/report/customerserv/laborContractRenewReport", "component": "./report/customerserv/LaborContractRenewReport/index", "pageName": "劳动合同续签率", "author": "王正荣"}, {"path": "/report/customerserv/customerInfoReport", "component": "./report/customerserv/CustomerInfoReport/index", "pageName": "全国客户情况", "author": "王正荣"}, {"path": "/report/customerserv/customerServiceEffect", "component": "./report/customerserv/CustomerServiceEffect/index", "pageName": "客服绩效考核", "author": "王正荣"}, {"path": "/report/qualitycontrol/sysBillTimelyRatio", "component": "./report/qualitycontrol/SysBillTimelyRatio/index", "pageName": "系统账单及时率实时/明细查询", "author": "王正荣"}, {"path": "/report/qualitycontrol/sysBillTimelyRatioFinal", "component": "./report/qualitycontrol/SysBillTimelyRatioFinal/index", "pageName": "系统账单最终完成率实时/明细查询", "author": "王正荣"}, {"path": "/report/qualitycontrol/systemBillCompleteRatio", "component": "./report/qualitycontrol/SystemBillCompleteRatio/index", "pageName": "系统账单完整率明细", "author": "王正荣"}, {"path": "/report/qualitycontrol/staffEntryRatio", "component": "./report/qualitycontrol/StaffEntryRatio/index", "pageName": "入职手续完成率实时/明细查询", "author": "王正荣"}, {"path": "/report/qualitycontrol/labourContractRatio", "component": "./report/qualitycontrol/LabourContractRatio/index", "pageName": "劳动合同签订完成率实时/明细查询", "author": "王正荣"}, {"path": "/report/qualitycontrol/socialSecurityCompleteRatio", "component": "./report/qualitycontrol/SocialSecurityCompleteRatio/index", "pageName": "社保/公积金完成率实时/明细查询", "author": "王正荣"}, {"path": "/report/qualitycontrol/sepRecSendorRatio", "component": "./report/qualitycontrol/SepRecSendorRatio/index", "pageName": "离职手续完成率实时/明细查询(派单地/接单地)", "author": "王正荣"}, {"path": "/report/qualitycontrol/employeesTimelyRateReport", "component": "./report/qualitycontrol/EmployeesTimelyRateReport/index", "pageName": "通知员工及时率实时/明细查询", "author": "王正荣"}, {"path": "/report/qualitycontrol/payInputInterfaceReport", "component": "./report/qualitycontrol/PayInputInterfaceReport/index", "pageName": "导入接口人数统计", "author": "陈刚强"}, {"path": "/report/qualitycontrol/customerPayReturnRatio", "component": "./report/qualitycontrol/CustomerPayReturnRatio/index", "pageName": "客户回款率实时/明细查询（按客户的数量/金额考核）FN", "author": "陈刚强"}, {"path": "/report/qualitycontrol/customerPayReturnRatioc", "component": "./report/qualitycontrol/CustomerPayReturnRatioc/index", "pageName": "客户回款率实时/明细查询（按客户的数量/金额考核）CS", "author": "陈刚强"}, {"path": "/report/qualitycontrol/payrollRoleAuditReport", "component": "./report/qualitycontrol/PayrollRoleAuditReport/index", "pageName": "支付质控点（审批节点）", "author": "陈刚强"}, {"path": "/report/qualitycontrol/payrollApplyRatioReport", "component": "./report/qualitycontrol/PayrollApplyRatioReport/index", "pageName": "支付质控点（整体）", "author": "陈刚强"}, {"path": "/report/qualitycontrol/orderRecSenderRatio", "component": "./report/qualitycontrol/OrderRecSenderRatio/index", "pageName": "订单完成率实时/明细查询(派单地/接单地)", "author": "陈刚强"}, {"path": "/report/qualitycontrol/billUnlockRatio", "component": "./report/qualitycontrol/BillUnlockRatio/index", "pageName": "账单解锁率实时/明细查询", "author": "陈刚强"}, {"path": "/report/qualitycontrol/checkPersonalOrderReport", "component": "./report/qualitycontrol/CheckPersonalOrderReport/index", "pageName": "核对个人订单及时率实时/明细查询", "author": "陈刚强"}, {"path": "/report/qualitycontrol/perfectingPersonalOrderReport", "component": "./report/qualitycontrol/PerfectingPersonalOrderReport/index", "pageName": "完善个人订单及时率实时/明细查询", "author": "陈刚强"}, {"path": "/report/qualitycontrol/personIncrementDetail", "component": "./report/qualitycontrol/PersonIncrementDetail/index", "pageName": "增员明细查询", "author": "王正荣"}, {"path": "/report/qualitycontrol/personDecrementDetail", "component": "./report/qualitycontrol/PersonDecrementDetail/index", "pageName": "减员明细查询", "author": "王正荣"}, {"path": "/report/qualitycontrol/increaseAndDecreaseStaffReport", "component": "./report/qualitycontrol/IncreaseAndDecreaseStaffReport/index", "pageName": "增减员统计表", "author": "王正荣"}, {"path": "/report/qualitycontrol/contractDeclarationReport", "component": "./report/qualitycontrol/ContractDeclarationReport/index", "pageName": "大合同申报转移数据报表", "author": "王正荣"}, {"path": "/report/qualitycontrol/contractPriceCompare", "component": "./report/qualitycontrol/ContractPriceCompare/index", "pageName": "实际执行价格与签约价格对比表", "author": "王正荣"}, {"path": "/report/qualitycontrol/evacuateOrder", "component": "./report/qualitycontrol/EvacuateOrder/index", "pageName": "撤单", "author": "王正荣"}, {"path": "/report/qualitycontrol/evacuateWarningQuery", "component": "./report/qualitycontrol/EvacuateWarningQuery/index", "pageName": "撤单预警", "author": "王正荣"}, {"path": "/report/qualitycontrol/collectionPaymentReport", "component": "./report/qualitycontrol/CollectionPaymentReport/index", "pageName": "代收代付", "author": "王正荣"}, {"path": "/report/qualitycontrol/complainAppSelect", "component": "./report/qualitycontrol/ComplainAppSelect/index", "pageName": "投诉", "author": "王正荣"}, {"path": "/report/qualitycontrol/clientEmpVisitedReport", "component": "./report/qualitycontrol/ClientEmpVisitedReport/index", "pageName": "客户端员工账号使用率", "author": "王正荣"}, {"path": "/report/qualitycontrol/clientCustVisitedReport", "component": "./report/qualitycontrol/ClientCustVisitedReport/index", "pageName": "客户端客户账号使用率", "author": "王正荣"}, {"path": "/report/qualitycontrol/billDifferenceReport", "component": "./report/qualitycontrol/BillDifferenceReport/index", "pageName": "账单对比表", "author": "王正荣"}, {"path": "/report/qualitycontrol/realityBillDifference", "component": "./report/qualitycontrol/RealityBillDifference/index", "pageName": "实做与账单差异", "author": "陈刚强"}, {"path": "/report/qualitycontrol/customerCollectInfoReport", "component": "./report/qualitycontrol/CustomerCollectInfoReport/index", "pageName": "客户收款情况统计", "author": "陈刚强"}, {"path": "/report/qualitycontrol/newCustTransferRatioQAReport", "component": "./report/qualitycontrol/NewCustTransferRatioQAReport/index", "pageName": "新签的客户转化率统计", "author": "陈刚强"}, {"path": "/report/qualitycontrol/customerServiceReport", "component": "./report/qualitycontrol/CustomerServiceReport/index", "pageName": "客户服务报表统计", "author": "陈刚强"}, {"path": "/report/opermanagement/operatingManagement", "component": "./report/opermanagement/OperatingManagement/index", "pageName": "经营管理报表", "author": "王正荣"}, {"path": "/report/opermanagement/operatingManageDetail", "component": "./report/opermanagement/OperatingManageDetail/index", "pageName": "服务情况统计表", "author": "王正荣"}, {"path": "/report/monitor/asigneeSocialSecDifference", "component": "./report/monitor/AsigneeSocialSecDifference/index", "pageName": "接单地与社保地差异查询", "author": "王正荣"}, {"path": "/report/monitor/orderRealityDifference", "component": "./report/monitor/OrderRealityDifference/index", "pageName": "订单与实做的差异查询", "author": "王正荣"}, {"path": "/report/flashreport/flashreport", "component": "./report/flashreport/Flashreport/index", "pageName": "REPORT", "author": "王正荣"}, {"path": "/sales/cust/delegateform", "component": "./Sales/CustomerManage/DelegateCustomerForm/index", "pageName": "纯代发客户备案表", "author": "王正荣"}, {"path": "/sales/cust/payrollSendingList", "component": "./Sales/CustomerManage/PayrollSendingList/index", "pageName": "工资单发送列表", "author": "王正荣"}, {"path": "/sales/cust/noNeedCalculateForm", "component": "./Sales/CustomerManage/NoNeedCalculateForm/index", "pageName": "不需核算客户备案表", "author": "王正荣"}, {"path": "/sales/cust/infoquery", "component": "./Sales/CustomerManage/CustomerQuery/index", "pageName": "客户信息查询(客服)", "author": "朱江华"}, {"path": "/sales/cust/group-company", "component": "./Sales/CustomerManage/GroupCompanyManager/index", "pageName": "集团公司维护", "author": "刘双"}, {"path": "/sales/cust-crm/cccust", "component": "./crm/cust/CCCust/index", "pageName": "陌拜客户", "author": "刘双"}, {"path": "/sales/cust-crm/CustReport", "component": "./crm/cust/CustReport/index", "pageName": "客户信息查询(销售)", "author": "朱江华"}, {"path": "/sales/product/productManagement", "component": "./Sales/Product/ProductManagement/index", "pageName": "产品维护(销售)", "author": "陈国祥"}, {"path": "/sales/product/productType", "component": "./Sales/Product/ProductType/index", "pageName": "产品类型维护(销售)", "author": "陈国祥"}, {"path": "/sales/product/SubProduct", "component": "./Sales/Product/SubProduct/index", "pageName": "产品子类型(销售)", "author": "陈国祥"}, {"path": "/sales/product/InsuranceType", "component": "./Sales/Product/InsuranceType/index", "pageName": "险种维护(销售)", "author": "陈国祥"}, {"path": "/sales/product/InsuranceProvider", "component": "./Sales/Product/InsuranceProvider/index", "pageName": "供应商维护(销售)", "author": "陈国祥"}, {"path": "/sales/cust/CustomerPayerAssert", "component": "./Sales/CustomerManage/CustomerPayerAssert/index", "pageName": "客户付款方维护", "author": "朱江华"}, {"path": "/sales/cust/CustomerPayerQuery", "component": "./Sales/CustomerManage/CustomerPayerQuery/index", "pageName": "客户付款方查询", "author": "朱江华"}, {"path": "/sales/cust-crm/pre-cust", "component": "./crm/cust/QueryPreRegCust/index", "pageName": "正式客户预录入", "author": "刘双"}, {"path": "/sales/cust-crm/formal-cust", "component": "./crm/cust/QueryFormalCust/index", "pageName": "正在跟进客户查看", "author": "刘双"}, {"path": "/sales/cust/salaryRelatedAssert", "component": "./Sales/CustomerManage/SalaryRelatedAssert/index", "pageName": "薪资相关约定维护", "author": "赵煜颢"}, {"path": "/sales/cust/salaryRelatedQuery", "component": "./Sales/CustomerManage/SalaryRelatedQuery/index", "pageName": "薪资相关约定查询", "author": "赵煜颢"}, {"path": "/sales/cust-crm/viewShareArea", "component": "./crm/cust/viewShareArea/index", "pageName": "共享区(六大区)", "author": "朱江华"}, {"path": "/sales/cust-crm/allotShareArea", "component": "./crm/cust/allotShareArea/index", "pageName": "共享区跨区分配", "author": "朱江华"}, {"path": "/sales/quotation/quotationTempManage", "component": "./Sales/Quotation/QuotationTempManage/index", "pageName": "报价单模板维护", "author": "赵煜颢"}, {"path": "/sales/contract/query", "component": "./Sales/Contract/Query", "pageName": "合同查询（客服）", "author": "侯成"}, {"path": "/sales/contract/manage", "component": "./Sales/Contract/Manage", "pageName": "合同管理（销售）", "author": "侯成"}, {"path": "/sales/quotation/quotationManage", "component": "./Sales/Quotation/QuotationManage/index", "pageName": "报价单管理(销售)", "author": "赵煜颢"}, {"path": "/sales/quotation/quotationQuery", "component": "./Sales/Quotation/QuotationQuery/index", "pageName": "报价单查询(客服)", "author": "赵煜颢"}, {"path": "/sales/cust-crm/viewDelArea", "component": "./crm/cust/viewDelArea/index", "pageName": "删除区(六大区)", "author": "朱江华"}, {"path": "/sales/cust-crm/allotDelArea", "component": "./crm/cust/allotDelArea/index", "pageName": "删除区跨区分配", "author": "朱江华"}, {"path": "/sales/cust-crm/viewShareAreaAll", "component": "./crm/cust/viewShareAreaAll/index", "pageName": "共享区（全国）", "author": "朱江华"}, {"path": "/sales/cust-crm/viewShareAreaLocal", "component": "./crm/cust/viewShareAreaLocal/index", "pageName": "共享区（本地）", "author": "朱江华"}, {"path": "/sales/cust-crm/viewShareAreaYyzx", "component": "./crm/cust/viewShareAreaYyzx/index", "pageName": "共享区（运营中心）", "author": "朱江华"}, {"path": "/sales/cust-crm/viewDelAreaLocal", "component": "./crm/cust/viewDelAreaLocal/index", "pageName": "删除区（本地）", "author": "朱江华"}, {"path": "/sales/cust-crm/viewDelAreaAll", "component": "./crm/cust/viewDelAreaAll/index", "pageName": "删除区（全国）", "author": "朱江华"}, {"path": "/sales/cust-crm/viewDelAreaYyzx", "component": "./crm/cust/viewDelAreaYyzx/index", "pageName": "删除区（运营中心）", "author": "朱江华"}, {"path": "/sales/cust-crm/queryCrmBusinessLog", "component": "./crm/cust/QueryCrmBusinessLog/index", "pageName": "客户状态变更流水", "author": "陈刚强"}, {"path": "/sales/cust-crm/queryCustDuplicate", "component": "./crm/cust/QueryCustDuplicate/index", "pageName": "客户查重", "author": "陈刚强"}, {"path": "/sales/cust-crm/customerSimilarity", "component": "./crm/cust/CustomerSimilarity/index", "pageName": "客户查重报表", "author": "陈刚强"}, {"path": "/sales/cust-crm/salesRelatedDoc", "component": "./crm/cust/SalesRelatedDoc/index", "pageName": "销售相关文档", "author": "王正荣"}, {"path": "/sales/contract/modelContractQuery", "component": "./Sales/Contract/ModelContractQuery", "pageName": "范本合同管理", "author": "陈刚强"}, {"path": "/sales/contract/queryContractVersion", "component": "./Sales/Contract/QueryContractVersion", "pageName": "合同版本号维护", "author": "陈刚强"}, {"path": "/sales/cust-crm/customerService", "component": "./crm/cust/QueryCustomerService", "pageName": "客户服务情况（实时）", "author": "刘夏梅"}, {"path": "/sales/cust-crm/customerServiceMonth", "component": "./crm/cust/QueryCustomerServiceMonth", "pageName": "客户服务情况（月度）", "author": "刘夏梅"}, {"path": "/sales/cust-crm/debtreminder", "component": "./crm/cust/QueryDebtReminder", "pageName": "客户欠款提醒", "author": "刘夏梅"}, {"path": "/sales/cust-crm/queryMarketActivity", "component": "./crm/cust/QueryMarketActivity", "pageName": "市场活动", "author": "陈刚强"}, {"path": "/sales/cust-crm/queryPreRegMarket", "component": "./crm/cust/QueryPreRegMarket", "pageName": "市场活动客户预录入", "author": "陈刚强"}, {"path": "/sales/informationShare/queryMarketActivitySign", "component": "./crm/informationShare/QueryMarketActivitySign", "pageName": "市场活动签到", "author": "陈刚强"}, {"path": "/sales/informationShare/queryClassActivitySign", "component": "./crm/informationShare/QueryClassActivitySign", "pageName": "课程活动报名", "author": "陈刚强"}, {"path": "/sales/cust/QueryClientCompany", "component": "./Sales/CustomerManage/QueryClientCompany/index", "pageName": "客户端开放列表", "author": "刘夏梅"}, {"path": "/sysmanage/accounts", "component": "./sysmanage/Account/index", "pageName": "账号管理", "author": "侯成"}, {"path": "/sysmanage/functions", "component": "./sysmanage/Sysfunction/index", "pageName": "系统功能管理", "author": "侯成"}, {"path": "/sysmanage/role", "component": "./sysmanage/Role/index", "pageName": "角色管理", "author": "刘双"}, {"path": "/sysmanage/modifypassword", "component": "./sysmanage/ModifyPassword/index", "pageName": "密码修改", "author": "侯成"}, {"path": "/sysmanage/allocateservice", "component": "./sysmanage/AllocateService/index", "pageName": "分配客服", "author": "侯成"}, {"path": "/sysmanage/department", "component": "./sysmanage/Department/index", "pageName": "内部组织架构", "author": "侯成"}, {"path": "/sysmanage/queryMimicInfo", "component": "./sysmanage/QueryMimicInfo/index", "pageName": "模拟人列表维护", "author": "陈刚强"}, {"path": "/sysmanage/newestUpdateSelect", "component": "./sysmanage/NewestUpdateSelect/index", "pageName": "最新更新维护", "author": "陈刚强"}, {"path": "/sysmanage/datePublishSelect", "component": "./sysmanage/DatePublishSelect/index", "pageName": "信息发布", "author": "刘夏梅"}, {"path": "/sysmanage/ssReportConfiguration", "component": "./sysmanage/SsReportConfiguration/index", "pageName": "社保报表配置", "author": "王正荣"}, {"path": "/sysmanage/support", "component": "./sysmanage/Support/index", "pageName": "在线支持", "author": "孙尚阳"}, {"path": "/sysmanage/onlineTask", "component": "./sysmanage/OnlineTask/index", "pageName": "在线任务", "author": "孙尚阳"}, {"path": "/sysmanage/workFlowManage", "component": "./sysmanage/WorkFlowManage/index", "pageName": "流程定义", "author": "孙尚阳"}, {"path": "/test/wageformula", "component": "./payroll/WageFormulaExam", "pageName": "wageformula", "author": "test"}]