import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/allocateCustomer/queryCbmList
     * @desc 查询客服权限列表
查询客服权限列表
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.EmployeeHireSep();
export const url = '/rhro-service-1.0/allocateCustomer/queryCbmList:POST';
export const initialUrl = '/rhro-service-1.0/allocateCustomer/queryCbmList';
export const cacheKey = '_allocateCustomer_queryCbmList_POST';
export async function request(
  data: defs.admin.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/allocateCustomer/queryCbmList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/allocateCustomer/queryCbmList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
