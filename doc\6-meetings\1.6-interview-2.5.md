<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-06-01 16:44:57
 * @LastAuthor: 侯成
 * @LastTime: 2020-07-14 15:27:23
 * @message: message
-->

## 泛型使用

有代码如下：

```ts
type Partial<T> = {
  [P in keyof T]?: T[P];
};

type Record<K extends keyof any, T> = {
  [P in K]: T;
};

interface IUser {
  userId: number;
  userName: string;
  address?: string;
}

const params: Partial<IUser> = {};
const userFilters: Record<keyof IUser, string[]> = {};
const filters: Partial<Record<keyof IUser, string[]>> = {};
```

请问 `params` 是个啥类型。（易）

请问 `userFilters` 是个啥类型。（中）

请问 `filters` 是个啥类型。（难）

## 文档阅读

以下说的啥？

The Difference Between Interfaces and Type Aliases.

As we mentioned, type aliases can act sort of like interfaces; however, there are some subtle differences. One difference is that interfaces create a new name that is used everywhere. Type aliases don’t create a new name — for instance, error messages won’t use the alias name. In the code below, hovering over interfaced in an editor will show that it returns an Interface, but will show that aliased returns object literal type.

```ts
type Alias = { num: number };
interface Interface {
  num: number;
}
declare function aliased(arg: Alias): Alias;
declare function interfaced(arg: Interface): Interface;
```

In older versions of TypeScript, type aliases couldn’t be extended or implemented from (nor could they extend/implement other types). As of version 2.7, type aliases can be extended by creating a new intersection type e.g. `type Cat = Animal & { purrs: true }`. Because an ideal property of software is being open to extension, you should always use an interface over a type alias if possible. On the other hand, if you can’t express some shape with an interface and you need to use a union or tuple type, type aliases are usually the way to go.
