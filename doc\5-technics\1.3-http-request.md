<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-09-18 10:06:30
 * @LastAuthor   : 侯成
 * @LastTime     : 2019-12-20 15:52:06
 * @message: 
 -->
# HTTP请求

## 关于各种HTTP协议

 协议      |     可用编码          | 长度限制 | 多次操作 |安全性 
 -------- | ----------- | ------------- | ------------- | ------------- | -------------
 GET      | `application/x-www-form-urlencoded` | 2048 Chars | 无害 | 数据明文发送，无安全性。<br> 挂载于URL上，所有人可见。
 POST      | `application/x-www-form-urlencoded` <br> `multipart/form-data` <br> `application/json` | 无限制 | 重新提交 | 数据明文发送，无安全性。<br>抓包可见。
 PUT      | 同上 | 同上 |  无害  | 同上
 DELETE      | 同上 | 同上 |  无害  | 同上

## GET与POST区别

GET与POST是两种常用请求的方式，这两种协议最明显的区别就是：**没区别**。

它们只是 `HTTP` 协议中两种请求方式，而 `HTTP` 协议是基于 `TCP/IP` 的应用层协议，无论 GET 还是 POST，用的都是同一个传输层协议，所以在传输上，没有区别。

若现有数据：
```json
{
  "name": "你的名字",
  "age": 23,
  "address": "ShangHai"
}
```

<br>
1. 编码为 `application/x-www-form-urlencoded` 时：

* 在`method`为`GET`时，这是`RequestMapping`中`consumes`的默认值。


GET 方法简约版报文如下：
```sh
GET /index.do?name=你的名字&age=23&address=ShangHai HTTP/1.1
Host: localhost
```

POST 方法简约版报文如下：
```sh
POST /index.do HTTP/1.1
Host: localhost
Content-Type: application/x-www-form-urlencoded

name=你的名字&age=23&address=ShangHai
```

`application/x-www-form-urlencoded`编码，是最常用的表单提交方式。请求数据被组合成 queryPramas的形式，即以 `&` 符号相隔的字符串。

在`jQuery` 时代， `$("form").serialize()` 调用之后，数据即被序列化为这种形式。

在POST中使用这种编码：
```java
@ApiOperation(value = "修改密码", notes = "修改密码")
@PostMapping(value = "/urls", consumes = "application/x-www-form-urlencoded")
public CommonResponse modifyPassword(@RequestParam(value = "userId", required = true) Long userId,
                                     @RequestParam(value = "password", required = true) String password,
                                     @RequestParam(value = "oldPassword", required = true) String oldPassword) {
                                     }
```
<br>
<br>
2. `multipart/form-data` 编码。

此编码类型为POST独有，指定传输数据为二进制类型，比如图片、mp3、文件。常用于文件传输。
<br>
<br>
3. `application/json` 编码。

* 在`method`为`POST`时，这是`RequestMapping`中`consumes`的默认值。


此编码类型，用以告知服务端，消息主体是序列化后的JSON字符串。即`JSON.stringify`后的形式。

相比于`form-urlencoded`的形式下，键值对以 `&` 分隔，这种编码格式可以支持的数据格式要丰富得多，可是提交任意复杂的嵌套结构。

这种发送数据的编码方式，即为后端同志们常说的 `body`。

上述请求数据将序列化为：
```json
{"name":"你的名字","age":23,"address":"ShangHai","sub":[1,2,3]}
```

需要说明的是，在 `HTTP`标准中，并未禁止`GET` 请求携带 `body` 数据。

但在实际工程中，大多数前端工具不支持在 `GET` 请求设置 `body` 数据。

同时，服务器端的`nginx` 代理转发请求时，也会丢弃 `GET` 请求中的 `body` 数据。
