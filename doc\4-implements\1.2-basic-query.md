<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-29 09:38:30
 * @lastTime: 2019-08-28 14:36:35
 * @LastAuthor: 侯成
 * @message:
 -->

# 基本查询

关于 表单+表格 的基本查询页面的实现。以 `src/pages/sysmanage/Account/index.tsx` 为例。

## 外部引用

导入自外部的类型，方法，组件

```tsx
// src\pages\emphiresep\SubcontractManage\index.tsx
import { Dispatch } from '@/models/connect';
import { PaginationConfig } from 'antd/es/pagination';
import { SorterResult } from 'antd/es/table';
import { FormComponentProps } from 'antd/es/form';
import { TablePage, pageRequest } from '@/utils/methods/pagenation';
import { mapToFilter, filterToQuery, mapToRender } from '@/utils/methods/tables';

import ModiyPwdForm from './ModifyPwd';
```

来自于`Antd` 的类型定义：`FormComponentProps, Dispatch, PaginationConfig`。

来自于自定义的类型定义：`TablePage`。

自定义的公共方法：`pageRequest`

### 基础类型定义

```ts
// src/utils/pagenation.ts
export interface TablePage<T> {
  list: Array<T>;
  pagination: PaginationConfig;
}
```

`TablePage` 是分页配置类型，是`StandardTable` 组件的`data`属性所需参数的类型，这里进行了简单的封装，使用时传递当前正在使用的类型。例：

```ts
// src\pages\emphiresep\SubcontractManage\index.tsx
interface SubcontractManageProps extends FormComponentProps {
  subcontracts: TablePage<TSubcontractDTO>;
  subcontractColumn: TSubcontractDTO;
}
// 或直接使用
interface AccountProps extends FormComponentProps {
  accounts: TablePage<AccountTable>;
  // ... ...
}
```

关于`TablePage`的具体使用。

## 表单实现

### 基础类型定义

```tsx
// src\pages\emphiresep\SubcontractManage\index.tsx
type TSubcontractQuery = defs.sale.SubcontractQuery;
type TSubcontractDTO = defs.sale.SubcontractDTO;

interface SubcontractManageProps extends FormComponentProps {
  subcontracts: TablePage<TSubcontractDTO>;
  subcontractColumn: TSubcontractDTO;
  loading: boolean;
  dispatch?: Dispatch;
}

interface BtnForbidden {
  formValues: TSubcontractQuery;
  selectedRows: TSubcontractDTO[];
}

interface AccountStates {
  currentAccount: AccountTable;
  formValues: {};
  selectedRows: AccountTable[];
  btnForbidden: BtnForbidden;
  onModiyPwdForm: boolean;
}
// ... ...
class SubcontractManageF extends React.Component<SubcontractManageProps, SubcontractManageStates> {
  state: SubcontractManageStates = {
    formValues: {
      assignmentType: assignmentTypeCode.unclear,
    },
    selectedRows: [],
    onCustomerPop: false,
    onSubcontractAdd: false,
    onSubcontractModify: false,
    onSubcontractView: false,
  };
  // ... ...
}
```

`SubcontractManageProps` 是组件`SubcontractManage`需要接收的`props`的类型声明，用于标明有哪些属性将通过外部传入，类似于函数的参数定义，更准确的理解是面向对象中，类的实例化参数定义。

`SubcontractManageStates` 是组件`SubcontractManage`内部`state`的类型定义。组件内部`state`，是组件内的公共对象，调用`steState`对其进行更新，将会引发所在`react`组件自动更新。

`defs.sale.SubcontractQuery` 是提交给后端时，所需要提供的参数的类型定义。其定义由前后端约定，通常包含两部分，一部分为查询条件，本例中为`areaType, contractId, custId, roleGrade`，一部分为分页配置参数，本例中为`pageNum, pageSize, firstPage`。查询条件参数一般来自于表单输入，分页参数来自于表格中分页导航条，并需要调用公共方法`pageRequest`进行参数转换。

## 组件内部实现

### `columns`属性定义

```tsx
const columns: StandardTableColumnProps<TSubcontractDTO>[] = [
  {
    title: '客户编号',
    dataIndex: 'custId',
  },
  {
    title: '客户名称',
    dataIndex: 'custName',
  },
  {
    title: '大合同类别',
    dataIndex: 'contractType',
    render: mapToRender(contractTypeMap),
    filters: mapToFilter(contractTypeMap),
  },
];
```

`columns` 是`StandardTable`组件所需的表格列属性的定义，`render` 属性可以定义特殊的渲染规则。`columns`的定义位置，可位于组件定义外部或内部，视情况而定，若`render`属性的方法中，需要用到组件内的属性或方法，即需要通过`this` 访问的值，则`columns`应应当定义于组件内部。

### 按钮可用性方法实现

```tsx
renderForm() {
  const { selectedRows, onCustomerPop } = this.state;
  // 选中条目不为一条时
  const notOne = selectedRows.length !== 1;
  // 选中条目不为零条时
  const onZero = selectedRows.length === 0;
  return (
    <Form onSubmit={this.handleSearch} layout="inline" className={styles.popFormBase}>
      <Row>
        <Col md={24} sm={24}>
          <Button style={\{ marginLeft: 8 \}} type="primary" htmlType="submit">
            查询
          </Button>
          <Button style={\{ marginLeft: 8 \}} disabled={notOne}>
            查看
          </Button>
          <Button style={\{ marginLeft: 8 \}} onClick={this.haddleSubcontractAdd}>
            新增
          </Button>
          <Button style={\{ marginLeft: 8 \}} disabled={notOne}>
            修改
          </Button>
          <Button style={\{ marginLeft: 8 \}} disabled={onZero}>
            删除
          </Button>
        </Col>
      </Row>
    </Form>
  )
}
```

本组方法应当视情况自行改造。

### 查询方法实现

三个核心方法

```tsx
// src\pages\emphiresep\SubcontractManage\index.tsx
// 组件加载即查询，视需求而定。
componentDidMount() {
  this.querysubcontracts();
}

querysubcontracts = (
    queryInput: TSubcontractQuery | undefined,
    pagination?: PaginationConfig,
) => {
  const { dispatch } = this.props;
  const { formValues } = this.state;
  // 若传递进来的值包含数据，则覆盖state.formValues中的对应属性
  const values = { ...formValues, ...queryInput } as TSubcontractQuery;
  const params = pageRequest<TSubcontractQuery>(values, pagination);
  dispatch({
    type: 'subcontract/qSubcontracts',
    payload: params,
  });
};
handleStandardTableChange = (
  pagination: PaginationConfig,
  filtersArg: Record<keyof TSubcontractDTO, string[]>,
  sorter: SorterResult<TSubcontractDTO>,
) => {
  const filterQuery = filterToQuery(filtersArg);
  this.querysubcontracts(filterQuery, pagination);
};

handleSearch = (event: React.FormEvent<any>) => {
  const { form } = this.props;
  event.preventDefault();
  // const { querysubcontracts } = this.props;
  form.validateFields((err, fieldsValue) => {
    if (err) return;
    this.setState({ formValues: { ...fieldsValue } });
    this.querysubcontracts(fieldsValue);
  });
};
```

`querysubcontracts` 是本组中的核心方法，接收两个可选参数，`queryInput` 是表单输入数据，表单提交事件响应。 `handleSearch`调用此方法时，将会传入表单中的数据。 `handleStandardTableChange` 调用此方法时，将会传入分页配置数据`pagination`。

`handleSearch` 是的定义在表单`onSubmit`事件上的处理方法，接收一个`event`参数，表单提交时，此方法被调用。 `this.props.form` 是由`Form.create<SubcontractManageProps>()(SubcontractManageF)`传入的属性，其类型定义在`FormComponentProps`中，`form.validateFields`由`antd Form`组件提供表单校验方法。其参数是一个回调函数，回调函数接收两个参数，`err`表示校验结果，`fieldsValue`表示表单中的数据。当`err`不为空时，直接返回，为空时，表示校验通过，继续进行处理。释义：

```tsx
// querysubcontracts
// 若传递进来的值包含数据，则覆盖state.formValues中的对应属性
const values = { ...formValues, ...queryInput } as TSubcontractQuery;
```

对传入的`fieldsValue`进行进行数据合并，若传递进来的值包含数据，则覆盖 state.formValues 中的对应属性。此外本行做了类型转换。

```tsx
// handleSearch
this.setState({ formValues: { ...fieldsValue } });
```

调用`setState`方法， 设置组件里`formValues`的值，这里使用`{...values}`结构`values`，避免对原数据进行更改。

```tsx
// handleSearch
this.querysubcontracts(fieldsValue);
```

调用`queryAccounts`方法，直接将`fieldsValue`传入，表格中暂无数据，`pagination`参数为空。此处需解释，`setState`调用之后`formValues`的值在本次组件生命周期内，无法通过`this.state.formValues`获取其值，所以需要手工传入`fieldsValue`。

```tsx
// queryAccounts
const { formValues } = this.state;
const valuesInit = queryInput ? queryInput : formValues;
```

`valuesInit` 由`formValues，queryInput` 复合而成。当表单提交时， `formValues` 中为旧表单值，不予使用，直接使用传入的`queryInput`作为下一步的请求数据。

当表格分页导航翻页时：

```tsx
// handleStandardTableChange
const filterQuery = filterToQuery(filtersArg);
this.querysubcontracts(filterQuery, pagination);
```

`queryInput`传递`undefined`，`pagination`为控件提供的数据。在`queryAccounts`方法中，将使用`formValues`作为作为后续的请求数据。

```tsx
// querysubcontracts
const params = pageRequest<TSubcontractQuery>(values, pagination);
dispatch({
  type: 'subcontract/qSubcontracts',
  payload: params,
});
```

将过滤后的表单数据`values`传入`pageRequest`，进行`pageValues`参数转换，得到后端可以识别的请求数据`params`，最后掉用 api 请求，发送数据。

### 查询后的数据接收

```ts
// src/pages/sysmanage/models/account.ts
*qSubcontracts({ payload }, { call, put }) {
  const res = yield call(API.query.subcontract.getSubcontractList.request, payload);
  const response = responseData(res);
  const resPage = pageResponse<TSubcontractDTO>(response, payload);
  yield put({
    type: 'uSubcontracts',
    payload: resPage,
  });
}
```

主要处理为：

```tsx
const resPage = pageResponse<TSubcontractDTO>(response, payload);
```

将后端返回数据传入`pageResponse`，得到`StandardTable`组件可以直接使用的数据`resPage`。

```tsx
uSubcontracts(state, action) {
  const payload = action.payload as TablePage<TSubcontractDTO>;
  return {
    ...state,
    subcontracts: {
      list: payload.list,
      pagination: payload.pagination,
    },
  };
}
```

将`pageResponse`处理后的数据更新至`state.accounts`。`subcontracts`组件由`dva`提供的`connect`方法追踪此`subcontracts`属性，在数据更新时，`SubcontractManage`组件将自动更新。
