<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-25 17:05:50
 * @lastTime: 2019-10-29 17:15:10
 * @LastAuthor: 侯成
 * @message: 
 -->
# Summary

* [前言](preface.md)「houcheng」

* 工程
  * [项目结构](1-projects/1.1-struct.md)
  * [文件作者信息](1-projects/1.2-koro-file-header.md)
* [公共组件](2-components/1.0-preface.md)
  * [片段组件](2-components/1.1-selectors.md)
  * [公用下拉框](2-components/1.2-autoSelectForm.md)
  * [表单组件](2-components/1.3-forms.md)
  * [标准弹窗](2-components/2.1-standard-pop.md)
  * [增强版表格](2-components/2.2-enhanced-table.md)
  * [枚举表单](2-components/2.3-enumerate-form.md)
  * [日期范围选择](2-components/2.4-date-range.md)
  * [通用选择器](2-components/2.5-base-selectors.md)
  * [可编辑表格](2-components/2.6-writable.md)
  * [新增修改表单组件](2-components/2.7-editeForm.md)
  * [详情名称展示](2-components/2.8-codeToView.md)
* 公共方法
  * [utils公共方法](3-modules/1.1-utils.md)
  * [分页](3-modules/1.2-pagenation.md)
  * [请求后处理](3-modules/1.3-message.md)
  * [API接口](3-modules/1.4-pont.md)
* [业务功能](4-implements/1.0-startup.md)
  * [开发教程](4-implements/1.0-startup.md)
  * [登录实现](4-implements/1.1-login.md)
  * [基础查询](4-implements/1.2-basic-query.md)
  * [按钮权限](4-implements/1.3-button-permission.md)
  * [缓存版查询](4-implements/1.4-cached-query.md)
* 前言技术
  * [大路由管理](5-technics/1.1-sysfuncs.md)
  * [HTTP请求](5-technics/1.3-http-request.md)
