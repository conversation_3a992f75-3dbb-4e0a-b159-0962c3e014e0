{"cust.delegateform": "赵煜颢 纯代发客户备案表 | ./Sales/CustomerManage/DelegateCustomerForm/index", "cust.payrollSendingList": "赵煜颢 工资单发送列表 | ./Sales/CustomerManage/PayrollSendingList/index", "cust.noNeedCalculateForm": "赵煜颢 不需核算客户备案表 | ./Sales/CustomerManage/NoNeedCalculateForm/index", "cust.infoquery": "严小强 客户信息查询(客服) | ./Sales/CustomerManage/CustomerQuery/index", "cust.group-company": "刘双 集团公司维护 | ./Sales/CustomerManage/GroupCompanyManager/index", "cust-crm.cccust": "刘双 陌拜客户 | ./crm/cust/CCCust/index", "cust-crm.CustReport": "严小强 客户信息查询(销售) | ./crm/cust/CustReport/index", "product.productManagement": "陈国祥 产品维护(销售) | ./Sales/Product/ProductManagement/index", "product.productType": "陈国祥 产品类型维护(销售) | ./Sales/Product/ProductType/index", "product.SubProduct": "陈国祥 产品子类型(销售) | ./Sales/Product/SubProduct/index", "product.InsuranceType": "陈国祥 险种维护(销售) | ./Sales/Product/InsuranceType/index", "product.InsuranceProvider": "陈国祥 供应商维护(销售) | ./Sales/Product/InsuranceProvider/index", "cust.CustomerPayerAssert": "严小强 客户付款方维护 | ./Sales/CustomerManage/CustomerPayerAssert/index", "cust.CustomerPayerQuery": "严小强 客户付款方查询 | ./Sales/CustomerManage/CustomerPayerQuery/index", "cust-crm.pre-cust": "刘双 正式客户预录入 | ./crm/cust/QueryPreRegCust/index", "cust-crm.formal-cust": "刘双 正在跟进客户查看 | ./crm/cust/QueryFormalCust/index", "cust.salaryRelatedAssert": "赵煜颢 薪资相关约定维护 | ./Sales/CustomerManage/SalaryRelatedAssert/index", "cust.salaryRelatedQuery": "赵煜颢 薪资相关约定查询 | ./Sales/CustomerManage/SalaryRelatedQuery/index", "cust-crm.viewShareArea": "严小强 共享区(六大区) | ./crm/cust/viewShareArea/index", "cust-crm.allotShareArea": "严小强 共享区跨区分配 | ./crm/cust/allotShareArea/index", "quotation.quotationTempManage": "赵煜颢 报价单模板维护 | ./Sales/Quotation/QuotationTempManage/index", "quotation.healthProductTempManage": "陈国祥 职场健康报价单模板维护 | ./Sales/Quotation/QuotationTempManage/index", "quotation.batchQuotationTempManage": "陈国祥 批量维护报价单模板 | ./Sales/Quotation/BatchQuotationTempManage/index", "contract.query": "侯成 合同查询（客服） | ./Sales/Contract/Query", "contract.manage": "侯成 合同管理（销售） | ./Sales/Contract/Manage", "quotation.quotationManage": "赵煜颢 报价单管理(销售) | ./Sales/Quotation/QuotationManage/index", "quotation.quotationQuery": "赵煜颢 报价单查询(客服) | ./Sales/Quotation/QuotationQuery/index", "cust-crm.viewDelArea": "严小强 删除区(六大区) | ./crm/cust/viewDelArea/index", "cust-crm.allotDelArea": "严小强 删除区跨区分配 | ./crm/cust/allotDelArea/index", "cust-crm.viewShareAreaAll": "严小强 共享区（全国） | ./crm/cust/viewShareAreaAll/index", "cust-crm.viewShareAreaLocal": "严小强 共享区（本地） | ./crm/cust/viewShareAreaLocal/index", "cust-crm.viewShareAreaYyzx": "严小强 共享区（运营中心） | ./crm/cust/viewShareAreaYyzx/index", "cust-crm.viewDelAreaLocal": "严小强 删除区（本地） | ./crm/cust/viewDelAreaLocal/index", "cust-crm.viewDelAreaAll": "严小强 删除区（全国） | ./crm/cust/viewDelAreaAll/index", "cust-crm.viewDelAreaYyzx": "严小强 删除区（运营中心） | ./crm/cust/viewDelAreaYyzx/index", "cust-crm.queryCrmBusinessLog": "赵煜颢 客户状态变更流水 | ./crm/cust/QueryCrmBusinessLog/index", "cust-crm.queryCustDuplicate": "赵煜颢 客户查重 | ./crm/cust/QueryCustDuplicate/index", "cust-crm.customerSimilarity": "赵煜颢 客户查重报表 | ./crm/cust/CustomerSimilarity/index", "cust-crm.salesRelatedDoc": "赵煜颢 销售相关文档 | ./crm/cust/SalesRelatedDoc/index", "contract.modelContractQuery": "赵煜颢 范本合同管理 | ./Sales/Contract/ModelContractQuery", "contract.queryContractVersion": "赵煜颢 合同版本号维护 | ./Sales/Contract/QueryContractVersion", "cust-crm.customerService": "刘夏梅 客户服务情况（实时） | ./crm/cust/QueryCustomerService", "cust-crm.customerServiceMonth": "刘夏梅 客户服务情况（月度） | ./crm/cust/QueryCustomerServiceMonth", "cust-crm.debtreminder": "刘夏梅 客户欠款提醒 | ./crm/cust/QueryDebtReminder", "cust-crm.queryMarketActivity": "赵煜颢 市场活动 | ./crm/cust/QueryMarketActivity", "cust-crm.queryPreRegMarket": "赵煜颢 市场活动客户预录入 | ./crm/cust/QueryPreRegMarket", "informationShare.queryMarketActivitySign": "赵煜颢 市场活动签到 | ./crm/informationShare/QueryMarketActivitySign", "informationShare.queryClassActivitySign": "赵煜颢 课程活动报名 | ./crm/informationShare/QueryClassActivitySign", "cust.QueryClientCompany": "刘夏梅 客户端开放列表 | ./Sales/CustomerManage/QueryClientCompany/index", "cust.noDisabilityBenefitsList": "孙尚阳 不收取残障金客户列表 | ./Sales/CustomerManage/NoDisabilityBenefitsList/index", "cust.ClientUsageRecord": "李龙翔 客户端使用备案 | ./Sales/CustomerManage/ClientUsageRecord/index"}