/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-09-23 18:12:41
 * @LastAuthor: 侯成
 * @LastTime: 2021-04-25 13:49:05
 * @message: message
 * 合同审批通过 接口响应非常长
 * activityNameEn activityNameCn
 * 退回修改 0
 * 法务审批 8
 * 印章人员盖章 10
 * 质控审批 11
 * 数据中心核对 12
 * 分公司财务审批 14
 * 集团业务财务审批 15
 * 福利人员审批 20
 * 体检项目审批 22
 * 集团体检审批 23
 * 各区财务体检审批 24
 * 各区薪资审批 25
 *
 */
import React, { useEffect, useState } from 'react';
import { Button, Tabs, Form, InputNumber, Upload, Modal, Tooltip, Input } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { EditeFormProps, FormInstance } from '@/components/EditeForm';
import Codal from '@/components/Codal';
import { Writable, useWritable } from '@/components/Writable';
import { FormElement1, FormElement3, RowElementButton } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import {
  ProdcutLineSelector,
  BooleanSelector,
  SignFlagManualSelector,
  CustProductLineSelector,
} from '@/components/Selectors/BaseDropDown';
import { ContractTypeSelector, mapToSelectors } from '@/components/Selectors';
import { useSelector } from 'umi';
import { ConnectState } from '@/models/connect';
import {
  ContractSubTypeSelector,
  BranchTitleSelector,
  BranchTitleSpeSelector,
  CommonBaseDataSelector,
  GetSignFlagManualSelect,
} from '@/components/Selectors/BaseDataSelectors';
import {
  validateCurrency,
  validateEmail,
  validateNaturalDay,
  validateNaturalNumber,
  validateNaturalPositive,
  naturalNumberRegx,
  validatePositive,
} from '@/utils/forms/validate';
import { stdDateFormat } from '@/utils/methods/times';
import { DateRange } from '@/components/DateRange4';
import {
  agereedAmtReceiveMonMap,
  contractAreaTypeMap,
  contractEndDateTypeMap,
  contractEndDateTypeNameMap,
  paymentModeMap,
  payMonthMapMap,
} from '@/utils/settings/sales/contract';
import { CompetitorSelector, PayTypeSelector, ContractCategorySelector } from './Seletors';
import { CODE_SUCCESS, msgErr, msgOk, resError } from '@/utils/methods/message';
import { isEmpty } from 'lodash';
import { Calculator } from '@/utils/methods/calculator';
import { stdBoolTrueSrting } from '@/utils/settings';
import { Switchs } from '@/components/Selectors/Switch';
import { InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import QuotationUpdateForm from '@/pages/Sales/Quotation/QuotationManage/Forms/QuotationDetailForm';
import { Typography } from 'antd';
import { downloadFileWithAlert, downloadFileWithId } from '@/utils/methods/file';
import { GeneralInputRenderOption } from '@/components/Writable/libs/GeneralInput';
import { notEmpty } from '@/utils/methods/checker';
import { StrDatePicker } from '@/components/DateComp/StrDatePicker';
import { AsyncButton } from '@/components/Forms/Confirm';
import DetailForm from '@/components/EditeForm/DetailForm';
import { shallowEqual } from 'react-redux';
import { Store } from 'antd/lib/form/interface';
import { AlertTab } from '@/components/AlertTabPane/AlertTab';
import { DeleteOutlined, ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons';
import { getCurrentMenu, getProDefId, getUserId } from '@/utils/model';
import { FileSize } from '@/components/UploadForm';
import {
  colorRed,
  QuostatusMap,
} from '@/pages/emphiresep/sendorder/CustomerSubcontract/ContractView';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import Group from '@/components/Group';
import ImportForm from '@/components/UploadForm/ImportForm';
import { NonStaCoctApprPop } from '@/components/StandardPop/NonStaCoctApprPop';
import { validDateCost, getFileExtension } from './ContractForm';
import { EmployeePop } from '@/components/StandardPop/EmployeePop';
import AddForm from '@/components/EditeForm/AddForm';
import QuotationAddForm from '../../Quotation/QuotationManage/Forms/QuotationAddForm';
import { multipleNum } from '../../Quotation/QuotationManage';
import { PreviewCodal } from './PreviewFile';

const { Link } = Typography;

const { TabPane } = Tabs;

const serviceQuotation: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_quotation',
};
const serviceProductLine: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_productLine',
};
const serviceCustPayer: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_custPayer',
};
const serviceApproveReject: IServiceType = {
  ...API.crm.slDisaReason.queryByPage,
  cacheKey: API.crm.slDisaReason.queryByPage + '_approveReject',
};
export enum contractFormScene {
  add,
  update,
  renew,
}

enum tabScene {
  sales = '1',
  cs = '2',
  quotation = '3',
  legal = '4',
  prepay = '5',
  custPayer = '6',
  reject = '7',
}

interface QueryContractDetailInfoWinProps {
  [props: string]: any;
  modal: [boolean, CallableFunction];
  contract?: Partial<defs.sale.ContractDTO>;
  additional?: POJO;
  onConfirm: () => void;
  scene: 'approve' | 'back' | undefined;
}

const service = API.sale.customer.sel;
const apis = [
  'contractManageService,getContractAttachmentByContractId',
  'contractManageService,getContractApproveRelatedAttachmentByContractId',
  'contractManageService,queryContractById',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getPayerByContractId',
  'contractManageService,getQuotationByContractId',
  'contractManageService,getProductLineByContractId',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,initData',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,save',
  'serviceTypeService,getSub',
  'quotationService,getQuotation',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,delQuotationInContract',
  'contractManageService,delProductLineInContract',
];

const codalNames = ['QuerySelectedQutationWin'];

type TContractDTO = defs.sale.ContractDTO;

const dafaultContractFormConfig = {
  winIsRenew: false,
  winIsRenewOperate: false,
  isPaymentQAResultUpdate: false,
  isBeijingDepartment: false,
  isCommitApprove: false,
  isSelectFirstLegalId: false,
  isUploadApproveRelatedAttachment: false,
  isSelectCsBySale: false,
  isCalculateGrossProfit: false,
  isPhysicalExamination: false,
  isTravelServices: false,
  isEditeAdvancePaymentRatio: false,
  isEditeTravelServicesRatio: false,
  isUpdateContractCategery: true,
  isExecuteStatus: false,
  internalMoneyNeeded: false,
  contractEndDateTypeEnable: true,
  isIssuingSalary: false,
  isRiskRatio: false,
  isRetireTransact: false,
  isHealthCheckup: false,
  isWelfareGather: false,
  isHealthOnther: false,
  isHealthPlatform: false,
  isRetQuotaGrantedRatio: false,
  isRetirementBusiness: false,
  isRetirementBusinessList: false,
};
const contractFormConfig = { ...dafaultContractFormConfig };

const ContractApproveWin: React.FC<QueryContractDetailInfoWinProps> = (props) => {
  const { scene, title, modal, contract: _contract, additional, onConfirm } = props;
  const [visible, setVisible] = modal;
  if (!visible) {
    Object.keys(dafaultContractFormConfig).forEach((key) => {
      contractFormConfig[key] = dafaultContractFormConfig[key];
    });
    return null;
  }
  const contract = { ..._contract, ...additional };
  const { contractId, activityStatus, parentContractId } = contract;
  const currentUser = useSelector((state: ConnectState) => state.user.currentUser, shallowEqual);
  const userBranchId = currentUser.profile?.governingBranch;
  const querySelectedQutationWin = useState(false);
  const quotationViewModal = useState(false);
  const quotationDetail = useState({});
  // console.log('contract in ContractApproveWin:', contract)
  // console.log('contract.contractType in ContractApproveWin:', contract.contractType)
  const [contractCategory, setContractCategory] = useState(contract.contractCategery);
  const [contractSubTypeNameMap, setcontractSubTypeNameMap] = useState<POVO>({});
  const [contractTypeNameMap, setcontractTypeNameMap] = useState<POVO>({});
  const [currentContract, setCurrentContarct] = useState<TContractDTO>({});
  const [importFile, setimportFile] = useState<Partial<TContractDTO>>({});
  const [branchTitleDepartId, setBranchTitleDepartId] = useState('');
  const [currentCustId, setcurrentCustId] = useState<string | undefined>(contract.custId);
  const [contractFormConfigCount, setContractFormConfigCount] = useState(0);
  const [signFlagManual, setsignFlagManual] = useState<string>();
  const [file, setFile] = useState<POJO | undefined>(undefined);
  const [subTypeId, setSubTypeId] = useState<string>();
  const [rejectTitle, setRejectTitle] = useState<string>('');
  const [rejectVisible, setRejectVisible] = useState<boolean | undefined>(false);
  const [contractType, setContractType] = useState<string>();
  const [importType, setImportType] = useState<string>('');
  const [retireFile, setRetireFileFile] = useState<any>({});
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [fileData, setFileData] = useState<any>([]);
  const [previewFile, setPreviewFile] = useState<string>('');
  const [previewFileType, setPreviewFileType] = useState<string>('');
  const previewModal = useState(false);

  const funcId = '10702000';
  const processDefId = getProDefId();
  const userId = getUserId();
  const setContractFormConfig = (data: POJO<boolean>, toState?: boolean) => {
    Object.keys(data).forEach((key) => {
      contractFormConfig[key] = data[key];
    });
    if (toState) setContractFormConfigCount(contractFormConfigCount + 1);
  };

  const [currentSales, setcurrentSales] = useState<string | undefined>(
    contract.currentSales || currentUser.userId,
  );
  const [onShowImport, setOnShowImport] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [rejectLoading, setRejectLoading] = useState(false);
  const isContractRedList = useState<string[]>([]);
  const producttColorList = useState<string[]>([]);
  const quotationColorList = useState<string[]>([]);

  const [mainForm] = Form.useForm();
  const [salesForm] = Form.useForm();
  // 这个没有了
  const [csForm] = Form.useForm();
  const [legalForm] = Form.useForm();
  const [prepayForm] = Form.useForm();
  const [approvalMemoForm] = Form.useForm();

  const writableQuotation = useWritable({ service: serviceQuotation });
  const writableProductLine = useWritable({ service: serviceProductLine });
  const writableCustPayer = useWritable({ service: serviceCustPayer });
  const writableApproveReject = useWritable({ service: serviceApproveReject });
  const writableApproveRejectView = useWritable({ service: serviceApproveReject });
  const writableUpload = useWritable({ service: serviceCustPayer });
  const writableRetire = useWritable({
    service: { ...serviceCustPayer, cachekey: serviceCustPayer + '1' },
  });
  useEffect(() => {
    if (!visible) {
      setFile(undefined);
    }
  }, [visible]);
  const [tabErrors, setTabErrors] = useState({
    mainForm: false,
    salesForm: false,
    csForm: false,
    legalForm: false,
    prepayForm: false,
    approveOpinionForm: false,
    writableQuotation: false,
    writableProductLine: false,
    writableCustPayer: false,
    writableUpload: false,
    writableRetire: false,
  });
  let activityNameEnIs12: any = false;
  let activityNameEnIs30: any = false;
  let activityNameEnIs31: any = false;
  let activityNameEnIsItem: any = false;
  let activityNameEnNot12: any = false;
  let activityNameEnIsApprove: any = false;
  if (contract?.processDefId === '910001782' || contract?.processDefId === '910001649') {
    activityNameEnIs12 = contract.activityNameEn === '9'; // '数据中心核对
    activityNameEnIs30 = contract.activityNameEn === '12'; // '销售补充数据
    activityNameEnIs31 = contract.activityNameEn === '2'; // '质控判定
    activityNameEnIsItem = contract.activityNameEn === '9' || contract.activityNameEn === '11'; // '数据中心核对 质控中心
    activityNameEnNot12 = !activityNameEnIs12;
    activityNameEnIsApprove =
      contract.activityNameEn !== '9' &&
      contract.activityNameEn !== '11' &&
      contract.activityNameEn !== '2';
  } else {
    activityNameEnIs12 = contract.activityNameEn === '12'; // '数据中心核对
    activityNameEnIs30 = contract.activityNameEn === '30'; // '销售补充数据
    activityNameEnIs31 = contract.activityNameEn === '31'; // '质控判定
    activityNameEnIsItem = contract.activityNameEn === '12' || contract.activityNameEn === '11'; // '数据中心核对 质控中心
    activityNameEnNot12 = !activityNameEnIs12;
    activityNameEnIsApprove =
      contract.activityNameEn !== '12' &&
      contract.activityNameEn !== '11' &&
      contract.activityNameEn !== '31';
  }

  const getDepartInfo = async (departmentId: string | undefined) => {
    // 天大地大，找不到一个获取部门信息的接口。
    if (!departmentId) return {};
    const branchLi = await API.admin.department.queryBranchList.requests({ departmentId });
    if (!branchLi) return {};
    if (!branchLi.list || branchLi.list.length === 0) return {};
    return branchLi.list[0];
  };

  useEffect(() => {
    (async () => {
      if (parentContractId) {
        const list = await API.crm.contractManage.getNotSameParentRecordByContractId.requests({
          contractId: contractId,
        });
        const productList =
          await API.crm.contractManage.getNotSameParentProductLineByContractId.requests({
            contractId: contractId,
            parentContractId: parentContractId,
          });
        const quotationList =
          await API.crm.contractManage.getNotSameParentQuotsByContractId.requests({
            contractId: contractId,
            parentContractId: parentContractId,
          });
        producttColorList[1](productList.list || []);
        quotationColorList[1](quotationList.list || []);
        isContractRedList[1](list.split(',') || []);
      }
    })();
  }, []);
  useEffect(() => {
    if (!contractId) return setLoadingData(false);
    setLoadingData(true);
    (async () => {
      // *********, 	20110614-G-0731-008, available
      let isPaymentQAResultUpdate = false;
      let isBeijingDepartment = false;
      let isSelectCsBySale = false;
      let isSelectFirstLegalId = false;
      let isCalculateGrossProfit = false;
      let isUpdateContractCategery = false;
      let isPhysicalExamination = false;
      let isTravelServices = false;
      let isEditeAdvancePaymentRatio = false;
      let isEditeTravelServicesRatio = false;
      let isUploadApproveRelatedAttachment = false;
      let isRiskRatio = false;
      let isRetireTransact = false; //退休办理
      let isHealthCheckup = false; //健康体检
      let isWelfareGather = false; //福利采集
      let isHealthOnther = false; //福利其他三项
      let isHealthPlatform = false; //福利平台业务
      let isRetirementBusiness = false; //是否有退休业务
      let isRetirementBusinessList = false; //是否有退休业务

      const contr: TContractDTO = await API.crm.contractManage.getContractById.requests({
        contractId,
      });
      const contrAddition = {} as Partial<TContractDTO>;
      const {
        areaType,
        contractHeadcount,
        contractAvgAmt,
        agreedWageArriveDay: _agreedWageArriveDay,
      } = contract;
      const isIssuingSalary = contr.isIssuingSalary === stdBoolTrueSrting;
      const {
        isAddedAttachment,
        liabilityCs,
        currentSales,
        contractType,
        contractSubType,
        isTravelServices: _isTravelServices,
        departmentId,
        governingArea,
        governingBranchName,
        paymentMode,
      } = contr;
      const agreedWageArriveDay = Number(_agreedWageArriveDay);
      // if (contractHeadcount) {
      //   contrAddition.totalPrice = Calculator.multiply(
      //     contractHeadcount,
      //     contractAvgAmt!,
      //   ).toString();
      // }
      if (
        (areaType === '3' && agreedWageArriveDay > 15) ||
        (areaType === '1' && agreedWageArriveDay > 20)
      ) {
        isPaymentQAResultUpdate = true;
      }

      if (currentSales) {
        isSelectCsBySale = true;
        contrAddition.areaId = governingArea;
        contrAddition.departmentId = departmentId;
        contrAddition.departmentName = governingBranchName;
        // contrAddition.areaId = await API.crm.contractManage.getAreaIdByCurrentSales.requests({
        //   saleId: currentSales,
        // });
        // const departmentId = await API.crm.contractManage.getDepartmentIdByCurrentSales.requests({
        //   saleId: currentSales,
        // });
        // contrAddition.departmentId = departmentId;
        if (departmentId) {
          // const departmentInfo = await getDepartInfo(departmentId);
          // contrAddition.departmentName = departmentInfo.departmentName;
          // contrAddition.areaId = departmentInfo.governingAreaId;
          setBranchTitleDepartId(departmentId);
          if (departmentId === '11504') {
            isBeijingDepartment = true;
          }
        }
      } else {
        isSelectCsBySale = false;
        isSelectFirstLegalId = false;
        contrAddition.firstLegalApproveId = undefined;
        contrAddition.firstLegalApproveName = undefined;
      }

      if (liabilityCs) {
        // 不用怀疑，这里就是为了查个名字
        const empInfo = await API.admin.employee.queryEmployee.requests({ empId: liabilityCs });
        contrAddition.liabilityCsName = empInfo?.employeeName;
      }

      if (contractType) {
        isUpdateContractCategery = true;
      }
      contr?.contractSubType && setSubTypeId(contr?.contractSubType);
      if (contractSubType) {
        if (['8', '9'].includes(contractSubType)) {
          isCalculateGrossProfit = true;
        }
        if (contractSubType === '10') {
          isPhysicalExamination = true;
        }

        if (contractSubType === '*********') {
          isTravelServices = true;
        }
        if (contractSubType === '406') {
          isRiskRatio = true;
        }
        if (
          contractSubType === '210' ||
          contractSubType === '15' ||
          contractSubType === '14' ||
          contractSubType === '11' ||
          contractSubType === '12' ||
          contractSubType === '13' ||
          contractSubType === '*********'
        ) {
          //退休
          isRetireTransact = true;
          isRetirementBusinessList = true;
        } else {
          isRetireTransact = false;
          isRetirementBusinessList = false;
        }
        if (contractSubType === '501') {
          //福利集采
          isWelfareGather = true;
        }
        if (contractSubType === '502') {
          //平台业务-福利
          isHealthPlatform = true;
        }
        if (contractSubType === '503') {
          //健康体检
          isHealthCheckup = true;
        }
        if (contractSubType === '504' || contractSubType === '505' || contractSubType === '506') {
          //合同小类为健康管理、雇主险、补医保
          isHealthOnther = true;
        }
        if (contractSubType === '210') {
          //合同小类为退休捆绑小类
          isRetirementBusiness = true;
          isRetirementBusinessList = true;
          mainForm.setFieldsValue({
            isRetirementBusiness: '1',
          });
        } else {
          isRetirementBusiness = false;
          mainForm.setFieldsValue({
            // isRetirementBusiness: '',
            isRetQuotaGranted: '0',
            retirementGiftCount: '',
          });
          isRetirementBusinessList = false;
          // mainForm.setFieldsValue({
          //   isRetirementBusiness: '',
          // });
        }
        if (contr.isRetirementBusiness === '1') {
          isRetirementBusinessList = true;
        } else {
          isRetirementBusinessList = false;
        }
      }

      // if (!contractVersion) {
      //   if (!contractSubType || !contractType) {
      //     contrAddition.contractVersion = '未匹配';
      //   } else {
      //     const contractVersionType = contractType === '4' ? '2' : '1';
      //     const contractVersionRes = await API.sale.contract.getContractVersion.request({
      //       contractVersionType: contractVersionType,
      //       contractType: contractType,
      //       contractSubType: contractSubType,
      //     });
      //     if (resError(contractVersionRes)) {
      //       contrAddition.contractVersion = '未匹配';
      //     } else {
      //       contrAddition.contractVersion = contractVersionRes.data;
      //     }
      //   }
      // }

      if (paymentMode == '3') {
        isEditeAdvancePaymentRatio = true;
      }
      if (_isTravelServices == '1') {
        isEditeTravelServicesRatio = true;
      }
      let importFileInfo = {};
      const quotations = await API.crm.contractManage.getQuotationByContractId.requests({
        contractId,
      });
      writableQuotation.setNewData(quotations);

      const productLines = await API.crm.contractManage.getProductLineByContractId.requests({
        contractId,
      });
      writableProductLine.setNewData(productLines);
      const payers = await API.crm.contractManage.getPayerByContractId.requests({ contractId });
      writableCustPayer.setNewData(payers);
      if (contractType !== '5') {
        const attachments = await API.crm.contractManage.getContractAttachmentByContractId.request({
          contractId,
        });
        if (!resError(attachments)) {
          const { data } = attachments;
          // if (data.fileId) {
          importFileInfo = {
            importFileId: data.fileId,
            importFileName: data.attName,
            contractFileUploadDt: data.createDt,
            contractFileRemark: data.remark,
          };
          setimportFile(importFileInfo);

          // }
          legalForm.setFieldsValue({ ...importFileInfo, contractFileRemark: data.remark });
        }
      }

      writableApproveRejectView.request({
        contractId: contract.contractId,
        processInstanceId: contract.processInstanceId,
      });

      const relatedAttachment =
        await API.crm.contractManage.getContractApproveRelatedAttachmentByContractId.request({
          contractId,
        });
      if (!resError(relatedAttachment)) {
        // console.log('relatedAttachment in getContractApproveRelatedAttachmentByContractId:', relatedAttachment)
        if (relatedAttachment.data) {
          contrAddition.approveRelatedAttachment = relatedAttachment.data.fileId;
          contrAddition.approveRelatedAttachmentName = relatedAttachment.data.attName;
          isUploadApproveRelatedAttachment = true;
        }
      }

      setContractFormConfig({
        ...contractFormConfig,
        isPaymentQAResultUpdate,
        isBeijingDepartment,
        isSelectCsBySale,
        isSelectFirstLegalId,
        isCalculateGrossProfit,
        isUpdateContractCategery,
        isPhysicalExamination,
        isEditeAdvancePaymentRatio,
        isEditeTravelServicesRatio,
        isIssuingSalary,
        isTravelServices,
        isRiskRatio,
        isRetireTransact,
        isHealthCheckup, //健康体检
        isWelfareGather, //福利采集
        isHealthOnther, //福利其他三项
        isHealthPlatform, //福利平台业务
        isRetirementBusiness, //是否有退休业务
        isRetirementBusinessList, //是否有退休业务
      });
      // 依次是，getContractById接口返回的值
      // 外部父级组件传入的contract对象
      // 本次回调中，其他接口获取到的额外的值
      const fullContract = {
        ...contract,
        ...contr,
        ...contrAddition,
        ...importFileInfo,
        ...(contr?.travelServicesRatio && {
          travelServicesRatio: contr?.travelServicesRatio * 100,
        }),
      };
      if (fullContract.contractEndDateType) {
        fullContract.contractEndDateType =
          contractEndDateTypeNameMap.get(fullContract.contractEndDateType) ||
          fullContract.contractEndDateType;
      }
      // console.log('fullContract in ContractApproveWin:', fullContract)
      if (naturalNumberRegx.test(String(fullContract.contractType))) {
        setContractType(fullContract.contractType);
      }
      setCurrentContarct(fullContract);
      setSubTypeId(fullContract?.contractSubType);
      mainForm.setFieldsValue(fullContract);
      salesForm.setFieldsValue(fullContract);
      // csForm.setFieldsValue(fullContract);
      approvalMemoForm.setFieldsValue(fullContract);
      legalForm.setFieldsValue(fullContract);
      prepayForm.setFieldsValue(fullContract);
      writableUpload.setNewData(fullContract?.contractFileList);
      writableRetire.setNewData(fullContract?.contractRetireeList);
      // getContractApproveRelatedAttachmentByContractId若获得数据，则下一句不需要
      if (!isUploadApproveRelatedAttachment) isAddedAttachmentOnChange(isAddedAttachment);
      setLoadingData(false);
    })();
  }, [contractId]);

  const readOnly = scene === 'approve';

  // const onCurrentSalesChange = (sales: defs.sale.DropdownList) => {
  //   if (!sales || isEmpty(sales)) {
  //     const typeList = null;
  //     setContractFormConfig({
  //       ...contractFormConfig,
  //       isSelectFirstLegalId: false,
  //       isSelectCsBySale: false,
  //     });
  //     legalForm.setFieldsValue({
  //       firstLegalApproveId: null,
  //       firstLegalApproveName: null,
  //     });
  //     return;
  //   }
  //   const { key } = sales;
  //   setcurrentSales(key);
  //   API.crm.contractManage.getAreaIdByCurrentSales.requests({ saleId: key! }).then((data) => {
  //     setCurrentContarct({ ...currentContract, areaId: data });
  //   });

  //   API.crm.contractManage.getDepartmentIdByCurrentSales.requests({ saleId: key! }).then((data) => {
  //     setCurrentContarct({ ...currentContract, departmentId: data });
  //     if (data) {
  //       setBranchTitleDepartId(data);
  //       if (data === '11504') {
  //         setContractFormConfig({
  //           ...contractFormConfig,
  //           isBeijingDepartment: true,
  //         });
  //       }
  //     }
  //   });

  //   setContractFormConfig({
  //     ...contractFormConfig,
  //     isSelectFirstLegalId: true,
  //   });
  // };

  const onContractHeadcountChange = (value: string | number | undefined) => {
    if (!value) {
      setCurrentContarct({ ...currentContract, totalPrice: '0' });
      mainForm.setFieldsValue({ totalPrice: '0' });
      return;
    }
    const contractHeadcount = String(value);
    const contractAvgAmt = mainForm.getFieldValue('contractAvgAmt');
    if (contractAvgAmt) {
      const totalPrice = String(Calculator.multiply(contractHeadcount, contractAvgAmt));
      mainForm.setFieldsValue({ totalPrice });
      setCurrentContarct({ ...currentContract, totalPrice });
    }
    setCurrentContarct({ ...currentContract, isQuarterlyPaymentLess20: undefined });
  };

  const onAreaTypeChange = (value: string) => {
    const agreedWageArriveDay = Number(prepayForm.getFieldValue('agreedWageArriveDay'));
    if (
      (value === '3' && agreedWageArriveDay > 15) ||
      (value === '1' && agreedWageArriveDay > 20)
    ) {
      setContractFormConfig({ ...contractFormConfig, isPaymentQAResultUpdate: true });
    } else {
      setContractFormConfig({ ...contractFormConfig, isPaymentQAResultUpdate: false });
    }
    mainForm.setFieldsValue({ isPaymentQAResult: '' });
  };

  const onAgreedWageArriveDayChange = (value: string) => {
    const agreedWageArriveDay = Number(value);
    const areaType = mainForm.getFieldValue('areaType');
    if (
      (areaType === '3' && agreedWageArriveDay > 15) ||
      (areaType === '1' && agreedWageArriveDay > 20)
    ) {
      setContractFormConfig({ ...contractFormConfig, isPaymentQAResultUpdate: true });
    } else {
      setContractFormConfig({ ...contractFormConfig, isPaymentQAResultUpdate: false });
    }
    mainForm.setFieldsValue({ isPaymentQAResult: '' });
  };

  const onContractCategoryChange = (contractCategory: string) => {
    setContractCategory(contractCategory);
    const { contractSubType } = mainForm.getFieldsValue(['contractType', 'contractSubType']);
    if (!contractSubType) {
      mainForm.setFieldsValue({ contractVersion: '未匹配' });
      return;
    }
    getContractVersion();
  };

  const isAddedAttachmentOnChange = (value?: string) => {
    if (value === stdBoolTrueSrting) {
      return setContractFormConfig({
        ...contractFormConfig,
        isUploadApproveRelatedAttachment: true,
      });
    }
    setContractFormConfig({ ...contractFormConfig, isUploadApproveRelatedAttachment: false });
    mainForm.setFieldsValue({
      approveRelatedAttachment: undefined,
      approveRelatedAttachmentName: undefined,
    });
  };

  const onGrossProfitChange = (value: string | number | undefined) => {
    // 如果当前输入为空，则计算结果不会产生变化，可以直接返回。
    if (!value) return;
    // 下面的getFieldsValue能否得到最新的值，尚待验证。
    const { income, tax, executionCost, agentBusiness } = mainForm.getFieldsValue([
      'income',
      'tax',
      'executionCost',
      'agentBusiness',
    ]);
    const grossProfit = new Calculator(0);
    grossProfit.plus(income).minus(tax).minus(executionCost).minus(agentBusiness);
    mainForm.setFieldsValue({ grossProfit: grossProfit.toNumber() });
  };

  const paymentModeOnChange = (value: string | undefined) => {
    if (value === '3') {
      return setContractFormConfig({ ...contractFormConfig, isEditeAdvancePaymentRatio: true });
    }
    setContractFormConfig({ ...contractFormConfig, isEditeAdvancePaymentRatio: false });
    mainForm.setFieldsValue({ advancePaymentRatio: undefined });
  };

  const isInternalPaymentOnChange = (value: string | undefined) => {
    if (value === stdBoolTrueSrting) {
      return setContractFormConfig({ ...contractFormConfig, internalMoneyNeeded: true });
    }
    setContractFormConfig({ ...contractFormConfig, internalMoneyNeeded: false });
    mainForm.setFieldsValue({ internalMoney: undefined });
  };

  const handdleCustConfirm = (value?: defs.sale.Customer) => {
    const custInfo = {
      custId: undefined,
      custName: undefined,
      custCode: undefined,
      hrContract: undefined,
      contactTel: undefined,
      email: undefined,
    };
    if (!value) {
      mainForm.setFieldsValue(custInfo);
      return;
    }
    custInfo.custId = value.custId;
    custInfo.custName = value.custName;
    custInfo.custCode = value.custCode;
    custInfo.hrContract = value.hrContract;
    custInfo.contactTel = value.contactTel;
    custInfo.email = value.email;
    mainForm.setFieldsValue(custInfo);
    setcurrentCustId(value.custId);
  };

  const onValueFoundContractType = (value: string | undefined, text: string | undefined) => {
    if (!value) return;
    mainForm.setFieldsValue({ contractType: value, ontractTypeName: text });
  };

  const onContractSubTypeSelectorLoaded = (data: POJO[]) => {
    if (!data || !data[0]) return;
    // const contractSubType = mainForm.getFieldValue('contractSubType')
    // const st = data.find(d => d.value === contractSubType)
    getContractVersion();
  };

  const onValueFoundContractSubType = (value: string | undefined, text: string | undefined) => {
    if (!value) return;
    mainForm.setFieldsValue({ contractSubType: value, ontractSubTypeName: text });
    getContractVersion();
  };

  const onContractSubTypeChange = (subTypeId: string) => {
    const { contractType } = mainForm.getFieldsValue(['contractType']);
    let isUpdateContractCategery: boolean;
    let isCalculateGrossProfit: boolean;
    let isPhysicalExamination: boolean;
    let isTravelServices: boolean;
    let isRiskRatio: boolean;
    if (subTypeId !== '7' && contractType === '4') {
      mainForm.setFieldsValue({ contractCategery: '2' });
      isUpdateContractCategery = false;
    } else {
      isUpdateContractCategery = true;
    }
    if (['8', '9'].includes(subTypeId)) {
      isCalculateGrossProfit = true;
    } else {
      isCalculateGrossProfit = false;
      mainForm.setFieldsValue({
        income: null,
        tax: null,
        executionCost: null,
        agentBusiness: null,
        grossProfit: null,
      });
    }
    if (subTypeId === '10') {
      isPhysicalExamination = true;
    } else {
      isPhysicalExamination = false;
      mainForm.setFieldsValue({
        peIncome: null,
        peExecutionCost: null,
        peGrossProfit: null,
        paymentMode: null,
        isInternalPayment: null,
        internalMoney: null,
        advancePaymentRatio: null,
      });
    }

    if (subTypeId === '*********') {
      isTravelServices = true;
    } else {
      isTravelServices = false;
      mainForm.setFieldsValue({
        isTravelServices: null,
        travelServicesRatio: null,
      });
    }
    if (subTypeId === '406') {
      isRiskRatio = true;
    } else {
      isRiskRatio = false;
      mainForm.setFieldsValue({
        riskSharingRatio: null,
        riskPremiumRatio: null,
      });
    }
    setContractFormConfig({
      ...contractFormConfig,
      isUpdateContractCategery,
      isCalculateGrossProfit,
      isPhysicalExamination,
      isTravelServices,
      isRiskRatio,
    });
    if (subTypeId) {
      getContractVersion();
    } else {
      mainForm.setFieldsValue({ contractVersion: '未匹配' });
    }
  };

  const onContractTypeChange = (svcTypeId: string) => {
    mainForm.setFieldsValue({ contractSubType: null });
    if (!svcTypeId) return;
    mainForm.setFieldsValue({ contractSubType: null, contractSubTypeName: null });
  };

  const contractAvgAmtonChange = (value: string | undefined) => {
    if (!value) {
      setCurrentContarct({ ...currentContract, totalPrice: '0' });
      mainForm.setFieldsValue({ totalPrice: '0' });
      return;
    }

    const contractAvgAmt = String(value);
    const contractHeadcount = mainForm.getFieldValue('contractHeadcount');
    if (contractHeadcount) {
      const totalPrice = String(Calculator.multiply(contractHeadcount, contractAvgAmt));
      mainForm.setFieldsValue({ totalPrice });
      setCurrentContarct({ ...currentContract, totalPrice });
    }
  };

  const getContractVersion = (params?: POJO) => {
    const { contractType, contractSubType, contractCategery } = mainForm.getFieldsValue([
      'contractType',
      'contractSubType',
      'contractCategery',
    ]);
    if (!contractSubType || !contractType) return;
    API.sale.contract.getContractVersion
      .request({
        contractVersionType: contractCategery,
        contractType: contractType,
        contractSubType: contractSubType,
        ...params,
      })
      .then((res: StdRes<string>) => {
        if (resError(res)) {
          return mainForm.setFieldsValue({ contractVersion: '未匹配' });
        }
        mainForm.setFieldsValue({ contractVersion: res.data || '未匹配' });
      });
  };

  const handdleMultiConfirmQoutaions = (quotations?: defs.emphiresep.Quotation[]) => {
    writableQuotation.setNewData(quotations);
  };

  const viewQuotation = async (quotationId: string) => {
    const data = await API.sale.quotation.getQuotationData.requests(
      { quotationId: quotationId },
      { params: { quotationId: quotationId } },
    );
    let initData: POJO = {};
    if (data.quotationList.length > 0) {
      initData = { ...initData, ...data, ...data.quotationList[0], ...data.saleList[0] };
    }
    if (data.saleList.length > 0) {
      initData = { ...initData, atr: multipleNum(data.saleList[0].atr) };
    }

    if (data.businessDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.businessDetailList[0].vatr,
        watr: parseFloat(data.businessDetailList[0].atr) * 100,
      };
    }
    if (data.healthDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.healthDetailList[0].vatr,
        watr: parseFloat(data.healthDetailList[0].atr) * 100,
      };
    }

    if (data.EmployerDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.EmployerDetailList[0].vatr,
        watr: parseFloat(data.EmployerDetailList[0].atr) * 100,
      };
    }
    quotationDetail[1]({ ...initData, type: 'VIEW', text: '', title: '查看报价单' });
    quotationViewModal[1](true);
  };

  const SignFlagManualSelectoronChange = (signFlagM: string) => {
    setsignFlagManual(signFlagM);
    if (signFlagM === '4') {
      mainForm.setFieldsValue({
        contractEndDate: undefined,
        contractEndDateType: undefined,
      });
      setContractFormConfig({ contractEndDateTypeEnable: false }, true);
    } else {
      setContractFormConfig({ contractEndDateTypeEnable: true }, true);
    }
  };

  const onChangecontractEndDateType = (value: string) => {
    mainForm.setFieldsValue({ contractEndDate: undefined });
  };

  const onisIssuingSalaryChange = (isIssuingSalary: string) => {
    setContractFormConfig({ isIssuingSalary: isIssuingSalary === stdBoolTrueSrting });
  };
  const searchCustTianyan = (disabled: boolean) => {
    return (
      <Typography.Link
        // disabled={disabled || !nonStaCoctApprId}
        onClick={() => window.open('https://www.tianyancha.com/search?key=' + disabled)}
      >
        核查客户
      </Typography.Link>
    );
  };
  const formColumns: EditeFormProps[] = [
    { label: '合同编号', fieldName: 'contractCode' },
    {
      label: '合同名称',
      fieldName: 'contractName',
      rules: [{ required: true, message: '请输入合同名称' }],
    },
    {
      label: colorRed(
        isContractRedList[0].includes('3') || isContractRedList[0].includes('4'),
        '总售价',
      ),
      fieldName: 'totalPrice',
      rules: [{ required: true, message: '请输入总售价' }],
      inputProps: {
        disabled: true,
      },
    },
    {
      label: colorRed(isContractRedList[0].includes('1'), '合同大类名称'),
      fieldName: 'contractType',
      rules: [{ required: true, message: '请输入合同大类名称' }],
      render: (outerForm: FormInstance, record?: any) => (
        <ContractTypeSelector
          onValueFound={onValueFoundContractType}
          onChange={onContractTypeChange}
          code={record?.contractType}
        />
      ),
    },
    {
      label: colorRed(isContractRedList[0].includes('2'), '合同小类名称'),
      fieldName: 'contractSubType',
      rules: [{ required: true, message: '请输入合同小类名称' }],
      render: (outerForm: FormInstance, record?: any) => (
        <ContractSubTypeSelector
          skipEmptyParam
          params={{ svcTypeId: contractType }}
          onValueFound={onValueFoundContractSubType}
          onLoaded={onContractSubTypeSelectorLoaded}
          onChange={onContractSubTypeChange}
          code={record?.contractSubType}
        />
      ),
    },
    {
      label: colorRed(isContractRedList[0].includes('3'), '预计签约人数'),
      fieldName: 'contractHeadcount',
    },
    {
      label: '客户编号',
      fieldName: 'custCode',
      // rules: [{ required: true, message: '请输入客户编号' }],
      inputRender: (outerForm: FormInstance) => (
        <CustomerPop
          rowValue="custId-custName-custCode"
          // handdleConfirm={handdleCustConfirm}
          keyMap={{
            custId: 'custId',
            custCode: 'custCode',
            custName: 'custName',
            hrContract: 'hrContract',
            contactTel: 'contactTel',
            email: 'email',
          }}
          disabled={true}
          addonAfter={searchCustTianyan(outerForm.getFieldValue('custName'))}
        />
      ),
    },
    {
      label: '客户全称',
      fieldName: 'custName',
    },
    // {
    //   label: '现销售',
    //   fieldName: 'currentSales',
    //   rules: [{ required: true, message: '请输入现销售' }],
    //   inputRender: (outerForm: FormInstance) => {
    //     return (
    //       <CurrentSalesSelector
    //         disabled={!currentCustId}
    //         onConfirm={onCurrentSalesChange}
    //         params={{ custId: currentCustId, deptId: userBranchId }}
    //         skipEmptyParam
    //       />
    //     );
    //   },
    // },
    {
      label: '现销售',
      fieldName: 'salesName',
    },
    {
      label: '客户联系人',
      fieldName: 'hrContract',
      inputRender: 'string',
      inputProps: {
        disabled: activityNameEnNot12,
      },
    },
    {
      label: '邮件地址',
      fieldName: 'email',
      inputRender: 'string',
      rules: [{ validator: validateEmail }],
      inputProps: {
        disabled: activityNameEnNot12,
      },
    },
    {
      label: '联系电话',
      fieldName: 'contactTel',
      inputRender: 'string',
      // editable="{(((privatePopObject.processDefCode == 'ContractCs')
      inputProps: {
        disabled: activityNameEnNot12,
      },
      // editable="{(((privatePopObject.processDefCode == 'ContractCs')
    },
    {
      label: '开始日期',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '开始日期',
              dataIndex: 'contractStartDate',
              rules: [{ required: true, message: '请输入开始日期' }],
              disabled: activityNameEnNot12,
            },
            {
              title: '结束日期',
              dataIndex: 'contractStopDate',
              rules: [{ required: true, message: '请输入结束日期' }],
              disabled: activityNameEnNot12,
            },
          ]}
          // colConf={column3}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: colorRed(isContractRedList[0].includes('9'), '签约方公司抬头'),
      fieldName: 'signBranchTitleId',
      rules: [{ required: true, message: '请输入签约方公司抬头' }],
      inputRender: () => {
        if (subTypeId === '501' || subTypeId === '502') {
          //福利签约方抬头
          return <GetSignFlagManualSelect params={{ type: '9128' }} />;
        } else if (subTypeId === '504') {
          //健康管理
          return <GetSignFlagManualSelect params={{ type: '9129' }} />;
        } else if (subTypeId === '505' || subTypeId === '506') {
          //雇主险 补医保
          return <GetSignFlagManualSelect params={{ type: '9130' }} />;
        } else if (subTypeId === '503') {
          //健康体检
          return <GetSignFlagManualSelect params={{ type: '9131' }} />;
        } else {
          return (
            <BranchTitleSpeSelector skipEmptyParam params={{ departmentId: branchTitleDepartId }} />
          );
        }
      },
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '合同类别',
      fieldName: 'contractCategery',
      rules: [{ required: true, message: '请输入合同类别' }],
      render: (_: any, record: any) => (
        <ContractCategorySelector
          onChange={onContractCategoryChange}
          code={record?.contractCategery}
        />
      ),
      inputProps: {
        disabled: !contractFormConfig.isUpdateContractCategery,
      },
    },
    {
      label: '合同版本',
      fieldName: 'contractVersion',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '现销售城市',
      fieldName: 'cityName',
    },
    {
      label: colorRed(isContractRedList[0].includes('5'), '服务区域类型'),
      fieldName: 'areaType',
      rules: [{ required: true, message: '请输入服务区域类型' }],
      render: (_: any, record: any) => contractAreaTypeMap.get(record?.areaType),
    },
    {
      label: colorRed(isContractRedList[0].includes('10'), '缴费类型'),
      fieldName: 'payType',
      rules: [{ required: true, message: '请输入缴费类型' }],
      inputRender: () => <PayTypeSelector />,
      inputProps: {
        disabled: activityNameEnNot12,
      },
    },
    {
      label: '现销售大区',
      fieldName: 'formerGoverningAreaName',
    },
    {
      label: '范本修改版合同备注',
      fieldName: 'modelModifyVersionRemark',
    },

    {
      label: '服务地区',
      fieldName: 'svcRegion',
    },

    {
      label: '备注',
      fieldName: 'memo',
    },
    {
      label: '是否抢单',
      fieldName: 'isRob',
      render: (_: any, record: any) => <BooleanSelector code={record?.isRob} />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: '竞争对手',
      fieldName: 'competitor',
      br: true,
      render: (_: any, record: any) => <CompetitorSelector order="asc" code={record?.competitor} />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: colorRed(isContractRedList[0].includes('23'), '是否为已有客户所推荐'),
      fieldName: 'isCustRecommend',
      rules: [{ required: true, message: '请输入是否为已有客户所推荐' }],
      render: (_: any, record: any) => (
        <BooleanSelector order="asc" code={record?.isCustRecommend} />
      ),
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: colorRed(isContractRedList[0].includes('8'), '是否代发薪资'),
      fieldName: 'isIssuingSalary',
      rules: [{ required: true, message: '请选择是否代发薪资' }],
      // render: (_: any, record: any) => (
      //   <BooleanSelector order="asc" code={record?.isIssuingSalary} />
      // ),
      inputRender: () => (
        <BooleanSelector
          order="asc"
          onChange={onisIssuingSalaryChange}
          disabled={activityNameEnNot12}
        />
      ),
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: colorRed(isContractRedList[0].includes('6'), '是否集中一地投保'),
      fieldName: 'isSameInsur',
      rules: [{ required: true, message: '请选择是否集中一地投保' }],
      // render: (_: any, record: any) => <BooleanSelector code={record?.isSameInsur} />,
      inputRender: () => <BooleanSelector order="asc" disabled={activityNameEnNot12} />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: colorRed(isContractRedList[0].includes('7'), '是否增强型代理'),
      fieldName: 'enhancedAgent',
      noBorder: true,
      // rules: [{ required: true, message: '请选择是否增强型代理' }],
      render: (_: any, record: any) => <Switchs code={record?.enhancedAgent} />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: '是否客服二次开发',
      fieldName: 'isSecondaryDev',
      noBorder: true,
      render: (_: any, record: any) => <Switchs code={record?.isSecondaryDev} />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: '是否降价、垫付、账期延期',
      fieldName: 'isDefer',
      noBorder: true,
      render: (_: any, record: any) => <Switchs code={record?.isDefer} />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: '是否包含退休业务',
      fieldName: 'isRetirementBusiness',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: true,
      },
      inputRender: () => <Switchs />,
    },
    {
      label: '是否有赠送退休额度',
      fieldName: 'isRetQuotaGranted',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: true,
        // onChange: (e: any) => isRetQuotaGrantedOnChange(e),
      },
      inputRender: () => <Switchs />,
    },
    {
      label: '赠送退休数量',
      fieldName: 'retirementGiftCount',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: true,
      },
      rules: [{ validator: validateNaturalPositive('格式错误必须为正整数') }],
      inputRender: 'number',
    },

    {
      label: colorRed(isContractRedList[0].includes('15'), '含差旅服务'),
      fieldName: 'isTravelServices',
      inputProps: {
        hidden: !contractFormConfig.isTravelServices,
      },
      noBorder: true,
      render: (_: any, record: any) => <Switchs code={record?.isTravelServices} />,
    },
    {
      label: colorRed(isContractRedList[0].includes('16'), '差旅服务费比例%'),
      fieldName: 'travelServicesRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isTravelServices,
        disabled: true,
      },
      rules: [
        {
          required: contractFormConfig.isEditeTravelServicesRatio,
          message: '请输入0~100的两位小数',
        },
      ],
    },
    {
      label: '体检税率%',
      fieldName: 'whPeRate',
      render: (_: any, record: any) => (
        <GetSignFlagManualSelect
          params={{
            type: '9133',
          }}
          code={record?.whPeRate}
        />
      ),
      inputProps: { hidden: !contractFormConfig.isHealthCheckup },
      rules: [
        {
          required: contractFormConfig.isHealthCheckup,
          message: '体检税率%不能为空',
        },
      ],
    },
    {
      label: '提成销售',
      fieldName: 'whCommissionSale',
      inputRender: () => {
        return (
          <EmployeePop
            rowValue="whCommissionSale-whCommissionSaleName"
            keyMap={{
              whCommissionSale: 'EMPID',
              whCommissionSaleName: 'REALNAME',
            }}
            disabled={true}
          />
        );
      },
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      // rules: [
      //   {
      //     required:
      //       contractFormConfig.isHealthCheckup ||
      //       contractFormConfig.isWelfareGather ||
      //       contractFormConfig.isHealthOnther,
      //     message: '提成销售不能为空',
      //   },
      // ],
    },
    // {
    //   label: '合同审核相关的附件',
    //   fieldName: 'approveRelatedAttachment',
    //   inputRender: 'upload',
    //   inputProps: {
    //     maxSize: FileSize._20MB, // 最大只能传_20MB
    //     fileName: 'approveRelatedAttachmentName',
    //     // 这里必传，当代办页使用此页面是，funcId不存在，导致上传出错。
    //     bizType: '10702000',
    //     // disabled: !contractFormConfig.isUploadApproveRelatedAttachment,//说是不让使用了 先行注释掉
    //     disabled: true,
    //   },
    //   rules: [
    //     {
    //       required: contractFormConfig.isUploadApproveRelatedAttachment,
    //       message: '【合同审核相关的附件】不能为空',
    //     },
    //   ],
    // },
    // 收入
    {
      label: colorRed(
        isContractRedList[0].includes('11') ||
          isContractRedList[0].includes('12') ||
          isContractRedList[0].includes('13') ||
          isContractRedList[0].includes('14'),
        '毛利',
      ),
      fieldName: 'grossProfit',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【毛利】不能为空',
        },
        {
          pattern: /(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d{1,2})?$)/,
          message: '必须为数字，且只能保留两位小数',
        },
        // { validator: validateCurrency('收入格式错误') },
      ],
      inputProps: { hidden: !contractFormConfig.isCalculateGrossProfit },
    },
    {
      label: '毛利率%',
      fieldName: 'peGrossProfit',
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【毛利率%】不能为空',
        },
      ],
    },
    {
      label: '职场健康付款方式',
      fieldName: 'paymentMode',
      inputRender: () => mapToSelectors(paymentModeMap, { onChange: paymentModeOnChange }),
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
        disabled: true,
      },
      rules: [
        {
          required: contractFormConfig.isHealthCheckup,
          message: '职场健康付款方式',
        },
      ],
    },
    {
      label: '职场健康预付款比例%',
      fieldName: 'advancePaymentRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
        disabled: true,
      },
      rules: [
        {
          required: contractFormConfig.isEditeAdvancePaymentRatio,
          message: '【职场健康预付款比例】不能为空',
        },
      ],
    },
    {
      label: '职场健康毛利率%',
      fieldName: 'whMargin',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
        disabled: true,
      },
      rules: [{ pattern: /^(0|[1-9]\d*)(?:\.\d{1,3})?$/, message: '最多保留三位小数' }],
    },
    {
      label: '毛利率%',
      fieldName: 'peGrossProfit',
      inputProps: { hidden: !contractFormConfig.isPhysicalExamination },
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【毛利率%】不能为空',
        },
      ],
    },
    {
      label: '收入',
      fieldName: 'income',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【收入】不能为空',
        },
        { validator: validateCurrency('收入格式错误') },
      ],
      inputProps: { hidden: !contractFormConfig.isCalculateGrossProfit },
    },
    {
      label: colorRed(isContractRedList[0].includes('12'), '税费'),
      fieldName: 'tax',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【税费】不能为空',
        },
        { validator: validatePositive('税费格式错误') },
      ],
      inputProps: {
        hidden: !contractFormConfig.isCalculateGrossProfit,
      },
    },
    {
      label: colorRed(isContractRedList[0].includes('13'), '执行成本'),
      fieldName: 'executionCost',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【执行成本】不能为空',
        },
        {
          validator: validateCurrency('代收代付格式错误。'),
        },
      ],
      inputProps: {
        hidden: !contractFormConfig.isCalculateGrossProfit,
      },
    },
    {
      label: colorRed(isContractRedList[0].includes('14'), '代收代付'),
      fieldName: 'agentBusiness',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【合同审核相关的附件】不能为空',
        },
        {
          validator: validateCurrency('代收代付格式错误。'),
        },
      ],
      inputProps: {
        hidden: !contractFormConfig.isCalculateGrossProfit,
      },
    },
    {
      label: colorRed(isContractRedList[0].includes('11'), '收入'),
      fieldName: 'peIncome',
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【收入】不能为空',
        },
        {
          validator: validateCurrency('收入格式错误。'),
        },
      ],
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
    },
    {
      label: '预估成本',
      fieldName: 'peExecutionCost',
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【合同审核相关的附件】不能为空',
        },
        {
          validator: validateCurrency('预估成本格式错误。'),
        },
      ],
      inputProps: { hidden: !contractFormConfig.isPhysicalExamination },
    },
    {
      label: '付款方式',
      fieldName: 'paymentMode',
      render: (_, record) => paymentModeMap.get(record?.paymentMode),
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【付款方式】不能为空',
        },
      ],
    },
    {
      label: '预付款比例%',
      fieldName: 'advancePaymentRatio',
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
        disabled: !contractFormConfig.isEditeAdvancePaymentRatio,
      },
    },
    {
      label: '开票顺序',
      fieldName: 'whInvoiceOrder',
      render: (_: any, record: any) => (
        <GetSignFlagManualSelect
          // style={{ color: whInvoiceOrderId === '1' ? 'red' : '#000000' }}
          params={{
            type: '9127',
          }}
          code={record?.whInvoiceOrder}
        />
      ),
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '垫付额度',
      fieldName: 'prepayAmt',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '预计垫付时长（天）',
      fieldName: 'whExpectedPrepayDay',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '项目编号',
      fieldName: 'whItemCode',
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '销售发票类型',
      fieldName: 'whSaleInvoiceType',
      render: (_, record) => (
        <GetSignFlagManualSelect
          params={{
            type: '9134',
          }}
          code={record?.whSaleInvoiceType}
        />
      ),
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      rules: [
        {
          required:
            contractFormConfig.isHealthCheckup ||
            contractFormConfig.isWelfareGather ||
            contractFormConfig.isHealthOnther ||
            contractFormConfig.isHealthPlatform,
          message: '【销售发票类型】不能为空',
        },
      ],
    },
    {
      label: '返佣收入',
      fieldName: 'whRebateIncome',
      inputProps: {
        hidden: !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '返佣税费',
      fieldName: 'whRebateTax',
      inputProps: {
        hidden: !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '合同寄送地址',
      fieldName: 'whContractSendAddress',
      inputProps: { hidden: !contractFormConfig.isWelfareGather },
      colNumber: 1,
    },
    {
      label: '采购发票类型',
      fieldName: 'whPurchaseInvoiceType',
      render: (_: any, record: any) => (
        <GetSignFlagManualSelect
          params={{
            type: '9137',
          }}
          code={record?.whPurchaseInvoiceType}
        />
      ),
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '垫付备注',
      fieldName: 'whPrepayRemark',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '销售发票内容',
      fieldName: 'whSaleInvoiceContent',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '采购发票内容',
      fieldName: 'whPurchaseInvoiceContent',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '预付款时间',
      fieldName: 'whAdvancePaymentDt',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '尾款时间',
      fieldName: 'whFinalPaymentDt',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '支付供货商货款时间',
      fieldName: 'whSupplierPaymentDt',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '预付款金额',
      fieldName: 'whAdvancePaymentAmt',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '尾款金额',
      fieldName: 'whFinalPaymentAmt',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '风险分担比例%',
      fieldName: 'riskSharingRatio',
      inputProps: {
        hidden: !contractFormConfig.isRiskRatio,
      },
    },
    {
      label: '风险金比例%',
      fieldName: 'riskPremiumRatio',
      inputProps: {
        hidden: !contractFormConfig.isRiskRatio,
      },
    },
    {
      label: '是否内支',
      fieldName: 'isInternalPayment',
      render: (_: any, record: any) => (
        <Switchs onChange={isInternalPaymentOnChange} code={record?.isInternalPayment} />
      ),
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
    },
    {
      label: '内支金额',
      fieldName: 'internalMoney',
      rules: [
        {
          required: contractFormConfig.internalMoneyNeeded,
          message: '【预付款比例】不能为空',
        },
        {
          validator: validateCurrency('预估成本格式错误。'),
        },
      ],
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
        disabled: !contractFormConfig.internalMoneyNeeded,
      },
    },
    // {
    //   label: '非标合同审批单',
    //   fieldName: 'nonStaCoctApprId',
    //   inputProps: { disabled: !userBranchId || !currentCustId || !currentSales },
    //   inputRender: () => {
    //     // console.log('currentCustId in nonStaCoctApprId:', currentCustId)
    //     return (
    //       <NonStaCoctApprPop
    //         rowValue="nonStaCoctApprId-applyCode"
    //         addonAfter={addonNonStaCoctApprId(!userBranchId)}
    //         fixedValues={{ custId: currentCustId, currentSales }}
    //       />
    //     );
    //   },
    // },
    // {
    //   label: '用章审核意见',
    //   fieldName: 'sealOpinion',
    // },
    // {
    //   label: 'QA审核意见',
    //   fieldName: 'qaApprove',
    // },

    // {
    //   label: '是否有补充附件',
    //   fieldName: 'isAddedAttachment',
    //   rules: [{ required: true, message: '请输入是否有补充附件' }],
    //   render: (_: any, record: any) => <BooleanSelector code={record?.isAddedAttachment} />,
    // },
    // {
    //   label: '合同审核相关的附件',
    //   fieldName: 'approveRelatedAttachment',
    //   inputRender: 'upload',
    //   inputProps: {
    //     maxSize: FileSize._20MB,
    //     fileName: 'approveRelatedAttachmentName',
    //     bizType: '10702000',
    //     disabled: !contractFormConfig.isUploadApproveRelatedAttachment,
    //   },
    //   rules: [
    //     {
    //       required: contractFormConfig.isUploadApproveRelatedAttachment,
    //       message: '【合同审核相关的附件】不能为空',
    //     },
    //   ],
    // },
    // 收入
    {
      label: '本次续签是否需要调整合同条款？',
      fieldName: 'isAdjustRenewContract',
      inputRender: () => <BooleanSelector value="1" />,
      rules: [
        {
          required: contractFormConfig.winIsRenew,
          message: '[本次续签是否需要调整合同条款？]不能为空',
        },
      ],
      inputProps: {
        hidden: !contractFormConfig.winIsRenew,
        disabled: true,
      },
    },
    {
      label: '合同最终结束日期类型',
      fieldName: 'contractEndDateType',
      inputRender: () =>
        mapToSelectors(contractEndDateTypeMap, { onChange: onChangecontractEndDateType }),
      hidden: activityNameEnNot12,
      inputProps: {
        disabled: !contractFormConfig.contractEndDateTypeEnable,
      },
      // rules: [
      //   {
      //     validator: (_: any, value: string) => {
      //       // if (activityNameEnNot12) return Promise.resolve();contractEndDateType
      //       const signFlagManual = mainForm.getFieldValue('signFlagManual');
      //       if (signFlagManual === '4') return Promise.resolve();
      //       if (notEmpty(value)) return Promise.resolve();
      //       return Promise.reject('新签标识不为[补充协议]时，这里必填');
      //     },
      //   },
      // ],
    },
    {
      label: '最终结束日期',
      fieldName: 'contractEndDate',
      hidden: activityNameEnNot12,
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.contractEndDateType !== curValues.contractEndDateType;
      },
      inputRender: (outerForm: FormInstance, options: Partial<EditeFormProps>) => {
        const disabled = outerForm.getFieldValue('contractEndDateType') !== '3';
        return <StrDatePicker disabled={disabled} />;
      },
      rules: [
        {
          validator: (_: any, value: string) => {
            if (activityNameEnNot12) return Promise.resolve();
            const contractEndDateType = mainForm.getFieldValue('contractEndDateType');
            if (contractEndDateType !== '3') return Promise.resolve();
            if (notEmpty(value)) return Promise.resolve();
            return Promise.reject('合同最终结束日期类型[有固定日期]时，这里必填');
          },
        },
      ],
    },
    {
      label: '新签标识',
      fieldName: 'signFlagManual',
      inputRender: () => <SignFlagManualSelector onChange={SignFlagManualSelectoronChange} />,
      inputProps: {
        hidden: activityNameEnNot12,
      },
    },
  ];
  const customRequest = (option: any) => {
    const { file, onError, onSuccess } = option;
    API.minio.minIo.uploadFile
      .request({
        file,
        functionId: funcId,
      })
      .then((res) => {
        onSuccess(res, file);
        setFile({
          supplyShowFileName: file.name,
          supplyShowFilePath: res?.message,
        });
      })
      .catch(onError);
  };
  const onRemove = () => {
    setFile(undefined);
  };
  const showUploadList = {
    showRemoveIcon: true,
    removeIcon: <DeleteOutlined />,
  };
  const exportFile = (minioPath: string, fileName: string) => {
    if (!minioPath || !fileName) {
      return msgErr('无文件下载');
    }
    API.minio.minIo.downloadFile
      .request({ minioPath, fileName }, { responseType: 'blob' })
      .then((res) => {
        if (res) {
          downloadFileWithAlert(res, fileName);
        }
      });
  };
  const isFilePath = () => {
    let flag = false;
    if (activityNameEnIs30) {
      flag = true;
    } else {
      if (currentContract.supplyShowFilePath) {
        flag = false;
      } else {
        flag = true;
      }
    }
    return flag;
  };
  const salesFormColumns: EditeFormProps[] = [
    {
      label: colorRed(isContractRedList[0].includes('4'), '签约人均价格'),
      fieldName: 'contractAvgAmt',
      rules: [
        { required: true, message: '请输入签约人均价格' },
        { validator: validateCurrency('人均价格格式不正确') },
      ],
      inputRender: 'number',
      inputProps: {
        onChange: contractAvgAmtonChange,
        disabled: activityNameEnNot12,
      },
    },
    {
      label: '预计12个月内可达到人数',
      fieldName: 'estimatedHeadcount',
      rules: [{ required: true, message: '请输入预计12个月内可达到人数' }],
    },
    {
      label: '预估首次账单日期',
      fieldName: 'estimateFirstBillDate',
      rules: [{ required: true, message: '请输入预估首次账单日期' }],
    },
    {
      label: '未来商机',
      fieldName: 'furtureOpportunity',
    },
    // {
    //   label: '销售补充附件',
    //   fieldName: '',
    //   inputRender: () => (
    //     <div style={{ display: 'inline-block' }}>
    //       <Upload
    //         customRequest={customRequest}
    //         maxCount={1}
    //         showUploadList={showUploadList}
    //         // onPreview={() => onPreview(type)}
    //         // fileList={fileList}
    //         onRemove={onRemove}
    //       >
    //         <Button>
    //           <UploadOutlined /> 附件{' '}
    //         </Button>
    //       </Upload>
    //     </div>
    //   ),
    //   inputProps: {
    //     hidden: !activityNameEnIs30,
    //     maxSize: FileSize._20MB, // 最大只能传_20MB
    //   },
    // },
    // {
    //   label: '销售补充附件',
    //   fieldName: '',
    //   inputRender: () => (
    //     <Typography.Link
    //       onClick={() =>
    //         exportFile(currentContract.supplyShowFilePath, currentContract?.supplyShowFileName)
    //       }
    //     >
    //       {currentContract?.supplyShowFileName}
    //     </Typography.Link>
    //   ),
    //   inputProps: {
    //     hidden: isFilePath(),
    //   },
    // },
    // {
    //   label: '销售审核意见',
    //   fieldName: 'salesApprove',
    //   br: true,
    //   colNumber: 1,
    // },
    // {
    //   label: '销售补充附件说明（历史）',
    //   fieldName: 'supplyShow',
    //   br: true,
    //   colNumber: 1,
    //   inputProps: {
    //     hidden: !currentContract.supplyShow,
    //   },
    // },
    // {
    //   label: '销售补充附件说明',
    //   fieldName: 'supplyShowNew',
    //   inputRender: 'text',
    //   inputProps: {
    //     disabled: !activityNameEnIs30,
    //     hidden: !activityNameEnIs30,
    //   },
    // },
  ];

  const prepayFormColumns: EditeFormProps[] = [
    {
      label: colorRed(isContractRedList[0].includes('17'), '账单日期(天)'),
      fieldName: 'billDt',
      inputRender: 'number',
      rules: [{ validator: validateNaturalDay() }],
      inputProps: { disabled: activityNameEnNot12 },
    },
    {
      label: colorRed(isContractRedList[0].includes('18'), '约定到款月'),
      fieldName: 'agereedAmtReceiveMon',
      inputRender: () => mapToSelectors(agereedAmtReceiveMonMap),
      inputProps: { disabled: activityNameEnNot12 },
    },
    {
      label: colorRed(isContractRedList[0].includes('19'), '约定到款日(天)'),
      fieldName: 'agreedWageArriveDay',
      rules: [
        // { min: 1, message: '日期应当不得小于1' },
        // { min: 31, message: '日期应当不得大于31' },
        { validator: validateNaturalDay() },
        { required: true, message: '请输入约定到款日(天)' },
      ],
      inputRender: () => {
        return <InputNumber onChange={onAgreedWageArriveDayChange} />;
      },
      inputProps: { disabled: activityNameEnNot12 },
    },
    {
      label: '账期（天）',
      fieldName: 'creditPeriod',
      rules: [{ validator: validateNaturalNumber('账期（天）应为自然数') }],
      inputRender: 'string',
      inputProps: { disabled: activityNameEnNot12 },
    },
    {
      label: colorRed(isContractRedList[0].includes('21'), '薪资发放月'),
      fieldName: 'payMonth',
      inputRender: () => mapToSelectors(payMonthMapMap),
      inputProps: { disabled: activityNameEnNot12 },
      rules: [
        {
          required: activityNameEnIs12 && contractFormConfig.isIssuingSalary,
          message: '当[代发薪资]为是时，薪资发放月必填',
        },
      ],
    },
    {
      label: colorRed(isContractRedList[0].includes('22'), '薪资发放日'),
      fieldName: 'agreedPayDt',
      inputRender: 'number',
      rules: [{ validator: validateNaturalDay() }],
      inputProps: { disabled: activityNameEnNot12 },
    },
    {
      label: colorRed(isContractRedList[0].includes('20'), '垫款额度'),
      fieldName: 'prepayAmt',
      rules: [{ validator: validateCurrency('垫款额度格式不正确') }],
      inputRender: 'string',
      inputProps: { disabled: activityNameEnNot12 },
    },
    // {
    //   label: '垫付审核意见',
    //   fieldName: 'prepayApproval',
    // },
  ];

  const productLineColumns: WritableColumnProps<any>[] = [
    {
      title: '产品线名称',
      dataIndex: 'productlineName',
      // render: (_: any, record: any) => {
      //   const params = { custId: currentCustId, saleId: currentSales };
      //   return (
      //     <CustProductLineSelector
      //       code={record?.productlineId}
      //       params={{
      //         ...params,
      //       }}
      //     />
      //   );
      // },
      render: (value, record) => {
        const id = record.smcontractProductlineId;
        const data: any = producttColorList[0].find((item: any) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && data?.returnType === '1') {
          return colorRed(true, value);
        } else {
          return value;
        }
      },
      rules: [{ required: true, message: '请输入产品线名称' }],
    },
    {
      title: '签约人数',
      dataIndex: 'compactNumber',
      // inputRender: (options: GeneralInputRenderOption<any>) => {
      //   if (activityNameEnIs12) return 'number';
      //   return options.record.compactNumber;
      // },
      render: (value, record) => {
        const id = record.smcontractProductlineId;
        const data = producttColorList[0].find((item) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && (data?.returnType === '2' || data?.returnType === '4')) {
          return colorRed(true, value);
        } else {
          return value;
        }
      },
      rules: [
        { required: true, message: '请输入签约人数' },
        { validator: validateNaturalPositive(), message: '签约人数格式错误' },
      ],
    },
    {
      title: '人均销售收入',
      dataIndex: 'averageMoney',
      // inputRender: 'number',
      render: (value, record) => {
        const id = record.smcontractProductlineId;
        const data = producttColorList[0].find((item) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && (data?.returnType === '3' || data?.returnType === '4')) {
          return colorRed(true, value);
        } else {
          return value;
        }
      },
      inputProps: { disabled: true },
      rules: [{ required: true, message: '请输入人均销售收入' }],
    },
  ];

  const csFormColumns: EditeFormProps[] = [
    {
      label: '指定交接客服',
      fieldName: 'liabilityCsName',
      // 没有 winIsCs 这个流程了
    },
    {
      label: '指定交接事项',
      fieldName: 'transferRemark',
    },
    // {
    //   label: '客服审核意见',
    //   fieldName: 'csApproval',
    // },
  ];

  const quotationColumns: WritableColumnProps<any>[] = [
    {
      title: '报价单编号',
      dataIndex: 'quotationCode',
      render: (value, record) => {
        const id = record.quotationCode;
        const data: any = quotationColorList[0].find((item) => {
          return id === item?.QUOTATION_CODE;
        });
        if (!isEmpty(data)) {
          if (data?.returnType && data?.returnType === '1') {
            return colorRed(true, value);
          }
        } else {
          return value;
        }
      },
    },
    {
      title: '报价单名称',
      dataIndex: 'quotationName',
    },
    { title: '预计签约人数', dataIndex: 'suppltMedInsurHeadCount' },
    { title: '备注', dataIndex: 'remark' },
    { title: '售价', dataIndex: 'quotationTotalSalPrice' },
    { title: '职场健康总售价', dataIndex: 'quotationTotalOhPrice' },
    {
      title: '报价单状态',
      dataIndex: 'status',
      render: (outerForm?: FormInstance, record?: Store) => QuostatusMap.get(record?.status),
    },
    {
      title: '查看明细',
      dataIndex: 'view',
      render: (text: string, record: any) => (
        <Link onClick={() => viewQuotation(record.quotationId)}>查看</Link>
      ),
    },
  ];

  const legalFormColumns: EditeFormProps[] = [
    {
      label: '首个法务',
      fieldName: 'firstLegalApproveId',
      inputRender: () => (
        <InnerUserPop
          rowValue="firstLegalApproveId-firstLegalApproveName"
          keyMap={{ firstLegalApproveId: 'EMPID', firstLegalApproveName: 'REALNAME' }}
          fixedValues={{ roleCode: 60095 }}
        />
      ),
      inputProps: { disabled: true },
    },
    // {
    //   label: '合同附件',
    //   fieldName: 'importFileId',
    //   render: (outerForm?: FormInstance, record?: any) => (
    //     <Typography.Link
    //       onClick={() => downloadFileWithId(record?.importFileId, record?.importFileName)}
    //     >
    //       {record?.importFileName}
    //     </Typography.Link>
    //   ),
    //   inputProps: { editable: true },
    // },
    // {
    //   label: '附件上传时间',
    //   fieldName: 'contractFileUploadDt',
    // },
    {
      label: '合同附件备注',
      fieldName: 'contractFileRemark',
    },
    // {
    //   label: '法务审核意见',
    //   fieldName: 'legalApproval',
    // },
  ];

  const custPayerColumns: WritableColumnProps<any>[] = [
    { title: '付款方编号', dataIndex: 'custPayerId' },
    { title: '付款方名称', dataIndex: 'payerName' },
    {
      title: '发票抬头',
      dataIndex: 'checkTitle',
    },
  ];
  const approveRejectColumns: WritableColumnProps<any>[] = [
    { title: '步骤', dataIndex: 'activityNameCn' },
    {
      title: '驳回原因',
      dataIndex: 'reasonStr',
      // inputRender: () => {
      //   return <CommonBaseDataSelector params={{ type: '36505' }} allowClear />;
      // },
      // rules: [{ required: true, message: '驳回原因必填' }],
    },
    {
      title: '驳回备注',
      dataIndex: 'reasonBz',
      render: (value) => {
        return (
          <Tooltip placement="left" title={value}>
            <span>{value}</span>
          </Tooltip>
        );
      },
      // inputRender: 'string',
      // rules: [{ required: true, message: '驳回备注必填' }],
    },
    { title: '操作人', dataIndex: 'createByStr' },
    { title: '创建时间', dataIndex: 'createDt' },
    { title: '驳回批次', dataIndex: 'disaBatchId' },
  ];
  const itemApproveCulumns: WritableColumnProps<any>[] = [
    { title: '步骤', dataIndex: 'activityNameCn' },
    {
      title: '驳回原因',
      dataIndex: 'reasonId',
      inputRender: (options) => {
        const { serial } = options;
        return (
          <CommonBaseDataSelector
            onConfirm={(data) => {
              writableApproveReject.setFieldsValue(serial, {
                ...options.record,
                reasonId: data.key || '',
                reasonStr: data.shortName || '',
              });
            }}
            params={{ type: '36505' }}
            allowClear
          />
        );
      },
      rules: [{ required: true, message: '驳回原因必填' }],
    },
    {
      title: '驳回备注',
      dataIndex: 'reasonBz',
      inputRender: 'text',
      rules: [{ required: true, message: '驳回备注必填' }],
    },
    { title: '操作人', dataIndex: 'createByStr' },
    { title: '创建时间', dataIndex: 'createDt' },
  ];
  const approvalMemoFormColumns: EditeFormProps[] = [
    {
      label: '历史审批意见',
      fieldName: 'salesApprove',
      br: true,
      inputProps: { disabled: true },
      inputRender: 'text',
      colNumber: 1,
    },
    {
      label: '审批意见',
      fieldName: 'approveOpinion',
      inputRender: 'text',
      rules: [
        {
          required: activityNameEnIsApprove,
          message: '审批意见必填',
        },
      ],
    },
  ];

  const addcustPayer = () => {
    if (readOnly) return;
    writableCustPayer.addRows();
  };
  const addApproveReject = () => {
    const { all: list } = writableApproveReject.getList();
    if (list.length > 9) {
      return msgErr('仅能添加十条数据');
    }
    writableApproveReject.addRows({
      contractId: contractId,
      reasonId: '1',
      reasonStr: '缺少附件',
      workitemId: contract.workitemId,
    });
  };
  const deleteApproveReject = () => {
    const selectedRows = writableApproveReject?.selectedRows;
    if (!selectedRows.length) {
      msgErr('请先选择要删除的数据');
      return;
    } else {
      // 如果是新增的数据直接删除本地数据，如果是远程数据则请求接口删除
      const result = selectedRows
        .map((value) => {
          if (value.disaReasonId) {
            return value;
          }
        })
        .filter((item) => item);
      const localResult = selectedRows
        .map((value) => {
          if (!value.disaReasonId) {
            return value;
          }
        })
        .filter((item) => item);
      Modal.confirm({
        title: '',
        icon: <ExclamationCircleOutlined />,
        content: `选中${selectedRows.length}行数据,将进行删除，是否继续？`,
        onOk() {
          if (result.length > 0) {
            API.crm.slDisaReason.deleteById.requests(result).then(() => {
              msgOk('删除成功');
              writableApproveReject.request({ ...contract });
            });
          }
          if (localResult.length > 0) {
            writableApproveReject.deleteRows(localResult);
          }
        },
      });
    }
  };
  const saveApproveReject = async () => {
    const { added, updated, all } = await writableApproveReject.validateFields();
    if (all.length < 1) {
      return msgErr('当前没有可以保存的数据');
    }
    await API.crm.slDisaReason.getContractList.requests({
      contractId: contractId,
      disaBatchId: contract.disaBatchId,
      activityNameEn: contract.activityNameEn,
      slDisaReasonList: all,
      addSlDisaReasonList: added,
      uptSlDisaReasonList: updated,
    });
    msgOk('保存成功');
    writableApproveReject.request({ ...contract });
  };
  const deletecustPayer = () => {
    if (readOnly) return;
    writableCustPayer.deleteRows(writableCustPayer.selectedRows);
  };
  // console.log('currentContract in QueryContractDetailInfoWin:', currentContract)
  // console.log('importFile in QueryContractDetailInfoWin:', importFile)
  const rejectApprove = async () => {
    const formData = await collecteFullContract('reject');
    const { added, updated, all } = await writableApproveReject.validateFields();
    if (all.length < 1) {
      return msgErr('当前没有可以保存的数据');
    }
    setLoadingData(true);
    setRejectLoading(true);
    await API.crm.slDisaReason.getContractList.requests({
      contractId: contractId,
      activityNameEn: contract.activityNameEn,
      slDisaReasonList: all,
      disaBatchId: contract.disaBatchId,
      addSlDisaReasonList: added,
      uptSlDisaReasonList: updated,
    });

    if (rejectTitle === '需要销售补充信息') {
      const list = await writableApproveReject.request({ ...contract });

      const formData = await collecteFullContract('approve');
      const { disaBatchId } = await API.crm.contractManage.getContractById.requests({
        contractId: contractId,
      });
      if (list.list.length < 1) {
        setLoadingData(false);
        setRejectLoading(false);
        return msgErr('最少需要添加一条驳回原因的数据');
      }
      // setLoadingData(true);
      try {
        if (activityNameEnIs12) {
          await API.crm.contractManage.approveSaleUploadFileBackPrevious.requests({
            ...formData,
            supplyMark: '0',
            contractCreateBy: contract?.createBy,
            slDisaReasonList: list.list,
            disaBatchId: disaBatchId,
          });
        } else {
          await API.crm.contractManage.approve.requests({
            ...formData,
            supplyMark: '0',
            contractCreateBy: contract?.createBy,
            slDisaReasonList: list.list,
            disaBatchId: disaBatchId,
          });
        }
      } catch (e) {
        setLoadingData(false);
        setRejectLoading(false);
        throw e;
      }
      msgOk('通过此合同的请求成功');
      setLoadingData(false);
      setRejectLoading(false);
      setRejectVisible(false);
      setVisible(false);
      onConfirm();
    } else {
      const list = await writableApproveReject.request({ ...contract });
      const { disaBatchId } = await API.crm.contractManage.getContractById.requests({
        contractId: contractId,
      });
      if (list.list.length < 1) {
        setLoadingData(false);
        setRejectLoading(false);
        return msgErr('最少需要添加一条驳回原因的数据');
      }
      // setLoadingData(true);
      try {
        await API.crm.contractManage.back.requests({
          ...formData,
          slDisaReasonList: list.list,
          disaBatchId: disaBatchId,
        });
      } catch (e) {
        setLoadingData(false);
        throw e;
      }
      msgOk('驳回此合同的请求成功');
      setLoadingData(false);
      setRejectLoading(false);
      setRejectVisible(false);
      setVisible(false);
      onConfirm();
    }
  };
  const collecteFullContract = async (
    action: 'approve' | 'save' | 'reject' | 'terminate',
    status?: any,
  ) => {
    let mainData: Store;
    let salesData: Store;
    let legalFormData: Store;
    let prepayFormData: Store;
    let quotations: POJO;
    let productLines: POJO;
    let custPayers: POJO;
    let approvalMemoFormData: Store;
    let importData: Store;
    let retireData: Store;
    const tabErrs = { ...tabErrors };

    if (action !== 'approve' && action !== 'save') {
      mainData = mainForm.getFieldsValue();
      salesData = salesForm.getFieldsValue();
      legalFormData = legalForm.getFieldsValue();
      prepayFormData = prepayForm.getFieldsValue();

      quotations = writableQuotation.getList();
      productLines = writableProductLine.getList();
      custPayers = writableCustPayer.getList();
      approvalMemoFormData = approvalMemoForm.getFieldsValue();
      importData = writableUpload.getList();
      retireData = writableRetire.getList();
    } else {
      try {
        mainData = await mainForm.validateFields();
        tabErrs.mainForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, mainForm: true });
        throw e;
      }

      try {
        salesData = await salesForm.validateFields();
        tabErrs.salesForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, salesForm: true });
        throw e;
      }

      try {
        legalFormData = await legalForm.validateFields();
        tabErrs.legalForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, legalForm: true });
        throw e;
      }

      try {
        prepayFormData = await prepayForm.validateFields();
        tabErrs.prepayForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, prepayForm: true });
        throw e;
      }

      quotations = await writableQuotation.validateFields();
      productLines = await writableProductLine.validateFields();
      custPayers = await writableCustPayer.validateFields();
      approvalMemoFormData = await approvalMemoForm.validateFields();
      importData = await writableUpload.validateFields();
      retireData = await writableRetire.validateFields();
    }

    if (quotations.visibleCount === 0 || productLines.visibleCount === 0) {
      msgErr('报价单产品线都至少需要一条');
      return Promise.reject();
    }
    let fileListNum3 = 0;
    let fileListNum1 = 0;
    let fileListNum2 = 0;
    let fileListNum6 = 0;
    let fileListNum7 = 0;
    let fileListNum9 = 0;
    let fileListNumNew = 0;
    for (const item of importData.visible) {
      if (item?.fileType === '3') {
        fileListNum3 += 1;
      }
      if (item?.fileType === '1') {
        fileListNum1 += 1;
      }
      if (item?.fileType === '6') {
        fileListNum6 += 1;
      }
      if (item?.fileType === '7') {
        fileListNum7 += 1;
      }
      if (item?.fileType === '2') {
        fileListNum2 += 1;
      }

      if (item?.fileType === '9') {
        fileListNum9 += 1;
      }
      if (!item?.createDt) {
        fileListNumNew += 1;
      }
    }
    if (action !== 'reject') {
      if (mainData.contractSubType !== '503') {
        if (fileListNum1 < 1 && fileListNum3 < 1) {
          msgErr('合同小类非体检时，合同文件或者补充协议必填一个。');
          return Promise.reject();
        }
      }
      if (contract.activityNameEn === '8') {
        if (fileListNum6 < 1) {
          msgErr('相关附件，用印完成后销售环节必须上传一个盖章版合同文件附件。');
          return Promise.reject();
        }
      }
      if (contract.activityNameEn === '12') {
        if (fileListNum7 < 1) {
          msgErr('相关附件，补充说明文件至少填写一项。');
          return Promise.reject();
        }
      }
      if (contract?.processDefId === '910001782') {
        if (contract.activityNameEn === '3') {
          if (status === 'approve' && mainData.contractSubType === '503') {
            if (fileListNum2 < 1) {
              msgErr('相关附件，销售支持环节必须上传一个项目审批表附件。');
              return Promise.reject();
            }
          }
        }
      }

      if (mainData.isRetirementBusiness === '1') {
        if (fileListNum9 < 1) {
          msgErr('相关附件，退休相关业务时必有必须上传一个包含退休人员列表附件。');
          return Promise.reject();
        }
      }
      if (contract.activityNameEn === '8' || contract.activityNameEn === '12') {
        if (fileListNumNew < 1) {
          msgErr('销售补充信息/销售上传附件，这两个步骤一定要至少新上传一个附件');
          return Promise.reject();
        }
      }
    }

    let payerIds: string | undefined = undefined;
    if (custPayers.allCount > 0) {
      // TODO: 这里原码是Array
      payerIds = custPayers.visible.map((cust) => cust.custPayerId).join(',');
    }
    const quoIds = quotations.visible.map((quot) => quot.quotationId).join(',');
    // setCurrentContarct({ ...currentContract, quotationIds: quotationIds.join(',') })
    const contractProductLineIds: string[] = [];
    const productLineIds: string[] = [];
    const compactNumbers: string[] = [];
    const averageMoneys: string[] = [];
    productLines.visible.forEach((line) => {
      contractProductLineIds.push(line.smcontractProductlineId);
      productLineIds.push(line.productlineId);
      compactNumbers.push(line.compactNumber);
      averageMoneys.push(line.averageMoney);
    });
    const productLineIdLogs = productLineIds.join(',');
    const formData = {
      ...currentContract,
      ...mainData,
      ...salesData,
      ...legalFormData,
      ...prepayFormData,
      ...approvalMemoFormData,
      ...(mainData?.travelServicesRatio && {
        travelServicesRatio: mainData?.travelServicesRatio / 100,
      }),
      quoIds,
      contractProductLineIds: contractProductLineIds.join(','),
      productLineIdLogs,
      productLineIds: productLineIdLogs,
      compactNumbers: compactNumbers.join(','),
      averageMoneys: averageMoneys.join(','),
      contractFileList: importData.visible,
      contractRetireeList: retireData.visible,
    };
    if (payerIds) formData.payerIds = payerIds;
    if (contractTypeNameMap.hasOwnProperty(formData.contractType)) {
      const contractTypeCode = contractTypeNameMap[formData.contractType];
      formData.contractTypeName = formData.contractType;
      formData.contractType = contractTypeCode;
    }
    if (contractSubTypeNameMap.hasOwnProperty(formData.contractSubType)) {
      const contractSubTypeCode = contractSubTypeNameMap[formData.contractSubType];
      formData.contractSubTypeName = formData.contractSubType;
      formData.contractSubType = contractSubTypeCode;
    }
    return formData;
  };
  const collecteFullContract3 = async (action: 'approve' | 'reject' | 'terminate') => {
    const values =
      action === 'approve'
        ? await Promise.all([
            mainForm.validateFields(),
            salesForm.validateFields(),
            legalForm.validateFields(),
            prepayForm.validateFields(),
            writableQuotation.validateFields(),
            writableProductLine.validateFields(),
            writableCustPayer.validateFields(),
            approvalMemoForm.validateFields(),
          ])
        : [
            mainForm.getFieldsValue(),
            salesForm.getFieldsValue(),

            legalForm.getFieldsValue(),
            prepayForm.getFieldsValue(),
            writableQuotation.getList(),
            writableProductLine.getList(),
            writableCustPayer.getList(),
            approvalMemoForm.getFieldsValue(),
          ];

    const [
      mainData,
      salesData,
      legalFormData,
      prepayFormData,
      quotations,
      productLines,
      custPayers,
      approvalMemoFormData,
    ] = values;
    if (quotations.visibleCount === 0 || productLines.visibleCount === 0) {
      msgErr('报价单产品线都至少需要一条');
      return Promise.reject();
    }

    let payerIds: string | undefined = undefined;
    if (custPayers.allCount > 0) {
      // TODO: 这里原码是Array
      payerIds = custPayers.visible.map((cust) => cust.custPayerId).join(',');
    }
    const quoIds = quotations.visible.map((quot) => quot.quotationId).join(',');
    // setCurrentContarct({ ...currentContract, quotationIds: quotationIds.join(',') })
    const contractProductLineIds: string[] = [];
    const productLineIds: string[] = [];
    const compactNumbers: string[] = [];
    const averageMoneys: string[] = [];
    productLines.visible.forEach((line) => {
      contractProductLineIds.push(line.smcontractProductlineId);
      productLineIds.push(line.productlineId);
      compactNumbers.push(line.compactNumber);
      averageMoneys.push(line.averageMoney);
    });
    const productLineIdLogs = productLineIds.join(',');
    const formData = {
      ...currentContract,
      ...mainData,
      ...salesData,
      ...legalFormData,
      ...prepayFormData,
      ...approvalMemoFormData,
      quoIds,
      contractProductLineIds: contractProductLineIds.join(','),
      productLineIdLogs,
      productLineIds: productLineIdLogs,
      compactNumbers: compactNumbers.join(','),
      averageMoneys: averageMoneys.join(','),
    };
    if (payerIds) formData.payerIds = payerIds;
    if (contractTypeNameMap.hasOwnProperty(formData.contractType)) {
      const contractTypeCode = contractTypeNameMap[formData.contractType];
      formData.contractTypeName = formData.contractType;
      formData.contractType = contractTypeCode;
    }
    if (contractSubTypeNameMap.hasOwnProperty(formData.contractSubType)) {
      const contractSubTypeCode = contractSubTypeNameMap[formData.contractSubType];
      formData.contractSubTypeName = formData.contractSubType;
      formData.contractSubType = contractSubTypeCode;
    }
    return formData;
  };

  const saveCrm = async () => {
    const formData = await collecteFullContract('save');
    if (activityNameEnIs12) {
      if (!formData.agereedAmtReceiveMon) {
        formData.agereedAmtReceiveMon = null;
      }
      if (!formData?.contractEndDateType && formData?.signFlagManual !== '4') {
        return msgErr('保存时，新签标识不为[补充协议]时，合同最终结束日期类型必填');
      }
      if (!formData.payMonth) {
        formData.payMonth = null;
      }
    }
    setLoadingData(true);

    try {
      await Promise.all([
        API.crm.contractManage.save.requests(formData),
        API.crm.contractManage.saveBizFieldLog.requests(formData),
      ]);
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('保存合同成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };
  const simplicity = async () => {
    const formData = await collecteFullContract('approve');
    Modal.confirm({
      content: `将执行简约流程，是否继续？`,
      onOk: async () => {
        setLoadingData(true);
        try {
          await API.crm.contractManage.approve.requests({ ...formData, isNormalApprove: 0 });
        } catch (e) {
          setLoadingData(false);
          throw e;
        }
        msgOk('通过此合同的请求成功');
        setLoadingData(false);
        setVisible(false);
        onConfirm();
      },
    });
  };
  const normal = async () => {
    const formData = await collecteFullContract('approve');
    Modal.confirm({
      content: `将执行较长的正常审批流程，是否确定？`,
      onOk: async () => {
        setLoadingData(true);
        try {
          await API.crm.contractManage.approve.requests({ ...formData, isNormalApprove: 1 });
        } catch (e) {
          setLoadingData(false);
          throw e;
        }
        msgOk('通过此合同的请求成功');
        setLoadingData(false);
        setVisible(false);
        onConfirm();
      },
    });
  };
  const approve = async () => {
    const formData = await collecteFullContract('approve', 'approve');
    const itemValues = await approvalMemoForm.getFieldValue('approveOpinion');
    if (!itemValues) {
      return msgErr('请填写审批意见');
    }
    if (activityNameEnIs12) {
      if (!formData.agereedAmtReceiveMon) {
        formData.agereedAmtReceiveMon = null;
      }
      if (!formData?.contractEndDateType && formData?.signFlagManual !== '4') {
        return msgErr('审批通过时，新签标识不为[补充协议]时，合同最终结束日期类型必填');
      }
      if (!formData.payMonth) {
        formData.payMonth = null;
      }
    }
    setLoadingData(true);
    try {
      await API.crm.contractManage.approve.requests({
        ...formData,
        contractCreateBy: contract?.createBy,
      });
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('通过此合同的请求成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };
  const confirmApprove = async () => {
    const formData = await collecteFullContract('approve');
    const formValues = { ...formData };
    // if (!file?.supplyShowFilePath && !formData?.supplyShowNew) {
    //   return msgErr('补充附件或者补充附件说明至少填写一项');
    // }
    setLoadingData(true);
    try {
      await API.crm.contractManage.approveBackPrevious.requests(formValues);
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('通过此合同的请求成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };
  const handleSalesInfo = async () => {
    if (title) {
      setRejectVisible(true);
      setRejectTitle('需要销售补充信息');
      writableApproveReject.request({ ...contract });
      return;
    }
    const formData = await collecteFullContract('approve');
    setLoadingData(true);
    try {
      await API.crm.contractManage.approve.requests({
        ...formData,
        supplyMark: '0',
        contractCreateBy: contract?.createBy,
      });
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('通过此合同的请求成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };
  const reject = async () => {
    if (activityNameEnIsItem || activityNameEnIs31) {
      // const formData = await collecteFullContract('reject');
      // const list = await API.crm.slDisaReason.queryByPage.requests({
      //   contractId,
      // });
      writableApproveReject.request({ ...contract });
      setRejectVisible(true);
      setRejectTitle('驳回');
      return;
    }
    const formData = await collecteFullContract('reject');
    setLoadingData(true);
    try {
      await API.crm.contractManage.back.requests(formData);
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('驳回此合同的请求成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };

  const terminate = async () => {
    const formData = await collecteFullContract('terminate');
    setLoadingData(true);
    try {
      await API.crm.contractManage.terminal.requests(formData);
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('终止此合同的请求成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };
  const ImportColumns: WritableColumnProps<any>[] = [
    { title: '附件类型', dataIndex: 'fileTypeName' },
    // { title: '附件名称', dataIndex: 'fileTypeName' },
    { title: '备注', dataIndex: 'remark' },
    // { title: '上传步骤', dataIndex: 'fileTypeName' },
    { title: '上传人', dataIndex: 'createBy' },
    { title: '上传时间', dataIndex: 'createDt' },
    {
      title: '下载',
      dataIndex: 'filePath',
      render: (text, record) => {
        return (
          <Link onClick={() => exportImportFile(record.filePath, record.fileName)}>
            {record.fileName ? record.fileName : ''}
          </Link>
        );
      },
    },
    {
      title: '预览',
      dataIndex: '',
      render: (text, record) => {
        return (
          <Link onClick={() => filePreview(record.filePath, record.fileName)}>
            {record.fileName ? record.fileName : ''}
          </Link>
        );
      },
    },
  ];
  const filePreview = (fileId: string, fileName: string) => {
    if (!fileId || !fileName) {
      return msgErr('无文件预览');
    }
    API.minio.minIo.downloadFile
      .request({ minioPath: fileId, fileName }, { responseType: 'blob' })
      .then((res) => {
        if (res) {
          setPreviewFileType(getFileExtension(fileId));
          const url = URL.createObjectURL(res);
          setPreviewUrl(url);
          setPreviewFile(res);
          previewModal[1](true);
        }
      });
  };
  const exportImportFile = (fileId: string, fileName: string) => {
    if (!fileId || !fileName) {
      return msgErr('无文件下载');
    }
    API.minio.minIo.downloadFile
      .request({ minioPath: fileId, fileName }, { responseType: 'blob' })
      .then((res) => {
        if (res) {
          downloadFileWithAlert(res, fileName);
        }
      });
  };
  const retireColumns: WritableColumnProps<any>[] = [
    { title: '证件号码', dataIndex: 'idCardNum', inputRender: 'string' },
    {
      title: '姓名',
      dataIndex: 'empName',
      inputRender: 'string',
      rules: [{ required: true, message: '请填写姓名' }],
    },
    {
      title: '备注',
      dataIndex: 'bz',
      inputRender: 'string',
    },
  ];
  const onImport = (option: any) => {
    const { file } = option;
    setRetireFileFile({
      ...retireFile,
      fileName: file?.name,
    });
  };
  const handleFileType = (v, o) => {
    setRetireFileFile({ ...retireFile, fileTypeName: o.title });
  };
  let filelist: any[] = [];
  const customFileRequest = async (option: any) => {
    const { file, onError, onSuccess } = option;
    await API.minio.minIo.uploadFile
      .request({
        file,
        functionId: '10702000',
      })
      .then((res) => {
        filelist = filelist.concat({
          fileName: file.name,
          filePath: res?.message,
        });
        onSuccess(res, file);
        setFileData(filelist);
      })
      .catch(onError);
  };
  const onFileRemove = (file) => {
    const list = fileData.filter((item) => item.fileName !== file.name);
    setFileData(list);
  };
  const ImportFormColumns: EditeFormProps[] = [
    {
      label: '附件类型',
      fieldName: 'fileType',
      rules: [{ required: true, message: '请选择附件类型' }],
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9132',
          }}
          onChange={(value, option) => handleFileType(value, option)}
        />
      ),
      colNumber: 3,
    },
    {
      label: '备注',
      fieldName: 'remark',
      inputRender: 'text',
    },
    {
      label: '上传文件',
      fieldName: 'upload',
      rules: [{ required: true, message: '请选择文件' }],
      inputRender: () => (
        <div style={{ display: 'inline-block' }}>
          <Upload
            customRequest={customFileRequest}
            maxCount={10}
            showUploadList={showUploadList}
            // onPreview={() => onPreview(type)}
            // fileList={fileList}
            multiple
            onRemove={onFileRemove}
          >
            <Button>
              <UploadOutlined /> 附件{' '}
            </Button>
          </Upload>
        </div>
      ),
    },
  ];
  const ImportFormColumns1: EditeFormProps[] = [
    {
      label: '上传文件',
      fieldName: 'upload',
      rules: [{ required: true, message: '请选择文件' }],
      inputRender: 'upload',
      inputProps: {
        maxSize: FileSize._20MB, // 最大只能传_20MB
        bizType: '10702000',
        onChange: onImport,
      },
    },
  ];
  const onImportBtn = (type: string) => {
    setImportType(type);
    setOnShowImport(true);
  };
  const importHandle = async (data: POJO) => {
    if (fileData?.length < 1) return msgErr('请选择文件');
    if (importType === '1') {
      const fileList: any[] = [];
      fileData.forEach((item) => {
        fileList.push({ ...item, ...data, ...retireFile, activityNameEn: contract.activityNameEn });
      });
      await writableUpload.addRows(fileList);
      setFile(undefined);
    }
    setOnShowImport(false);
  };
  const onDelImport = (type: string) => {
    // setImportType(type);
    const itemData = writableUpload.selectedSingleRow;
    if (contract.activityNameEn !== itemData?.activityNameEn) {
      return msgErr('仅能删除当前步骤上传的文件');
    }
    writableUpload.deleteRows(writableUpload.selectedSingleRow);
  };
  const productLineHealthColumns: WritableColumnProps<any>[] = [
    {
      title: '产品线名称',
      dataIndex: 'productlineName',
      // render: (_: any, record: any) => {
      //   const params = { custId: currentCustId, saleId: currentSales };
      //   return (
      //     <CustProductLineSelector
      //       code={record?.productlineId}
      //       params={{
      //         ...params,
      //       }}
      //     />
      //   );
      // },
      render: (value, record) => {
        const id = record.smcontractProductlineId;
        const data: any = producttColorList[0].find((item: any) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && data?.returnType === '1') {
          return colorRed(true, value);
        } else {
          return value;
        }
      },
      rules: [{ required: true, message: '请输入产品线名称' }],
    },
    {
      title: '签约人数',
      dataIndex: 'compactNumber',
      // inputRender: (options: GeneralInputRenderOption<any>) => {
      //   if (activityNameEnIs12) return 'number';
      //   return options.record.compactNumber;
      // },
      render: (value, record) => {
        const id = record.smcontractProductlineId;
        const data = producttColorList[0].find((item) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && (data?.returnType === '2' || data?.returnType === '4')) {
          return colorRed(true, value);
        } else {
          return value;
        }
      },
      rules: [
        { required: true, message: '请输入签约人数' },
        { validator: validateNaturalPositive(), message: '签约人数格式错误' },
      ],
    },
    {
      title: '金额',
      dataIndex: 'averageMoney',
      // inputRender: 'number',
      render: (value, record) => {
        const id = record.smcontractProductlineId;
        const data = producttColorList[0].find((item) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && (data?.returnType === '3' || data?.returnType === '4')) {
          return colorRed(true, value);
        } else {
          return value;
        }
      },
      inputProps: { disabled: true },
      rules: [{ required: true, message: '请输入人均销售收入' }],
    },
  ];
  const onBatchDownload = async () => {
    const rows = writableUpload.selectedRows;
    const minioPaths = rows.map((item) => {
      return { filePath: item.filePath, fileName: item.fileName };
    });
    if (minioPaths.length < 1) return msgErr('请选择文件后再进行操作');
    const res = await API.minio.minIo.downloadFilesAsZipStream.request(
      { minioPaths },
      { params: { minioPaths }, responseType: 'blob' },
    );
    if (!res) {
      msgErr('导出数据失败');
      return;
    }
    if (minioPaths.length === 1) {
      const data = minioPaths[0];
      downloadFileWithAlert(res, `${data.fileName}.zip`);
    } else {
      downloadFileWithAlert(res, '相关附件.zip');
    }
  };
  return (
    <Codal
      title={title}
      visible={visible}
      onCancel={() => setVisible(false)}
      width="80%"
      loading={loadingData}
      footer={
        <RowElementButton>
          {contract.activityNameEn === '11' || activityNameEnIs12 ? (
            <AsyncButton onClick={handleSalesInfo}>需要销售补充信息</AsyncButton>
          ) : null}
          {activityNameEnIs30 && <AsyncButton onClick={confirmApprove}>提交再审批</AsyncButton>}
          {activityNameEnIs31 && (
            <AsyncButton type="primary" onClick={simplicity}>
              {' '}
              简约流程{' '}
            </AsyncButton>
          )}
          {!activityNameEnIs30 && !activityNameEnIs31 && (
            <AsyncButton type="primary" onClick={approve}>
              审批通过
            </AsyncButton>
          )}
          {/* {activityNameEnIs30 ? (
            <AsyncButton onClick={confirmApprove}>提交再审批</AsyncButton>
          ) : (
            <AsyncButton type="primary" onClick={approve}>
              审批通过
            </AsyncButton>
          )} */}
          <AsyncButton hidden={!activityNameEnIsItem} onClick={saveCrm}>
            保存
          </AsyncButton>
          {activityNameEnIs31 ? <AsyncButton onClick={normal}>正常审批</AsyncButton> : null}
          {additional?.back ? <AsyncButton onClick={reject}>驳回</AsyncButton> : null}

          {additional?.terminal ? <AsyncButton onClick={terminate}>终止</AsyncButton> : null}

          <Button onClick={() => setVisible(false)}>取消</Button>
        </RowElementButton>
      }
    >
      <Tabs type="card">
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.mainForm}>合同基本信息</AlertTab>}
          key={'1'}
        >
          <FormElement3 form={mainForm}>
            <EnumerateFields
              // disabled={readOnly}
              record={currentContract}
              outerForm={mainForm}
              colNumber={3}
              formColumns={formColumns}
            />
          </FormElement3>
        </TabPane>
        {contractType !== '5' ? (
          <TabPane
            forceRender
            tab={
              <AlertTab
                onError={
                  tabErrors.salesForm ||
                  tabErrors.csForm ||
                  tabErrors.legalForm ||
                  tabErrors.prepayForm
                }
              >
                业务部门
              </AlertTab>
            }
            key={'2'}
          >
            <Group title="销售相关">
              <FormElement3 form={salesForm} initialValues={currentContract}>
                <EnumerateFields
                  record={currentContract}
                  outerForm={salesForm}
                  colNumber={3}
                  formColumns={salesFormColumns}
                />
              </FormElement3>
            </Group>
            <Group title="客服相关">
              <DetailForm detailData={currentContract} colNumber={3} formColumns={csFormColumns} />
            </Group>
            <Group title="法务相关">
              <FormElement3 form={legalForm} initialValues={{ ...currentContract, ...importFile }}>
                <EnumerateFields
                  record={currentContract}
                  outerForm={legalForm}
                  colNumber={3}
                  formColumns={legalFormColumns}
                />
              </FormElement3>
            </Group>
            <Group title="垫付相关">
              <FormElement3 form={prepayForm} initialValues={currentContract}>
                <EnumerateFields
                  record={currentContract}
                  outerForm={prepayForm}
                  colNumber={3}
                  formColumns={prepayFormColumns}
                />
              </FormElement3>
            </Group>
          </TabPane>
        ) : null}
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.writableProductLine}>报价单</AlertTab>}
          key={'3'}
        >
          <RowElementButton>
            {/* <Button onClick={quotationAdd}>新增</Button> */}
            <Button disabled>新增</Button>
            <Button disabled>删除</Button>
          </RowElementButton>
          <Writable
            wriTable={writableQuotation}
            service={serviceQuotation}
            columns={quotationColumns}
            editable={false}
          />
          <RowElementButton>
            <Button disabled>新增</Button>
            <Button disabled>删除</Button>
          </RowElementButton>
          <Writable
            wriTable={writableProductLine}
            service={serviceProductLine}
            columns={contractType === '5' ? productLineHealthColumns : productLineColumns}
          />
        </TabPane>
        <TabPane
          forceRender
          //  tab="销售相关"
          tab={<AlertTab onError={tabErrors.writableCustPayer}>付款方列表</AlertTab>}
          key={'4'}
        >
          <RowElementButton>
            <Button disabled>新增</Button>
            <Button disabled>删除</Button>
          </RowElementButton>
          <Writable
            wriTable={writableCustPayer}
            service={serviceCustPayer}
            columns={custPayerColumns}
            disabled={readOnly}
          />
        </TabPane>
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.writableUpload}>相关附件</AlertTab>}
          key={'5'}
        >
          <RowElementButton>
            <Button onClick={() => onImportBtn('1')}>新增</Button>
            <Button onClick={() => onDelImport('1')}>删除</Button>
            <Button onClick={onBatchDownload}>批量下载</Button>
          </RowElementButton>
          <Writable
            wriTable={writableUpload}
            service={serviceProductLine}
            columns={ImportColumns}
            notShowPagination
            disabled={readOnly}
          />
        </TabPane>
        {contractFormConfig.isRetirementBusinessList ? (
          <TabPane
            forceRender
            tab={<AlertTab onError={tabErrors.writableRetire}>退休人员列表</AlertTab>}
            key={'6'}
          >
            <RowElementButton>
              <Button disabled>新增</Button>
              <Button disabled>删除</Button>
              {/* <Button onClick={() => onImportBtn('2')}>导入</Button> */}
              {/* <ImportForm
              btnName="导入"
              downBtnName="下载模板"
              fileSuffix=".xls"
              ruleId="10702030"
              bizType="11"
              serviceName="slContractRetireeService"
              btnEnable={true}
              showImpHisBtn={false}
              showResultBtn={true}
              afterUpload={(res) => {
                API.crm.contractManage.getSlContractRetireeList
                  .requests({ batchId: res })
                  .then((data: any) => {
                    if (data?.list.length > 0) writableRetire.setNewData(data?.list);
                  });
              }}
            // afterUpload={afterUpload}
            /> */}
            </RowElementButton>
            <Writable
              wriTable={writableRetire}
              service={serviceProductLine}
              columns={retireColumns}
              disabled={readOnly}
            />
          </TabPane>
        ) : null}
        <TabPane tab="驳回原因" key={tabScene.reject}>
          <Writable
            wriTable={writableApproveRejectView}
            service={serviceApproveReject}
            columns={approveRejectColumns}
            readOnly
          />
        </TabPane>
      </Tabs>
      <FormElement1 form={approvalMemoForm} initialValues={currentContract}>
        <EnumerateFields
          record={currentContract}
          outerForm={prepayForm}
          colNumber={1}
          formColumns={approvalMemoFormColumns}
        />
      </FormElement1>
      <QuotationAddForm
        width={1200}
        modal={quotationViewModal}
        // listOptions={optionFunc}
        initialValues={quotationDetail[0]}
      />
      <Codal
        title={rejectTitle}
        visible={rejectVisible}
        onCancel={() => {
          setRejectVisible(false);
          writableApproveReject.setNewData([]);
        }}
        width="80%"
        loading={rejectLoading}
      >
        <RowElementButton>
          <Button onClick={addApproveReject}>新增</Button>
          <Button onClick={deleteApproveReject}>删除</Button>
          <Button onClick={saveApproveReject}>保存</Button>
          <Button onClick={rejectApprove}>{rejectTitle}</Button>
        </RowElementButton>
        <Writable
          wriTable={writableApproveReject}
          service={serviceApproveReject}
          columns={itemApproveCulumns}
          // disabled={readOnly}
        />
      </Codal>
      <AddForm
        title="附件上传"
        visible={onShowImport}
        hideHandle={() => setOnShowImport(false)}
        submitHandle={importHandle}
        formColumns={importType === '1' ? ImportFormColumns : ImportFormColumns1}
        okText="上传"
      ></AddForm>
      <PreviewCodal
        fileUrl={previewUrl}
        modal={previewModal}
        fileType={previewFileType}
        file={previewFile}
      />
    </Codal>
  );
};

export { ContractApproveWin };
