{"pub.CMM_CardModule": "刘双 银联卡类型管理 | ./sfk/index", "pub.CMM_PlaceOfPay": "刘双 支付地抬头信息查询 | ./sfk/index", "pub.CMM_RouteManagerPlaceModule": "刘双 路由组关联支付地 | ./sfk/index", "pub.CMM_RouteManagerBatchModule": "刘双 路由组规则维护 | ./sfk/index", "pub.CMM_RouteGroupManager": "刘双 路由组规则维护 | ./sfk/index", "pub.CMM_RouteManagerBatch": "刘双 路由组规则维护 | ./sfk/index", "customer.CMM_ClientInfoBrowse": "刘双 客户信息浏览 | ./sfk/index", "customer.CMM_ClientManagerPlaceModule": "刘双 客户风控组管理 | ./sfk/index", "customer.CMM_ControlClientSetModule": "刘双 客户风控规则管理 | ./sfk/index", "errorCode.CMM_TranscodingRulesModule": "刘双 错误码维护 | ./sfk/index", "errorCode.CMM_TranscodingModule": "刘双 本地错误码 | ./sfk/index", "org.CMM_CORGMngModule": "刘双 合作机构基本信息管理 | ./sfk/index", "org.CMM_CORGFeeModule": "刘双 合作机构费率信息管理 | ./sfk/index", "org.CMM_ControlRegularSetModule": "刘双 机构风控规则设置 | ./sfk/index", "transaction.CMM_SmsFormMngModule": "刘双 短信模板管理 | ./sfk/index", "transaction.CMM_TransactionSetModule": "刘双 事务预警规则设置 | ./sfk/index", "transaction.CMM_InformationSetModule": "刘双 事务邮件短信设置 | ./sfk/index", "settle.RPT_InBatchModule": "刘双 HRO批次查询 | ./sfk/index", "settle.CAP_MercTxnInfoModule": "刘双 交易信息查询 | ./sfk/index", "settle.RPT_OutBatchModule": "刘双 接出批量信息查询 | ./sfk/index", "settle.PWM_ReturnRemittanceModule": "刘双 退票查询 | ./sfk/index", "report.RPT_CorgFeeModule": "刘双 合作机构手续费报表 | ./sfk/index", "report.CAP_AgingRptModule": "刘双 交易成功率统计报表 | ./sfk/index", "report.CAP_BatchRptModule": "刘双 批量执行情况报表 | ./sfk/index", "report.CAP_TransFailRptModule": "刘双 交易失败原因统计 | ./sfk/index", "report.CAP_ReportModule": "刘双 综合报表 | ./sfk/index", "org.CMM_CnlManager": "刘双 渠道基本信息管理 | ./sfk/index", "receipt.CAP_AcctWhiteModule": "刘双 银行账号白名单管理 | ./sfk/index", "receipt.CAP_HijnlInfoModule": "刘双 银行流水查询 | ./sfk/index", "pub.CMM_PlaceOfPayCus": "刘双 客户支付地抬头信息查询 | ./sfk/index", "pub.CMM_RouteManagerPlaceCusModule": "刘双 客户支付地路由组维护 | ./sfk/index"}