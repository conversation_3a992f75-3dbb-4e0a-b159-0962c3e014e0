<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-09-18 14:17:17
 * @LastAuthor: 侯成
 * @LastTime: 2019-09-18 14:22:36
 * @message: 
 -->
# 问题汇总

数组添加元素
```tsx
// src\pages\basedata\ProductMaintenance\index.tsx

let idArray: Array<number> = [];
selectedRows.map(item => {
  idArray = [...idArray, item.prodId];
});
const ids = idArray.join(',');
```
* `map` 误用
* `Array` 合并为考虑性能

按钮禁用
```tsx
const onZero = selectedRows.length === 0;
const onOne = selectedRows.length === 1;
```
情况未全面覆盖
应为：
```tsx
const onZero = selectedRows.length === 0;
const notOne = selectedRows.length !== 1;
```
