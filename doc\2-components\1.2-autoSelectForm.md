<!--
 * @Author: 严小强
 * @Email: <EMAIL>
 * @Date: 2019-09-12 15:21:07
 * @LastAuthor: 侯成
 * @LastTime: 2019-09-17 16:41:27
 * @message: 
 -->

# ~~下拉选择框组件~~
~~根据后台接口数据生成下拉选择框，同时可根据后台返回数据id获取数据name。~~

# 本组件已废弃

![](../assets/images/2.2-auto-selectors.png)

## 生成下拉选择框组件

```tsx
// 组件目录
// src/components/AutoSelectForm/index.tsx
class AutoSelectForm extends React.Component<SelectFromProps> {}
```

参数说明：
* `dataType`,对应后台接口调用的switch判断依据。
以及所有Antd Select 组件自带的所有参数（已做筛选功能，亦可自行更改）。

引用方式：

组件内：

```tsx
export class DownDownProductSelector extends React.Component<SelectProps> {
  render() {
    return <AutoSelectForm dataType={dataTypeDownDownProduct} {...this.props} />;
  }
}
export const dataTypeBranch = 'branch';
```

models:

```tsx
// src/models/global.ts
case 'branch':
  const branch = yield API.query.branch.getBranchList.request({
    pageNum: 0,
    pageSize: 0,
  });
  if (branch.code === CODE_SUCCESS) carryOn.data = mapBranchData(branch.data.list);
  break;
```

页面：
```tsx
import { BranchSelector } from '@/components/AutoSelectForm/index';

<FormItem label="出账单分公司">
  {getFieldDecorator('payerId', {
    rules: formBillTemplate.payerId,
  })(<BranchSelector allowClear />)}
</FormItem>
```





## 获取数据name组件

```tsx
// 组件目录
// src/components/AutoSelectForm/CodeToName.tsx
```

参数说明：
* `dataType`,对应后台接口调用的switch判断依据。
* `code`,想要获取对应name的数据id。

引用方式：

组件内：

```tsx
import {
  dataTypeBranch,
} from './index';

const BranchName: React.FC<CodeNameProps> = props => {
  const { code } = props;
  return <CodeToName dataType={dataTypeBranch} code={code} />;
};

export { BranchName };
```

models:

```tsx
// src/models/global.ts
case 'branch':
  const branch = yield API.query.branch.getBranchList.request({
    pageNum: 0,
    pageSize: 0,
  });
  if (branch.code === CODE_SUCCESS) carryOn.data = mapBranchData(branch.data.list);
  break;
```

页面：
```tsx
import { BranchName } from '@/components/AutoSelectForm/CodeToName';

<BranchName code='1'/>
```
