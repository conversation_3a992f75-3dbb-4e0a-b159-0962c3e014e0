import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/account/queryAccountList
     * @desc 根据条件得到管理账号的分页查询结果
根据条件得到管理账号的分页查询结果
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.AccountManageDTO();
export const url = '/rhro-service-1.0/account/queryAccountList:POST';
export const initialUrl = '/rhro-service-1.0/account/queryAccountList';
export const cacheKey = '_account_queryAccountList_POST';
export async function request(
  data: defs.admin.AccountQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/account/queryAccountList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.AccountQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/account/queryAccountList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
