import React, { useState, useEffect, useRef } from 'react';
import { Button, Form, Space, Typography } from 'antd';
import { getCurrentMenu, getCurrentUser } from '@/utils/model';
import { getUUid } from '@/utils/utils';
import UploadForm, { FileInfo } from '@/components/UploadForm';
import { isEmpty } from 'lodash';
import moment from 'moment';
import dict from '@/locales/zh-CN/locale';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { AsyncButton, ConfirmButton } from '@/components/Forms/Confirm';
import { AreaBranchDataSelector } from '@/components/Selectors';
import Codal from '@/components/Codal';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { mapToSelectors, mapToSelectView } from '@/components/Selectors/FuncSelectors';
import {
  verifyStatusType,
  invoiceStatusType,
  isOrNoarray,
  monthMap,
  verifyType,
} from '@/utils/settings/finance/queryExBill';
import StandardTable from '@/components/StandardTable';
import { WritableInstance } from '@/components/Writable';
import LinkDetailForBill from './components/LinkDetailForBill';
import BillCreatorSubSelectPop from './components/BillCreatorSubSelectPop';
import { downloadFileWithId, navigate, objectToUrlPath } from '@/utils/methods/file';
import { stdMonthFormatMoDash } from '@/utils/methods/times';
import { msgErr, msgOk, resError, resErrorMsg } from '@/utils/methods/message';
import styles from './index.less';
import ChooseMailTypeForm from './components/ChooseMailTypeForm';
import { Calculator } from '@/utils/methods/calculator';

interface QueryBillProps {
  [props: string]: any;
}
const codalColumns: WritableColumnProps<any>[] = [
  { title: '客户', dataIndex: 'CUST_NAME' },
  { title: '客户账套', dataIndex: 'RECEIVABLE_TEMPLT_NAME' },
  { title: '账单年月', dataIndex: 'BILL_YM' },
  { title: '财务应收年月', dataIndex: 'FIN_RECEIVABLE_YM' },
  { title: '账单人数', dataIndex: 'HEAD_COUNT' },
  { title: '创建时间', dataIndex: 'CREATE_DT' },
  { title: '生成状态', dataIndex: 'REMARK' },
  { title: '应收金额', dataIndex: 'RECEIVABLESUM' },
];

const exportService = API.finance.receivable.toDownLoad;
const checkBigCustService = API.finance.receivable.checkBigCustomer;
const sendBigCustService = API.finance.receivable.sendBigCustomer;
let _options: WritableInstance;
const QueryBill: React.FC<QueryBillProps> = (props) => {
  const [visible, setVisible] = useState(false);
  const [showDetailForBill, setShowDetailForBill] = useState(false);
  const [record, setRecord] = useState({});
  const [billAmount, setTotalBillAmount] = useState('');
  const [billTempMap, setBillTempMap] = useState<Map<number, string>>(new Map());
  const [branchListMap, setBranchListMap] = useState<Map<string, string>>(new Map());
  const [userListMap, setUserListMap] = useState<Map<number, string>>(new Map());
  const [departMentList, setDepartMentList] = useState<Map<number, string>>(new Map());
  const [btnVisable, setBtnVisable] = useState(false);
  const [showMailTypeForm, setShowMailTypeForm] = useState(false);
  const [empId, setEmpId] = useState();
  const [isSendBill, setIsSendBill] = useState(true);
  const prevSelectedSingleRow = useRef<any>(null);
  const [form] = Form.useForm();
  const user = getCurrentUser();
  const currentMenu = getCurrentMenu() as POJO;
  const BillListService = API.finance.receivable.getBillList;
  const service = {
    ...BillListService,
    cacheKey: props.billFlag ? BillListService.cacheKey + 1 : BillListService.cacheKey + 2,
  };
  const [table, tableOptions] = StandardTable.useStandardTable({
    service: API.finance.receivable.getLogList,
  });
  const columns: WritableColumnProps<any>[] = [
    { title: '集团公司编号', dataIndex: 'GROUPID', fixed: 'left' },
    { title: '集团公司名称', dataIndex: 'GROUPNAME', ellipsis: true, fixed: 'left' },
    { title: '客户', dataIndex: 'CUSTNAME', ellipsis: true, fixed: 'left' },
    { title: '客户账套', dataIndex: 'RECEIVABLETEMPLTNAME', fixed: 'left' },
    { title: '合同大类', dataIndex: 'CONTRACTTYPESTR' },
    { title: '合同小类', dataIndex: 'CONTRACTSUBTYPENAME' },
    { title: '签单方分公司抬头', dataIndex: 'SIGNBRANCHTITLENAME', ellipsis: true },
    { title: '账单年月', dataIndex: 'BILLYM' },
    { title: '财务应收年月', dataIndex: 'FINRECEIVABLEYM' },
    { title: '应收金额', dataIndex: 'RECEIVABLEAMT' },
    {
      title: '账单版本',
      dataIndex: 'VERSIONID',
      render: (value, record) => {
        return (
          <Typography.Link
            onClick={(e) => {
              e.preventDefault();
              setShowDetailForBill(true);
              setRecord(record);
            }}
          >
            {value}
          </Typography.Link>
        );
      },
    },
    {
      title: '账单状态',
      dataIndex: 'ISLOCKED',
      render: (value) => mapToSelectView(isOrNoarray, value),
    },
    { title: '首次生成时间', dataIndex: 'CREATEDT' },
    {
      title: '生成人',
      dataIndex: 'CREATEBY',
      render: (value) => mapToSelectView(userListMap, value),
    },
    { title: '实际锁定日', dataIndex: 'LOCKDT' },
    {
      title: '账单附件',
      dataIndex: 'FILENAME',
      render: (value, record) => {
        return (
          <Typography.Link
            onClick={(e) => {
              e.preventDefault();
              download(record.FILEID, record.FILENAME);
            }}
          >
            {value}
          </Typography.Link>
        );
      },
    },
    {
      title: '核销状态',
      dataIndex: 'VERIFYSTATUS',
      render: (value) => mapToSelectView(verifyStatusType, value),
    },
    {
      title: '核销方式',
      dataIndex: 'VERIFYTYPENAME',
      // render: (value) => mapToSelectView(verifyStatusType, value),
    },

    {
      title: '开票状态',
      dataIndex: 'INVOICESTATUS',
      render: (value) => mapToSelectView(invoiceStatusType, value),
    },
    { title: '最后出账单时间', dataIndex: 'LASTCREATEDT' },
    { title: '出账单大区', dataIndex: 'AREANAME' },
    { title: '出账单分公司', dataIndex: 'PROVIDERNAME', ellipsis: true },
    { title: '约定出账单日', dataIndex: 'agreedDt' },
    { title: '约定帐单锁定日', dataIndex: 'agreedBillLockDt' },
    { title: '约定到款日(天)', dataIndex: 'agreedWageArriveDay' },
    {
      title: '到款所属月',
      dataIndex: 'amtReceivedMon',
      render: (value) => mapToSelectView(monthMap, value),
    },
    { title: '推送时间', dataIndex: 'PUSHDT' },
  ];
  const formColumns: EditeFormProps[] = [
    {
      label: '客户',
      fieldName: 'custName',
      inputRender: () => (
        <CustomerPop
          title="选择客户"
          rowValue="custId-custName"
          keyMap={{
            custId: 'custId',
            custName: 'custName',
          }}
          fixedValues={{ isFinnace: '1' }}
          handdleConfirm={custIdChanged}
        />
      ),
    },
    {
      label: '客户账套',
      fieldName: 'receivableTempltId',
      inputRender: () => mapToSelectors(billTempMap, { showSearch: true }),
    },
    {
      label: '账单年月起始月',
      fieldName: 'billYmSt',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '账单年月截止月',
      fieldName: 'billYmEd',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '财务应收起始年月',
      fieldName: 'finReceivableYmSt',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '财务应收截止年月',
      fieldName: 'finReceivableYmEd',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '应收金额>=',
      fieldName: 'receivableAmtSt',
      inputRender: 'string',
      rules: [
        {
          pattern: /^[0-9][0-9]*([.][0-9]+)?$/,
          message: '请输入应收金额数字',
        },
      ],
    },
    {
      label: '应收金额<=',
      fieldName: 'receivableAmtEd',
      inputRender: 'string',
      rules: [
        {
          pattern: /^[0-9][0-9]*([.][0-9]+)?$/,
          message: '请输入应收金额数字',
        },
      ],
    },
    {
      label: '账单状态',
      fieldName: 'isLocked',
      inputRender: () => mapToSelectors(isOrNoarray, { allowClear: true }),
    },
    {
      label: '账单方',
      fieldName: 'payerId',
      inputRender: () => mapToSelectors(departMentList, { showSearch: true }),
    },
    {
      label: '核销状态',
      fieldName: 'verifyStatus',
      inputRender: () => mapToSelectors(verifyStatusType, { allowClear: true }),
    },
    {
      label: '核销方式',
      fieldName: 'verifyType',
      inputRender: () => mapToSelectors(verifyType, { allowClear: true }),
    },
    {
      label: '开票状态',
      fieldName: 'invoiceStatus',
      inputRender: () => mapToSelectors(invoiceStatusType, { allowClear: true }),
    },
    {
      label: '出账单大区',
      fieldName: 'areaId',
      inputRender: () => (
        <AreaBranchDataSelector
          onChange={(val) => {
            areaComboChanged(val as any);
          }}
        />
      ),
    },
    {
      label: '出账单分公司',
      fieldName: 'providerId',
      inputRender: () => mapToSelectors(branchListMap, { showSearch: true }),
    },
    {
      label: '生成人',
      fieldName: 'createBy',
      inputRender: () => {
        return (
          <BillCreatorSubSelectPop
            rowValue="RN-EMPID-REALNAME"
            keyMap={{
              RN: 'RN',
              creater: 'EMPID',
              REALNAME: 'REALNAME',
            }}
            handdleConfirm={(val: any) => {
              setEmpId(val?.creater);
            }}
          />
        );
      },
    },
  ];

  useEffect(() => {
    getAreaBranchDropDownList();
    getBranchDropDownList();
    getDepartMentList();
    getUserList();
  }, []);

  useEffect(() => {
    if (!props.billFlag) {
      return;
    }
    const fieldsValue = props.form?.getFieldsValue();
    const { billYm, billtempId, custName, custId } = fieldsValue || {};
    setBtnVisable(true);
    form.setFieldsValue({
      custName,
      receivableTempltId: billtempId,
      billYmSt: billYm,
      billYmEd: billYm,
    });
    initBillTempDropDownList(custId);
    _options.request({
      ...fieldsValue,
      billYmSt: billYm,
      billYmEd: billYm,
      receivableTempltId: billtempId,
    });
  }, []);

  // 客户名称改变
  const custIdChanged = (values: any) => {
    const { custId } = values;
    form.setFieldsValue({
      receivableTempltId: '',
    });
    initBillTempDropDownList(custId);
  };

  // 获取客户账套下拉
  const initBillTempDropDownList = async (custId: string) => {
    const params = {
      statementName: 'receive.getbillTempDropDown',
      custId: custId,
    };
    const data = await API.basedata.baseDataCls.getDorpDownList.requests(params, { params });
    const map = new Map<number, string>();
    data.list.forEach((e: any) => map.set(+e.key, e.shortName));
    setBillTempMap(map);
  };
  // 获取签单地
  const getDepartMentList = async () => {
    const params = {
      statementName: 'receive.getDepartmentList',
    };
    const data = await API.basedata.baseDataCls.getDorpDownList.requests(params, { params });
    const map = new Map<number, string>();
    data.list.forEach((e: any) => map.set(+e.key, e.shortName));
    setDepartMentList(map);
  };

  // 雇员list
  const getUserList = async () => {
    const params = {
      statementName: 'cc.getUserList',
    };
    const data = await API.basedata.baseDataCls.getDorpDownList.requests(params, { params });
    const map = new Map<number, string>();
    data.list.forEach((e: any) => map.set(+e.key, e.shortName));
    setUserListMap(map);
  };

  // 获取账单大区下拉数据
  const getAreaBranchDropDownList = async () => {
    try {
      const data = await API.report.inDecMember.initAreaBranchData.requests({});
      const areaList = data.areaList || [];
      const branchList = data.branchList || [];
      const areaMap = new Map<string, string>();
      const branchMap = new Map<string, string>();
      areaList.forEach((e: any) => areaMap.set(e.key, e.shortName));
      branchList.forEach((e: any) => branchMap.set(e.key, e.shortName));
    } catch (e) {
      msgErr('获取数据失败');
    }
  };

  // 大区联动
  const areaComboChanged = (areaId?: number) => {
    if (!areaId) return;
    form.setFieldsValue({ payerName: undefined });
    getBranchDropDownList(areaId!);
  };

  // 获取分公司下拉框
  const getBranchDropDownList = async (areaId?: number) => {
    let params = {
      departmentGrade: '3',
    };
    if (areaId) {
      params = {
        ...params,
        governingAreaId: areaId,
      } as any;
    }
    try {
      const data = await API.report.inDecMember.getDepartmentDropdownList.requests(params);
      const branchList = data.list || [];
      const branchMap = new Map<string, string>();
      branchList.forEach((e: any) => branchMap.set(e.key, e.shortName));
      setBranchListMap(branchMap);
    } catch (e) {
      msgErr('获取数据失败');
    }
  };
  // 报表账单 BillServSummaryRpt
  const generalBillServSummaryRpt = async () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据');
    const obj: POJO = {
      recId: row.RECEIVABLEID,
      type: '1',
      baseRptURL: user.reportDir,
      modulePath: currentMenu.reportUrl || '/np/finance/billServSummary.jsp',
    };
    let path = objectToUrlPath(obj, user);
    const guid = getUUid().toUpperCase();
    await API.report.reportQc.insertBoLog.requests({
      batchNo: guid,
      userId: user.profile.userId,
      rptCallDt: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    });
    path += `&uuid=${guid}`;
    navigate(path, '账单汇总表');
  };

  // 导出数据
  const onExport = async () => {
    _options.handleExport(
      {
        service: exportService,
        data: (value: POJO) => {
          return {
            ...value,
            condition: {
              ...value.condition,
              exportTag: 'exportBilllist',
            },
          };
        },
      },
      { columns: columns, fileName: '应收.xlsx' },
    );
  };

  const getTotalBillAmount = async () => {
    try {
      const params = handleQueries(form.getFieldsValue());
      const data = await API.finance.receivable.getTotalBillAmount.requests(params);
      setTotalBillAmount(data);
    } catch (e) {
      msgErr('请求数据失败');
    }
  };
  const getLogList = async () => {
    const { BILLYM, RECEIVABLETEMPLTID } = _options?.selectedSingleRow;
    setVisible(true);
    if (!BILLYM) {
      return;
    }
    tableOptions.request({
      billYm: BILLYM,
      tempIdArray: RECEIVABLETEMPLTID,
    });
  };
  // 锁定按钮单击事件的处理方法
  const handleLockFun = async () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据');
    if (row.ISLOCKED === 1) {
      msgErr(dict.thisBillIslocked);
      return;
    }
    const params = {
      receivableTempltId: row.RECEIVABLETEMPLTID,
      billYm: row.BILLYM,
      receivableId: row.RECEIVABLEID,
      wageIds: row.WAGEIDS,
      custName: row.CUSTNAME,
      receivableTempltName: row.RECEIVABLETEMPLTNAME,
      custId: row.CUSTOMERID,
    };
    try {
      const data = await API.finance.receivable.getIsReceiveAudit.requests(params);
      if (data > 0) {
        msgErr('该账单存在客户一次性费用审批流程，请等待审批结束进行锁定');
        return;
      }
      const res = await API.finance.receivable.lockable.requests(params);
      if (res.code === 200) {
        msgOk(res.message);
        _options?.request();
        return;
      } else {
        msgErr(res.message);
      }

      // if (res) {
      //   msgErr(dict.hintMsg_ok);
      //   _options?.request();
      //   return;
      // }
      // msgErr(dict.flushBill);
    } catch (e) {
      msgErr('获取数据失败');
    }
  };
  // 上传文件
  const updateFileId = async (fileInfo: FileInfo) => {
    const { RECEIVABLEID } = _options?.selectedSingleRow;
    const { fileName, fileId } = fileInfo;
    try {
      await API.finance.receivable.updateFileId.requests({
        fileName,
        fileId,
        receivableId: RECEIVABLEID,
      });
      msgOk('上传成功');
      _options.request();
    } catch (e) {
      msgErr('获取数据失败');
    }
  };
  const download = async (fileId: string, fileName: string) => {
    if (!fileId) return msgErr('文件不存在');
    downloadFileWithId(fileId, fileName);
  };
  const setVisibleFun = () => {
    setShowDetailForBill(false);
  };

  // 申请开票
  const createOrInvoice = async () => {
    const { selectedRows } = _options;
    if (selectedRows.length < 1) {
      return msgErr('请先选择数据');
    }
    // RHRO-5939 增加判断
    let positiveNumberTotal = 0;
    selectedRows.forEach((e: any) => {
      positiveNumberTotal = Calculator.add(positiveNumberTotal, e.RECEIVABLEAMT || '0');
    });
    if (positiveNumberTotal && positiveNumberTotal < 0) {
      return msgErr(
        '选择账单的总金额为负，无法自动提交对应的开票申请，请手动进行开票申请操作，请知晓',
      );
    }
    const key =
      selectedRows[0].CUSTOMERID +
      '-' +
      selectedRows[0].CUSTPAYERID +
      '-' +
      selectedRows[0].PAYEEID;
    for (let index = 0; index < selectedRows.length; index++) {
      const element = selectedRows[index];
      if (element.CUSTOMERID + '-' + element.CUSTPAYERID + '-' + element.PAYEEID !== key) {
        return msgErr('只可以对同一客户下、同一客户付款方、同一收款方的数据同时操作');
      }
      if (element.INVOICETYPE != '0') {
        return msgErr('只有开票方式都为见票付款的数据才可以自动申请开票');
      }
      if (element.ISLOCKED != '1') {
        return msgErr('只有账单状态都为已锁定的数据才可以自动申请开票');
      }
      if (element.INVOICESTATUS != '0') {
        return msgErr('只有开票状态都为未开票的数据才可以自动申请开票');
      }
    }
    setRecord({
      custPayerId: selectedRows[0].CUSTPAYERID,
      SIGNBRANCHTITLEID: selectedRows[0].SIGNBRANCHTITLEID,
    });
    setShowMailTypeForm(true);
  };

  const submitCreateInvoice = async (data: POJO) => {
    setRecord({});
    setShowMailTypeForm(false);
    const { selectedRows } = _options;
    const receivableIds = selectedRows
      .map((i) => {
        return i.RECEIVABLEID;
      })
      .join(',');
    const res: any = await API.finance.invoiceMain.autoCreateOrInvoiceInfo.requests({
      receivableIds,
      invoiceType: '0',
      ...data,
    });
    if (res.isSuccess == '0') {
      msgOk('操作成功');
    } else {
      msgErr(res.msg);
    }
  };
  const getSendBigCust = async () => {
    const selectedSingleRow = _options?.selectedSingleRow;
    await sendBigCustService.requests({
      receivableId: String(selectedSingleRow?.RECEIVABLEID),
      groupId: String(selectedSingleRow?.GROUPID),
    });
    msgOk('推送账单成功');
  };
  const renderButtons = (options: WritableInstance) => {
    _options = options;
    const { selectedSingleRow } = options;
    if (
      prevSelectedSingleRow.current?.RECEIVABLEID !== selectedSingleRow?.RECEIVABLEID &&
      selectedSingleRow?.ISLOCKED !== 0 &&
      selectedSingleRow?.GROUPID
    ) {
      prevSelectedSingleRow.current = selectedSingleRow;
      checkBigCustService.request({ groupId: selectedSingleRow?.GROUPID }).then((res: any) => {
        if (res.code === 200) {
          Number(res.data) ? setIsSendBill(false) : setIsSendBill(true);
        } else {
          setIsSendBill(true);
          msgErr(res.message);
        }
      });
    }
    const disabled = isEmpty(selectedSingleRow);
    return (
      <Space size="small">
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <ConfirmButton service={exportService} disabled={disabled} onClick={onExport}>
          导出数据
        </ConfirmButton>
        <Button disabled={disabled} onClick={getLogList}>
          查看账单日志
        </Button>
        {btnVisable && (
          <Button disabled={disabled} onClick={handleLockFun}>
            锁定
          </Button>
        )}
        <Button disabled={disabled} onClick={generalBillServSummaryRpt}>
          账单汇总表
        </Button>
        <AsyncButton disabled={disabled} onClick={createOrInvoice}>
          申请开票
        </AsyncButton>
        <AsyncButton
          disabled={disabled || isSendBill || selectedSingleRow?.ISLOCKED === 0}
          onClick={getSendBigCust}
        >
          推送账单
        </AsyncButton>
        <UploadForm
          title="上传附件"
          disabled={disabled}
          bizType="11"
          onUploadSuccess={(fileInfo) => {
            updateFileId(fileInfo);
          }}
        />
        <div className={styles.billAmount}>
          <span>{`合计:${billAmount}`}</span>
        </div>
      </Space>
    );
  };
  const handleQueries = (values: POJO) => {
    for (const key in values) {
      if (
        values[key] === null ||
        values[key] === undefined ||
        values[key] === '' ||
        (typeof values[key] === 'object' && Object.keys(values[key]).length === 0)
      ) {
        delete values[key];
      }
    }
    return values;
  };
  return (
    <>
      <CachedPage
        service={service}
        form={form}
        fixedValues={{ userId: user.profile.userId, creater: empId }}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        onSuccess={({ list }) => {
          if (list.length > 0) {
            getTotalBillAmount();
          }
        }}
      />
      <Codal
        className={styles.modalBox}
        title="查看账单日志"
        visible={visible}
        onCancel={() => setVisible(false)}
        width={1000}
      >
        <StandardTable columns={codalColumns} dragable notShowPagination {...(table as any)} />
      </Codal>
      <LinkDetailForBill
        showDetailForBill={showDetailForBill}
        setVisibleFun={setVisibleFun}
        dataTransObj={record}
      />
      <ChooseMailTypeForm
        visible={showMailTypeForm}
        hideHandle={() => {
          setRecord({});
          setShowMailTypeForm(false);
        }}
        data={record}
        submitHandle={submitCreateInvoice}
      />
    </>
  );
};

export default QueryBill;
