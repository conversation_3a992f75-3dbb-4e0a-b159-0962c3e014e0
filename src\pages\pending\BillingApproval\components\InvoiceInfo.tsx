import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import Codal from '@/components/Codal';
import { AsyncButton } from '@/components/Forms/Confirm';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { InvoiceDeptmentSelector, mapToSelectors } from '@/components/Selectors';
import StandardTable, { StandardTableColumnProps } from '@/components/StandardTable';
import { downloadFile, downloadFileWithId } from '@/utils/methods/file';
import { formatAmount } from '@/utils/methods/format';
import { msgErr, msgOk } from '@/utils/methods/message';
import { pageResponseRhro } from '@/utils/methods/pagenation';
import { invoiceStatusMap, verifyStatusTypeMap } from '@/utils/settings/finance/invoice';
import { tableIndexKey } from '@/utils/settings/forms';
import { Button, Checkbox, Form, Modal, Typography } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import InvoiceDetail from './InvoiceDetail';

interface InvoiceInfoProps {
  visible: boolean;
  hideHandle: (refresh?: boolean) => void;
  data?: POJO;
}

const InvoiceInfo = (props: InvoiceInfoProps) => {
  // const service = API.finance.invoiceMain.updateBillingNumById

  const hadIvoiceStatusType = new Map<number, string>([
    [0, '未开票'],
    [1, '部分开票'],
    [2, '全部开票'],
  ]);

  const invoiceStatusTypeMap = new Map<number, string>([
    [1, '预开票'],
    [2, '已开票'],
    [3, '作废'],
    [4, '审核中'],
    [5, '驳回'],
  ]);

  const [form] = Form.useForm();

  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [list1, setList1] = useState<POJO[]>([]);
  const [list2, setList2] = useState<POJO[]>([]);
  const [list3, setList3] = useState<POJO[]>([]);

  const columns1: StandardTableColumnProps<any>[] = [
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '客户名称', dataIndex: 'custName' },
    { title: '客户账套', dataIndex: 'receivableTempltName' },
    { title: '财务应收年月', dataIndex: 'receivableMon' },
    { title: '应收金额', dataIndex: 'receivableAmt', render: formatAmount },
    {
      title: '开票状态',
      dataIndex: 'invoiceStatus',
      render: (val = '0') => hadIvoiceStatusType.get(+val),
    },
    {
      title: '核销状态',
      dataIndex: 'verifyStatus',
      render: (val = '0') => verifyStatusTypeMap.get(+val),
    },
    { title: '未开票金额', dataIndex: 'invoiceBalance', render: formatAmount },
    { title: '已开票金额', dataIndex: 'invoiceMoney', render: formatAmount },
    { title: '小额调整', dataIndex: 'rateOdds', render: formatAmount },
    { title: '已开票附加金额', dataIndex: 'additionalMoney', render: formatAmount },
  ];

  const columns2: StandardTableColumnProps<any>[] = [
    { title: '到款日期', dataIndex: 'ARRIVE_DATE' },
    { title: '到款客户', dataIndex: 'CUST_NAME' },
    { title: '到款金额', dataIndex: 'ARRIVE_AMOUNT', render: formatAmount },
    { title: '核销金额', dataIndex: 'VERIFY_AMT', render: formatAmount },
    { title: '小额调整', dataIndex: 'MINOR_ADJUSTMENT', render: formatAmount },
    { title: '核销日期', dataIndex: 'VERIFY_DT' },
    { title: '核销人', dataIndex: 'VERIFY_BY' },
  ];

  const columns3: StandardTableColumnProps<any>[] = [
    { title: '开票时间', dataIndex: 'invoiceDate' },
    { title: '发票编号', dataIndex: 'invoiceCode' },
    { title: '开票金额', dataIndex: 'invoiceAmount', render: formatAmount },
    { title: '备注', dataIndex: 'invoiceRemark' },
    {
      title: '发票状态',
      dataIndex: 'invoiceStatus',
      render: (val = '0') => invoiceStatusTypeMap.get(+val),
    },
    {
      title: '附件',
      dataIndex: 'fileName',
      render: (val, record) => (
        <Typography.Link
          onClick={(e) => {
            e.preventDefault();
            download(record.fileId, record.fileName);
          }}
        >
          {val}
        </Typography.Link>
      ),
    },
  ];

  const formColumns: EditeFormProps[] = [
    { label: '发票编号', fieldName: 'invoiceCode', inputRender: 'string' },
    {
      label: '开票分公司',
      fieldName: 'departmentId',
      inputRender: () => (
        <InvoiceDeptmentSelector
          params={{ statementName: 'OR_INVOICE_INFO.selectGoverningBranch' }}
        />
      ),
    },
    {
      label: '客户编号',
      fieldName: 'custCode',
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    {
      label: '发票金额',
      fieldName: 'invoiceAmount',
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    { label: '发票日期', fieldName: 'invoiceDate', inputRender: 'date' },
    {
      label: '开票时间',
      fieldName: 'createDt',
      inputProps: { disabled: true },
      inputRender: 'date',
    },
    {
      label: '开票状态',
      fieldName: 'invoiceStatus',
      inputProps: { disabled: true },
      inputRender: () => mapToSelectors(invoiceStatusMap),
    },
    {
      label: '调整金额',
      fieldName: 'adjustAmount',
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    {
      label: '不足的余额不再开票',
      fieldName: 'smallAdjust',
      inputRender: () => <Checkbox disabled checked={props.data?.adjustMent != 0} />,
    },
    {
      label: '备注',
      fieldName: 'invoiceMemo',
      inputRender: 'text',
      inputProps: { disabled: true },
    },
    { label: '审批备注', fieldName: 'newRemark', inputRender: 'text' },
  ];

  useEffect(() => {
    if (props.visible) {
      initForm();
      request();
    } else {
      form.resetFields();
    }
  }, [props.visible]);

  const initForm = () => {
    const now = moment(new Date()).format('YYYY-MM-DD');
    form.setFieldsValue({
      createDt: now,
      invoiceStatus: '2',
      smallAdjust: props.data?.adjustMent !== 0,
    });
  };

  /** 请求数据 */
  const request = async () => {
    try {
      setLoading(true);
      const res = await API.finance.invoiceMain.quertVerifyInvoice.requests(props.data || {});
      const UnInvoiceDetail = pageResponseRhro({ list: res.UnInvoiceDetail || [], total: 0 })
        .list as POJO[];
      const ReceiptsInfo = pageResponseRhro({ list: res.ReceiptsInfo || [], total: 0 })
        .list as POJO[];
      const OrInvoiceInfo = pageResponseRhro({ list: res.OrInvoiceInfo || [], total: 0 })
        .list as POJO[];
      setList1(UnInvoiceDetail); // table1
      setList2(ReceiptsInfo); // table1
      setList3(OrInvoiceInfo); // table3
      if (OrInvoiceInfo && OrInvoiceInfo.length) {
        form.setFieldsValue({ ...OrInvoiceInfo[0] });
      }
      if (UnInvoiceDetail && UnInvoiceDetail.length) {
        form.setFieldsValue({ custCode: UnInvoiceDetail[0].custCode });
      }
    } finally {
      setLoading(false);
    }
  };

  /** 下载文件 */
  const download = async (fileId: string, fileName: string) => {
    if (!fileId) return;
    downloadFileWithId(fileId, fileName);
  };

  /** 通过 */
  const onPass = () => {
    Modal.confirm({
      content: '您确认要通过该次审批吗!',
      onOk: async () => {
        const params = {
          invoiceId: props.data?.invoiceId,
          custPayerId: props.data?.custPayerId,
        };
        const res1 = await API.finance.invoiceMain.getBusinessInfo.requests({
          id: props.data?.invoiceId,
        });
        if (res1 && res1.isSuccess === '0') {
          await updateStatus();
          msgOk('操作成功');
          props.hideHandle(true);
          // const res = await API.finance.invoiceMain.sendBusiness.requests(params);
          // if (res) {
          //   if (res.isSuccess === '0') {
          //     await updateStatus();
          //     msgOk('操作成功');
          //     props.hideHandle(true);
          //   } else {
          //     msgErr(res.msg);
          //   }
          // } else {
          //   msgErr('审批失败');
          // }
        } else {
          msgErr(res1 ? res1.msg : '审批失败');
        }
      },
    });
  };

  const updateStatus = async () => {
    let invoiceStatus = '2';
    const values = form.getFieldsValue();
    const approval = values.invoiceMemo;
    let currentRecrd: POJO = {};
    const updateAyyar = list1.map((e) => {
      currentRecrd = { ...e };
      if (e.invoiceBalance == '0') {
        return {
          invoiceStatus: '2',
          receivableId: e.receivableId,
        };
      } else {
        invoiceStatus = '1';
        return {
          invoiceStatus: '1',
          receivableId: e.receivableId,
        };
      }
    });
    invoiceStatus = currentRecrd.verifyStatus == '2' ? '2' : '1';
    const obj: POJO = {
      invoiceId: props.data?.invoiceId,
      invoiceStatus,
      headName: values.headName,
      invoiceMemo: values.invoiceMemo,
      invoiceCode: values.invoiceCode,
      invoiceDate: values.invoiceDate,
    };
    await API.finance.invoiceMain.updateInvoiceInfoReStatus.requests({
      ...obj,
      reList: updateAyyar,
      workItemId: props.data?.workitemId,
      approval,
      createBy: props.data?.createBy,
      invoiceEmail: props.data?.invoiceEmail || '',
    });
    await API.finance.invoiceMain.syncJinDieData.requests({
      invoiceId: props.data?.invoiceId,
    });
  };

  /** 驳回 */
  const onAbort = () => {
    Modal.confirm({
      content: '您确认要驳回此次审批吗,这将终止该发票的开票流程并且作废此发票!',
      onOk: verifyBreak,
    });
  };

  /** 驳回审批 */
  const verifyBreak = async () => {
    const values = form.getFieldsValue();
    const approval = values.invoiceMemo; // 备注
    const newRemark = values.newRemark; // 新备注
    const params: POJO = {
      invoiceId: props.data?.invoiceId,
      invoiceStatus: '5',
      headName: values.headName,
      invoiceMemo: approval,
      newRemark,
      createBy: values.createBy || props.data?.createBy,
      workItemId: props.data?.workitemId,
      approval,
    };
    if (list1.length) params.custName = list1[0].custName;
    await API.finance.invoiceMain.terminalPro.requests(params);
    msgOk('操作成功');
    props.hideHandle(true);
  };

  /** 开票明细 */
  const onDetail = () => {
    setDetailVisible(true);
  };

  const renderFooter = () => {
    return (
      <div style={{ textAlign: 'center' }}>
        <Button onClick={onDetail}>开票明细</Button>
        <AsyncButton style={{ marginLeft: 8 }} onClick={onPass}>
          审批通过
        </AsyncButton>
        <AsyncButton style={{ marginLeft: 8 }} onClick={onAbort}>
          驳回
        </AsyncButton>
      </div>
    );
  };

  const renderTable1 = () => {
    return (
      <StandardTable
        rowKey={tableIndexKey}
        columns={columns1}
        data={{ list: list1, pagination: {} }}
        notShowPagination
        notShowRowSelection
        loading={loading}
        scroll={{ y: 300 }}
      />
    );
  };

  const renderTable = () => {
    return (
      <div
        style={{
          marginTop: 8,
          marginBottom: 8,
          display: 'flex',
          justifyContent: 'space-between',
          flexDirection: 'row',
        }}
      >
        <div style={{ width: '50%', marginRight: '1%' }}>
          <StandardTable
            rowKey={tableIndexKey}
            columns={columns2}
            data={{ list: list2, pagination: {} }}
            notShowPagination
            notShowRowSelection
            loading={loading}
            scroll={{ x: 'max-content', y: 300 }}
          />
        </div>
        <div style={{ width: '49%' }}>
          <StandardTable
            rowKey={tableIndexKey}
            columns={columns3}
            data={{ list: list3, pagination: {} }}
            notShowPagination
            notShowRowSelection
            loading={loading}
            scroll={{ x: 'max-content', y: 300 }}
          />
        </div>
      </div>
    );
  };

  const renderForm = () => {
    return (
      <FormElement3 form={form}>
        <EnumerateFields outerForm={form} colNumber={3} formColumns={formColumns} />
      </FormElement3>
    );
  };

  const renderModals = () => {
    const row = list1[0];
    const values = { ...list3[0] };
    return (
      <InvoiceDetail
        visible={detailVisible}
        parButtonIsVisible={false}
        hideHandle={() => setDetailVisible(false)}
        updateBillingId={(id) => {
          const next = [...list3];
          next[0] = { ...values, billingId: id };
          setList3(next);
        }}
        data={{
          invoiceId: props.data?.invoiceId,
          custId: props.data?.custId,
          dataTransObj: {
            groupId: props.data?.groupId,
            groupName: props.data?.groupName,
            mailType: props.data?.mailType,
            invoiceEmail: props.data?.invoiceEmail,
            custCode: row?.custCode,
            custName: row?.custName,
            receivableIds: list1.map((e) => e.receivableId).join(','),
            billYm: row?.billYm,
            finReceivableYm: row?.receivableMon,
            orInvoiceInfo: { ...values },
            headName: values?.headName,
            invoiceMemo: form.getFieldValue('invoiceMemo'),
            invoiceAmt: values?.invoiceAmount,
          },
        }}
        isApprove
      />
    );
  };

  return (
    <Codal
      title="开票信息"
      visible={props.visible}
      onCancel={() => props.hideHandle()}
      width={1300}
      footer={renderFooter()}
    >
      {renderTable1()}
      {renderTable()}
      {renderForm()}
      {renderModals()}
    </Codal>
  );
};

export default InvoiceInfo;
