{"/basedata/province": "./basedata/ProvinceInfo/index", "/basedata/city": "./basedata/CityInfo/index", "/basedata/area-code": "./basedata/AreaCode/index", "/basedata/country": "./basedata/CountryInfo/index", "/basedata/competitor": "./basedata/Competitor/index", "/basedata/sys-branch-title": "./basedata/SysBranchTitle/index", "/basedata/socialsecurity/prodratio": "./basedata/Socialsecurity/ProductRatio/index", "/basedata/socialsecurity/maintenance": "./basedata/Socialsecurity/Maintenance/index", "/basedata/socialsecurity/comboManage": "./basedata/Socialsecurity/ComboManage/index", "/basedata/socialsecurity/PersonCategory": "./basedata/Socialsecurity/PersonCategory/index", "/basedata/taxRateTable": "./basedata/TaxRateTable/index", "/basedata/baseDataXml": "./basedata/BaseDataXml/index", "/basedata/jindieOrg": "./basedata/JindieOrg/index", "/basedata/branchInvoice": "./basedata/BranchInvoice/index", "/basedata/branchback": "./basedata/BranchBack/index", "/basedata/hire": "./basedata/Hire/index", "/basedata/welfareType": "./basedata/WelfareType/index", "/basedata/businessType": "./basedata/BusinessType/index", "/basedata/emp-maintain": "./basedata/EmpMaintain/index", "/basedata/socialsecurity/servicePoint": "./basedata/Socialsecurity/ServicePoint/index", "/basedata/type-definition": "./basedata/TypeDefinition/index", "/basedata/instance-definition": "./basedata/InstanceDefinition/index", "/basedata/serviceType": "./basedata/ServiceType/index", "/basedata/fileProvider": "./basedata/FileProvider/index", "/basedata/materialsInfo/materialsAssert": "./basedata/MaterialsInfo/MaterialsAssert/index", "/basedata/materialsInfo/materialPackage": "./basedata/MaterialsInfo/MaterialPackage/index", "/basedata/medicalIns/MedicalAssert": "./basedata/medicalIns/MedicalAssert/index", "/basedata/medicalIns/ImportMedical": "./basedata/medicalIns/ImportMedical/index", "/basedata/specialSignerTitleMaintance": "./basedata/SpecialSignerTitleMaintance/index", "/emphiresep/sendorder/customerbubcontract": "./emphiresep/sendorder/CustomerSubcontract/index", "/emphiresep/sendorder/querytransferandsubcontract": "./emphiresep/sendorder/QueryTransferAndSubcontract/index", "/emphiresep/sendorder/handover": "./emphiresep/sendorder/QueryTransferInfo/index", "/emphiresep/sendorder/ManageTemplate": "./emphiresep/sendorder/ManageTemplate/index", "/emphiresep/emporder/QueryEmpOrderListForGen": "./emphiresep/emporder/QueryEmpOrderListForGen/index", "/emphiresep/emporder/QueryClientOrderForAdd": "./emphiresep/emporder/QueryClientOrderForAdd/index", "/emphiresep/emporder/QueryEmpOrderListForPer": "./emphiresep/emporder/QueryEmpOrderListForPer/index", "/emphiresep/emporder/QueryEmpOrderListForCon": "./emphiresep/emporder/QueryEmpOrderListForCon/index", "/emphiresep/emporder/QueryEmpOrderListForEdit": "./emphiresep/emporder/QueryEmpOrderListForEdit/index", "/emphiresep/emporder/QueryEmployeeMonth": "./emphiresep/emporder/QueryEmployeeMonth/index", "/emphiresep/emporder/EmpBankCardMaintainance": "./emphiresep/emporder/EmpBankCardMaintainance/index", "/emphiresep/emporder/UpdateOderEmpIDC": "./emphiresep/emporder/UpdateOderEmpIDC/index", "/emphiresep/emporder/ImportBankCard": "./emphiresep/emporder/ImportBankCard/index", "/emphiresep/emporder/ConfirmEmpOrderForAssigner": "./emphiresep/emporder/ConfirmEmpOrderForAssigner/index", "/emphiresep/emporder/QueryEmpOrderListForTransfer": "./emphiresep/emporder/QueryEmpOrderListForTransfer/index", "/emphiresep/emporder/QueryEmployeeOrder": "./emphiresep/emporder/QueryEmployeeOrder/index", "/emphiresep/emporder/EmployeeBaseInfoManage": "./emphiresep/emporder/EmployeeBaseInfoManage/index", "/emphiresep/emporder/NationWideEmpQuery": "./emphiresep/emporder/NationWideEmpQuery/index", "/emphiresep/emporder/QueryImpOrderLite": "./emphiresep/emporder/QueryImpOrderLite/index", "/emphiresep/emporder/QueryImpOrderPro": "./emphiresep/emporder/QueryImpOrderPro/index", "/emphiresep/emporder/QueryImpWrongOrderPro": "./emphiresep/emporder/QueryImpWrongOrderPro/index", "/emphiresep/emporder/BatchAlterEmpOrderManage": "./emphiresep/emporder/BatchAlterEmpOrderManage/index", "/emphiresep/emporder/QueryBatchDelProduct": "./emphiresep/emporder/QueryBatchDelProduct/index", "/emphiresep/emporder/EmpFeeSearchReport": "./emphiresep/emporder/EmpFeeSearchReport/index", "/emphiresep/emporder/QueryEmpDetailInfo": "./emphiresep/emporder/QueryEmpDetailInfo/index", "/emphiresep/emporder/QueryAdjustment": "./emphiresep/emporder/QueryAdjustment/index", "/emphiresep/emporder/QueryMinBaseAdjustment": "./emphiresep/emporder/QueryMinBaseAdjustment/index", "/emphiresep/quitManager/QueryEmpOrderListForSepApply": "./emphiresep/quitManager/QueryEmpOrderListForSepApply/index", "/emphiresep/quitManager/QueryExEmpOrderListForSepCon": "./emphiresep/quitManager/QueryExEmpOrderListForSepCon/index", "/emphiresep/quitManager/QueryEmpOrderListForSepCon": "./emphiresep/quitManager/QueryEmpOrderListForSepCon/index", "/emphiresep/quitManager/QueryClientOrderForReduce": "./emphiresep/quitManager/QueryClientOrderForReduce/index", "/emphiresep/quitManager/batchQuitLite": "./emphiresep/quitManager/BatchQuitLite/index", "/emphiresep/empSMSInfo": "./emphiresep/empSMSInfo/index", "/emphiresep/callcenter/QueryCcWorkOrder": "./emphiresep/callcenter/QueryCcWorkOrder/index", "/emphiresep/hire/materialsCollection": "./emphiresep/hire/MaterialsCollection/index", "/emphiresep/hire/search": "./emphiresep/hire/MaterialsCollectionSearch/index", "/emphiresep/hire/queryEmpHireFromApp": "./emphiresep/hire/QueryEmpHireFromApp/index", "/emphiresep/hire/HireClassify": "./emphiresep/hire/HireClassify/index", "/emphiresep/hire/HireClassifyAgain": "./emphiresep/hire/HireClassifyAgain/index", "/emphiresep/hire/HireListType": "./emphiresep/hire/HireListType/index", "/emphiresep/hire/queryCmpAccount": "./emphiresep/hire/QueryCmpAccount/index", "/emphiresep/hire/queryCmpAccountControl": "./emphiresep/hire/QueryCmpAccountControl/index", "/empwelfare/socialmanage/QuerySSLock": "./empwelfare/LockManage/Social", "/empwelfare/socialmanage/report/SocialSecReport": "./empwelfare/Report/SocialSecReport", "/empwelfare/providentFund/QuerySSLock": "./empwelfare/LockManage/Provident", "/empwelfare/providentFund/report/ProvidentFunReport": "./empwelfare/Report/ProvidentFunReport", "/empwelfare/socialmanage/socialapply": "./empwelfare/Socialmanage/SocialApply", "/empwelfare/socialmanage/SocialUpdateSelect": "./empwelfare/Socialmanage/SocialUpdateSelect", "/empwelfare/providentFund/UpdateSelect": "./empwelfare/ProvidentFund/PfUpdateSelect", "/empwelfare/providentFund/SocialApplySelect": "./empwelfare/ProvidentFund/ProvidentFundApply", "/empwelfare/socialmanage/socialProcess": "./empwelfare/Socialmanage/SocialProcess/index", "/empwelfare/socialmanage/ssacct": "./empwelfare/Socialmanage/Ssacct", "/empwelfare/socialmanage/QuerySsAdjustment": "./empwelfare/Socialmanage/QuerySsAdjustment", "/empwelfare/socialmanage/QuerySsMinBaseAdjustment": "./empwelfare/Socialmanage/QuerySsMinBaseAdjustment", "/empwelfare/providentFund/QueryPfAdjustment": "./empwelfare/ProvidentFund/QueryPfAdjustment", "/empwelfare/providentFund/QueryPfMinBaseAdjustment": "./empwelfare/ProvidentFund/QueryPfMinBaseAdjustment", "/empwelfare/providentFund/ssacct": "./empwelfare/ProvidentFund/Ssacct", "/empwelfare/socialmanage/SocialPay": "./empwelfare/PayManage/SocialPay", "/empwelfare/providentFund/ProvidentFundPay": "./empwelfare/PayManage/ProvidentFundPay", "/empwelfare/socialmanage/ssImpBatch": "./empwelfare/Socialmanage/QuerySSImpBatch", "/empwelfare/providentFund/ssImpBatch": "./empwelfare/ProvidentFund/QuerySSImpBatch", "/empwelfare/socialmanage/socialSecurity": "./empwelfare/Socialmanage/QuerySocialSecurity", "/empwelfare/providentFund/socialSecurity": "./empwelfare/ProvidentFund/QuerySocialSecurity", "/empwelfare/socialmanage/socialMakeUp": "./empwelfare/Socialmanage/QuerySocialMakeUp", "/empwelfare/providentFund/socialMakeUp": "./empwelfare/ProvidentFund/QuerySocialMakeUp", "/empwelfare/laborcontract": "./empwelfare/LaborcontractManage", "/empwelfare/providentFund/process": "./empwelfare/ProvidentFund/ProvidentFundProcess", "/empwelfare/socialmanage/socialStop": "./empwelfare/Socialmanage/SocialStop", "/empwelfare/providentFund/fundStop": "./empwelfare/ProvidentFund/ProvidentFundStop", "/empwelfare/socialmanage/SocialChangeSelect": "./empwelfare/Socialmanage/SocialChange", "/empwelfare/providentFund/ChangeSelect": "./empwelfare/ProvidentFund/PfChange", "/empwelfare/ebmtransact/simpleBusiness/SimpleImpInterface": "./empwelfare/Ebmtransact/SimpleBusiness/SimpleImpInterface", "/empwelfare/ebmtransact/simpleBusiness/SimpleBusinessTransact": "./empwelfare/Ebmtransact/SimpleBusiness/SimpleBusinessTransact", "/empwelfare/ebmtransact/simpleBusiness/SimpleBusinessQuery": "./empwelfare/Ebmtransact/SimpleBusiness/SimpleBusinessQuery", "/empwelfare/ebmtransact/EbmTransactQuery": "./empwelfare/Ebmtransact/EbmTransactQuery", "/empwelfare/ebmtransact/HospitalTransact": "./empwelfare/Ebmtransact/HospitalTransact", "/empwelfare/ebmtransact/QueryEmpBizBooking": "./empwelfare/Ebmtransact/QueryEmpBizBooking", "/empwelfare/ebmtransact/SocialTransact": "./empwelfare/Ebmtransact/SocialTransact", "/empwelfare/ebmtransact/PorvidentTransact": "./empwelfare/Ebmtransact/PorvidentTransact", "/empwelfare/ebmtransact/OnTransact": "./empwelfare/Ebmtransact/OnTransact", "/empwelfare/socialmanage/QuerySIBackPayImp": "./empwelfare/Socialmanage/QuerySIBackPayImp", "/empwelfare/socialmanage/AdditionalSiQuery": "./empwelfare/Socialmanage/AdditionalSiQuery", "/empwelfare/providentFund/QuerySIBackPayImp": "./empwelfare/ProvidentFund/QuerySIBackPayImp", "/empwelfare/providentFund/AdditionalSiQuery": "./empwelfare/ProvidentFund/AdditionalSiQuery", "/empwelfare/socialmanage/QuerySsAndFundPayDetail": "./empwelfare/Socialmanage/QuerySsAndFundPayDetail", "/empwelfare/socialmanage/socialBudget": "./empwelfare/Socialmanage/BudgetQuery", "/empwelfare/filemanagement/QueryFileManagement": "./empwelfare/filemanagement/QueryFileManagement", "/empwelfare/filemanagement/FilePay": "./empwelfare/FilePay", "/externalSupplier/receive/QueryExBill": "./externalSupplier/Receive/QueryExBill/index", "/externalSupplier/receive/CreateExBillPrc": "./externalSupplier/Receive/CreateExBillPrc/index", "/externalSupplier/receive/ExOnecharges": "./externalSupplier/Receive/ExOnecharges/index", "/externalSupplier/report/ExSupPayByDeptReport": "./externalSupplier/report/ExSupPayByDeptReport", "/externalSupplier/report/ExSupPayByPrvdReport": "./externalSupplier/report/ExSupPayByPrvdReport", "/externalSupplier/emporder/QueryExEmpOrderListForPer": "./externalSupplier/emporder/QueryExEmpOrderListForPer/index", "/externalSupplier/emporder/QueryExEmpOrderListForEdit": "./externalSupplier/emporder/QueryExEmpOrderListForEdit/index", "/externalSupplier/emporder/QueryExEmployeeOrder": "./externalSupplier/emporder/QueryExEmployeeOrder/index", "/externalSupplier/emporder/QueryChangeFeeTemplate": "./externalSupplier/emporder/QueryChangeFeeTemplate/index", "/externalSupplier/emporder/QueryExEmpProOrderListForPer": "./externalSupplier/emporder/QueryExEmpProOrderListForPer/index", "/externalSupplier/emporder/QueryImpOrderProEx": "./externalSupplier/emporder/QueryImpOrderProEx/index", "/externalSupplier/suppliermanage/QueryPrvdAssignCS": "./externalSupplier/suppliermanage/QueryPrvdAssignCS/index", "/externalSupplier/suppliermanage/Provider": "./externalSupplier/suppliermanage/Provider/index", "/externalSupplier/suppliermanage/QueryProviderGroup": "./externalSupplier/suppliermanage/QueryProviderGroup/index", "/externalSupplier/payApprove/prvdPayApprove": "./externalSupplier/payApprove/prvdPayApprove/index", "/externalSupplier/payApprove/prvdPayApply": "./externalSupplier/payApprove/prvdPayApply/index", "/externalSupplier/payApprove/filePrvdPayApply": "./externalSupplier/payApprove/filePrvdPayApply/index", "/externalSupplier/receive/ExBillLockAndUnLock": "./externalSupplier/Receive/ExBillLockAndUnLock/index", "/externalSupplier/receive/ExBillPrintReport": "./externalSupplier/Receive/ExBillPrintReport/index", "/externalSupplier/payApprove/prvdPayQuery": "./externalSupplier/payApprove/prvdPayQuery/index", "/externalSupplier/payApprove/prvdPaySummaryQuery": "./externalSupplier/payApprove/prvdPaySummaryQuery/index", "/externalSupplier/payApprove/filePrvdPayQuery": "./externalSupplier/payApprove/filePrvdPayQuery/index", "/externalSupplier/payApprove/filePrvdPaySummaryQuery": "./externalSupplier/payApprove/filePrvdPaySummaryQuery/index", "/externalSupplier/payApprove/QueryDisabilityImp": "./externalSupplier/payApprove/QueryDisabilityImp/index", "/externalSupplier/payApprove/QueryPrvdTotalPayment": "./externalSupplier/payApprove/QueryPayment/QueryPrvdTotalPayment", "/externalSupplier/payApprove/QueryPrvdPayment": "./externalSupplier/payApprove/QueryPayment/QueryPrvdPayment", "/externalSupplier/payApprove/QueryPrvdPaymentDetail": "./externalSupplier/payApprove/QueryPrvdPaymentDetail", "/finance/receive/QueryBill": "./finance/Receive/QueryBill/index", "/finance/receive/CreateBill": "./finance/Receive/CreateBill/index", "/finance/receive/Onecharges": "./finance/Receive/Onecharges/index", "/finance/receive/BillLockAndUnlock": "./finance/Receive/BillLockAndUnlock/index", "/finance/receive/BillPrintReport": "./finance/Receive/BillPrintReport/index", "/finance/receive/QueryBillUnlockInfo": "./finance/Receive/QueryBillUnlockInfo/index", "/finance/receive/SpecialBillPrintReport": "./finance/Receive/SpecialBillPrintReport/index", "/finance/receive/BillGroupManage": "./finance/Receive/BillGroupManage/index", "/finance/receive/EmpBillQuery": "./finance/Receive/EmpBillQuery/index", "/finance/receive/OrderBill": "./finance/Receive/OrderBill/index", "/finance/receive/OrderBillLog": "./finance/Receive/OrderBillLog/index", "/finance/receive/QueryBillPc": "./finance/Receive/QueryBillPc/index", "/finance/gathering/UploadCash": "./finance/Gathering/UploadCash/index", "/finance/gathering/InvalidReceipt": "./finance/Gathering/InvalidReceipt", "/finance/gathering/InvalidInvoice": "./finance/Gathering/Invoice/InvalidInvoice/index", "/finance/gathering/ReceiptsQuery": "./finance/Gathering/ReceiptsQuery", "/finance/gathering/ArrivalGathering": "./finance/Gathering/ArrivalGathering", "/finance/gathering/QueryInvoice": "./finance/Gathering/Invoice/QueryInvoice/index", "/finance/gathering/InvoiceEntry": "./finance/Gathering/InvoiceEntry/index", "/finance/gathering/Receipts": "./finance/Gathering/Receipts/index", "/finance/pay/OtherPay": "./finance/Pay/OtherPay/index", "/finance/pay/Query": "./finance/Pay/Query/index", "/finance/pay/QuerySocialAssignerProviderPay": "./finance/Pay/QuerySocialAssignerProviderPay/index", "/infopublication/policy/siteQuery": "./infopublication/Policy/SiteQuery/index", "/infopublication/policy/calculationSsCust": "./infopublication/Policy/CalculationSsCust/index", "/infopublication/policy/calculationSsSale": "./infopublication/Policy/CalculationSsSale/index", "/infopublication/policy/calculationSsCustMult": "./infopublication/Policy/CalculationSsCustMult/index", "/infopublication/policy/calculationSsCityMult": "./infopublication/Policy/CalculationSsCityMult/index", "/infopublication/policy/surveySsPolicyCust": "./infopublication/Policy/SurveySsPolicyCust/index", "/infopublication/policy/surveySsPolicySale": "./infopublication/Policy/SurveySsPolicySale/index", "/infopublication/policy/surveySsPolicyCustMult": "./infopublication/Policy/SurveySsPolicyCustMult/index", "/infopublication/policy/surveySsPolicyCityMult": "./infopublication/Policy/SurveySsPolicyCityMult/index", "/payroll/agentWageIdCardNumManage": "./payroll/AgentWageIdCardNumManage/index", "/payroll/editEmpBankCard": "./payroll/EditEmpBankCard/index", "/payroll/ndhsqj/QueryCalAnnuaData": "./payroll/CalAnnual/QueryCalAnnuaData", "/payroll/ndhsqj/CalAnnualDeclareImp": "./payroll/CalAnnual/CalAnnualDeclareImp", "/payroll/ndhsqj/CalAnnualResultImp": "./payroll/CalAnnual/CalAnnualResultImp", "/payroll/ndhsqj/CalAnnualImportEmp": "./payroll/CalAnnual/CalAnnualImportEmp", "/payroll/ndhsqj/KJYWRGL": "./payroll/ndhsqj/KJYWRGL/index", "/payroll/ndhsqj/hsqjkhgl": "./payroll/ndhsqj/hsqjkhgl/index", "/payroll/deduction/queryUploadDeductionDetail": "./payroll/deduction/QueryUploadDeductionDetail/index", "/payroll/deduction/taxReportHugeCust": "./payroll/deduction/TaxReportHugeCust/index", "/payroll/deduction/taxReportProvider": "./payroll/deduction/TaxReportProvider/index", "/payroll/deduction/taxReportIndependent": "./payroll/deduction/TaxReportIndependent/index", "/payroll/deduction/custTaxReport": "./payroll/deduction/CustTaxReport/index", "/payroll/deduction/taxReportSupply": "./payroll/deduction/TaxReportSupply/index", "/payroll/deduction/uploadTaxDeductionLarge": "./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionLarge", "/payroll/deduction/uploadTaxDeductionSingle": "./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionSingle", "/payroll/deduction/uploadTaxDeductionSupplier": "./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionSupplier", "/payroll/deduction/specialDeductionReport": "./payroll/deduction/SpecialDeductionReport/index", "/payroll/queryPayRollClass": "./payroll/QueryPayRollClass/index", "/payroll/payRollClassItemManage": "./payroll/PayRollClassItemManage/index", "/payroll/queryPayRollClassItem": "./payroll/QueryPayRollClassItem/index", "/payroll/queryTaxRate": "./payroll/taxrate/QueryTaxRate", "/payroll/queryWithholdAgent": "./payroll/withholdAgent/QueryWithholdAgent", "/payroll/bankcardQuery": "./payroll/bankcardqry/BankcardQuery", "/payroll/XZFFPC/HFRYBB": "./payroll/XZFFPC/HFRYBB/index", "/payroll/XZFFPC/YBFFRYBB": "./payroll/XZFFPC/YBFFRYBB/index", "/payroll/XZFFPCXN/XJFFPCXN": "./payroll/XZFFPCXN/XJFFPCXN/index", "/payroll/bswjdc": "./payroll/bswjdc/index", "/payroll/BatchFeedBackSendResult": "./payroll/BatchFeedBackSendResult", "/payroll/EmployeeInfoAgentWageManage": "./payroll/EmployeeInfoAgentWageManage", "/payroll/QueryPayRollResult": "./payroll/QueryPayRollResult", "/payroll/QueryPaySendStage": "./payroll/QueryPaySendStage", "/payroll/rptPsnSetFailureManages": "./payroll/rptPsnSetFailureManages", "/payroll/XZDFS/SENDMAIL": "./payroll/XZDFS/SENDMAIL/index", "/payroll/distributePayrollSpecialiat": "./payroll/DistributePayrollSpecialiat/index", "/payroll/queryEosWageData": "./payroll/QueryEosWageData/index", "/payroll/XZDFS/SENDMAILRESULT": "./payroll/XZDFS/SENDMAILRESULT/index", "/payroll/XZFFPCXN/SZFFJGXN": "./payroll/XZFFPCXN/SZFFJGXN/index", "/payroll/XZFFPCXN/XZZFCXXN": "./payroll/XZFFPCXN/XZZFCXXN/index", "/payroll/XZFFPCXN/HFRYBBXN": "./payroll/XZFFPCXN/HFRYBBXN/index", "/payroll/archives/CheckHrieDtManages": "./payroll/archives/CheckHrieDtManages/index", "/payroll/archives/QueryCheckHireDt": "./payroll/archives/QueryCheckHireDt/index", "/payroll/archives/CheckEmpTypeManages": "./payroll/archives/CheckEmpTypeManages/index", "/payroll/archives/QueryEmpType": "./payroll/archives/QueryEmpType/index", "/payroll/DataInterfaceManage": "./payroll/DataInterfaceManage", "/payroll/PureDataInterfaceManage": "./payroll/PureDataInterfaceManage", "/payroll/PayRollArchivesManage": "./payroll/PayRollArchivesManage/index", "/payroll/CountPayManage": "./payroll/CountPayManage/index", "/payroll/XZFFPC/XJFFPC": "./payroll/XZFFPC/XJFFPC/index", "/payroll/XZFFPC/QueryPayResult": "./payroll/XZFFPC/QueryPayResult/index", "/payroll/XZFFPC/firstFeedBackSendManages": "./payroll/XZFFPC/FirstFeedBackSendManages/index", "/payroll/XZFFPC/secondFeedBackSendManages": "./payroll/XZFFPC/SecondFeedBackSendManages/index", "/payroll/XZFFPC/updateSuccessManages": "./payroll/XZFFPC/UpdateSuccessManages/index", "/payroll/XZFFPC/updateBatchPayRollSpecialiat": "./payroll/XZFFPC/UpdateBatchPayRollSpecialiat/index", "/payroll/XZFFPC/SetPayResult": "./payroll/XZFFPC/SetPayResult/index", "/payroll/XZFFPC/QueryWageAgainSend": "./payroll/XZFFPC/QueryWageAgainSend/index", "/payroll/send/PayTaxBatchManage": "./payroll/send/PayTaxBatchManage", "/payroll/withholdAgentLimitMaintance": "./payroll/WithholdAgentLimitMaintance", "/payroll/payTaxes": "./payroll/PayTaxes", "/report/finance/finReceivableReport": "./report/finance/FinReceivableReport/index", "/report/finance/arrearInfoReport": "./report/finance/ArrearInfoReport/index", "/report/finance/internalSettlementReport": "./report/finance/InternalSettlementReport/index", "/report/finance/finEfOrReport": "./report/finance/FinEfOrReport/index", "/report/finance/finReceiptDetailRpt": "./report/finance/FinReceiptDetailRpt/index", "/report/finance/finReceivableDetailRpt": "./report/finance/FinReceivableDetailRpt/index", "/report/finance/paymentByGroupRpt": "./report/finance/PaymentByGroupRpt/index", "/report/finance/investorBillReport": "./report/finance/InvestorBillReport/index", "/report/finance/investorCountReport": "./report/finance/InvestorCountReport/index", "/report/finance/finReceiptInvoiceBill": "./report/finance/FinReceiptInvoiceBill/index", "/report/finance/customerDailyVersion": "./report/finance/CustomerDailyVersion/index", "/report/finance/servfeeReturnRptQuery": "./report/finance/ServfeeReturnRptQuery/index", "/report/finance/noInvoiceBill": "./report/finance/NoInvoiceBill/index", "/report/sales/contractApprovalStepTimeReport": "./report/sales/ContractApprovalStepTimeReport/index", "/report/sales/customerServiceStatus": "./report/sales/CustomerServiceStatus/index", "/report/customerserv/clientCustReport": "./report/customerserv/ClientCustReport/index", "/report/customerserv/clientGroupReport": "./report/customerserv/ClientGroupReport/index", "/report/customerserv/decreaseMemberReport": "./report/customerserv/DecreaseMemberReport/index", "/report/customerserv/increaseMemberReport": "./report/customerserv/IncreaseMemberReport/index", "/report/customerserv/laborContractRenewReport": "./report/customerserv/LaborContractRenewReport/index", "/report/customerserv/customerInfoReport": "./report/customerserv/CustomerInfoReport/index", "/report/customerserv/customerServiceEffect": "./report/customerserv/CustomerServiceEffect/index", "/report/qualitycontrol/sysBillTimelyRatio": "./report/qualitycontrol/SysBillTimelyRatio/index", "/report/qualitycontrol/sysBillTimelyRatioFinal": "./report/qualitycontrol/SysBillTimelyRatioFinal/index", "/report/qualitycontrol/systemBillCompleteRatio": "./report/qualitycontrol/SystemBillCompleteRatio/index", "/report/qualitycontrol/staffEntryRatio": "./report/qualitycontrol/StaffEntryRatio/index", "/report/qualitycontrol/labourContractRatio": "./report/qualitycontrol/LabourContractRatio/index", "/report/qualitycontrol/socialSecurityCompleteRatio": "./report/qualitycontrol/SocialSecurityCompleteRatio/index", "/report/qualitycontrol/sepRecSendorRatio": "./report/qualitycontrol/SepRecSendorRatio/index", "/report/qualitycontrol/employeesTimelyRateReport": "./report/qualitycontrol/EmployeesTimelyRateReport/index", "/report/qualitycontrol/payInputInterfaceReport": "./report/qualitycontrol/PayInputInterfaceReport/index", "/report/qualitycontrol/customerPayReturnRatio": "./report/qualitycontrol/CustomerPayReturnRatio/index", "/report/qualitycontrol/customerPayReturnRatioc": "./report/qualitycontrol/CustomerPayReturnRatioc/index", "/report/qualitycontrol/payrollRoleAuditReport": "./report/qualitycontrol/PayrollRoleAuditReport/index", "/report/qualitycontrol/payrollApplyRatioReport": "./report/qualitycontrol/PayrollApplyRatioReport/index", "/report/qualitycontrol/orderRecSenderRatio": "./report/qualitycontrol/OrderRecSenderRatio/index", "/report/qualitycontrol/billUnlockRatio": "./report/qualitycontrol/BillUnlockRatio/index", "/report/qualitycontrol/checkPersonalOrderReport": "./report/qualitycontrol/CheckPersonalOrderReport/index", "/report/qualitycontrol/perfectingPersonalOrderReport": "./report/qualitycontrol/PerfectingPersonalOrderReport/index", "/report/qualitycontrol/personIncrementDetail": "./report/qualitycontrol/PersonIncrementDetail/index", "/report/qualitycontrol/personDecrementDetail": "./report/qualitycontrol/PersonDecrementDetail/index", "/report/qualitycontrol/increaseAndDecreaseStaffReport": "./report/qualitycontrol/IncreaseAndDecreaseStaffReport/index", "/report/qualitycontrol/contractDeclarationReport": "./report/qualitycontrol/ContractDeclarationReport/index", "/report/qualitycontrol/contractPriceCompare": "./report/qualitycontrol/ContractPriceCompare/index", "/report/qualitycontrol/evacuateOrder": "./report/qualitycontrol/EvacuateOrder/index", "/report/qualitycontrol/evacuateWarningQuery": "./report/qualitycontrol/EvacuateWarningQuery/index", "/report/qualitycontrol/collectionPaymentReport": "./report/qualitycontrol/CollectionPaymentReport/index", "/report/qualitycontrol/complainAppSelect": "./report/qualitycontrol/ComplainAppSelect/index", "/report/qualitycontrol/clientEmpVisitedReport": "./report/qualitycontrol/ClientEmpVisitedReport/index", "/report/qualitycontrol/clientCustVisitedReport": "./report/qualitycontrol/ClientCustVisitedReport/index", "/report/qualitycontrol/billDifferenceReport": "./report/qualitycontrol/BillDifferenceReport/index", "/report/qualitycontrol/realityBillDifference": "./report/qualitycontrol/RealityBillDifference/index", "/report/qualitycontrol/customerCollectInfoReport": "./report/qualitycontrol/CustomerCollectInfoReport/index", "/report/qualitycontrol/newCustTransferRatioQAReport": "./report/qualitycontrol/NewCustTransferRatioQAReport/index", "/report/qualitycontrol/customerServiceReport": "./report/qualitycontrol/CustomerServiceReport/index", "/report/opermanagement/operatingManagement": "./report/opermanagement/OperatingManagement/index", "/report/opermanagement/operatingManageDetail": "./report/opermanagement/OperatingManageDetail/index", "/report/monitor/asigneeSocialSecDifference": "./report/monitor/AsigneeSocialSecDifference/index", "/report/monitor/orderRealityDifference": "./report/monitor/OrderRealityDifference/index", "/report/flashreport/flashreport": "./report/flashreport/Flashreport/index", "/sales/cust/delegateform": "./Sales/CustomerManage/DelegateCustomerForm/index", "/sales/cust/payrollSendingList": "./Sales/CustomerManage/PayrollSendingList/index", "/sales/cust/noNeedCalculateForm": "./Sales/CustomerManage/NoNeedCalculateForm/index", "/sales/cust/infoquery": "./Sales/CustomerManage/CustomerQuery/index", "/sales/cust/group-company": "./Sales/CustomerManage/GroupCompanyManager/index", "/sales/cust-crm/cccust": "./crm/cust/CCCust/index", "/sales/cust-crm/CustReport": "./crm/cust/CustReport/index", "/sales/product/productManagement": "./Sales/Product/ProductManagement/index", "/sales/product/productType": "./Sales/Product/ProductType/index", "/sales/product/SubProduct": "./Sales/Product/SubProduct/index", "/sales/product/InsuranceType": "./Sales/Product/InsuranceType/index", "/sales/product/InsuranceProvider": "./Sales/Product/InsuranceProvider/index", "/sales/cust/CustomerPayerAssert": "./Sales/CustomerManage/CustomerPayerAssert/index", "/sales/cust/CustomerPayerQuery": "./Sales/CustomerManage/CustomerPayerQuery/index", "/sales/cust-crm/pre-cust": "./crm/cust/QueryPreRegCust/index", "/sales/cust-crm/formal-cust": "./crm/cust/QueryFormalCust/index", "/sales/cust/salaryRelatedAssert": "./Sales/CustomerManage/SalaryRelatedAssert/index", "/sales/cust/salaryRelatedQuery": "./Sales/CustomerManage/SalaryRelatedQuery/index", "/sales/cust-crm/viewShareArea": "./crm/cust/viewShareArea/index", "/sales/cust-crm/allotShareArea": "./crm/cust/allotShareArea/index", "/sales/quotation/quotationTempManage": "./Sales/Quotation/QuotationTempManage/index", "/sales/contract/query": "./Sales/Contract/Query", "/sales/contract/manage": "./Sales/Contract/Manage", "/sales/quotation/quotationManage": "./Sales/Quotation/QuotationManage/index", "/sales/quotation/quotationQuery": "./Sales/Quotation/QuotationQuery/index", "/sales/cust-crm/viewDelArea": "./crm/cust/viewDelArea/index", "/sales/cust-crm/allotDelArea": "./crm/cust/allotDelArea/index", "/sales/cust-crm/viewShareAreaAll": "./crm/cust/viewShareAreaAll/index", "/sales/cust-crm/viewShareAreaLocal": "./crm/cust/viewShareAreaLocal/index", "/sales/cust-crm/viewShareAreaYyzx": "./crm/cust/viewShareAreaYyzx/index", "/sales/cust-crm/viewDelAreaLocal": "./crm/cust/viewDelAreaLocal/index", "/sales/cust-crm/viewDelAreaAll": "./crm/cust/viewDelAreaAll/index", "/sales/cust-crm/viewDelAreaYyzx": "./crm/cust/viewDelAreaYyzx/index", "/sales/cust-crm/queryCrmBusinessLog": "./crm/cust/QueryCrmBusinessLog/index", "/sales/cust-crm/queryCustDuplicate": "./crm/cust/QueryCustDuplicate/index", "/sales/cust-crm/customerSimilarity": "./crm/cust/CustomerSimilarity/index", "/sales/cust-crm/salesRelatedDoc": "./crm/cust/SalesRelatedDoc/index", "/sales/contract/modelContractQuery": "./Sales/Contract/ModelContractQuery", "/sales/contract/queryContractVersion": "./Sales/Contract/QueryContractVersion", "/sales/cust-crm/customerService": "./crm/cust/QueryCustomerService", "/sales/cust-crm/customerServiceMonth": "./crm/cust/QueryCustomerServiceMonth", "/sales/cust-crm/debtreminder": "./crm/cust/QueryDebtReminder", "/sales/cust-crm/queryMarketActivity": "./crm/cust/QueryMarketActivity", "/sales/cust-crm/queryPreRegMarket": "./crm/cust/QueryPreRegMarket", "/sales/informationShare/queryMarketActivitySign": "./crm/informationShare/QueryMarketActivitySign", "/sales/informationShare/queryClassActivitySign": "./crm/informationShare/QueryClassActivitySign", "/sales/cust/QueryClientCompany": "./Sales/CustomerManage/QueryClientCompany/index", "/sysmanage/accounts": "./sysmanage/Account/index", "/sysmanage/functions": "./sysmanage/Sysfunction/index", "/sysmanage/role": "./sysmanage/Role/index", "/sysmanage/modifypassword": "./sysmanage/ModifyPassword/index", "/sysmanage/allocateservice": "./sysmanage/AllocateService/index", "/sysmanage/department": "./sysmanage/Department/index", "/sysmanage/queryMimicInfo": "./sysmanage/QueryMimicInfo/index", "/sysmanage/newestUpdateSelect": "./sysmanage/NewestUpdateSelect/index", "/sysmanage/datePublishSelect": "./sysmanage/DatePublishSelect/index", "/sysmanage/ssReportConfiguration": "./sysmanage/SsReportConfiguration/index", "/sysmanage/support": "./sysmanage/Support/index", "/sysmanage/onlineTask": "./sysmanage/OnlineTask/index", "/sysmanage/workFlowManage": "./sysmanage/WorkFlowManage/index", "/test/wageformula": "./payroll/WageFormulaExam"}