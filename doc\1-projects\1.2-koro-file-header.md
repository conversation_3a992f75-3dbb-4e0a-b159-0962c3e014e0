<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-09-06 17:54:27
 * @LastAuthor: 侯成
 * @LastTime: 2019-09-10 19:07:21
 * @message: 
 -->

# koroFileHeader的使用

## 如何给文件添加作者信息

插件介绍 [github.com/OBKoro1/koro1FileHeader](https://github.com/OBKoro1/koro1FileHeader)

本地配置：
打开`.vscode/settings.json` 文件，更改为以下内容：
```json
{
    "editor.tabSize": 2,
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true
    },
    "fileheader.customMade": {
        "Author": "你的名字",
        "Email": "你的邮箱",
        "Date": "Do not edit",
        "LastEditors": "你的名字",
        "LastEditTime": "Do not edit",
        "Description": ""
    },
    "fileheader.configObj": {
        "specialOptions": {
            "LastEditors": "LastAuthor",
            "LastEditTime": "LastTime",
            "Description": "message"
        }
    },
    "commitHooks": {
      "allowHooks": false, // 默认允许进行hooks，设为false即可关闭
    }
}
```
若文件不存在，请自己新建。`fileheader.customMade`和`fileheader.configObj`是这个插件的配置项。请将 `Author` 和 `Email` 的值更改为正确内容，其他项保持不变。

## 使用简介

1. **文件头部添加注释**:
   
   *  在文件开头添加注释，记录文件信息
   *  支持用户高度自定义注释选项
   *  保存文件的时候，自动更新最后的编辑时间和编辑人
   *  快捷键：`window`：`ctrl + alt + i`, `mac`：`ctrl + cmd + i`

2. **在光标处添加函数注释**:

    * 在光标处自动生成一个注释模板，下方有栗子
    * 支持用户高度自定义注释选项
    * 快捷键：`window`：`ctrl + alt + t`, `mac`：`ctrl + cmd+t`
    * 快捷键不可用很可能是被占用了,[参考这里](https://github.com/OBKoro1/koro1FileHeader/issues/5)

##　效果展示

```tsx
// src/components/DateRange/index.tsx

/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-25 16:00:03
 * @lastTime: 2019-07-25 17:10:44
 * @LastAuthor: Do not edit
 * @message: 
 */
import { DatePicker, Col } from 'antd';
import * as moment from 'moment';
import React from 'react';
import Form, { WrappedFormUtils } from 'antd/lib/form/Form';
import { DateRangeFormItem } from '@/utils/settings/forms';

import FormItem from '@/components/Forms/FormItem';
const backFormatter = 'YYYY/MM/DD'
```
