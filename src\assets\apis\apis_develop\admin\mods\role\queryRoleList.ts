import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/role/queryRoleList
     * @desc 查询角色列表
查询角色列表
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.RoleDTO();
export const url = '/rhro-service-1.0/role/queryRoleList:POST';
export const initialUrl = '/rhro-service-1.0/role/queryRoleList';
export const cacheKey = '_role_queryRoleList_POST';
export async function request(
  data: defs.admin.roleQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/role/queryRoleList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.roleQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/role/queryRoleList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
