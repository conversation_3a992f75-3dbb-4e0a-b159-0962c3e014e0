import React, { useState } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, FormInstance, Typography, message } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance, useWritable } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import {
  CommonBaseDataSelector,
  BusTypeDropdownListSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { LocalBusinessContentForm, BusinessNodeForm, NodeMaterialForm } from './components';

export const isOrNoMap = new Map<number | string, string>([
  ['1', '是'],
  ['0', '否'],
]);

export const handleAttributeMap = new Map<number | string, string>([
  ['1', '流程业务'],
  ['2', '单次业务'],
]);

export const handleObjectMap = new Map<number | string, string>([
  ['1', '客户'],
  ['2', '员工'],
]);

export const handleMethodMap = new Map<number | string, string>([
  ['1', '员工办理'],
  ['2', '客户办理'],
]);

export const employmentStatusMap = new Map<number | string, string>([
  ['1', '在职'],
  ['2', '离职'],
  ['3', '入职未生效'],
]);

export const actualStatusMap = new Map<number | string, string>([
  ['1', '在缴'],
  ['2', '停缴'],
  ['3', '已过期'],
]);

const LocalBusinessMaintenance = () => {
  const [form] = Form.useForm();
  
  // 各地业务内容弹窗状态
  const [businessContentVisible, setBusinessContentVisible] = useState(false);
  const [businessContentInitialInfo, setBusinessContentInitialInfo] = useState<any>({});
  
  // 业务节点弹窗状态
  const [businessNodeVisible, setBusinessNodeVisible] = useState(false);
  const [businessNodeInitialInfo, setBusinessNodeInitialInfo] = useState<any>({});
  
  // 业务节点材料弹窗状态
  const [nodeMaterialVisible, setNodeMaterialVisible] = useState(false);
  const [nodeMaterialInitialInfo, setNodeMaterialInitialInfo] = useState<any>({});
  
  // 选中的业务内容记录
  const [selectedBusinessContent, setSelectedBusinessContent] = useState<any>(null);
  // 选中的业务节点记录
  const [selectedBusinessNode, setSelectedBusinessNode] = useState<any>(null);
  
  const wriTableSub = useWritable({
    service: API.welfaremanage.ebmtransact.getData,
  });
  
  const wriTableMaterial = useWritable({
    service: API.welfaremanage.ebmtransact.getData,
  });

  // 处理业务内容选择
  const handleBusinessContentSelect = (record: any) => {
    setSelectedBusinessContent(record);
    if (record?.businessContentId) {
      wriTableSub.request({ businessContentId: record.businessContentId });
    } else {
      wriTableSub.request({ businessContentId: null });
    }
  };
  
  // 处理业务节点选择
  const handleBusinessNodeSelect = (record: any) => {
    setSelectedBusinessNode(record);
    if (record?.businessNodeId) {
      wriTableMaterial.request({ businessNodeId: record.businessNodeId });
    } else {
      wriTableMaterial.request({ businessNodeId: null });
    }
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '所属城市',
      fieldName: 'city',
      inputRender: 'string',
    },
    {
      label: '业务类型',
      fieldName: 'businessType',
      inputRender: (outerForm: FormInstance) => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
          onConfirm={() => {
            outerForm.setFieldsValue({ businessProject: undefined });
          }}
        />
      ),
    },
    {
      label: '业务项目',
      fieldName: 'businessProject',
      inputRender: (outerForm: FormInstance) => (
        <BusTypeDropdownListSelector
          allowClear
          params={{ categoryId: outerForm.getFieldValue('businessType') ?? '' }}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.businessType !== curValues.businessType;
      },
    },
    {
      label: '业务内容',
      fieldName: 'businessContent',
      inputRender: 'string',
    },
    {
      label: '办理属性',
      fieldName: 'handleAttribute',
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'handleObject',
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'handleMethod',
      inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    },
    {
      label: '是否微信显示',
      fieldName: 'isWechatShow',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'isClientShow',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
  ];

  const localBusinessColumns: WritableColumnProps<any>[] = [
    { title: '所属城市', dataIndex: 'city' },
    { title: '业务类型', dataIndex: 'businessTypeName' },
    { title: '业务项目', dataIndex: 'businessProjectName' }  ,
    { title: '业务内容', dataIndex: 'businessContentName' },
    { title: '办理属性', dataIndex: 'handleAttributeName' },
    { title: '办理对象', dataIndex: 'handleObjectName' },
    { title: '办理方式', dataIndex: 'handleMethodName' },
    {
      title: '是否微信显示',
      dataIndex: 'isWechatShow',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '是否客户端显示',
      dataIndex: 'isClientShow',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    { title: '业务内容说明', dataIndex: 'contentDesc' },
    {
      title: '是否有政府性收费',
      dataIndex: 'hasGovFee',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    { title: '政府性收费金额', dataIndex: 'govFeeAmount' },
    {
      title: '是否可以自助办理',
      dataIndex: 'canSelfService',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '入离职状态',
      dataIndex: 'employmentStatus',
      render: (text: any) => employmentStatusMap.get(text) || text,
    },
    { title: '缴纳产品要求', dataIndex: 'productRequirement' },
    {
      title: '实做状态',
      dataIndex: 'actualStatus',
      render: (text: any) => actualStatusMap.get(text) || text,
    },
    { title: '员工自助办理途径', dataIndex: 'selfServiceMethod' },
    {
      title: '业务内容状态',
      dataIndex: 'status',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '是否被引用',
      dataIndex: 'isReferenced',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
  ];

  const businessNodeColumns: WritableColumnProps<any>[] = [
    { title: '业务节点顺序', dataIndex: 'nodeOrder' },
    { title: '业务节点', dataIndex: 'businessNodeName' },
    {
      title: '是否收取资料',
      dataIndex: 'collectMaterial',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '是否支付津贴待遇',
      dataIndex: 'payAllowance',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    { title: '办理周期', dataIndex: 'processingTime' },
    {
      title: '业务节点状态',
      dataIndex: 'nodeStatus',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    {
      title: '是否被引用',
      dataIndex: 'isReferenced',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
  ];

  const nodeMaterialColumns: WritableColumnProps<any>[] = [
    { title: '材料编号', dataIndex: 'materialCode' },
    { title: '材料名称', dataIndex: 'materialName' },
    {
      title: '是否原件',
      dataIndex: 'isOriginal',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    { title: '材料数量', dataIndex: 'quantity' },
    {
      title: '是否返还材料',
      dataIndex: 'returnMaterial',
      render: (text: any) => isOrNoMap.get(text) || text,
    },
    { title: '用印签字方', dataIndex: 'signatureParty' },
    { title: '材料模板', dataIndex: 'template' },
  ];

  const renderButtons = (options: WritableInstance) => (
    <>
      <Button type="primary" htmlType="submit">
        查询
      </Button>
      {/* <AuthButtons funcId="local_business_btn"> */}
        <Button
          onClick={() => {
            setBusinessContentInitialInfo({});
            setBusinessContentVisible(true);
          }}
        >
          新增
        </Button>
      {/* </AuthButtons> */}
      <AuthButtons funcId="local_business_btn">
        <Button
          onClick={() => {
            const selectedRows = options.selectedRows;
            if (!selectedRows || selectedRows.length === 0) {
              message.warning('请选择要修改的记录');
              return;
            }
            if (selectedRows.length > 1) {
              message.warning('只能选择一条记录进行修改');
              return;
            }
            setBusinessContentInitialInfo(selectedRows[0]);
            setBusinessContentVisible(true);
          }}
        >
          修改
        </Button>
      </AuthButtons>
      <AuthButtons funcId="local_business_btn">
        <AsyncButton>删除</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="local_business_btn">
        <AsyncButton>发布</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="local_business_btn">
        <AsyncButton>失效</AsyncButton>
      </AuthButtons>
      <AuthButtons funcId="local_business_btn">
        <AsyncButton
          onClick={() => {
            options.handleExport(
              { service: API.welfaremanage.ebmtransact.exportExcel },
              {
                columns: localBusinessColumns,
                condition: { ...options.queries },
                fileName: '各地业务维护.xlsx',
              },
            );
          }}
        >
          导出数据
        </AsyncButton>
      </AuthButtons>
      {/* 各地业务内容弹窗 */}
      <LocalBusinessContentForm
        modal={[businessContentVisible, setBusinessContentVisible]}
        listOptions={options}
        initialInfo={businessContentInitialInfo}
      />
    </>
  );

  const renderBusinessNodeButtons = (options: WritableInstance) => (
    <>
      <AuthButtons funcId="local_business_btn">
        <Button
          disabled={!selectedBusinessContent}
          onClick={() => {
            if (!selectedBusinessContent) {
              message.warning('请先选择业务内容');
              return;
            }
            setBusinessNodeInitialInfo({
              businessContentId: selectedBusinessContent.businessContentId,
              businessContentName: selectedBusinessContent.businessContentName,
            });
            setBusinessNodeVisible(true);
          }}
        >
          新增
        </Button>
      </AuthButtons>
      <AuthButtons funcId="local_business_btn">
        <Button
          onClick={() => {
            const selectedRows = options.selectedRows;
            if (!selectedRows || selectedRows.length === 0) {
              message.warning('请选择要修改的记录');
              return;
            }
            if (selectedRows.length > 1) {
              message.warning('只能选择一条记录进行修改');
              return;
            }
            setBusinessNodeInitialInfo(selectedRows[0]);
            setBusinessNodeVisible(true);
          }}
        >
          修改
        </Button>
      </AuthButtons>
      <AuthButtons funcId="local_business_btn">
        <AsyncButton>删除</AsyncButton>
      </AuthButtons>
      {/* 业务节点弹窗 */}
      <BusinessNodeForm
        modal={[businessNodeVisible, setBusinessNodeVisible]}
        listOptions={options}
        initialInfo={businessNodeInitialInfo}
      />
    </>
  );

  const renderNodeMaterialButtons = (options: WritableInstance) => (
    <>
      <AuthButtons funcId="local_business_btn">
        <Button
          disabled={!selectedBusinessNode}
          onClick={() => {
            if (!selectedBusinessNode) {
              message.warning('请先选择业务节点');
              return;
            }
            setNodeMaterialInitialInfo({
              businessNodeId: selectedBusinessNode.businessNodeId,
            });
            setNodeMaterialVisible(true);
          }}
        >
          新增
        </Button>
      </AuthButtons>
      <AuthButtons funcId="local_business_btn">
        <Button
          onClick={() => {
            const selectedRows = options.selectedRows;
            if (!selectedRows || selectedRows.length === 0) {
              message.warning('请选择要修改的记录');
              return;
            }
            if (selectedRows.length > 1) {
              message.warning('只能选择一条记录进行修改');
              return;
            }
            setNodeMaterialInitialInfo(selectedRows[0]);
            setNodeMaterialVisible(true);
          }}
        >
          修改
        </Button>
      </AuthButtons>
      <AuthButtons funcId="local_business_btn">
        <AsyncButton>删除</AsyncButton>
      </AuthButtons>
      {/* 业务节点材料弹窗 */}
      <NodeMaterialForm
        modal={[nodeMaterialVisible, setNodeMaterialVisible]}
        listOptions={options}
        initialInfo={nodeMaterialInitialInfo}
      />
    </>
  );

  return (
    <>
      <CachedPage
        service={API.welfaremanage.ebmtransact.getData}
        formColumns={formColumns}
        columns={localBusinessColumns}
        renderButtons={renderButtons}
        form={form}
        rowKey="businessContentId"
        onSelectSingleRow={handleBusinessContentSelect}
      />
      <CachedPage
        service={API.welfaremanage.ebmtransact.getData}
        formColumns={[]}
        columns={businessNodeColumns}
        renderButtons={renderBusinessNodeButtons}
        rowKey="businessNodeId"
        wriTable={wriTableSub}
        onSelectSingleRow={handleBusinessNodeSelect}
      />
      <CachedPage
        service={API.welfaremanage.ebmtransact.getData}
        formColumns={[]}
        columns={nodeMaterialColumns}
        renderButtons={renderNodeMaterialButtons}
        rowKey="materialId"
        wriTable={wriTableMaterial}
      />
    </>
  );
};

export default LocalBusinessMaintenance;