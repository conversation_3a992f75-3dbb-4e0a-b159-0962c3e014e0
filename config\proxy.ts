/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-06-01 13:52:06
 * @LastAuthor: 侯成
 * @LastTime: 2020-12-10 10:18:59
 * @message: message
 */

// import local from './locales/config-local';
// import pre from './locales/config-pre';
// import dev from './locales/config-dev';
// import test from './locales/config-test';
// import pro from './locales/config-pro';

// 在src以外使用import，可能会造成服务器上编译错误。
const local = require('./locales/config-local').default;
const pre = require('./locales/config-pre').default;
const dev = require('./locales/config-dev').default;
const test = require('./locales/config-test').default;
const pro = require('./locales/config-pro').default;

export default {
  local: local,
  preview: pre,
  develop: dev,
  test: test,
  production: pro,
};
