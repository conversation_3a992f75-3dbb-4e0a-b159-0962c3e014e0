/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-11-01 17:39:54
 * @LastAuthor: 侯成
 * @LastTime: 2021-03-04 19:06:34
 * @message: message
 */

// import { LocaleConfig } from './types';
import configDev from './config-dev';
const changeOrigin = true;
const PORT = process.env.APP_PORT;
const { API, TEST } = process.env;
const targetStabe = 'http://172.17.30.123:8080'; // 开发稳定库
const targetIntime = `http://172.17.23.123:${PORT || 8080}`; // 开发实时库
const targetTest = 'http://172.17.20.92'; // 测试库
const targetTest_js = 'http://172.100.10.32'; // 金税测试库
const personal = 'http://192.168.18.75:8080';
//test环境
const TEST_API = {
  js: targetTest,
};
//dev环境
const DEV_API = {
  js: targetIntime,
};
const localProxy = {
  '/rhro-service-1.0': {
    target: TEST_API[TEST] || DEV_API[API] || targetStabe,
    changeOrigin,
  },
};

const ConfigLoc = {
  ...configDev,
  ...localProxy,
};

export default ConfigLoc;
