{"sendorder.customerbubcontract": "严小强 客户小合同管理 | ./emphiresep/sendorder/CustomerSubcontract/index", "sendorder.querytransferandsubcontract": "严小强 交接单录入和小合同派单提醒 | ./emphiresep/sendorder/QueryTransferAndSubcontract/index", "sendorder.handover": "严小强 客户交接单管理 | ./emphiresep/sendorder/QueryTransferInfo/index", "sendorder.ManageTemplate": "赵煜灏 客户账单模板管理 | ./emphiresep/sendorder/ManageTemplate/index", "emporder.QueryEmpOrderListForGen": "严小强 生成个人订单 | ./emphiresep/emporder/QueryEmpOrderListForGen/index", "emporder.QueryClientOrderForAdd": "严小强 客户端增员 | ./emphiresep/emporder/QueryClientOrderForAdd/index", "emporder.QueryEmpOrderListForPer": "严小强 完善个人订单 | ./emphiresep/emporder/QueryEmpOrderListForPer/index", "emporder.QueryEmpOrderListForCon": "严小强 确认个人订单 | ./emphiresep/emporder/QueryEmpOrderListForCon/index", "emporder.QueryEmpOrderListForEdit": "严小强 变更个人订单 | ./emphiresep/emporder/QueryEmpOrderListForEdit/index", "emporder.QueryEmployeeMonth": "刘夏梅 个人订单月度查询 | ./emphiresep/emporder/QueryEmployeeMonth/index", "emporder.EmpBankCardMaintainance": "严小强 员工银行卡维护 | ./emphiresep/emporder/EmpBankCardMaintainance/index", "emporder.UpdateOderEmpIDC": "严小强 证件号码修改 | ./emphiresep/emporder/UpdateOderEmpIDC/index", "emporder.ImportBankCard": "严小强 批量导入银行卡信息 | ./emphiresep/emporder/ImportBankCard/index", "emporder.ConfirmEmpOrderForAssigner": "严小强 变更订单确认 | ./emphiresep/emporder/ConfirmEmpOrderForAssigner/index", "emporder.QueryEmpOrderListForTransfer": "严小强 申报转移 | ./emphiresep/emporder/QueryEmpOrderListForTransfer/index", "emporder.QueryEmployeeOrder": "严小强 个人订单查询 | ./emphiresep/emporder/QueryEmployeeOrder/index", "emporder.EmployeeBaseInfoManage": "严小强 员工信息查询 | ./emphiresep/emporder/EmployeeBaseInfoManage/index", "emporder.NationWideEmpQuery": "严小强 全国雇员查询 | ./emphiresep/emporder/NationWideEmpQuery/index", "emporder.QueryImpOrderLite": "赵煜颢 跨客户批量导入个人订单 | ./emphiresep/emporder/QueryImpOrderLite/index", "emporder.QueryImpOrderPro": "赵煜颢 批量导入个人订单产品 | ./emphiresep/emporder/QueryImpOrderPro/index", "emporder.QueryImpWrongOrderPro": "赵煜颢 非社保公积金批量导入个人订单 | ./emphiresep/emporder/QueryImpWrongOrderPro/index", "emporder.BatchAlterEmpOrderManage": "严小强 批量变更账单模板或收费模板 | ./emphiresep/emporder/BatchAlterEmpOrderManage/index", "emporder.QueryBatchDelProduct": "严小强 批量删除个人订单产品 | ./emphiresep/emporder/QueryBatchDelProduct/index", "emporder.EmpFeeSearchReport": "刘夏梅 雇员费用段横表查询 | ./emphiresep/emporder/EmpFeeSearchReport/index", "emporder.QueryEmpDetailInfo": "刘夏梅 员工详细信息查询 | ./emphiresep/emporder/QueryEmpDetailInfo/index", "emporder.QueryAdjustment": "赵煜颢 社保公积金调整 | ./emphiresep/emporder/QueryAdjustment/index", "emporder.QueryMinBaseAdjustment": "赵煜颢 社保公积金最低基数调整 | ./emphiresep/emporder/QueryMinBaseAdjustment/index", "quitManager.QueryEmpOrderListForSepApply": "赵煜颢 申报员工离职 | ./emphiresep/quitManager/QueryEmpOrderListForSepApply/index", "quitManager.QueryExEmpOrderListForSepCon": "赵煜颢 接单方确认离职 | ./emphiresep/quitManager/QueryExEmpOrderListForSepCon/index", "quitManager.QueryEmpOrderListForSepCon": "赵煜颢 派单方确认离职 | ./emphiresep/quitManager/QueryEmpOrderListForSepCon/index", "quitManager.QueryClientOrderForReduce": "赵煜颢 客户端减员 | ./emphiresep/quitManager/QueryClientOrderForReduce/index", "quitManager.batchQuitLite": "赵煜颢 批量离职申请 | ./emphiresep/quitManager/BatchQuitLite/index", "empSMSInfo": "刘夏梅 短信通知 | ./emphiresep/empSMSInfo/index", "callcenter.QueryCcWorkOrder": "严小强 工单查询 | ./emphiresep/callcenter/QueryCcWorkOrder/index", "hire.materialsCollection": "严小强 入职材料收缴 | ./emphiresep/hire/MaterialsCollection/index", "hire.search": "严小强 入职材料收缴查询 | ./emphiresep/hire/MaterialsCollectionSearch/index", "hire.queryEmpHireFromApp": "严小强 入职办理 | ./emphiresep/hire/QueryEmpHireFromApp/index", "hire.HireClassify": "刘夏梅 入职分类 | ./emphiresep/hire/HireClassify/index", "hire.HireClassifyAgain": "刘夏梅 入职重新分类 | ./emphiresep/hire/HireClassifyAgain/index", "hire.HireListType": "刘夏梅 入职名单 | ./emphiresep/hire/HireListType/index", "hire.queryCmpAccount": "严小强 批量生成易智汇账号 | ./emphiresep/hire/QueryCmpAccount/index", "hire.queryCmpAccountControl": "严小强 入职附件上传监控 | ./emphiresep/hire/QueryCmpAccountControl/index", "emporder.QueryClientOrderForChange": "严小强 客户端变更确认 | ./emphiresep/emporder/QueryClientOrderForChange/index", "callcenter.automaticNotification": "严小强 入职办理自动通知 | ./emphiresep/callcenter/AutomaticNotification/index", "smsTemplate.smsScriptConfiguration": "严小强 短信话术配置 | ./emphiresep/smsTemplate/SmsScriptConfiguration/index", "smsTemplate.BlockCustomerSMS": "刘夏梅 屏蔽客户短信 | ./emphiresep/smsTemplate/BlockCustomerSMS/index", "smsTemplate.SMSNotificationLog": "刘夏梅 短信通知日志 | ./emphiresep/smsTemplate/SMSNotificationLog/index", "customerorder.customerorderForTransfer": "严小强 大客户转移 | ./emphiresep/customerorder/CustomerorderForTransfer/index", "customerorder.customerorderForAdd": "严小强 大客户增员 | ./emphiresep/customerorder/CustomerorderForAdd/index", "customerorder.customerorderForLaborContract": "刘夏梅 大客户接口劳动合同查看（单个）| ./emphiresep/customerorder/ForLaborContract/index", "hire.lowCodeHireTemplate": "严小强 入职模板配置| ./emphiresep/hire/LowCodeHireTemplate/index", "quitManager.batchQuitLiteNew": "刘夏梅 批量离职申请(新) | ./emphiresep/quitManager/BatchQuitLiteNew/index", "hire.lowCodeHireFormTemplate": "陈国祥 低代码字段维护| ./emphiresep/hire/LowCodeHireFormTemplate/index", "customerorder.confirmationOfEmployeeBasicInformation": "严小强 员工基本信息确认 | ./emphiresep/customerorder/ConfirmationOfEmployeeBasicInformation/index"}