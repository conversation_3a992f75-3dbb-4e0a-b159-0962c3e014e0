# 公共方法

## `utils`模块
位置： `src/utils/utils.ts`

### `stringify`: url序列化方法：
```ts
export const stringify = (params: object | null, opts?: IOptions): string => {
  if (!params) return '';
  return build(params, opts);
};
```
`stringify`来自于对`searchparams`的封装，具体实现位于`src/utils/searchparams/index.ts`，具体作用是对字符串转义。示例：
```ts
const input = { redirect: 'http://localhost:8000/sysmanage/function' }
const output = "redirect=http%3A%2F%2Flocalhost%3A8000%2Fsysmanage%2Ffunction"
```
### `getPageQuery`: 网页请求参数解析
```ts
export function getPageQuery(): URLSearchParams {
  const queryParams = window.location.href.split('?')[1];
  return new URLSearchParams(queryParams);
}
```
从url中提取请求参数，是`stringify`的反函数。示例：

```ts
window.location.href = "http://localhost:8000/user/login?redirect=http%3A%2F%2Flocalhost%3A8000%2Fsysmanage%2Ffunction"
const output = URLSearchParams(redirect => 'http://localhost:8000/sysmanage/function')
```
### `nToString`: 数字转换为字符串
```ts
export const nToString = (value: number | undefined | null): string | null => {
  return value ? value.toString() : null;
};
```
此方法对 `undefined, null`做了检查，避免直接在空对象上调用`toString`方法产生错误。

### `responseData`: 提取后端接口数据
```ts
export const responseData = (response: any) => {
  const hasOwnProperty = Object.prototype.hasOwnProperty;
  if (hasOwnProperty.call(response, 'code') && hasOwnProperty.call(response, 'message')) {
    return hasOwnProperty.call(response, 'data') ? response.data : null;
  }
  return response;
};
```
此方法对接收到的数据作了属性检查，避免直接调取属性产生错误。

