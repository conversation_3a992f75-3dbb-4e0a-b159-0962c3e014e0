import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import Codal from '@/components/Codal';
import { AsyncButton } from '@/components/Forms/Confirm';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { mapToSelectors } from '@/components/Selectors';
import StandardTable from '@/components/StandardTable';
import { WritableInstance } from '@/components/Writable';
import { msgErr, msgOk, resError } from '@/utils/methods/message';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';
import { wechatProgressQueryMap } from '@/utils/settings/empwelfare/businessProcessing';
import { WritableColumnProps } from '@/utils/writable/types';
import { Button, Form, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import ViewMaterial from './ViewMaterial';

interface Props {
  [props: string]: any;
  visible: boolean;
  hideHandle: CallableFunction;
  title: string;
  initValues?: any;
}

const service = API.basedata.areaCode.list;

const EnterProcessForm: React.FC<Props> = (props) => {
  let options: WritableInstance;

  const { Text } = Typography;

  const { visible, hideHandle, title, initValues } = props;

  const [form] = Form.useForm();
  const [formEmp] = Form.useForm();
  const [formMid] = Form.useForm();
  const [formLast] = Form.useForm();

  const [processObject, setprocessObject] = useState<string | undefined>();
  const [viewMaterialModal, setViewMaterialModal] = useState(false);
  const [detailData, setDetailData] = useState<POJO | undefined>(undefined);

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      formEmp.resetFields();
    }
  }, [visible]);

  const formColumns: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '业务类型',
      fieldName: 'categoryName',
      inputRender: 'string',
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '业务内容',
      fieldName: 'busContent',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '办理属性',
      fieldName: ' transactPropertyName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '办理对象',
      fieldName: 'transactObjectName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '业务来源',
      fieldName: 'sourceTypeName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '客户名称',
      fieldName: 'custName',
      inputRender: 'string',
    },
    {
      label: '客户规模',
      fieldName: 'custScale',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '姓名',
      fieldName: 'empName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '证件号码',
      fieldName: 'cardNum',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '唯一号',
      fieldName: 'empCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '手机号码',
      fieldName: 'mobile',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '入离职状态',
      fieldName: 'empWorkState',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否单立户',
      fieldName: 'isFescoName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '缴费实体名称',
      fieldName: 'custPayEntityName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否收费业务',
      fieldName: 'isCharge',
      inputRender: () => mapToSelectors(yesNoDataMap, { disabled: true }),
    },
    {
      label: '是否收取客户费用',
      fieldName: 'isChargeCustName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '收取金额',
      fieldName: 'chargeAmount',
      inputRender: 'string',
      inputProps: { maxLength: 200 },
    },
    {
      label: '是否需要联系员工提交材料',
      fieldName: 'needEmpMaterialName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '微信端业务进度查询',
      fieldName: 'wechatProgressQuery',
      inputRender: () => mapToSelectors(wechatProgressQueryMap),
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '是否确认办理',
      fieldName: 'isConfirmHandleName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '业务状态',
      fieldName: 'statusName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '业务进度',
      fieldName: 'busSchedule',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
  ];

  const formColumnsEmp: EditeFormProps[] = [
    { label: '养老状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利起始月', fieldName: 'newRemark', inputRender: 'string' },
  ];

  const formColumnsLast: EditeFormProps[] = [
    {
      label: '办理过程',
      fieldName: 'processDescription',
      inputRender: 'text',
      inputProps: { disabled: true },
      colNumber: 1,
    },
    {
      label: '客户端审批材料下载',
      fieldName: 'newRemark',
      inputRender: () => {
        return <Button style={{ marginLeft: '5px' }}>下载</Button>;
      },
    },
  ];

  const formColumnsMid: EditeFormProps[] = [
    { label: '当前业务节点', fieldName: 'busCityNodeConfigName', inputRender: 'string' },
    { label: '是否收取材料', fieldName: 'newRemark', inputRender: 'string' },
    { label: '是否支持津贴待遇', fieldName: 'newRemark', inputRender: 'string' },
    { label: '办理周期', fieldName: 'actualProcessDays', inputRender: 'string' },
    { label: '是否有政府性收费', fieldName: 'newRemark', inputRender: 'string' },
    { label: '政府性收费金额', fieldName: 'newRemark', inputRender: 'string' },
  ];

  const detailColumns: WritableColumnProps<any>[] = [
    { title: '业务节点', dataIndex: 'applicationNodeId' },
    { title: '是否收取资料', dataIndex: 'materialName' },
    { title: '是否支付津贴待遇', dataIndex: 'num' },
    { title: '办理周期', dataIndex: 'price' },
    {
      title: '材料收集情况',
      dataIndex: 'price',
      render: (_, record) => <Button onClick={() => onViewMaterial(record)}>查看</Button>,
    },
    { title: '办理结果', dataIndex: 'nodeResult' },
    { title: '失败原因', dataIndex: 'failureReason' },
    { title: '实际办理天数', dataIndex: 'actualProcessDays' },
    { title: '是否有办理凭证', dataIndex: 'price' },
    {
      title: '办理凭证下载',
      dataIndex: 'price',
      render: (text, record) => <Button>下载</Button>,
    },
    { title: '材料原件收件人', dataIndex: 'price' },
    { title: '材料原件收件人电话', dataIndex: 'price' },
    { title: '材料原件寄件时间', dataIndex: 'price' },
    { title: '材料原件寄件单号', dataIndex: 'price' },
    { title: '操作', dataIndex: 'operate', render: (text, record) => <Button>下载</Button> },
  ];

  const onSave = async () => {
    const values = await form.validateFields();

    const { wechatProgressQueryName } = values;
    const res = await API.welfaremanage.ebmBusinessQuery.saveEbmBusinessInfo.request({
      applicationId: initValues?.applicationId,
      wechatProgressQueryName,
    });
    if (resError(res)) {
      msgErr(res.message);
      return;
    }
    msgOk('保存成功');
    hideHandle();
  };

  const CodalButtons = () => {
    return (
      <>
        <AsyncButton type="primary" onClick={() => onSave()}>
          保存
        </AsyncButton>
        <Button onClick={() => hideHandle()}>取消</Button>
      </>
    );
  };

  const onViewMaterial = (data: POJO) => {
    setDetailData(data);
    setViewMaterialModal(true);
  };

  return (
    <Codal
      title={title}
      width="90vw"
      visible={visible}
      onCancel={() => hideHandle()}
      footer={CodalButtons}
    >
      <Text>业务基本信息</Text>
      <FormElement3 form={form}>
        <EnumerateFields outerForm={form} formColumns={formColumns} />
      </FormElement3>
      {processObject === '2' && (
        <>
          <Text>员工基本信息</Text>
          <FormElement3 form={formEmp}>
            <EnumerateFields outerForm={formEmp} formColumns={formColumnsEmp} disabled={true} />
          </FormElement3>
        </>
      )}
      <FormElement3 form={formMid} style={{ marginTop: '30px' }}>
        <EnumerateFields outerForm={formMid} formColumns={formColumnsMid} disabled={true} />
      </FormElement3>
      <>
        <Text>业务办理详情</Text>
        <StandardTable
          data={{ list: [], pagination: {} }}
          columns={detailColumns}
          notShowPagination
        />
        <ViewMaterial
          title="材料清单"
          visible={viewMaterialModal}
          hideHandle={() => setViewMaterialModal(false)}
          initValues={detailData}
        />
      </>
      <FormElement3 form={formLast} style={{ marginTop: '30px' }}>
        <EnumerateFields outerForm={formLast} formColumns={formColumnsLast} disabled={true} />
      </FormElement3>
    </Codal>
  );
};

export default EnterProcessForm;
