import React, { useEffect, useState } from 'react';
import { Button, Form, FormInstance, Input, message, Typography } from 'antd';
import Codal from '@/components/Codal';
import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { AsyncButton } from '@/components/Forms/Confirm';
import { CachedPage } from '@/components/CachedPage';
import { WritableInstance } from '@/components/Writable';
import { WritableColumnProps } from '@/utils/writable/types';
import {
  BusTypeDropdownListSelector,
  CitySelector,
  CommonBaseDataSelector,
  mapToSelectors,
} from '@/components/Selectors';
import {
  busSourceMap,
  busTypeMap,
  processObjectMap,
} from '@/utils/settings/empwelfare/businessProcessing';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';
import { handleAttributeMap, handleObjectMap } from '../../NationwideBusiness';

interface Props {
  [props: string]: any;
  visible: boolean;
  hideHandle: CallableFunction;
  title: string;
  initValues?: any;
}

const initRules = {};

const service = API.basedata.areaCode.list;

const detailColumns: WritableColumnProps<any>[] = [
  { title: '业务节点', dataIndex: 'index' },
  { title: '是否收取资料', dataIndex: 'materialName' },
  { title: '是否支付津贴待遇', dataIndex: 'num' },
  { title: '办理周期', dataIndex: 'price' },
  { title: '材料收集情况', dataIndex: 'price' },
  { title: '办理结果', dataIndex: 'price' },
  { title: '失败原因', dataIndex: 'price' },
  { title: '实际办理天数', dataIndex: 'price' },
  { title: '是否有办理凭证', dataIndex: 'price' },
  {
    title: '办理凭证下载',
    dataIndex: 'price',
    render: (text, record, index) => <Button>下载</Button>,
  },
  { title: '材料原件收件人', dataIndex: 'price' },
  { title: '材料原件收件人电话', dataIndex: 'price' },
  { title: '材料原件寄件时间', dataIndex: 'price' },
  { title: '材料原件寄件单号', dataIndex: 'price' },
  { title: '操作', dataIndex: 'price', render: (text, record, index) => <Button>下载</Button> },
];
const EnterProcessForm: React.FC<Props> = (props) => {
  let options: WritableInstance;

  const { Text } = Typography;

  const { visible, hideHandle, title } = props;

  const [rules1, setRules1] = useState({ ...initRules });
  const [form] = Form.useForm();
  const [formEmp] = Form.useForm();
  const [formMid] = Form.useForm();

  const [processObject, setprocessObject] = useState<string | undefined>();

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      formEmp.resetFields();
    }
  }, [visible]);

  const formColumns: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityId',
      inputRender: () => <CitySelector disabled={true} />,
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm: FormInstance) => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
          onConfirm={() => {
            outerForm.setFieldsValue({ busnameClassId: undefined });
          }}
        />
      ),
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      inputRender: (outerForm: FormInstance) => (
        <BusTypeDropdownListSelector
          allowClear
          params={{ categoryId: outerForm.getFieldValue('categoryId') ?? '' }}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
    },
    {
      label: '业务内容',
      fieldName: 'newRemark',
      inputRender: () => mapToSelectors(processObjectMap, { disabled: true }),
    },
    {
      label: '办理属性',
      fieldName: ' transactProperty',
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'transactObject',
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    {
      label: '业务来源',
      fieldName: 'ywly',
      inputRender: () => mapToSelectors(busSourceMap, { disabled: true }),
    },
    {
      label: '客户名称',
      fieldName: 'custId',
      inputRender: (outerForm) => {
        return <CitySelector disabled={true} />;
      },
    },
    { label: '客户规模', fieldName: 'khgm', inputRender: 'string', inputProps: { disabled: true } },
    {
      label: '姓名',
      fieldName: 'empName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '证件号码',
      fieldName: 'idCardNum',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '唯一号',
      fieldName: 'empCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '手机号码',
      fieldName: 'mobile',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '入离职状态',
      fieldName: 'status',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否单立户',
      fieldName: 'isIndependent',
      inputRender: () => mapToSelectors(yesNoDataMap, { disabled: true }),
    },
    {
      label: '缴费实体名称',
      fieldName: 'custPayEntityName',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '是否收费业务',
      fieldName: 'sfsf',
      inputRender: () => mapToSelectors(yesNoDataMap, { disabled: true }),
      inputProps: { disabled: true },
    },
    {
      label: '是否收取客户费用',
      fieldName: 'sqfy',
      inputRender: () => mapToSelectors(yesNoDataMap),
      inputProps: { disabled: true },
    },
    { label: '收取金额', fieldName: 'sqje', inputRender: 'string', inputProps: { maxLength: 200 } },
    {
      label: '是否需要联系员工提交材料',
      fieldName: 'tjzl',
      inputRender: () => mapToSelectors(yesNoDataMap, { disabled: true }),
    },
    {
      label: '微信端业务进度查询',
      fieldName: 'wxyw',
      inputRender: 'string',
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '是否确认办理',
      fieldName: 'qrbl',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    { label: '业务状态', fieldName: 'ywzt', inputRender: 'string', inputProps: { disabled: true } },
    { label: '业务进度', fieldName: 'ywjd', inputRender: 'string', inputProps: { disabled: true } },
  ];

  const formColumnsEmp: EditeFormProps[] = [
    { label: '养老状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利起始月', fieldName: 'newRemark', inputRender: 'string' },
  ];

  const formColumnsLast: EditeFormProps[] = [
    {
      label: '办理过程',
      fieldName: 'newRemark',
      inputRender: 'text',
      inputProps: { disabled: true },
      colNumber: 1,
    },
    {
      label: '客户端审批材料',
      fieldName: 'newRemark',
      inputRender: () => {
        return <Button style={{ marginLeft: '5px' }}>下载</Button>;
      },
    },
  ];

  const formColumnsMid: EditeFormProps[] = [
    { label: '当前业务节点', fieldName: 'newRemark', inputRender: 'string' },
    { label: '是否收取材料', fieldName: 'newRemark', inputRender: 'string' },
    { label: '是否支持津贴待遇', fieldName: 'newRemark', inputRender: 'string' },
    { label: '办理周期', fieldName: 'newRemark', inputRender: 'string' },
    { label: '是否有政府性收费', fieldName: 'newRemark', inputRender: 'string' },
    { label: '政府性收费金额', fieldName: 'newRemark', inputRender: 'string' },
  ];

  const onSave = async () => {};

  const CodalButtons = () => {
    return (
      <>
        <AsyncButton type="primary" onClick={() => onSave()}>
          保存
        </AsyncButton>
        <Button onClick={() => hideHandle()}>取消</Button>
      </>
    );
  };
  return (
    <Codal
      title={title}
      width="90vw"
      visible={visible}
      onCancel={() => hideHandle()}
      footer={CodalButtons}
    >
      <Text>业务基本信息</Text>
      <FormElement3 form={form}>
        <EnumerateFields outerForm={form} formColumns={formColumns} rules={rules1} />
      </FormElement3>
      {processObject === '2' && (
        <>
          <Text>员工基本信息</Text>
          <FormElement3 form={formEmp}>
            <EnumerateFields outerForm={formEmp} formColumns={formColumnsEmp} disabled={true} />
          </FormElement3>
        </>
      )}
      <FormElement3 form={formMid} style={{ marginTop: '30px' }}>
        <EnumerateFields outerForm={formMid} formColumns={formColumnsMid} disabled={true} />
      </FormElement3>
      <CachedPage service={service} columns={detailColumns} formColumns={[]} />
    </Codal>
  );
};

export default EnterProcessForm;
