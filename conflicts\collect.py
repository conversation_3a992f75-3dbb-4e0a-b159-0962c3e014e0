# -*- coding: utf-8 -*-
'''
Author: 侯成
Email: <EMAIL>
Date: 2021-02-24 09:51:38
LastAuthor: 侯成
LastTime: 2021-02-24 16:54:33
message: message
git diff dev feature-js --stat-width=800 --stat-name-width=500 > stats.log

'''
import re
import os
import sys

DEV_DIR = "E:/cwork/candyHro/rhro_web2"
JINSHU_DIR = "E:/cwork/candyHro/featurejs"

all_files = []
def read_all_files():
    with open("stats.log", 'r', encoding="utf-8") as f:
        for line in f.readlines():
            if("|" in line):
                all_files.append(line.split("|")[0].strip())


def readAll(filename):
    line = []
    with open(filename, 'r', encoding="utf-8") as f:
        lines = f.readlines()
    collecttions = []
    parent_dir = ""
    for line in lines:
        line = line.strip()
        if not line.startswith("..."):
            dirs = line.split('/')
            if len(dirs) > 2:
                parent_dir = dirs[0] + "/" + dirs[1]
        else:
            line = line.replace("...", parent_dir)
        collecttions.append(line + "\n")
        collecttions.sort()
    with open("all.txt", 'w', encoding="utf-8") as f:
        f.writelines(collecttions)


def read_jinshu(filename):
    line = []
    with open(filename, 'r', encoding="utf-8") as f:
        lines = f.readlines()
    i = 0
    jinshus = ["# 这个文件在featurejs目录下运行，复制部份文件到rhro_web2\n\n"]
    devs = ["# 这个文件在rhro_web2目录下运行，复制部份文件到featurejs\n\n"]
    # print("all_files in read_jinshu:", all_files)
    for line in lines:
        line = line.strip()
        if line.startswith("config/routers"):
            continue
        parts = line.split("|")
        code_file = parts[0].strip()
        found = False
        for f in all_files:
            if code_file == f:
                found = True
                break
            elif code_file.replace("src/pages", "").replace("src/utils", "") in f:
                print("找到未匹配文件：", code_file)
                code_file = f
                if code_file == "src/pages/empwelfare/Socialmanage/{SocialApply => }/SocialApplyReceivable.tsx":
                    code_file = "src/pages/empwelfare/Socialmanage/SocialApply/SocialApplyReceivable.tsx"
                elif code_file == "src/pages/{SocialApply => }/SocialApplyReceivable.tsx":
                   code_file = "src/pages/empwelfare/Socialmanage/SocialApply/SocialApplyReceivable.tsx"
                print("尝试设置为", code_file)
        mark = parts[1].strip()

        if re.match(r"\d+.*", mark):
            devs.append("cp --path %s ../featurejs/\n" % code_file)
        else:
            i += 1
            print("mark:", mark)
            jinshus.append("cp --path %s ../rhro_web2/\n" % code_file)
    print("count:", i)
    with open("jinshus.sh", 'w', encoding="utf-8") as f:
        f.writelines(jinshus)
    with open("devs.sh", 'w', encoding="utf-8") as f:
        f.writelines(devs)

def migrate_file():
    dev_lines = []
    jinshus_lines = []
    with open("devs.txt", 'r', encoding="utf-8") as f:
        dev_lines = f.readlines()
    with open("jinshus.txt", 'r', encoding="utf-8") as f:
        dev_lines = f.readlines()
    for line in migrate_file:
        codefile = line.split(" ")[1]
        parent_path = os.path.dirname(codefile)
        target_path = os.path.join(DEV_DIR, parent_path)
        if not os.path.isdir(target_path):
            os.path
        target_file = os.path.join(DEV_DIR, parent_path, codefile)


'''
mode:
all, collect
'''


def main():
    read_all_files()
    mode = sys.argv[1] if len(sys.argv) > 1 else "diff"
    print("mode:", mode)
    if mode == "diff":
        readAll("diff-result.txt")
    elif mode == "jinshu":
        read_jinshu("allMarked.txt")


if __name__ == "__main__":
    main()
