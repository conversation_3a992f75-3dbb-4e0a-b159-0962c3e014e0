/*
 * @Author: 刘双
 * @Email: <EMAIL>
 * @Date: 2020-06-30 18:19:20
 * @LastAuthor: 刘双
 * @LastTime: 2021-01-14 15:06:10
 * @message:
 */

// https://umijs.org/config/
import { getMapByRouters } from '../src/utils/methods/layout';
import { defineConfig } from 'umi';
import proxy from './proxy';
import routeConf, { routerMap, otherRoutes } from './router.config';
const { versionid } = require('./version.json');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const keepalive = [
  ...Object.keys({ ...getMapByRouters(otherRoutes), ...routerMap }),
  '/dashboard/analysis',
  '/',
];
const { REACT_APP_ENV, APP_MODE } = process.env;

const prox = proxy[APP_MODE || 'develop'];

export default defineConfig({
  // hash: true,
  antd: {},
  dva: {
    hmr: false,
  },
  fastRefresh: {},
  locale: {
    // default zh-CN
    default: 'zh-CN',
    // default true, when it is true, will use `navigator.language` overwrite default
    antd: true,
    baseNavigator: true,
  },
  dynamicImport: {
    loading: '@/components/PageLoading/index',
  },
  targets: {
    ie: 11,
  },
  // umi routes: https://umijs.org/docs/routing
  routes: routeConf,
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    // ...darkTheme,
    'primary-color': '#545ba4;', // 全局主色
    'link-color': '#1890ff;', // 链接色
    'success-color': '#52c41a;', // 成功色
    'warning-color': '#faad14;', // 警告色
    'error-color': '#ff4d4f;', // 错误色
    'font-size-base': '12px;', // 主字号
    'heading-color': 'rgba(0, 0, 0, 1)', // 标题色
    'text-color': 'rgba(0, 0, 0, 1)', // 主文本色
    'text-color-secondary': 'rgba(0, 0, 0, 0.45)', // 次文本色
    'disabled-color': 'rgba(0, 0, 0, 0.25)', // 失效色
    'border-radius-base': '3px', // 组件/浮层圆角
    'border-color-base': '#d9d9d9', // 边框色
    'box-shadow-base':
      '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)', // 浮层阴影
  },
  title: false,
  ignoreMomentLocale: true,
  proxy: prox,
  manifest: {
    basePath: '/',
  },
  define: {
    APP_MODE,
  },
  terserOptions: {
    keep_classnames: true,
    keep_fnames: true,
  },
  plugins: ['@alitajs/keep-alive'],
  keepalive,
  chunks: ['vendors', 'umi'],
  chainWebpack: function (config, { webpack }) {
    config.merge({
      optimization: {
        splitChunks: {
          chunks: 'all',
          minChunks: 3,
          automaticNameDelimiter: '.',
          cacheGroups: {
            vendor: {
              name: 'vendors',
              test({ resource }) {
                return /[\\/]node_modules[\\/]/.test(resource);
              },
              priority: 10,
              minChunks: 1,
              chunks: 'all',
            },
          },
        },
      },
    });
    config.plugin('mini-css-extract-plugin').use(
      new MiniCssExtractPlugin({
        filename: `[name].${versionid}.css`,
        chunkFilename: `[id].${versionid}.css`,
      }),
    );
    config.output.filename(`[name].${versionid}.js`).end();
    config.output.chunkFilename(`[name].${versionid}.js`).end();
  },
});
