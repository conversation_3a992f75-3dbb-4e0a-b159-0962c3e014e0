/**
 * @description 三流合一规则-分公司规则
 */
import * as exportThreeToOneRule from './exportThreeToOneRule';
import * as getDisabilityAndFee from './getDisabilityAndFee';
import * as insertThreeToOneRule from './insertThreeToOneRule';
import * as queryThreeToOneRule from './queryThreeToOneRule';
import * as updateThreeToOneRule from './updateThreeToOneRule';

export {
  exportThreeToOneRule,
  getDisabilityAndFee,
  insertThreeToOneRule,
  queryThreeToOneRule,
  updateThreeToOneRule,
};
