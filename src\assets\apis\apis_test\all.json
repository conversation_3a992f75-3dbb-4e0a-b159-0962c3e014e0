["src/apis/admin/api.d.ts", "src/apis/admin/baseClass.ts", "src/apis/admin/index.ts", "src/apis/admin/mods/account/index.ts", "src/apis/admin/mods/account/queryAccountList.ts", "src/apis/admin/mods/account/queryCorrectRecord.ts", "src/apis/admin/mods/account/updateAccountStatus.ts", "src/apis/admin/mods/account/updateOneAccountStatus.ts", "src/apis/admin/mods/account/updatePasswordFromAccountManage.ts", "src/apis/admin/mods/allocateCustomer/index.ts", "src/apis/admin/mods/allocateCustomer/queryCbmAllList.ts", "src/apis/admin/mods/allocateCustomer/queryCbmList.ts", "src/apis/admin/mods/allocateCustomer/updateCbm.ts", "src/apis/admin/mods/department/areaDropDownList.ts", "src/apis/admin/mods/department/deleteDepart.ts", "src/apis/admin/mods/department/deleteFunctionDept.ts", "src/apis/admin/mods/department/getAllDropDownList.ts", "src/apis/admin/mods/department/getDepartmentDropdownList.ts", "src/apis/admin/mods/department/getDeptInternalCodeCount.ts", "src/apis/admin/mods/department/getKingdeeOrgDropDownList.ts", "src/apis/admin/mods/department/getSpecialType.ts", "src/apis/admin/mods/department/getSpecialTypeDropDownList.ts", "src/apis/admin/mods/department/getSubDepartment.ts", "src/apis/admin/mods/department/getSubDepartmentList.ts", "src/apis/admin/mods/department/getSuperDepartId.ts", "src/apis/admin/mods/department/getUserAndSubDeptCount.ts", "src/apis/admin/mods/department/getUserDepartment.ts", "src/apis/admin/mods/department/getUserDeptInfo.ts", "src/apis/admin/mods/department/index.ts", "src/apis/admin/mods/department/insertDeriveDept.ts", "src/apis/admin/mods/department/insertFunctionDept.ts", "src/apis/admin/mods/department/queryBranchList.ts", "src/apis/admin/mods/department/queryDepartmentById.ts", "src/apis/admin/mods/department/queryEmployeeForDept.ts", "src/apis/admin/mods/department/queryFunctionDeptList.ts", "src/apis/admin/mods/department/updateByDeptIdSelective.ts", "src/apis/admin/mods/department/updateFunctionDept.ts", "src/apis/admin/mods/employee/delEmpInfoInter.ts", "src/apis/admin/mods/employee/deleteEmpDeptRelation.ts", "src/apis/admin/mods/employee/deleteEmpRelative.ts", "src/apis/admin/mods/employee/deleteEmployee.ts", "src/apis/admin/mods/employee/getContractFinishedCount.ts", "src/apis/admin/mods/employee/getDepartmentWithCheckBox.ts", "src/apis/admin/mods/employee/getEmpDeptCount.ts", "src/apis/admin/mods/employee/getEmpInfoInter.ts", "src/apis/admin/mods/employee/getEmployeeCodeCount.ts", "src/apis/admin/mods/employee/getEmployeeDropdownList.ts", "src/apis/admin/mods/employee/getIdCardNumCount.ts", "src/apis/admin/mods/employee/getOtherUserList.ts", "src/apis/admin/mods/employee/getPersonInChargeDropdownList.ts", "src/apis/admin/mods/employee/getUserCount.ts", "src/apis/admin/mods/employee/getUserDepartmentWithCheckBox.ts", "src/apis/admin/mods/employee/getUserList.ts", "src/apis/admin/mods/employee/index.ts", "src/apis/admin/mods/employee/insertEmpDeptRelation.ts", "src/apis/admin/mods/employee/insertEmpRelative.ts", "src/apis/admin/mods/employee/insertEmployee.ts", "src/apis/admin/mods/employee/insertEmployeeAndRelatives.ts", "src/apis/admin/mods/employee/postGetEmpDeptCount.ts", "src/apis/admin/mods/employee/queryEmpRelative.ts", "src/apis/admin/mods/employee/queryEmployee.ts", "src/apis/admin/mods/employee/queryEmployeeAndRelatives.ts", "src/apis/admin/mods/employee/queryFunctionListByUserId.ts", "src/apis/admin/mods/employee/saveEmpInfoInter.ts", "src/apis/admin/mods/employee/updateEmpRelative.ts", "src/apis/admin/mods/employee/updateEmployee.ts", "src/apis/admin/mods/employee/updateEmployeeAndDept.ts", "src/apis/admin/mods/employee/updateEmployeeAndRelatives.ts", "src/apis/admin/mods/employee/updateToOtherUser.ts", "src/apis/admin/mods/employee/updateUserFunction.ts", "src/apis/admin/mods/employee/updateUserManage.ts", "src/apis/admin/mods/employee/updateUserManageDept.ts", "src/apis/admin/mods/index.ts", "src/apis/admin/mods/login/adminLogin.ts", "src/apis/admin/mods/login/fetchMemberStatistics.ts", "src/apis/admin/mods/login/forgotPassword.ts", "src/apis/admin/mods/login/generateVCode.ts", "src/apis/admin/mods/login/getUser.ts", "src/apis/admin/mods/login/index.ts", "src/apis/admin/mods/login/isLogin.ts", "src/apis/admin/mods/login/login.ts", "src/apis/admin/mods/login/logout.ts", "src/apis/admin/mods/login/modifyPassword.ts", "src/apis/admin/mods/login/postAdminLogin.ts", "src/apis/admin/mods/login/postForgotPassword.ts", "src/apis/admin/mods/login/postIsLogin.ts", "src/apis/admin/mods/login/postVerifyToken.ts", "src/apis/admin/mods/login/verifyToken.ts", "src/apis/admin/mods/menu/deleteSysFunction.ts", "src/apis/admin/mods/menu/getFunctionDTOTreeList.ts", "src/apis/admin/mods/menu/getFunctionTreeList.ts", "src/apis/admin/mods/menu/getFunctionsByUserId.ts", "src/apis/admin/mods/menu/getSpecialTypeByFunction.ts", "src/apis/admin/mods/menu/index.ts", "src/apis/admin/mods/menu/insertSysFunction.ts", "src/apis/admin/mods/menu/isFunctionRecordByUserRole.ts", "src/apis/admin/mods/menu/isRepeatFunctionRecord.ts", "src/apis/admin/mods/menu/postGetFunctionsByUserId.ts", "src/apis/admin/mods/menu/postIsRepeatFunctionRecord.ts", "src/apis/admin/mods/menu/queryFunctionListByRoleId.ts", "src/apis/admin/mods/menu/updateSysFunction.ts", "src/apis/admin/mods/role/deleteRole.ts", "src/apis/admin/mods/role/deleteRoleUser.ts", "src/apis/admin/mods/role/deleteRoleUserList.ts", "src/apis/admin/mods/role/getBizCategoryDropDown.ts", "src/apis/admin/mods/role/getRoleCount.ts", "src/apis/admin/mods/role/getRoleExistsUserCount.ts", "src/apis/admin/mods/role/getRoleGradeDropDown.ts", "src/apis/admin/mods/role/getRoleStatusDropDown.ts", "src/apis/admin/mods/role/getRoleUserCount.ts", "src/apis/admin/mods/role/getRolesByUserId.ts", "src/apis/admin/mods/role/getSubUserAuthority.ts", "src/apis/admin/mods/role/index.ts", "src/apis/admin/mods/role/insertRole.ts", "src/apis/admin/mods/role/insertRoleUser.ts", "src/apis/admin/mods/role/insertUserRole.ts", "src/apis/admin/mods/role/postGetRolesByUserId.ts", "src/apis/admin/mods/role/queryRoleDropdownList.ts", "src/apis/admin/mods/role/queryRoleList.ts", "src/apis/admin/mods/role/queryUserRoleList.ts", "src/apis/admin/mods/role/queryUsersByRole.ts", "src/apis/admin/mods/role/queryUsersNotInRole.ts", "src/apis/admin/mods/role/updateRole.ts", "src/apis/admin/mods/role/updateRoleFunction.ts", "src/apis/api-lock.json", "src/apis/api.d.ts", "src/apis/basedata/api.d.ts", "src/apis/basedata/baseClass.ts", "src/apis/basedata/index.ts", "src/apis/basedata/mods/areaCode/index.ts", "src/apis/basedata/mods/areaCode/list.ts", "src/apis/basedata/mods/areaCode/remove.ts", "src/apis/basedata/mods/areaCode/saveOrUpdate.ts", "src/apis/basedata/mods/baseDataCls/batchDelete.ts", "src/apis/basedata/mods/baseDataCls/batchSave.ts", "src/apis/basedata/mods/baseDataCls/getBasedataGetDropDownList.ts", "src/apis/basedata/mods/baseDataCls/getDorpDownList.ts", "src/apis/basedata/mods/baseDataCls/getDropDownList.ts", "src/apis/basedata/mods/baseDataCls/getSpecialTypeDropDownList.ts", "src/apis/basedata/mods/baseDataCls/index.ts", "src/apis/basedata/mods/baseDataCls/interList.ts", "src/apis/basedata/mods/baseDataCls/languageDropdownList.ts", "src/apis/basedata/mods/baseDataCls/list.ts", "src/apis/basedata/mods/baseDataCls/postBasedataInterBatchDelete.ts", "src/apis/basedata/mods/baseDataCls/save.ts", "src/apis/basedata/mods/baseDataCls/update.ts", "src/apis/basedata/mods/benefitRatio/batchDelete.ts", "src/apis/basedata/mods/benefitRatio/exportCust.ts", "src/apis/basedata/mods/benefitRatio/getBenefitRatio.ts", "src/apis/basedata/mods/benefitRatio/index.ts", "src/apis/basedata/mods/benefitRatio/insertBenefitRatio.ts", "src/apis/basedata/mods/benefitRatio/queryBenefitRatio.ts", "src/apis/basedata/mods/benefitRatio/remove.ts", "src/apis/basedata/mods/benefitRatio/toDownLoad.ts", "src/apis/basedata/mods/branchInvoice/index.ts", "src/apis/basedata/mods/branchInvoice/list.ts", "src/apis/basedata/mods/branchInvoice/remove.ts", "src/apis/basedata/mods/branchInvoice/saveOrUpdate.ts", "src/apis/basedata/mods/businessType/addBizSubType.ts", "src/apis/basedata/mods/businessType/addBizType.ts", "src/apis/basedata/mods/businessType/addExCust.ts", "src/apis/basedata/mods/businessType/delExCust.ts", "src/apis/basedata/mods/businessType/delSub.ts", "src/apis/basedata/mods/businessType/delSubTypeFile.ts", "src/apis/basedata/mods/businessType/delSup.ts", "src/apis/basedata/mods/businessType/downloadExCust.ts", "src/apis/basedata/mods/businessType/expBusnameSubtype.ts", "src/apis/basedata/mods/businessType/geBusnameClassDropdownList.ts", "src/apis/basedata/mods/businessType/getBusTypeDropdownList.ts", "src/apis/basedata/mods/businessType/getBusnameClassList.ts", "src/apis/basedata/mods/businessType/getBusnameSubtypeList.ts", "src/apis/basedata/mods/businessType/getBusnameTypeList.ts", "src/apis/basedata/mods/businessType/getCityDropdownList.ts", "src/apis/basedata/mods/businessType/getExCustList.ts", "src/apis/basedata/mods/businessType/getSsGroupDropdownList.ts", "src/apis/basedata/mods/businessType/getSubTypeDropdownList.ts", "src/apis/basedata/mods/businessType/getSupTypeDropdownList.ts", "src/apis/basedata/mods/businessType/index.ts", "src/apis/basedata/mods/businessType/insertBusnameClass.ts", "src/apis/basedata/mods/businessType/insertBusnameSubtype.ts", "src/apis/basedata/mods/businessType/insertBusnameType.ts", "src/apis/basedata/mods/businessType/listBusSubType.ts", "src/apis/basedata/mods/businessType/listBusTypePage.ts", "src/apis/basedata/mods/businessType/saveSub.ts", "src/apis/basedata/mods/businessType/saveSup.ts", "src/apis/basedata/mods/businessType/updateBizType.ts", "src/apis/basedata/mods/businessType/updateBusSubType.ts", "src/apis/basedata/mods/businessType/updateBusnameClass.ts", "src/apis/basedata/mods/businessType/updateBusnameSubtype.ts", "src/apis/basedata/mods/businessType/updateBusnameType.ts", "src/apis/basedata/mods/businessType/uploadBusSubTypeFile.ts", "src/apis/basedata/mods/city/batchDelete.ts", "src/apis/basedata/mods/city/batchSaveOrUpdate.ts", "src/apis/basedata/mods/city/cityDropDownList.ts", "src/apis/basedata/mods/city/getDropDownList.ts", "src/apis/basedata/mods/city/index.ts", "src/apis/basedata/mods/city/providerCityList.ts", "src/apis/basedata/mods/city/queryCityList.ts", "src/apis/basedata/mods/city/toDownLoad.ts", "src/apis/basedata/mods/competitor/batchDelete.ts", "src/apis/basedata/mods/competitor/batchSaveOrUpdate.ts", "src/apis/basedata/mods/competitor/getCompetitorDropdownList.ts", "src/apis/basedata/mods/competitor/index.ts", "src/apis/basedata/mods/competitor/list.ts", "src/apis/basedata/mods/country/batchDelete.ts", "src/apis/basedata/mods/country/batchSaveOrUpdate.ts", "src/apis/basedata/mods/country/countryDropDownList.ts", "src/apis/basedata/mods/country/index.ts", "src/apis/basedata/mods/country/list.ts", "src/apis/basedata/mods/empMaintain/get.ts", "src/apis/basedata/mods/empMaintain/getDropdownList.ts", "src/apis/basedata/mods/empMaintain/index.ts", "src/apis/basedata/mods/empMaintain/list.ts", "src/apis/basedata/mods/empMaintain/maintainPayProvider.ts", "src/apis/basedata/mods/empMaintain/remove.ts", "src/apis/basedata/mods/empMaintain/saveOrUpdate.ts", "src/apis/basedata/mods/empMaintainAcct/index.ts", "src/apis/basedata/mods/empMaintainAcct/list.ts", "src/apis/basedata/mods/empMaintainAcct/remove.ts", "src/apis/basedata/mods/empMaintainAcct/saveOrUpdate.ts", "src/apis/basedata/mods/empMaintainPayEntity/exportCompany.ts", "src/apis/basedata/mods/empMaintainPayEntity/exporting.ts", "src/apis/basedata/mods/empMaintainPayEntity/index.ts", "src/apis/basedata/mods/empMaintainPayEntity/insert.ts", "src/apis/basedata/mods/empMaintainPayEntity/list.ts", "src/apis/basedata/mods/empMaintainPayEntity/listPage.ts", "src/apis/basedata/mods/empMaintainPayEntity/listPageAllCondition.ts", "src/apis/basedata/mods/empMaintainPayEntity/remove.ts", "src/apis/basedata/mods/empMaintainPayEntity/update.ts", "src/apis/basedata/mods/fileProvider/batchDelete.ts", "src/apis/basedata/mods/fileProvider/index.ts", "src/apis/basedata/mods/fileProvider/list.ts", "src/apis/basedata/mods/fileProvider/reloadTemplate.ts", "src/apis/basedata/mods/fileProvider/save.ts", "src/apis/basedata/mods/fileProvider/toDownLoad.ts", "src/apis/basedata/mods/fileProvider/update.ts", "src/apis/basedata/mods/hireBase/findBaseAllType.ts", "src/apis/basedata/mods/hireBase/findSvcMaterial.ts", "src/apis/basedata/mods/hireBase/index.ts", "src/apis/basedata/mods/hireBase/queryMaterial.ts", "src/apis/basedata/mods/hireBase/queryServiceItem.ts", "src/apis/basedata/mods/hireBase/querySuperTypeTree.ts", "src/apis/basedata/mods/hireBase/saveBaseAllType.ts", "src/apis/basedata/mods/hireBase/saveHireService.ts", "src/apis/basedata/mods/hireBase/saveMaterial.ts", "src/apis/basedata/mods/hireBase/saveRelation.ts", "src/apis/basedata/mods/index.ts", "src/apis/basedata/mods/jindieOrg/getSuperOrgDropDownList.ts", "src/apis/basedata/mods/jindieOrg/index.ts", "src/apis/basedata/mods/jindieOrg/insertJindieOrg.ts", "src/apis/basedata/mods/jindieOrg/listPage.ts", "src/apis/basedata/mods/jindieOrg/sysncJindieOrg.ts", "src/apis/basedata/mods/jindieOrg/updateJindieOrg.ts", "src/apis/basedata/mods/jindieOrg/updateJindieOrgisValid.ts", "src/apis/basedata/mods/materialsInfo/delMaterialsInfo.ts", "src/apis/basedata/mods/materialsInfo/delPackage.ts", "src/apis/basedata/mods/materialsInfo/index.ts", "src/apis/basedata/mods/materialsInfo/listBusType.ts", "src/apis/basedata/mods/materialsInfo/listMaterialsInfoPage.ts", "src/apis/basedata/mods/materialsInfo/listMaterialsNotInPackage.ts", "src/apis/basedata/mods/materialsInfo/listPackge.ts", "src/apis/basedata/mods/materialsInfo/saveMaterialsInfo.ts", "src/apis/basedata/mods/materialsInfo/savePackage.ts", "src/apis/basedata/mods/materialsInfo/updateMaterialsInfo.ts", "src/apis/basedata/mods/medicalIns/addImpTask.ts", "src/apis/basedata/mods/medicalIns/del.ts", "src/apis/basedata/mods/medicalIns/index.ts", "src/apis/basedata/mods/medicalIns/save.ts", "src/apis/basedata/mods/medicalIns/sel.ts", "src/apis/basedata/mods/medicalIns/selImpResult.ts", "src/apis/basedata/mods/medicalIns/selImportTask.ts", "src/apis/basedata/mods/medicalIns/uptImpTask.ts", "src/apis/basedata/mods/payAccountCust/index.ts", "src/apis/basedata/mods/payAccountCust/insertPayAccountCust.ts", "src/apis/basedata/mods/payAccountCust/queryPayAccountCust.ts", "src/apis/basedata/mods/payAccountCust/stopPayAccountCust.ts", "src/apis/basedata/mods/payAccountCust/synAccountCust.ts", "src/apis/basedata/mods/payAccountSync/executeAccountSync.ts", "src/apis/basedata/mods/payAccountSync/getCustPayerDorpDownList.ts", "src/apis/basedata/mods/payAccountSync/index.ts", "src/apis/basedata/mods/payAccountSync/insertPayAccountSync.ts", "src/apis/basedata/mods/payAccountSync/queryBankInfo.ts", "src/apis/basedata/mods/payAccountSync/queryPayAccountSync.ts", "src/apis/basedata/mods/payAccountSync/updatePayAccountSync.ts", "src/apis/basedata/mods/personCategory/deletePersonCategoryById.ts", "src/apis/basedata/mods/personCategory/getPersonCategoryDropdownList.ts", "src/apis/basedata/mods/personCategory/index.ts", "src/apis/basedata/mods/personCategory/queryCategoryNameByCityId.ts", "src/apis/basedata/mods/personCategory/queryPersonCategoryGroupList.ts", "src/apis/basedata/mods/personCategory/savePersonCategoryInfo.ts", "src/apis/basedata/mods/personCategory/toDownLoad.ts", "src/apis/basedata/mods/productType/batchDelete.ts", "src/apis/basedata/mods/productType/batchDeleteSub.ts", "src/apis/basedata/mods/productType/batchSaveOrUpdate.ts", "src/apis/basedata/mods/productType/batchSaveOrUpdateSub.ts", "src/apis/basedata/mods/productType/checkHasSub.ts", "src/apis/basedata/mods/productType/index.ts", "src/apis/basedata/mods/productType/list.ts", "src/apis/basedata/mods/productType/listSub.ts", "src/apis/basedata/mods/province/batchDelete.ts", "src/apis/basedata/mods/province/index.ts", "src/apis/basedata/mods/province/insertOrUpdateProvinces.ts", "src/apis/basedata/mods/province/provinceDropDownList.ts", "src/apis/basedata/mods/province/queryProvinceList.ts", "src/apis/basedata/mods/region/getByCode.ts", "src/apis/basedata/mods/region/getRegionList.ts", "src/apis/basedata/mods/region/index.ts", "src/apis/basedata/mods/servicePoint/cityDropDownList.ts", "src/apis/basedata/mods/servicePoint/createServicePoint.ts", "src/apis/basedata/mods/servicePoint/createServicePointHis.ts", "src/apis/basedata/mods/servicePoint/getAreaDropDownList.ts", "src/apis/basedata/mods/servicePoint/getContactName.ts", "src/apis/basedata/mods/servicePoint/getContactTel.ts", "src/apis/basedata/mods/servicePoint/getServiceAssigneeName.ts", "src/apis/basedata/mods/servicePoint/getServiceGroupName.ts", "src/apis/basedata/mods/servicePoint/getServicePointPage.ts", "src/apis/basedata/mods/servicePoint/index.ts", "src/apis/basedata/mods/servicePoint/insertServicePoint.ts", "src/apis/basedata/mods/servicePoint/isDeletedServicePoint.ts", "src/apis/basedata/mods/servicePoint/provinceDropDownList.ts", "src/apis/basedata/mods/servicePoint/queryBranch.ts", "src/apis/basedata/mods/servicePoint/queryCityList.ts", "src/apis/basedata/mods/servicePoint/queryServicePointDetail.ts", "src/apis/basedata/mods/servicePoint/selectServicePointCityCount.ts", "src/apis/basedata/mods/servicePoint/updateServicePoint.ts", "src/apis/basedata/mods/servicePoint/updateServicePointisValid.ts", "src/apis/basedata/mods/servicePointCust/checkOnlyValidCustomer.ts", "src/apis/basedata/mods/servicePointCust/delCust.ts", "src/apis/basedata/mods/servicePointCust/delCustomerRecord.ts", "src/apis/basedata/mods/servicePointCust/getCustomer.ts", "src/apis/basedata/mods/servicePointCust/index.ts", "src/apis/basedata/mods/servicePointCust/save.ts", "src/apis/basedata/mods/servicePointCust/sel.ts", "src/apis/basedata/mods/servicePointCust/selectServicePointCustCount.ts", "src/apis/basedata/mods/serviceType/batchDelete.ts", "src/apis/basedata/mods/serviceType/batchDeleteSub.ts", "src/apis/basedata/mods/serviceType/batchSaveOrUpdate.ts", "src/apis/basedata/mods/serviceType/batchSaveOrUpdateSub.ts", "src/apis/basedata/mods/serviceType/getContractTypeDropdownList.ts", "src/apis/basedata/mods/serviceType/index.ts", "src/apis/basedata/mods/serviceType/list.ts", "src/apis/basedata/mods/serviceType/listSub.ts", "src/apis/basedata/mods/specialBranchTitle/index.ts", "src/apis/basedata/mods/specialBranchTitle/insertSpecialBranchTitle.ts", "src/apis/basedata/mods/specialBranchTitle/list.ts", "src/apis/basedata/mods/specialBranchTitle/updateSpecialBranchTitleDisable.ts", "src/apis/basedata/mods/stamp/expSelectStamp.ts", "src/apis/basedata/mods/stamp/index.ts", "src/apis/basedata/mods/stamp/selectStamp.ts", "src/apis/basedata/mods/stamp/updateSealDes.ts", "src/apis/basedata/mods/sysBranchTitle/delBranchTitleAbstract.ts", "src/apis/basedata/mods/sysBranchTitle/delBranchTitleBilling.ts", "src/apis/basedata/mods/sysBranchTitle/getBranchTitleAbstract.ts", "src/apis/basedata/mods/sysBranchTitle/getBranchTitleBillingPage.ts", "src/apis/basedata/mods/sysBranchTitle/getDepartmentDropdownList.ts", "src/apis/basedata/mods/sysBranchTitle/getJindieOrgDropDownList.ts", "src/apis/basedata/mods/sysBranchTitle/index.ts", "src/apis/basedata/mods/sysBranchTitle/listPage.ts", "src/apis/basedata/mods/sysBranchTitle/saveBranchTitle.ts", "src/apis/basedata/mods/sysBranchTitle/saveBranchTitleAbstract.ts", "src/apis/basedata/mods/sysBranchTitle/saveBranchTitleBilling.ts", "src/apis/basedata/mods/sysBranchTitle/saveBranchTitleEnable.ts", "src/apis/basedata/mods/sysBranchTitle/selectBranchTitleCount.ts", "src/apis/basedata/mods/sysBranchTitle/toDownLoad.ts", "src/apis/basedata/mods/sysBranchTitle/updateBranchTitle.ts", "src/apis/basedata/mods/taxPayerBenefit/add.ts", "src/apis/basedata/mods/taxPayerBenefit/del.ts", "src/apis/basedata/mods/taxPayerBenefit/index.ts", "src/apis/basedata/mods/taxPayerBenefit/list.ts", "src/apis/basedata/mods/taxPayerBenefit/toDownLoad.ts", "src/apis/basedata/mods/taxRate/getTaxBaseList.ts", "src/apis/basedata/mods/taxRate/getTaxDetailList.ts", "src/apis/basedata/mods/taxRate/getTaxRateByTaxId.ts", "src/apis/basedata/mods/taxRate/index.ts", "src/apis/basedata/mods/taxRate/remove.ts", "src/apis/basedata/mods/taxRate/saveOrUpdate.ts", "src/apis/basedata/mods/taxRuleService/exporting.ts", "src/apis/basedata/mods/taxRuleService/getBatchInfo.ts", "src/apis/basedata/mods/taxRuleService/index.ts", "src/apis/basedata/mods/threeToOneRule/exportThreeToOneRule.ts", "src/apis/basedata/mods/threeToOneRule/getDisabilityAndFee.ts", "src/apis/basedata/mods/threeToOneRule/getPolicyDropdownList.ts", "src/apis/basedata/mods/threeToOneRule/index.ts", "src/apis/basedata/mods/threeToOneRule/insertThreeToOneRule.ts", "src/apis/basedata/mods/threeToOneRule/queryThreeToOneRule.ts", "src/apis/basedata/mods/threeToOneRule/updateThreeToOneRule.ts", "src/apis/basedata/mods/usualTemplate/addUsualTemplate.ts", "src/apis/basedata/mods/usualTemplate/getUsualTemplates.ts", "src/apis/basedata/mods/usualTemplate/index.ts", "src/apis/basedata/mods/usualTemplate/modifyUsualTemplate.ts", "src/apis/basedata/mods/usualTemplate/remove.ts", "src/apis/basedata/mods/withholdAgentNorm/add.ts", "src/apis/basedata/mods/withholdAgentNorm/getlist.ts", "src/apis/basedata/mods/withholdAgentNorm/index.ts", "src/apis/basedata/mods/withholdAgentNorm/toDownLoad.ts", "src/apis/basedata/mods/withholdAgentNorm/update.ts", "src/apis/basedata2/api.d.ts", "src/apis/basedata2/baseClass.ts", "src/apis/basedata2/index.ts", "src/apis/basedata2/mods/areaCode/index.ts", "src/apis/basedata2/mods/areaCode/list.ts", "src/apis/basedata2/mods/areaCode/remove.ts", "src/apis/basedata2/mods/areaCode/saveOrUpdate.ts", "src/apis/basedata2/mods/baseDataCls/batchDelete.ts", "src/apis/basedata2/mods/baseDataCls/batchSave.ts", "src/apis/basedata2/mods/baseDataCls/getBasedataGetDropDownList.ts", "src/apis/basedata2/mods/baseDataCls/getDorpDownList.ts", "src/apis/basedata2/mods/baseDataCls/getDropDownList.ts", "src/apis/basedata2/mods/baseDataCls/getSpecialTypeDropDownList.ts", "src/apis/basedata2/mods/baseDataCls/index.ts", "src/apis/basedata2/mods/baseDataCls/interList.ts", "src/apis/basedata2/mods/baseDataCls/languageDropdownList.ts", "src/apis/basedata2/mods/baseDataCls/list.ts", "src/apis/basedata2/mods/baseDataCls/postBasedataInterBatchDelete.ts", "src/apis/basedata2/mods/baseDataCls/save.ts", "src/apis/basedata2/mods/baseDataCls/update.ts", "src/apis/basedata2/mods/benefitRatio/batchDelete.ts", "src/apis/basedata2/mods/benefitRatio/exportCust.ts", "src/apis/basedata2/mods/benefitRatio/getBenefitRatio.ts", "src/apis/basedata2/mods/benefitRatio/index.ts", "src/apis/basedata2/mods/benefitRatio/insertBenefitRatio.ts", "src/apis/basedata2/mods/benefitRatio/queryBenefitRatio.ts", "src/apis/basedata2/mods/benefitRatio/remove.ts", "src/apis/basedata2/mods/benefitRatio/toDownLoad.ts", "src/apis/basedata2/mods/branchInvoice/index.ts", "src/apis/basedata2/mods/branchInvoice/list.ts", "src/apis/basedata2/mods/branchInvoice/remove.ts", "src/apis/basedata2/mods/branchInvoice/saveOrUpdate.ts", "src/apis/basedata2/mods/businessType/addBizSubType.ts", "src/apis/basedata2/mods/businessType/addBizType.ts", "src/apis/basedata2/mods/businessType/addExCust.ts", "src/apis/basedata2/mods/businessType/delExCust.ts", "src/apis/basedata2/mods/businessType/delSub.ts", "src/apis/basedata2/mods/businessType/delSubTypeFile.ts", "src/apis/basedata2/mods/businessType/delSup.ts", "src/apis/basedata2/mods/businessType/downloadExCust.ts", "src/apis/basedata2/mods/businessType/expBusnameSubtype.ts", "src/apis/basedata2/mods/businessType/geBusnameClassDropdownList.ts", "src/apis/basedata2/mods/businessType/getBusTypeDropdownList.ts", "src/apis/basedata2/mods/businessType/getBusnameClassList.ts", "src/apis/basedata2/mods/businessType/getBusnameSubtypeList.ts", "src/apis/basedata2/mods/businessType/getBusnameTypeList.ts", "src/apis/basedata2/mods/businessType/getCityDropdownList.ts", "src/apis/basedata2/mods/businessType/getExCustList.ts", "src/apis/basedata2/mods/businessType/getSsGroupDropdownList.ts", "src/apis/basedata2/mods/businessType/getSubTypeDropdownList.ts", "src/apis/basedata2/mods/businessType/getSupTypeDropdownList.ts", "src/apis/basedata2/mods/businessType/index.ts", "src/apis/basedata2/mods/businessType/insertBusnameClass.ts", "src/apis/basedata2/mods/businessType/insertBusnameSubtype.ts", "src/apis/basedata2/mods/businessType/insertBusnameType.ts", "src/apis/basedata2/mods/businessType/listBusSubType.ts", "src/apis/basedata2/mods/businessType/listBusTypePage.ts", "src/apis/basedata2/mods/businessType/saveSub.ts", "src/apis/basedata2/mods/businessType/saveSup.ts", "src/apis/basedata2/mods/businessType/updateBizType.ts", "src/apis/basedata2/mods/businessType/updateBusSubType.ts", "src/apis/basedata2/mods/businessType/updateBusnameClass.ts", "src/apis/basedata2/mods/businessType/updateBusnameSubtype.ts", "src/apis/basedata2/mods/businessType/updateBusnameType.ts", "src/apis/basedata2/mods/businessType/uploadBusSubTypeFile.ts", "src/apis/basedata2/mods/city/batchDelete.ts", "src/apis/basedata2/mods/city/batchSaveOrUpdate.ts", "src/apis/basedata2/mods/city/cityDropDownList.ts", "src/apis/basedata2/mods/city/getDropDownList.ts", "src/apis/basedata2/mods/city/index.ts", "src/apis/basedata2/mods/city/providerCityList.ts", "src/apis/basedata2/mods/city/queryCityList.ts", "src/apis/basedata2/mods/city/toDownLoad.ts", "src/apis/basedata2/mods/competitor/batchDelete.ts", "src/apis/basedata2/mods/competitor/batchSaveOrUpdate.ts", "src/apis/basedata2/mods/competitor/getCompetitorDropdownList.ts", "src/apis/basedata2/mods/competitor/index.ts", "src/apis/basedata2/mods/competitor/list.ts", "src/apis/basedata2/mods/country/batchDelete.ts", "src/apis/basedata2/mods/country/batchSaveOrUpdate.ts", "src/apis/basedata2/mods/country/countryDropDownList.ts", "src/apis/basedata2/mods/country/index.ts", "src/apis/basedata2/mods/country/list.ts", "src/apis/basedata2/mods/empMaintain/get.ts", "src/apis/basedata2/mods/empMaintain/getDropdownList.ts", "src/apis/basedata2/mods/empMaintain/index.ts", "src/apis/basedata2/mods/empMaintain/list.ts", "src/apis/basedata2/mods/empMaintain/maintainPayProvider.ts", "src/apis/basedata2/mods/empMaintain/remove.ts", "src/apis/basedata2/mods/empMaintain/saveOrUpdate.ts", "src/apis/basedata2/mods/empMaintainAcct/index.ts", "src/apis/basedata2/mods/empMaintainAcct/list.ts", "src/apis/basedata2/mods/empMaintainAcct/remove.ts", "src/apis/basedata2/mods/empMaintainAcct/saveOrUpdate.ts", "src/apis/basedata2/mods/empMaintainPayEntity/exportCompany.ts", "src/apis/basedata2/mods/empMaintainPayEntity/exporting.ts", "src/apis/basedata2/mods/empMaintainPayEntity/index.ts", "src/apis/basedata2/mods/empMaintainPayEntity/insert.ts", "src/apis/basedata2/mods/empMaintainPayEntity/list.ts", "src/apis/basedata2/mods/empMaintainPayEntity/listPage.ts", "src/apis/basedata2/mods/empMaintainPayEntity/listPageAllCondition.ts", "src/apis/basedata2/mods/empMaintainPayEntity/remove.ts", "src/apis/basedata2/mods/empMaintainPayEntity/update.ts", "src/apis/basedata2/mods/fileProvider/batchDelete.ts", "src/apis/basedata2/mods/fileProvider/index.ts", "src/apis/basedata2/mods/fileProvider/list.ts", "src/apis/basedata2/mods/fileProvider/reloadTemplate.ts", "src/apis/basedata2/mods/fileProvider/save.ts", "src/apis/basedata2/mods/fileProvider/toDownLoad.ts", "src/apis/basedata2/mods/fileProvider/update.ts", "src/apis/basedata2/mods/hireBase/findBaseAllType.ts", "src/apis/basedata2/mods/hireBase/findSvcMaterial.ts", "src/apis/basedata2/mods/hireBase/index.ts", "src/apis/basedata2/mods/hireBase/queryMaterial.ts", "src/apis/basedata2/mods/hireBase/queryServiceItem.ts", "src/apis/basedata2/mods/hireBase/querySuperTypeTree.ts", "src/apis/basedata2/mods/hireBase/saveBaseAllType.ts", "src/apis/basedata2/mods/hireBase/saveHireService.ts", "src/apis/basedata2/mods/hireBase/saveMaterial.ts", "src/apis/basedata2/mods/hireBase/saveRelation.ts", "src/apis/basedata2/mods/index.ts", "src/apis/basedata2/mods/jindieOrg/getSuperOrgDropDownList.ts", "src/apis/basedata2/mods/jindieOrg/index.ts", "src/apis/basedata2/mods/jindieOrg/insertJindieOrg.ts", "src/apis/basedata2/mods/jindieOrg/listPage.ts", "src/apis/basedata2/mods/jindieOrg/sysncJindieOrg.ts", "src/apis/basedata2/mods/jindieOrg/updateJindieOrg.ts", "src/apis/basedata2/mods/jindieOrg/updateJindieOrgisValid.ts", "src/apis/basedata2/mods/materialsInfo/delMaterialsInfo.ts", "src/apis/basedata2/mods/materialsInfo/delPackage.ts", "src/apis/basedata2/mods/materialsInfo/index.ts", "src/apis/basedata2/mods/materialsInfo/listBusType.ts", "src/apis/basedata2/mods/materialsInfo/listMaterialsInfoPage.ts", "src/apis/basedata2/mods/materialsInfo/listMaterialsNotInPackage.ts", "src/apis/basedata2/mods/materialsInfo/listPackge.ts", "src/apis/basedata2/mods/materialsInfo/saveMaterialsInfo.ts", "src/apis/basedata2/mods/materialsInfo/savePackage.ts", "src/apis/basedata2/mods/materialsInfo/updateMaterialsInfo.ts", "src/apis/basedata2/mods/medicalIns/addImpTask.ts", "src/apis/basedata2/mods/medicalIns/del.ts", "src/apis/basedata2/mods/medicalIns/index.ts", "src/apis/basedata2/mods/medicalIns/save.ts", "src/apis/basedata2/mods/medicalIns/sel.ts", "src/apis/basedata2/mods/medicalIns/selImpResult.ts", "src/apis/basedata2/mods/medicalIns/selImportTask.ts", "src/apis/basedata2/mods/medicalIns/uptImpTask.ts", "src/apis/basedata2/mods/payAccountCust/index.ts", "src/apis/basedata2/mods/payAccountCust/insertPayAccountCust.ts", "src/apis/basedata2/mods/payAccountCust/queryPayAccountCust.ts", "src/apis/basedata2/mods/payAccountCust/stopPayAccountCust.ts", "src/apis/basedata2/mods/payAccountCust/synAccountCust.ts", "src/apis/basedata2/mods/payAccountSync/executeAccountSync.ts", "src/apis/basedata2/mods/payAccountSync/getCustPayerDorpDownList.ts", "src/apis/basedata2/mods/payAccountSync/index.ts", "src/apis/basedata2/mods/payAccountSync/insertPayAccountSync.ts", "src/apis/basedata2/mods/payAccountSync/queryBankInfo.ts", "src/apis/basedata2/mods/payAccountSync/queryPayAccountSync.ts", "src/apis/basedata2/mods/payAccountSync/updatePayAccountSync.ts", "src/apis/basedata2/mods/personCategory/deletePersonCategoryById.ts", "src/apis/basedata2/mods/personCategory/getPersonCategoryDropdownList.ts", "src/apis/basedata2/mods/personCategory/index.ts", "src/apis/basedata2/mods/personCategory/queryCategoryNameByCityId.ts", "src/apis/basedata2/mods/personCategory/queryPersonCategoryGroupList.ts", "src/apis/basedata2/mods/personCategory/savePersonCategoryInfo.ts", "src/apis/basedata2/mods/personCategory/toDownLoad.ts", "src/apis/basedata2/mods/productType/batchDelete.ts", "src/apis/basedata2/mods/productType/batchDeleteSub.ts", "src/apis/basedata2/mods/productType/batchSaveOrUpdate.ts", "src/apis/basedata2/mods/productType/batchSaveOrUpdateSub.ts", "src/apis/basedata2/mods/productType/checkHasSub.ts", "src/apis/basedata2/mods/productType/index.ts", "src/apis/basedata2/mods/productType/list.ts", "src/apis/basedata2/mods/productType/listSub.ts", "src/apis/basedata2/mods/province/batchDelete.ts", "src/apis/basedata2/mods/province/index.ts", "src/apis/basedata2/mods/province/insertOrUpdateProvinces.ts", "src/apis/basedata2/mods/province/provinceDropDownList.ts", "src/apis/basedata2/mods/province/queryProvinceList.ts", "src/apis/basedata2/mods/region/getByCode.ts", "src/apis/basedata2/mods/region/getRegionList.ts", "src/apis/basedata2/mods/region/index.ts", "src/apis/basedata2/mods/servicePoint/cityDropDownList.ts", "src/apis/basedata2/mods/servicePoint/createServicePoint.ts", "src/apis/basedata2/mods/servicePoint/createServicePointHis.ts", "src/apis/basedata2/mods/servicePoint/getAreaDropDownList.ts", "src/apis/basedata2/mods/servicePoint/getContactName.ts", "src/apis/basedata2/mods/servicePoint/getContactTel.ts", "src/apis/basedata2/mods/servicePoint/getServiceAssigneeName.ts", "src/apis/basedata2/mods/servicePoint/getServiceGroupName.ts", "src/apis/basedata2/mods/servicePoint/getServicePointPage.ts", "src/apis/basedata2/mods/servicePoint/index.ts", "src/apis/basedata2/mods/servicePoint/insertServicePoint.ts", "src/apis/basedata2/mods/servicePoint/isDeletedServicePoint.ts", "src/apis/basedata2/mods/servicePoint/provinceDropDownList.ts", "src/apis/basedata2/mods/servicePoint/queryBranch.ts", "src/apis/basedata2/mods/servicePoint/queryCityList.ts", "src/apis/basedata2/mods/servicePoint/queryServicePointDetail.ts", "src/apis/basedata2/mods/servicePoint/selectServicePointCityCount.ts", "src/apis/basedata2/mods/servicePoint/updateServicePoint.ts", "src/apis/basedata2/mods/servicePoint/updateServicePointisValid.ts", "src/apis/basedata2/mods/servicePointCust/checkOnlyValidCustomer.ts", "src/apis/basedata2/mods/servicePointCust/delCust.ts", "src/apis/basedata2/mods/servicePointCust/delCustomerRecord.ts", "src/apis/basedata2/mods/servicePointCust/getCustomer.ts", "src/apis/basedata2/mods/servicePointCust/index.ts", "src/apis/basedata2/mods/servicePointCust/save.ts", "src/apis/basedata2/mods/servicePointCust/sel.ts", "src/apis/basedata2/mods/servicePointCust/selectServicePointCustCount.ts", "src/apis/basedata2/mods/serviceType/batchDelete.ts", "src/apis/basedata2/mods/serviceType/batchDeleteSub.ts", "src/apis/basedata2/mods/serviceType/batchSaveOrUpdate.ts", "src/apis/basedata2/mods/serviceType/batchSaveOrUpdateSub.ts", "src/apis/basedata2/mods/serviceType/getContractTypeDropdownList.ts", "src/apis/basedata2/mods/serviceType/index.ts", "src/apis/basedata2/mods/serviceType/list.ts", "src/apis/basedata2/mods/serviceType/listSub.ts", "src/apis/basedata2/mods/specialBranchTitle/index.ts", "src/apis/basedata2/mods/specialBranchTitle/insertSpecialBranchTitle.ts", "src/apis/basedata2/mods/specialBranchTitle/list.ts", "src/apis/basedata2/mods/specialBranchTitle/updateSpecialBranchTitleDisable.ts", "src/apis/basedata2/mods/stamp/expSelectStamp.ts", "src/apis/basedata2/mods/stamp/index.ts", "src/apis/basedata2/mods/stamp/selectStamp.ts", "src/apis/basedata2/mods/stamp/updateSealDes.ts", "src/apis/basedata2/mods/sysBranchTitle/delBranchTitleAbstract.ts", "src/apis/basedata2/mods/sysBranchTitle/delBranchTitleBilling.ts", "src/apis/basedata2/mods/sysBranchTitle/getBranchTitleAbstract.ts", "src/apis/basedata2/mods/sysBranchTitle/getBranchTitleBillingPage.ts", "src/apis/basedata2/mods/sysBranchTitle/getDepartmentDropdownList.ts", "src/apis/basedata2/mods/sysBranchTitle/getJindieOrgDropDownList.ts", "src/apis/basedata2/mods/sysBranchTitle/index.ts", "src/apis/basedata2/mods/sysBranchTitle/listPage.ts", "src/apis/basedata2/mods/sysBranchTitle/saveBranchTitle.ts", "src/apis/basedata2/mods/sysBranchTitle/saveBranchTitleAbstract.ts", "src/apis/basedata2/mods/sysBranchTitle/saveBranchTitleBilling.ts", "src/apis/basedata2/mods/sysBranchTitle/saveBranchTitleEnable.ts", "src/apis/basedata2/mods/sysBranchTitle/selectBranchTitleCount.ts", "src/apis/basedata2/mods/sysBranchTitle/toDownLoad.ts", "src/apis/basedata2/mods/sysBranchTitle/updateBranchTitle.ts", "src/apis/basedata2/mods/taxPayerBenefit/add.ts", "src/apis/basedata2/mods/taxPayerBenefit/del.ts", "src/apis/basedata2/mods/taxPayerBenefit/index.ts", "src/apis/basedata2/mods/taxPayerBenefit/list.ts", "src/apis/basedata2/mods/taxPayerBenefit/toDownLoad.ts", "src/apis/basedata2/mods/taxRate/getTaxBaseList.ts", "src/apis/basedata2/mods/taxRate/getTaxDetailList.ts", "src/apis/basedata2/mods/taxRate/getTaxRateByTaxId.ts", "src/apis/basedata2/mods/taxRate/index.ts", "src/apis/basedata2/mods/taxRate/remove.ts", "src/apis/basedata2/mods/taxRate/saveOrUpdate.ts", "src/apis/basedata2/mods/taxRuleService/exporting.ts", "src/apis/basedata2/mods/taxRuleService/getBatchInfo.ts", "src/apis/basedata2/mods/taxRuleService/index.ts", "src/apis/basedata2/mods/threeToOneRule/exportThreeToOneRule.ts", "src/apis/basedata2/mods/threeToOneRule/getDisabilityAndFee.ts", "src/apis/basedata2/mods/threeToOneRule/getPolicyDropdownList.ts", "src/apis/basedata2/mods/threeToOneRule/index.ts", "src/apis/basedata2/mods/threeToOneRule/insertThreeToOneRule.ts", "src/apis/basedata2/mods/threeToOneRule/queryThreeToOneRule.ts", "src/apis/basedata2/mods/threeToOneRule/updateThreeToOneRule.ts", "src/apis/basedata2/mods/usualTemplate/addUsualTemplate.ts", "src/apis/basedata2/mods/usualTemplate/getUsualTemplates.ts", "src/apis/basedata2/mods/usualTemplate/index.ts", "src/apis/basedata2/mods/usualTemplate/modifyUsualTemplate.ts", "src/apis/basedata2/mods/usualTemplate/remove.ts", "src/apis/basedata2/mods/withholdAgentNorm/add.ts", "src/apis/basedata2/mods/withholdAgentNorm/getlist.ts", "src/apis/basedata2/mods/withholdAgentNorm/index.ts", "src/apis/basedata2/mods/withholdAgentNorm/toDownLoad.ts", "src/apis/basedata2/mods/withholdAgentNorm/update.ts", "src/apis/bigcustomer/api.d.ts", "src/apis/bigcustomer/baseClass.ts", "src/apis/bigcustomer/index.ts", "src/apis/bigcustomer/mods/bigAdd/getBigModelToken.ts", "src/apis/bigcustomer/mods/bigAdd/index.ts", "src/apis/bigcustomer/mods/bigAdd/insertEmployeeHireSep.ts", "src/apis/bigcustomer/mods/bigAdd/queryBigCustomerForAdd.ts", "src/apis/bigcustomer/mods/bigAdd/queryLaborParamsByAddId.ts", "src/apis/bigcustomer/mods/bigAdd/queryOrderById.ts", "src/apis/bigcustomer/mods/bigAdd/setBatchEmpAdd.ts", "src/apis/bigcustomer/mods/bigTransfer/index.ts", "src/apis/bigcustomer/mods/bigTransfer/queryBigTransfer.ts", "src/apis/bigcustomer/mods/bigTransfer/queryEmpFeeListById.ts", "src/apis/bigcustomer/mods/bigTransfer/setTransferEmpOrder.ts", "src/apis/bigcustomer/mods/index.ts", "src/apis/bigdata/api.d.ts", "src/apis/bigdata/baseClass.ts", "src/apis/bigdata/index.ts", "src/apis/bigdata/mods/bigData/getBigDataUrl.ts", "src/apis/bigdata/mods/bigData/index.ts", "src/apis/bigdata/mods/bigData/toChangeAnalysis.ts", "src/apis/bigdata/mods/bigData/toFundAnalysis.ts", "src/apis/bigdata/mods/bigData/toOverallAnalysis.ts", "src/apis/bigdata/mods/bigData/toPersonnelAttribute.ts", "src/apis/bigdata/mods/bigData/toPersonnelCost.ts", "src/apis/bigdata/mods/bigData/toPersonnelUnusualAction.ts", "src/apis/bigdata/mods/employeeHireSepBigData/getEmpFeeMonthForShow.ts", "src/apis/bigdata/mods/employeeHireSepBigData/getEmpFeeMonthTitleForShow.ts", "src/apis/bigdata/mods/employeeHireSepBigData/index.ts", "src/apis/bigdata/mods/index.ts", "src/apis/client/api.d.ts", "src/apis/client/baseClass.ts", "src/apis/client/index.ts", "src/apis/client/mods/clientHireSep/getAddInfo.ts", "src/apis/client/mods/clientHireSep/getEmpBankCardCount.ts", "src/apis/client/mods/clientHireSep/getEmpFeeListForAdd.ts", "src/apis/client/mods/clientHireSep/getEmpFeeListForAlter.ts", "src/apis/client/mods/clientHireSep/getEosReduceProcessById.ts", "src/apis/client/mods/clientHireSep/getHroEmployeeInfo.ts", "src/apis/client/mods/clientHireSep/getPersonTypeDropdownList.ts", "src/apis/client/mods/clientHireSep/index.ts", "src/apis/client/mods/clientHireSep/queryClientOrderForAdd.ts", "src/apis/client/mods/clientHireSep/queryClientOrderForAlter.ts", "src/apis/client/mods/clientHireSep/queryClientOrderForAlterEx.ts", "src/apis/client/mods/clientHireSep/queryClientOrderForReduce.ts", "src/apis/client/mods/clientHireSep/queryClientOrderForStop.ts", "src/apis/client/mods/clientHireSep/queryStopList.ts", "src/apis/client/mods/clientHireSep/setBatchEmpAdd.ts", "src/apis/client/mods/clientHireSep/setCancelStop.ts", "src/apis/client/mods/clientHireSep/setCommitAgain.ts", "src/apis/client/mods/clientHireSep/setCommitStop.ts", "src/apis/client/mods/clientHireSep/setEmpAddReject.ts", "src/apis/client/mods/clientHireSep/setEmpAlterReject.ts", "src/apis/client/mods/clientHireSep/setRejectStop.ts", "src/apis/client/mods/clientHireSep/updateAddStatus.ts", "src/apis/client/mods/clientHireSep/updateAlterStatus.ts", "src/apis/client/mods/clientHireSep/updateEmpBankCard.ts", "src/apis/client/mods/clientHireSep/updateEosAddEmpIdAndRelation.ts", "src/apis/client/mods/clientHireSep/updateReduceStatus.ts", "src/apis/client/mods/clientHireSep/updateStopStatus.ts", "src/apis/client/mods/clientInformation/cltSsGjjDelete.ts", "src/apis/client/mods/clientInformation/cltSsGjjPageList.ts", "src/apis/client/mods/clientInformation/cltSsGjjRelease.ts", "src/apis/client/mods/clientInformation/cltSsGjjSaveOrUpdate.ts", "src/apis/client/mods/clientInformation/index.ts", "src/apis/client/mods/custMeetingAndEvent/delEventFollowUp.ts", "src/apis/client/mods/custMeetingAndEvent/delMeetingRecord.ts", "src/apis/client/mods/custMeetingAndEvent/deleteShareCust.ts", "src/apis/client/mods/custMeetingAndEvent/getMeetingRecordById.ts", "src/apis/client/mods/custMeetingAndEvent/index.ts", "src/apis/client/mods/custMeetingAndEvent/saveAllShareCust.ts", "src/apis/client/mods/custMeetingAndEvent/saveEventFollowUp.ts", "src/apis/client/mods/custMeetingAndEvent/saveMeetingRecord.ts", "src/apis/client/mods/custMeetingAndEvent/saveShareCust.ts", "src/apis/client/mods/custMeetingAndEvent/selEventFollowUp.ts", "src/apis/client/mods/custMeetingAndEvent/selMeetingRecord.ts", "src/apis/client/mods/custMeetingAndEvent/selShareCust.ts", "src/apis/client/mods/custMeetingAndEvent/selShareMeetingRecord.ts", "src/apis/client/mods/index.ts", "src/apis/cmp/api.d.ts", "src/apis/cmp/baseClass.ts", "src/apis/cmp/index.ts", "src/apis/cmp/mods/bizBooking/batchUptConfirm.ts", "src/apis/cmp/mods/bizBooking/finishApprove.ts", "src/apis/cmp/mods/bizBooking/index.ts", "src/apis/cmp/mods/bizBooking/pageQuery.ts", "src/apis/cmp/mods/bizBooking/queryApproveList.ts", "src/apis/cmp/mods/bizBooking/uptCancel.ts", "src/apis/cmp/mods/bizBooking/uptConfirm.ts", "src/apis/cmp/mods/bizBooking/uptProFinish.ts", "src/apis/cmp/mods/bizProcPush/exeute.ts", "src/apis/cmp/mods/bizProcPush/index.ts", "src/apis/cmp/mods/classActivity/exportExcel.ts", "src/apis/cmp/mods/classActivity/getFileByFilePath.ts", "src/apis/cmp/mods/classActivity/getFileInfoById.ts", "src/apis/cmp/mods/classActivity/index.ts", "src/apis/cmp/mods/classActivity/queryClassActivityPage.ts", "src/apis/cmp/mods/classActivity/queryClassSignPeople.ts", "src/apis/cmp/mods/classActivity/queryClassSignPeoplePage.ts", "src/apis/cmp/mods/classActivity/saveClassActivity.ts", "src/apis/cmp/mods/classActivity/toDownLoad.ts", "src/apis/cmp/mods/classActivity/updateClassActivity.ts", "src/apis/cmp/mods/classActivity/uptClassActivityEnd.ts", "src/apis/cmp/mods/cmpAcct/baseDataDropDownListByMap.ts", "src/apis/cmp/mods/cmpAcct/billPc.ts", "src/apis/cmp/mods/cmpAcct/getColForDetail.ts", "src/apis/cmp/mods/cmpAcct/getCustomer.ts", "src/apis/cmp/mods/cmpAcct/getDetailForShow.ts", "src/apis/cmp/mods/cmpAcct/index.ts", "src/apis/cmp/mods/cmpAcct/insertCmpAcct.ts", "src/apis/cmp/mods/cmpAcct/queryCmpAcctBatchIetmPage.ts", "src/apis/cmp/mods/cmpAcct/queryCmpAcctBatchPage.ts", "src/apis/cmp/mods/cmpAcct/queryCmpAcctPage.ts", "src/apis/cmp/mods/cmpAcct/queryEmpHiresepMainId.ts", "src/apis/cmp/mods/cmpAcct/toDownLoad.ts", "src/apis/cmp/mods/cmpAcct/updateAcctBatchIetm.ts", "src/apis/cmp/mods/cmpAcct/updateCode.ts", "src/apis/cmp/mods/cmpMarketActivity/getExhibition.ts", "src/apis/cmp/mods/cmpMarketActivity/getFileByFilePath.ts", "src/apis/cmp/mods/cmpMarketActivity/getFileInfoById.ts", "src/apis/cmp/mods/cmpMarketActivity/index.ts", "src/apis/cmp/mods/cmpMarketActivity/insertSignPel.ts", "src/apis/cmp/mods/cmpMarketActivity/saveExhibition.ts", "src/apis/cmp/mods/cmpMarketActivity/uptExh.ts", "src/apis/cmp/mods/exhApply/getExhApply.ts", "src/apis/cmp/mods/exhApply/index.ts", "src/apis/cmp/mods/exhApply/selExhCityList.ts", "src/apis/cmp/mods/exhApply/toDownLoad.ts", "src/apis/cmp/mods/exhApply/uptExhApplyAgreeStatus.ts", "src/apis/cmp/mods/exhApply/uptExhApplyDisagreeStatus.ts", "src/apis/cmp/mods/exhApply/uptExhApplyStatus.ts", "src/apis/cmp/mods/exhSignLog/getExhSignLog.ts", "src/apis/cmp/mods/exhSignLog/index.ts", "src/apis/cmp/mods/exhSignLog/toDownLoad.ts", "src/apis/cmp/mods/exhSignPeople/delExhSignPeople.ts", "src/apis/cmp/mods/exhSignPeople/getExhSignPeople.ts", "src/apis/cmp/mods/exhSignPeople/index.ts", "src/apis/cmp/mods/exhSignPeople/toDownLoad.ts", "src/apis/cmp/mods/index.ts", "src/apis/cmp/mods/legalAdvice/index.ts", "src/apis/cmp/mods/legalAdvice/queryLegalAdvicePage.ts", "src/apis/cmp/mods/legalAdvice/sel.ts", "src/apis/cmp/mods/legalAdvice/toDownLoad.ts", "src/apis/combo/api.d.ts", "src/apis/combo/baseClass.ts", "src/apis/combo/index.ts", "src/apis/combo/mods/comboHro/addComboRatioByCombo.ts", "src/apis/combo/mods/comboHro/checkComboCust.ts", "src/apis/combo/mods/comboHro/checkComboCustInSpec.ts", "src/apis/combo/mods/comboHro/comboHROService.ts", "src/apis/combo/mods/comboHro/deleteSpecSuppPayment.ts", "src/apis/combo/mods/comboHro/enableCombo.ts", "src/apis/combo/mods/comboHro/getSSsubject.ts", "src/apis/combo/mods/comboHro/index.ts", "src/apis/combo/mods/comboHro/queryComboCust.ts", "src/apis/combo/mods/comboHro/queryComboGroupList.ts", "src/apis/combo/mods/comboHro/queryComboList.ts", "src/apis/combo/mods/comboHro/queryComboRatio.ts", "src/apis/combo/mods/comboHro/queryComboRatioByRatioId.ts", "src/apis/combo/mods/comboHro/queryComboRatioProductListForView.ts", "src/apis/combo/mods/comboHro/queryComboSepcBaseBound.ts", "src/apis/combo/mods/comboHro/queryCommonRatio.ts", "src/apis/combo/mods/comboHro/queryIncludCity.ts", "src/apis/combo/mods/comboHro/queryPersonCategoryList.ts", "src/apis/combo/mods/comboHro/queryProductBySsGroupId.ts", "src/apis/combo/mods/comboHro/queryRatioProductList.ts", "src/apis/combo/mods/comboHro/querySpecSuppPayment.ts", "src/apis/combo/mods/comboHro/querySsGroupByCityId.ts", "src/apis/combo/mods/comboHro/querySsGroupRatio.ts", "src/apis/combo/mods/comboHro/replaceSubContractCombo.ts", "src/apis/combo/mods/comboHro/saveCombo.ts", "src/apis/combo/mods/comboHro/saveComboCust.ts", "src/apis/combo/mods/comboHro/saveSpecSuppPayment.ts", "src/apis/combo/mods/comboHro/stopCombo.ts", "src/apis/combo/mods/comboHro/toDownLoad.ts", "src/apis/combo/mods/comboHro/updateComboRaitoList.ts", "src/apis/combo/mods/comboHro/updateComboRatio.ts", "src/apis/combo/mods/comboHro/updateComboRatioByCombo.ts", "src/apis/combo/mods/comboHro/updateReduceComboRatio.ts", "src/apis/combo/mods/comboHro/updateSpecBaseBoundList.ts", "src/apis/combo/mods/comboHro/updateStandard.ts", "src/apis/combo/mods/index.ts", "src/apis/commons/api.d.ts", "src/apis/commons/baseClass.ts", "src/apis/commons/index.ts", "src/apis/commons/mods/common/checkEmpIdBind.ts", "src/apis/commons/mods/common/execImpPrc.ts", "src/apis/commons/mods/common/execStmst.ts", "src/apis/commons/mods/common/exportSimpleExcelRptByTemplate.ts", "src/apis/commons/mods/common/getBaseDataInfoByCode.ts", "src/apis/commons/mods/common/getBatchData.ts", "src/apis/commons/mods/common/getCustomer.ts", "src/apis/commons/mods/common/getMultiteDictDataByCodes.ts", "src/apis/commons/mods/common/getPushServerIP.ts", "src/apis/commons/mods/common/getReportDir.ts", "src/apis/commons/mods/common/getReportDirBySparkSql.ts", "src/apis/commons/mods/common/getReportV4Dir.ts", "src/apis/commons/mods/common/getSSODomainUrl.ts", "src/apis/commons/mods/common/getServiceTicket.ts", "src/apis/commons/mods/common/index.ts", "src/apis/commons/mods/common/multiSend.ts", "src/apis/commons/mods/common/postCheckEmpIdBind.ts", "src/apis/commons/mods/common/postExecImpPrc.ts", "src/apis/commons/mods/common/postGetBaseDataInfoByCode.ts", "src/apis/commons/mods/common/postGetMultiteDictDataByCodes.ts", "src/apis/commons/mods/common/postGetPushServerIP.ts", "src/apis/commons/mods/common/postGetReportDir.ts", "src/apis/commons/mods/common/postGetReportDirBySparkSql.ts", "src/apis/commons/mods/common/postGetReportV4Dir.ts", "src/apis/commons/mods/common/postGetSSODomainUrl.ts", "src/apis/commons/mods/common/postGetServiceTicket.ts", "src/apis/commons/mods/common/postMultiSend.ts", "src/apis/commons/mods/common/queryBranch.ts", "src/apis/commons/mods/common/queryBranchId.ts", "src/apis/commons/mods/common/queryExBranch.ts", "src/apis/commons/mods/common/queryFileProvider.ts", "src/apis/commons/mods/common/queryInnerEmp.ts", "src/apis/commons/mods/common/queryInnerEmpAmb.ts", "src/apis/commons/mods/common/queryInnerEmpEx.ts", "src/apis/commons/mods/common/querySocialGroup.ts", "src/apis/commons/mods/exportFile/execute.ts", "src/apis/commons/mods/exportFile/index.ts", "src/apis/commons/mods/file/delCustFile.ts", "src/apis/commons/mods/file/delFile.ts", "src/apis/commons/mods/file/downloadExcel.ts", "src/apis/commons/mods/file/downloadFile.ts", "src/apis/commons/mods/file/generalDownloadFile.ts", "src/apis/commons/mods/file/getFileInfo.ts", "src/apis/commons/mods/file/getFileInfoById.ts", "src/apis/commons/mods/file/getPublicServerUrl.ts", "src/apis/commons/mods/file/index.ts", "src/apis/commons/mods/file/uploadFile.ts", "src/apis/commons/mods/idNumCheck/checkIDC.ts", "src/apis/commons/mods/idNumCheck/getTaxRateDropDownList.ts", "src/apis/commons/mods/idNumCheck/index.ts", "src/apis/commons/mods/idNumCheck/setWageCalculateFee.ts", "src/apis/commons/mods/impRule/del.ts", "src/apis/commons/mods/impRule/exportFile.ts", "src/apis/commons/mods/impRule/getAutoColumnInfo.ts", "src/apis/commons/mods/impRule/getBatchInfo.ts", "src/apis/commons/mods/impRule/getBatchInfoForPayRoll.ts", "src/apis/commons/mods/impRule/getRuleByCustIdAndBDCode.ts", "src/apis/commons/mods/impRule/index.ts", "src/apis/commons/mods/impRule/reloadTemplate.ts", "src/apis/commons/mods/impRule/save.ts", "src/apis/commons/mods/impRule/saveEffective.ts", "src/apis/commons/mods/impRule/sel.ts", "src/apis/commons/mods/impRule/selImpResult.ts", "src/apis/commons/mods/impRule/selImpResultForFirst.ts", "src/apis/commons/mods/impRule/uptImpRuleTemp.ts", "src/apis/commons/mods/impType/del.ts", "src/apis/commons/mods/impType/delErr.ts", "src/apis/commons/mods/impType/delItems.ts", "src/apis/commons/mods/impType/getTableInfo.ts", "src/apis/commons/mods/impType/index.ts", "src/apis/commons/mods/impType/save.ts", "src/apis/commons/mods/impType/saveEffective.ts", "src/apis/commons/mods/impType/saveErr.ts", "src/apis/commons/mods/impType/sel.ts", "src/apis/commons/mods/impType/selById.ts", "src/apis/commons/mods/impType/selErr.ts", "src/apis/commons/mods/impTypeItem/del.ts", "src/apis/commons/mods/impTypeItem/delItemColMapping.ts", "src/apis/commons/mods/impTypeItem/index.ts", "src/apis/commons/mods/impTypeItem/save.ts", "src/apis/commons/mods/impTypeItem/saveItemColMapping.ts", "src/apis/commons/mods/impTypeItem/sel.ts", "src/apis/commons/mods/impTypeItem/selXLSImpItemBDDataMpp.ts", "src/apis/commons/mods/impTypeItem/uptStatusToEffective.ts", "src/apis/commons/mods/importFile/importDataProcess.ts", "src/apis/commons/mods/importFile/index.ts", "src/apis/commons/mods/index.ts", "src/apis/crm/api.d.ts", "src/apis/crm/baseClass.ts", "src/apis/crm/index.ts", "src/apis/crm/mods/accountData/chkUser.ts", "src/apis/crm/mods/accountData/getUser.ts", "src/apis/crm/mods/accountData/index.ts", "src/apis/crm/mods/beforeSaleReport/customerSimilarity.ts", "src/apis/crm/mods/beforeSaleReport/downloadFile.ts", "src/apis/crm/mods/beforeSaleReport/exportExcel.ts", "src/apis/crm/mods/beforeSaleReport/index.ts", "src/apis/crm/mods/beforeSaleReport/queryBeforeSaleReport.ts", "src/apis/crm/mods/beforeSaleReport/queryContractApprovalStepTime.ts", "src/apis/crm/mods/beforeSaleReport/queryCustomerLost.ts", "src/apis/crm/mods/beforeSaleReport/queryCustomerService.ts", "src/apis/crm/mods/beforeSaleReport/queryLog.ts", "src/apis/crm/mods/beforeSaleReport/queryNonContractApprovalStepTime.ts", "src/apis/crm/mods/beforeSaleReport/querygetCustomerSimilarity.ts", "src/apis/crm/mods/contractApprovalStepTime/downloadFile.ts", "src/apis/crm/mods/contractApprovalStepTime/index.ts", "src/apis/crm/mods/contractApprovalStepTime/queryContractApprovalStepTime.ts", "src/apis/crm/mods/contractManage/approve.ts", "src/apis/crm/mods/contractManage/approveBackPrevious.ts", "src/apis/crm/mods/contractManage/approveSaleUploadFileBackPrevious.ts", "src/apis/crm/mods/contractManage/back.ts", "src/apis/crm/mods/contractManage/checkCsRoleByUserId.ts", "src/apis/crm/mods/contractManage/commitContractApprove.ts", "src/apis/crm/mods/contractManage/delContract.ts", "src/apis/crm/mods/contractManage/delContractQuotRelation.ts", "src/apis/crm/mods/contractManage/delFileInContract.ts", "src/apis/crm/mods/contractManage/delPayerInContract.ts", "src/apis/crm/mods/contractManage/delProductLineInContract.ts", "src/apis/crm/mods/contractManage/delQuotationInContract.ts", "src/apis/crm/mods/contractManage/effectModelContract.ts", "src/apis/crm/mods/contractManage/getActivityDefIdByProcessInsId.ts", "src/apis/crm/mods/contractManage/getAreaIdByCurrentSales.ts", "src/apis/crm/mods/contractManage/getContractApproveRelatedAttachmentByContractId.ts", "src/apis/crm/mods/contractManage/getContractAttachmentByContractId.ts", "src/apis/crm/mods/contractManage/getContractById.ts", "src/apis/crm/mods/contractManage/getContractList.ts", "src/apis/crm/mods/contractManage/getContractListForTransfer.ts", "src/apis/crm/mods/contractManage/getContractProLine.ts", "src/apis/crm/mods/contractManage/getContractQuos.ts", "src/apis/crm/mods/contractManage/getCurrentSales.ts", "src/apis/crm/mods/contractManage/getCustProductLine.ts", "src/apis/crm/mods/contractManage/getDepartmentIdByCurrentSales.ts", "src/apis/crm/mods/contractManage/getDropDownList.ts", "src/apis/crm/mods/contractManage/getEffectModelContract.ts", "src/apis/crm/mods/contractManage/getIsCsRoleByUserId.ts", "src/apis/crm/mods/contractManage/getModelContractList.ts", "src/apis/crm/mods/contractManage/getNonStaCoctAppr.ts", "src/apis/crm/mods/contractManage/getNotSameParentProductLineByContractId.ts", "src/apis/crm/mods/contractManage/getNotSameParentQuotsByContractId.ts", "src/apis/crm/mods/contractManage/getNotSameParentRecordByContractId.ts", "src/apis/crm/mods/contractManage/getPayerByContractId.ts", "src/apis/crm/mods/contractManage/getProductLineByContractId.ts", "src/apis/crm/mods/contractManage/getQueryContractApprove.ts", "src/apis/crm/mods/contractManage/getQuotationByContractId.ts", "src/apis/crm/mods/contractManage/getRenewContractById.ts", "src/apis/crm/mods/contractManage/getSalersByCustId.ts", "src/apis/crm/mods/contractManage/getSelectedPayer.ts", "src/apis/crm/mods/contractManage/getSelectedQuotation.ts", "src/apis/crm/mods/contractManage/getSignBranchTitleByDepartmentId.ts", "src/apis/crm/mods/contractManage/getSlContractRetireeList.ts", "src/apis/crm/mods/contractManage/index.ts", "src/apis/crm/mods/contractManage/initData.ts", "src/apis/crm/mods/contractManage/postQueryContractList.ts", "src/apis/crm/mods/contractManage/queryContracViewtById.ts", "src/apis/crm/mods/contractManage/save.ts", "src/apis/crm/mods/contractManage/saveBizFieldLog.ts", "src/apis/crm/mods/contractManage/saveModelContract.ts", "src/apis/crm/mods/contractManage/selectList.ts", "src/apis/crm/mods/contractManage/setRenewContract.ts", "src/apis/crm/mods/contractManage/terminal.ts", "src/apis/crm/mods/contractManage/toDownLoad.ts", "src/apis/crm/mods/contractManage/updateContractCurrentSale.ts", "src/apis/crm/mods/contractManage/updateModelContract.ts", "src/apis/crm/mods/contractManage/updateModelContractList.ts", "src/apis/crm/mods/contractManage/updateSignBranchTitle.ts", "src/apis/crm/mods/contractManage/uptContractFlagManual.ts", "src/apis/crm/mods/contractManage/uptContractSvcStop.ts", "src/apis/crm/mods/contractManage/uptContractToCRM.ts", "src/apis/crm/mods/contractManage/uptStatus.ts", "src/apis/crm/mods/crmContract/chgCust.ts", "src/apis/crm/mods/crmContract/chgSales.ts", "src/apis/crm/mods/crmContract/chkCustName.ts", "src/apis/crm/mods/crmContract/chkGroup.ts", "src/apis/crm/mods/crmContract/getChgSales.ts", "src/apis/crm/mods/crmContract/getContract.ts", "src/apis/crm/mods/crmContract/getGroup.ts", "src/apis/crm/mods/crmContract/getMember.ts", "src/apis/crm/mods/crmContract/index.ts", "src/apis/crm/mods/crmContract/newCust.ts", "src/apis/crm/mods/crmContract/uptCustName.ts", "src/apis/crm/mods/custReport/exportCust.ts", "src/apis/crm/mods/custReport/index.ts", "src/apis/crm/mods/custReport/postQuerySaleMail.ts", "src/apis/crm/mods/custReport/queryCustReport.ts", "src/apis/crm/mods/custReport/querySaleMail.ts", "src/apis/crm/mods/custReport/sendMail.ts", "src/apis/crm/mods/custReport/uploadAssPro.ts", "src/apis/crm/mods/customerService/customerSimilarity.ts", "src/apis/crm/mods/customerService/downloadFile.ts", "src/apis/crm/mods/customerService/index.ts", "src/apis/crm/mods/customerService/queryBeforeSaleReport.ts", "src/apis/crm/mods/customerService/queryContractApprovalStepTime.ts", "src/apis/crm/mods/customerService/queryCustomerLost.ts", "src/apis/crm/mods/customerService/queryCustomerService.ts", "src/apis/crm/mods/customerService/queryLog.ts", "src/apis/crm/mods/customerService/queryNonContractApprovalStepTime.ts", "src/apis/crm/mods/customerService/querygetCustomerSimilarity.ts", "src/apis/crm/mods/debtReminder/getReasonTypeList.ts", "src/apis/crm/mods/debtReminder/index.ts", "src/apis/crm/mods/debtReminder/queryDebtReminderList.ts", "src/apis/crm/mods/debtReminder/toDownLoad.ts", "src/apis/crm/mods/debtReminder/updateDebtReminder.ts", "src/apis/crm/mods/formalCust/addCustLoseReason.ts", "src/apis/crm/mods/formalCust/addUptCustLog.ts", "src/apis/crm/mods/formalCust/checkCustNameExists.ts", "src/apis/crm/mods/formalCust/delCustPro.ts", "src/apis/crm/mods/formalCust/delCustTrack.ts", "src/apis/crm/mods/formalCust/exportCust.ts", "src/apis/crm/mods/formalCust/index.ts", "src/apis/crm/mods/formalCust/postCheckCustNameExists.ts", "src/apis/crm/mods/formalCust/postIfmCust2FormalCust.ts", "src/apis/crm/mods/formalCust/postPostIfmCust2FormalCust.ts", "src/apis/crm/mods/formalCust/postQuerySinglenCustWithRemainProLine.ts", "src/apis/crm/mods/formalCust/queryCustTrack.ts", "src/apis/crm/mods/formalCust/queryFormalCust.ts", "src/apis/crm/mods/formalCust/queryFormalCustWithProLine.ts", "src/apis/crm/mods/formalCust/queryRandomCustGroupData.ts", "src/apis/crm/mods/formalCust/querySinglenCustWithRemainProLine.ts", "src/apis/crm/mods/formalCust/saveCustTrack.ts", "src/apis/crm/mods/formalCust/uptCustName.ts", "src/apis/crm/mods/formalCust/uptFormalCust.ts", "src/apis/crm/mods/hsSlCustomerVisit/createHsSlCustomerVisit.ts", "src/apis/crm/mods/hsSlCustomerVisit/getCustomerOtherInfo.ts", "src/apis/crm/mods/hsSlCustomerVisit/getData.ts", "src/apis/crm/mods/hsSlCustomerVisit/getEditableResult.ts", "src/apis/crm/mods/hsSlCustomerVisit/index.ts", "src/apis/crm/mods/hsSlCustomerVisit/postGetEditableResult.ts", "src/apis/crm/mods/ifmCust/delIfmCust.ts", "src/apis/crm/mods/ifmCust/delIfmCustTrack.ts", "src/apis/crm/mods/ifmCust/index.ts", "src/apis/crm/mods/ifmCust/postIfmCust.ts", "src/apis/crm/mods/ifmCust/queryApproveIfmCust.ts", "src/apis/crm/mods/ifmCust/queryIfmCust.ts", "src/apis/crm/mods/ifmCust/queryIfmCustTrack.ts", "src/apis/crm/mods/ifmCust/saveIfmCust.ts", "src/apis/crm/mods/ifmCust/saveIfmCustTrack.ts", "src/apis/crm/mods/ifmCust/uptCustAprvPass.ts", "src/apis/crm/mods/ifmCust/uptCustAprvReject.ts", "src/apis/crm/mods/ifmCust/uptCustAprvStop.ts", "src/apis/crm/mods/ifmCust/uptMarketCustAllot.ts", "src/apis/crm/mods/index.ts", "src/apis/crm/mods/marketActivity/delMarketActivity.ts", "src/apis/crm/mods/marketActivity/index.ts", "src/apis/crm/mods/marketActivity/queryMarketActivity.ts", "src/apis/crm/mods/marketActivity/saveMarketActivity.ts", "src/apis/crm/mods/monthService/getServicem.ts", "src/apis/crm/mods/monthService/index.ts", "src/apis/crm/mods/orginData/getCompany.ts", "src/apis/crm/mods/orginData/index.ts", "src/apis/crm/mods/shareArea/allotProductLine.ts", "src/apis/crm/mods/shareArea/allotProductLineMult.ts", "src/apis/crm/mods/shareArea/getMultCust.ts", "src/apis/crm/mods/shareArea/getShareArea.ts", "src/apis/crm/mods/shareArea/getShareAreaAllot.ts", "src/apis/crm/mods/shareArea/getShareAreaForPick.ts", "src/apis/crm/mods/shareArea/getSingleCust.ts", "src/apis/crm/mods/shareArea/getUnContraceCustNum.ts", "src/apis/crm/mods/shareArea/getUnContraceCustNumBySalesId.ts", "src/apis/crm/mods/shareArea/index.ts", "src/apis/crm/mods/shareArea/postGetUnContraceCustNumBySalesId.ts", "src/apis/crm/mods/slDisaReason/deleteById.ts", "src/apis/crm/mods/slDisaReason/exportExcel.ts", "src/apis/crm/mods/slDisaReason/getContractList.ts", "src/apis/crm/mods/slDisaReason/index.ts", "src/apis/crm/mods/slDisaReason/queryByPage.ts", "src/apis/ec/api.d.ts", "src/apis/ec/baseClass.ts", "src/apis/ec/index.ts", "src/apis/ec/mods/dailyFetchEleContract/generateEleContract.ts", "src/apis/ec/mods/dailyFetchEleContract/index.ts", "src/apis/ec/mods/ecBusiness/cancelEcEleBusiness.ts", "src/apis/ec/mods/ecBusiness/deleteEcEleBusiness.ts", "src/apis/ec/mods/ecBusiness/exportEleBusinessFile.ts", "src/apis/ec/mods/ecBusiness/exportFile.ts", "src/apis/ec/mods/ecBusiness/generateUploadTemplate.ts", "src/apis/ec/mods/ecBusiness/getEcBusinessPage.ts", "src/apis/ec/mods/ecBusiness/getEleBusinessInfo.ts", "src/apis/ec/mods/ecBusiness/getViewUrl.ts", "src/apis/ec/mods/ecBusiness/importDataEcBusiness.ts", "src/apis/ec/mods/ecBusiness/index.ts", "src/apis/ec/mods/ecBusiness/insertEcBusiness.ts", "src/apis/ec/mods/ecBusiness/postGenerateUploadTemplate.ts", "src/apis/ec/mods/ecBusiness/reEleBusinessSend.ts", "src/apis/ec/mods/ecBusiness/returnEcEleBusiness.ts", "src/apis/ec/mods/ecBusiness/signEcEleBusiness.ts", "src/apis/ec/mods/ecBusiness/startEcEleBusiness.ts", "src/apis/ec/mods/ecBusiness/updateEcBusiness.ts", "src/apis/ec/mods/ecBusiness/updateEcEleBusiness.ts", "src/apis/ec/mods/ecBusiness/writeEcEleBusiness.ts", "src/apis/ec/mods/ecQuitFileBatch/batchUploadFile.ts", "src/apis/ec/mods/ecQuitFileBatch/fileBatchReport.ts", "src/apis/ec/mods/ecQuitFileBatch/getUploadFileList.ts", "src/apis/ec/mods/ecQuitFileBatch/index.ts", "src/apis/ec/mods/ecQuitFileBatch/insertEcQuitFileBatch.ts", "src/apis/ec/mods/ecQuitFileBatch/selectFileBatchItemList.ts", "src/apis/ec/mods/ecQuitFileBatch/selectFileBatchList.ts", "src/apis/ec/mods/ecQuitFileBatch/uploadFile.ts", "src/apis/ec/mods/ecSso/index.ts", "src/apis/ec/mods/ecSso/token.ts", "src/apis/ec/mods/ecUser/addEcUser.ts", "src/apis/ec/mods/ecUser/batchDeleteEcUser.ts", "src/apis/ec/mods/ecUser/batchSyncEcUser.ts", "src/apis/ec/mods/ecUser/deleteEcUser.ts", "src/apis/ec/mods/ecUser/getEcUserSyncList.ts", "src/apis/ec/mods/ecUser/index.ts", "src/apis/ec/mods/index.ts", "src/apis/ec/mods/quitTask/deleteFileBatchItem.ts", "src/apis/ec/mods/quitTask/deleteQuitCert.ts", "src/apis/ec/mods/quitTask/deleteQuitTask.ts", "src/apis/ec/mods/quitTask/getAllEleSignProcessDropdownList.ts", "src/apis/ec/mods/quitTask/getCorporationDropdownList.ts", "src/apis/ec/mods/quitTask/getDefaultCorp.ts", "src/apis/ec/mods/quitTask/getEcQuitAll.ts", "src/apis/ec/mods/quitTask/getEleCorporationDropdownList.ts", "src/apis/ec/mods/quitTask/getEleSignProcessDropdownList.ts", "src/apis/ec/mods/quitTask/getLaborContract.ts", "src/apis/ec/mods/quitTask/getLaborContractMinioPath.ts", "src/apis/ec/mods/quitTask/getResignFileByOrderId.ts", "src/apis/ec/mods/quitTask/getSignProcessDropdownList.ts", "src/apis/ec/mods/quitTask/getSignProcessDropdownListForCert.ts", "src/apis/ec/mods/quitTask/getViewUrl.ts", "src/apis/ec/mods/quitTask/index.ts", "src/apis/ec/mods/quitTask/insertAndSumitQuitTask.ts", "src/apis/ec/mods/quitTask/insertQuitTask.ts", "src/apis/ec/mods/quitTask/invalidEleResignCertificate.ts", "src/apis/ec/mods/quitTask/invalidEleResignMaterial.ts", "src/apis/ec/mods/quitTask/reSendEleResignMaterial.ts", "src/apis/ec/mods/quitTask/revokeEleResignMaterial.ts", "src/apis/ec/mods/quitTask/saveEleResignCertificate.ts", "src/apis/ec/mods/quitTask/saveEleResignMaterial.ts", "src/apis/ec/mods/quitTask/selectCertificate.ts", "src/apis/ec/mods/quitTask/selectEmp.ts", "src/apis/ec/mods/quitTask/selectMaterial.ts", "src/apis/ec/mods/quitTask/sendEleResignMaterial.ts", "src/apis/ec/mods/quitTask/stampEleResignCertificate.ts", "src/apis/ec/mods/quitTask/stampEleResignMaterial.ts", "src/apis/ec/mods/quitTask/submitQuitCergificate.ts", "src/apis/ec/mods/quitTask/submitQuitTask.ts", "src/apis/ec/mods/quitTask/toDownLoad.ts", "src/apis/ec/mods/quitTask/updateAndSumitQuitTask.ts", "src/apis/ec/mods/quitTask/updateEleResignMaterial.ts", "src/apis/ec/mods/quitTask/updateQuitTask.ts", "src/apis/emphiresep/api.d.ts", "src/apis/emphiresep/baseClass.ts", "src/apis/emphiresep/index.ts", "src/apis/emphiresep/mods/agentWageIdCardNumManage/getQueryAgentEmpArchivesInfo.ts", "src/apis/emphiresep/mods/agentWageIdCardNumManage/index.ts", "src/apis/emphiresep/mods/agentWageIdCardNumManage/isExistEmpIdInHehs.ts", "src/apis/emphiresep/mods/agentWageIdCardNumManage/isOnlyIdCardNumInHehs.ts", "src/apis/emphiresep/mods/agentWageIdCardNumManage/saveEmpArchivesAndIdCardNum.ts", "src/apis/emphiresep/mods/agentWageIdCardNumManage/verifyEmpArchivesAndIdCardNum.ts", "src/apis/emphiresep/mods/annualAdjustment/createAdjustment.ts", "src/apis/emphiresep/mods/annualAdjustment/createAllMinAdjustment.ts", "src/apis/emphiresep/mods/annualAdjustment/createMinAdjustment.ts", "src/apis/emphiresep/mods/annualAdjustment/exportFile.ts", "src/apis/emphiresep/mods/annualAdjustment/generateUploadTemplate.ts", "src/apis/emphiresep/mods/annualAdjustment/getColumnsDesc.ts", "src/apis/emphiresep/mods/annualAdjustment/getLockedStatus.ts", "src/apis/emphiresep/mods/annualAdjustment/index.ts", "src/apis/emphiresep/mods/annualAdjustment/insertAdjustmentMinTask.ts", "src/apis/emphiresep/mods/annualAdjustment/insertAdjustmentTask.ts", "src/apis/emphiresep/mods/annualAdjustment/queryAdjTaskById.ts", "src/apis/emphiresep/mods/annualAdjustment/queryAdjTaskMinById.ts", "src/apis/emphiresep/mods/annualAdjustment/queryAdjustTaskDetailList.ts", "src/apis/emphiresep/mods/annualAdjustment/queryAdjustTaskList.ts", "src/apis/emphiresep/mods/annualAdjustment/queryAdjustmentTaskItemBySsGroupId.ts", "src/apis/emphiresep/mods/annualAdjustment/queryAdjustmentTaskItemList.ts", "src/apis/emphiresep/mods/annualAdjustment/queryAdjustmentTaskMinItemBySsGroupId.ts", "src/apis/emphiresep/mods/annualAdjustment/queryAdjustmentTaskMinItemList.ts", "src/apis/emphiresep/mods/annualAdjustment/updateAdjustmentMinTask.ts", "src/apis/emphiresep/mods/annualAdjustment/updateAdjustmentTask.ts", "src/apis/emphiresep/mods/annualAdjustment/updateFileId.ts", "src/apis/emphiresep/mods/batchDeleteProduct/getBatchDeleteProduct.ts", "src/apis/emphiresep/mods/batchDeleteProduct/getProductTypeAndProductList.ts", "src/apis/emphiresep/mods/batchDeleteProduct/index.ts", "src/apis/emphiresep/mods/batchDeleteProduct/insertBatchDeleteProduct.ts", "src/apis/emphiresep/mods/batchDeleteProduct/queryBatchDelProEmpList.ts", "src/apis/emphiresep/mods/batchDeleteProduct/queryBatchDelProductList.ts", "src/apis/emphiresep/mods/batchDeleteProduct/querySubcontractListByCustId.ts", "src/apis/emphiresep/mods/callCenter/exporting.ts", "src/apis/emphiresep/mods/callCenter/getAddOtherCallList.ts", "src/apis/emphiresep/mods/callCenter/getCcJobList.ts", "src/apis/emphiresep/mods/callCenter/getDropDownListMap.ts", "src/apis/emphiresep/mods/callCenter/getEmpCallListData.ts", "src/apis/emphiresep/mods/callCenter/getEntryCallDetail.ts", "src/apis/emphiresep/mods/callCenter/getHistory.ts", "src/apis/emphiresep/mods/callCenter/getHsCcStandardLanguage.ts", "src/apis/emphiresep/mods/callCenter/index.ts", "src/apis/emphiresep/mods/callCenter/insertCcJob.ts", "src/apis/emphiresep/mods/callCenter/insertCcJobObj.ts", "src/apis/emphiresep/mods/callCenter/insertCcWorkOrder.ts", "src/apis/emphiresep/mods/callCenter/insertHsCcMaterial.ts", "src/apis/emphiresep/mods/callCenter/insertHsCcStandardLanguage.ts", "src/apis/emphiresep/mods/callCenter/insertHsCcStatus.ts", "src/apis/emphiresep/mods/callCenter/selectHsForOrder.ts", "src/apis/emphiresep/mods/callCenter/selectLabourContract.ts", "src/apis/emphiresep/mods/callCenter/selectNewEntryCall.ts", "src/apis/emphiresep/mods/callCenter/selectNewLabourContractList.ts", "src/apis/emphiresep/mods/callCenter/selectNewLeftCall.ts", "src/apis/emphiresep/mods/callCenter/unCall.ts", "src/apis/emphiresep/mods/callCenter/updateCcJobForDistribute.ts", "src/apis/emphiresep/mods/callCenter/updateDistributeDefault.ts", "src/apis/emphiresep/mods/callCenter/updateOrSaveStandardLanguage.ts", "src/apis/emphiresep/mods/ccWorkOrder/approve.ts", "src/apis/emphiresep/mods/ccWorkOrder/exporting.ts", "src/apis/emphiresep/mods/ccWorkOrder/index.ts", "src/apis/emphiresep/mods/ccWorkOrder/queryCcWorkOrderPage.ts", "src/apis/emphiresep/mods/ccWorkOrder/queryCcWorkOrderPageApp.ts", "src/apis/emphiresep/mods/ccWorkOrder/updateCcWorkOrder.ts", "src/apis/emphiresep/mods/combo/getCitiesAndPersonCategories.ts", "src/apis/emphiresep/mods/combo/getCitiesBySubcontractId.ts", "src/apis/emphiresep/mods/combo/getComboList.ts", "src/apis/emphiresep/mods/combo/index.ts", "src/apis/emphiresep/mods/combo/querySpecBaseBoundByComboId.ts", "src/apis/emphiresep/mods/combo/selectPersonCategoryByCityId.ts", "src/apis/emphiresep/mods/empBankCard/getEmpHireSepList.ts", "src/apis/emphiresep/mods/empBankCard/getEmpWgPsnList.ts", "src/apis/emphiresep/mods/empBankCard/index.ts", "src/apis/emphiresep/mods/empBankCard/initData.ts", "src/apis/emphiresep/mods/empBankCard/postSaveList.ts", "src/apis/emphiresep/mods/empBankCard/save.ts", "src/apis/emphiresep/mods/empBankCard/saveEmpBankCard.ts", "src/apis/emphiresep/mods/empBankCard/sel.ts", "src/apis/emphiresep/mods/empBankCard/selErr.ts", "src/apis/emphiresep/mods/empBankCard/setSameSignDt.ts", "src/apis/emphiresep/mods/empBankCard/toDownLoad.ts", "src/apis/emphiresep/mods/empNativeQuery/index.ts", "src/apis/emphiresep/mods/empNativeQuery/queryAllEmpByCondition.ts", "src/apis/emphiresep/mods/empSmsRecord/index.ts", "src/apis/emphiresep/mods/empSmsRecord/pageQuery.ts", "src/apis/emphiresep/mods/empSmsRecord/postWorkFlow.ts", "src/apis/emphiresep/mods/empSmsRecord/remove.ts", "src/apis/emphiresep/mods/empSmsRecord/save.ts", "src/apis/emphiresep/mods/empSmsRecord/uptWorkFlowPass.ts", "src/apis/emphiresep/mods/empSmsRecord/uptWorkFlowStop.ts", "src/apis/emphiresep/mods/employeeBaseInfo/appoveEmployeeFilesInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/deleteEmployeeFilesInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/doGetEmpCodeSeq.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getAgentWageEmployeeBankInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmpHistoryInfoById.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeBankCard.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeBaseInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeBaseInfoById.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeBaseInfoByIdCard.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeCount.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeFilesInfoById.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeHistoryInfoById.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeInHireSepCount.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeRelative.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getEmployeeValidation.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getIdcTypeByName.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getQueryAgentEmpArchivesInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/getWageAgentEmployeeBaseInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/index.ts", "src/apis/emphiresep/mods/employeeBaseInfo/insertEmployeeBaseInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/isExistEmpIdInHehs.ts", "src/apis/emphiresep/mods/employeeBaseInfo/isOnlyIdCardNumInHehs.ts", "src/apis/emphiresep/mods/employeeBaseInfo/pageQueryEmployeeBaseInfoDetail.ts", "src/apis/emphiresep/mods/employeeBaseInfo/pageQueryOrderEmpInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/postDoGetEmpCodeSeq.ts", "src/apis/emphiresep/mods/employeeBaseInfo/postGetIdcTypeByName.ts", "src/apis/emphiresep/mods/employeeBaseInfo/postQueryEmployeeRelativeById.ts", "src/apis/emphiresep/mods/employeeBaseInfo/queryEmployeeRelativeById.ts", "src/apis/emphiresep/mods/employeeBaseInfo/saveEmpArchivesAndIdCardNum.ts", "src/apis/emphiresep/mods/employeeBaseInfo/saveEmployeeRelative.ts", "src/apis/emphiresep/mods/employeeBaseInfo/selEmpImpHis.ts", "src/apis/emphiresep/mods/employeeBaseInfo/setEffectiveEmployeeRelative.ts", "src/apis/emphiresep/mods/employeeBaseInfo/setInvalidEmployeeRelative.ts", "src/apis/emphiresep/mods/employeeBaseInfo/toDownLoad.ts", "src/apis/emphiresep/mods/employeeBaseInfo/updateBatchEmpIDC.ts", "src/apis/emphiresep/mods/employeeBaseInfo/updateEmployeeBaseInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/updateEmployeeFilesInfo.ts", "src/apis/emphiresep/mods/employeeBaseInfo/verifyEmpArchivesAndIdCardNum.ts", "src/apis/emphiresep/mods/employeeHireSep/checkIsBillStartMonthMandatory.ts", "src/apis/emphiresep/mods/employeeHireSep/doEmpOrderApprove.ts", "src/apis/emphiresep/mods/employeeHireSep/exportFile.ts", "src/apis/emphiresep/mods/employeeHireSep/exportTransferResult.ts", "src/apis/emphiresep/mods/employeeHireSep/getBatchAlterEmpOrderList.ts", "src/apis/emphiresep/mods/employeeHireSep/getBillAndQuotationDropdownList.ts", "src/apis/emphiresep/mods/employeeHireSep/getEditEmployeeHireSepDropdownList.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmpFeeMonthForShow.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmpFeeMonthForShowPage.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmpFeeMonthTitleForShow.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmpHireSepListByAlterId.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmpHireSepListInBatchAlterOrder.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmpOrderApproveList.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmployeeFeeInfoByEmpId.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmployeeFeeMonthDetailList.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmployeeHireSepDropdownList.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmployeeOrderById.ts", "src/apis/emphiresep/mods/employeeHireSep/getEmployeeOrderList.ts", "src/apis/emphiresep/mods/employeeHireSep/getMinBase.ts", "src/apis/emphiresep/mods/employeeHireSep/getProductCategoryListByAlterId.ts", "src/apis/emphiresep/mods/employeeHireSep/getProductDropdownList.ts", "src/apis/emphiresep/mods/employeeHireSep/getProductListByAlterId.ts", "src/apis/emphiresep/mods/employeeHireSep/getQuerySubContractListByAlterId.ts", "src/apis/emphiresep/mods/employeeHireSep/getSepReasonDropdownList.ts", "src/apis/emphiresep/mods/employeeHireSep/index.ts", "src/apis/emphiresep/mods/employeeHireSep/insertEmpHireSep.ts", "src/apis/emphiresep/mods/employeeHireSep/insertEmployeeHireSep.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpFeeHisList.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpFeeList.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpHireSepListForSepCon.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpHireSepListForTransfer.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpOrderListForCon.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpOrderListForEdit.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpOrderListForGen.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpOrderListForPer.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmpOrderListForSepApply.ts", "src/apis/emphiresep/mods/employeeHireSep/queryEmployeeTransferResult.ts", "src/apis/emphiresep/mods/employeeHireSep/querySepApplyProductList.ts", "src/apis/emphiresep/mods/employeeHireSep/querySepConfirmProductList.ts", "src/apis/emphiresep/mods/employeeHireSep/sendSms.ts", "src/apis/emphiresep/mods/employeeHireSep/setBatchAlterSelectEmpOrder.ts", "src/apis/emphiresep/mods/employeeHireSep/setBatchConfirm.ts", "src/apis/emphiresep/mods/employeeHireSep/setBatchConfirmEx.ts", "src/apis/emphiresep/mods/employeeHireSep/setSepApplyCancel.ts", "src/apis/emphiresep/mods/employeeHireSep/setTransferEmpOrder.ts", "src/apis/emphiresep/mods/employeeHireSep/updateBatchEmpOrderSepConfirm.ts", "src/apis/emphiresep/mods/employeeHireSep/updateBatchPending.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpHireSep.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpHireSepAndFee.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpHireSepList.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpHireSepReject.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpOrderBaseInfo.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpOrderListSepApply.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpOrderSepApply.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpOrderSepConfirm.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmpOrderToApprove.ts", "src/apis/emphiresep/mods/employeeHireSep/updateEmployeeHireSepNoSsOnly.ts", "src/apis/emphiresep/mods/eosEmpBaseInfo/checkBaseInfo.ts", "src/apis/emphiresep/mods/eosEmpBaseInfo/index.ts", "src/apis/emphiresep/mods/eosEmpBaseInfo/noNeedUpdateByIds.ts", "src/apis/emphiresep/mods/eosEmpBaseInfo/pageQueryEosEmpInfo.ts", "src/apis/emphiresep/mods/eosEmpBaseInfo/queryById.ts", "src/apis/emphiresep/mods/eosEmpBaseInfo/toDownLoad.ts", "src/apis/emphiresep/mods/eosEmpBaseInfo/updateById.ts", "src/apis/emphiresep/mods/eosEmpBaseInfo/updateByIds.ts", "src/apis/emphiresep/mods/hireClassify/classifyAgainGetMaterialList.ts", "src/apis/emphiresep/mods/hireClassify/classifyAgainGetSvcList.ts", "src/apis/emphiresep/mods/hireClassify/expExcelAgainNotClassify.ts", "src/apis/emphiresep/mods/hireClassify/expExcelHireListNotClassify.ts", "src/apis/emphiresep/mods/hireClassify/expExcelHireNotClassify.ts", "src/apis/emphiresep/mods/hireClassify/findHireListTypeSvcMaterial.ts", "src/apis/emphiresep/mods/hireClassify/getCustMaterialListByCustId.ts", "src/apis/emphiresep/mods/hireClassify/getDorpDownList.ts", "src/apis/emphiresep/mods/hireClassify/getHireMaterialReceiveListById.ts", "src/apis/emphiresep/mods/hireClassify/getSvcList.ts", "src/apis/emphiresep/mods/hireClassify/hireClassifyHisQuery.ts", "src/apis/emphiresep/mods/hireClassify/hireClassifyHisQueryFindCustAddMaterial.ts", "src/apis/emphiresep/mods/hireClassify/hireClassifyHisQueryFindLocalSvc.ts", "src/apis/emphiresep/mods/hireClassify/hireClassifyHisQueryFindRemoteSvc.ts", "src/apis/emphiresep/mods/hireClassify/hireListNoNeedClassify.ts", "src/apis/emphiresep/mods/hireClassify/hireListSaveClassifySvc.ts", "src/apis/emphiresep/mods/hireClassify/index.ts", "src/apis/emphiresep/mods/hireClassify/noNeedClassify.ts", "src/apis/emphiresep/mods/hireClassify/pageQueryAgainNotClassify.ts", "src/apis/emphiresep/mods/hireClassify/pageQueryHireClassify.ts", "src/apis/emphiresep/mods/hireClassify/pageQueryHireListNotClassify.ts", "src/apis/emphiresep/mods/hireClassify/pageQueryHireNotClassify.ts", "src/apis/emphiresep/mods/hireClassify/pageQueryHireNotClassifyFindBaseAllType.ts", "src/apis/emphiresep/mods/hireClassify/pageQueryHireNotClassifyLocalService.ts", "src/apis/emphiresep/mods/hireClassify/saveClassify.ts", "src/apis/emphiresep/mods/hireClassify/saveClassifyAgain.ts", "src/apis/emphiresep/mods/hireClassify/saveCustMaterial.ts", "src/apis/emphiresep/mods/hireClassify/selectCustMaterial.ts", "src/apis/emphiresep/mods/hireExport/expExcelHireFromAppList.ts", "src/apis/emphiresep/mods/hireExport/expExcelHireFromAppListByLowcode.ts", "src/apis/emphiresep/mods/hireExport/index.ts", "src/apis/emphiresep/mods/hireFromApp/createAssociation.ts", "src/apis/emphiresep/mods/hireFromApp/delAssociation.ts", "src/apis/emphiresep/mods/hireFromApp/getBaseDataIdByName.ts", "src/apis/emphiresep/mods/hireFromApp/getHireInfo.ts", "src/apis/emphiresep/mods/hireFromApp/index.ts", "src/apis/emphiresep/mods/hireFromApp/queryHireAssociationList.ts", "src/apis/emphiresep/mods/hireFromApp/queryHireFromAppList.ts", "src/apis/emphiresep/mods/hireFromApp/queryWechatField.ts", "src/apis/emphiresep/mods/hireFromApp/setBatchAssociate.ts", "src/apis/emphiresep/mods/hireFromApp/updateHiresepMain.ts", "src/apis/emphiresep/mods/hireHandle/createAssociation.ts", "src/apis/emphiresep/mods/hireHandle/delAssociation.ts", "src/apis/emphiresep/mods/hireHandle/expExcelHireFromAppList.ts", "src/apis/emphiresep/mods/hireHandle/generateAttachments.ts", "src/apis/emphiresep/mods/hireHandle/generateDownloadFile.ts", "src/apis/emphiresep/mods/hireHandle/getBaseDataIdByName.ts", "src/apis/emphiresep/mods/hireHandle/getHireInfo.ts", "src/apis/emphiresep/mods/hireHandle/index.ts", "src/apis/emphiresep/mods/hireHandle/postCreateAssociation.ts", "src/apis/emphiresep/mods/hireHandle/postDelAssociation.ts", "src/apis/emphiresep/mods/hireHandle/postGenerateDownloadFile.ts", "src/apis/emphiresep/mods/hireHandle/postGetHireInfo.ts", "src/apis/emphiresep/mods/hireHandle/postQueryWechatField.ts", "src/apis/emphiresep/mods/hireHandle/postRefreshAttachmentList.ts", "src/apis/emphiresep/mods/hireHandle/queryHireAssociationList.ts", "src/apis/emphiresep/mods/hireHandle/queryHireFromAppList.ts", "src/apis/emphiresep/mods/hireHandle/queryWechatField.ts", "src/apis/emphiresep/mods/hireHandle/refreshAttachmentList.ts", "src/apis/emphiresep/mods/hireHandle/saveHireInfo.ts", "src/apis/emphiresep/mods/hireHandle/setBatchAssociate.ts", "src/apis/emphiresep/mods/hireHandle/setBatchSync.ts", "src/apis/emphiresep/mods/hireHandle/updateHiresepMain.ts", "src/apis/emphiresep/mods/hireHandle/uploadFile.ts", "src/apis/emphiresep/mods/hireMaterial/deleteMaterialReceiveTemp.ts", "src/apis/emphiresep/mods/hireMaterial/getCapture.ts", "src/apis/emphiresep/mods/hireMaterial/getMainList.ts", "src/apis/emphiresep/mods/hireMaterial/getMaterialActreceiveList.ts", "src/apis/emphiresep/mods/hireMaterial/getMaterialHistoryList.ts", "src/apis/emphiresep/mods/hireMaterial/getMaterialList.ts", "src/apis/emphiresep/mods/hireMaterial/index.ts", "src/apis/emphiresep/mods/hireMaterial/insertHireMaterialActreceive.ts", "src/apis/emphiresep/mods/hireMaterial/insertHireMaterialActreceiveFromToDoTask.ts", "src/apis/emphiresep/mods/hireMaterial/insertHireMaterialReceive.ts", "src/apis/emphiresep/mods/hireMaterial/insertHireMaterialReceiveHis.ts", "src/apis/emphiresep/mods/hireMaterial/insertHireMaterialReceiveTemp.ts", "src/apis/emphiresep/mods/hireMaterial/insertMaterialReceiveWithTemp.ts", "src/apis/emphiresep/mods/hireMaterial/postDeleteMaterialReceiveTemp.ts", "src/apis/emphiresep/mods/hireMaterial/postInsertMaterialReceiveWithTemp.ts", "src/apis/emphiresep/mods/hireMaterial/seachList.ts", "src/apis/emphiresep/mods/hireMaterial/selectMaterial.ts", "src/apis/emphiresep/mods/impOrder/add.ts", "src/apis/emphiresep/mods/impOrder/addPure.ts", "src/apis/emphiresep/mods/impOrder/exporting.ts", "src/apis/emphiresep/mods/impOrder/index.ts", "src/apis/emphiresep/mods/impOrder/invalidate.ts", "src/apis/emphiresep/mods/impOrder/selImpOrderInfo.ts", "src/apis/emphiresep/mods/impOrder/selImpOrderInfoItem.ts", "src/apis/emphiresep/mods/impOrder/selImpOrderResult.ts", "src/apis/emphiresep/mods/impOrderPro/add.ts", "src/apis/emphiresep/mods/impOrderPro/exportFile.ts", "src/apis/emphiresep/mods/impOrderPro/index.ts", "src/apis/emphiresep/mods/impOrderPro/invalidate.ts", "src/apis/emphiresep/mods/impOrderPro/sel.ts", "src/apis/emphiresep/mods/impOrderPro/selImpOrderProResult.ts", "src/apis/emphiresep/mods/impOrderPro/selItem.ts", "src/apis/emphiresep/mods/index.ts", "src/apis/emphiresep/mods/receivable/approveReceivableTemplate.ts", "src/apis/emphiresep/mods/receivable/backReceivableTemplate.ts", "src/apis/emphiresep/mods/receivable/checkSameReceivableNameByDiffentId.ts", "src/apis/emphiresep/mods/receivable/commitWageOffSetFlow.ts", "src/apis/emphiresep/mods/receivable/delReceivableTemplateInterList.ts", "src/apis/emphiresep/mods/receivable/deleteReceivableTemplate.ts", "src/apis/emphiresep/mods/receivable/deleteReceivableTemplateApp.ts", "src/apis/emphiresep/mods/receivable/doApproveTemplateDelFlow.ts", "src/apis/emphiresep/mods/receivable/exportFile.ts", "src/apis/emphiresep/mods/receivable/exportImpHis.ts", "src/apis/emphiresep/mods/receivable/getBillQuotedBySubCount.ts", "src/apis/emphiresep/mods/receivable/getContractByCustWithState.ts", "src/apis/emphiresep/mods/receivable/getImpDetail.ts", "src/apis/emphiresep/mods/receivable/getMaxBillLockingDay.ts", "src/apis/emphiresep/mods/receivable/getProductDropdownListBySSGroup.ts", "src/apis/emphiresep/mods/receivable/getReceivableDropdownList.ts", "src/apis/emphiresep/mods/receivable/getReceivableFrequencyById.ts", "src/apis/emphiresep/mods/receivable/getReceivableFrequencyDropdownList.ts", "src/apis/emphiresep/mods/receivable/getReceivableTemplateById.ts", "src/apis/emphiresep/mods/receivable/getReceivableTemplateDropdownList.ts", "src/apis/emphiresep/mods/receivable/getReceivableTemplateInterList.ts", "src/apis/emphiresep/mods/receivable/getReceivableUpdateDropdownList.ts", "src/apis/emphiresep/mods/receivable/getTempltImpInfo.ts", "src/apis/emphiresep/mods/receivable/index.ts", "src/apis/emphiresep/mods/receivable/insertAndUpdateOneTimeFrequency.ts", "src/apis/emphiresep/mods/receivable/insertReceivableDisplay.ts", "src/apis/emphiresep/mods/receivable/insertReceivableFrequency.ts", "src/apis/emphiresep/mods/receivable/insertReceivablePrecision.ts", "src/apis/emphiresep/mods/receivable/insertReceivableTemplate.ts", "src/apis/emphiresep/mods/receivable/insertReceivableTemplateApprove.ts", "src/apis/emphiresep/mods/receivable/modifyDisplayAndPrecision.ts", "src/apis/emphiresep/mods/receivable/queryOneTimeFrequencyList.ts", "src/apis/emphiresep/mods/receivable/queryReceivableDisplayList.ts", "src/apis/emphiresep/mods/receivable/queryReceivableFrequencyList.ts", "src/apis/emphiresep/mods/receivable/queryReceivablePayerList.ts", "src/apis/emphiresep/mods/receivable/queryReceivablePrecisionAndDisplayList.ts", "src/apis/emphiresep/mods/receivable/queryReceivablePrecisionList.ts", "src/apis/emphiresep/mods/receivable/queryReceivableTemplateAppList.ts", "src/apis/emphiresep/mods/receivable/queryReceivableTemplateApprovalList.ts", "src/apis/emphiresep/mods/receivable/queryReceivableTemplateHisList.ts", "src/apis/emphiresep/mods/receivable/queryReceivableTemplateList.ts", "src/apis/emphiresep/mods/receivable/queryTemplateDelPage.ts", "src/apis/emphiresep/mods/receivable/saveReceivableTemplateInterList.ts", "src/apis/emphiresep/mods/receivable/setReceivableTemplate.ts", "src/apis/emphiresep/mods/receivable/terminalTemplateDelFlow.ts", "src/apis/emphiresep/mods/receivable/toolsDataByEntity.ts", "src/apis/emphiresep/mods/receivable/updateReceivableDisplay.ts", "src/apis/emphiresep/mods/receivable/updateReceivableFrequency.ts", "src/apis/emphiresep/mods/receivable/updateReceivablePrecision.ts", "src/apis/emphiresep/mods/receivable/updateReceivableTemplate.ts", "src/apis/emphiresep/mods/receivable/updateReceivableTemplateApp.ts", "src/apis/emphiresep/mods/receivable/updateReceivableTemplateApprove.ts", "src/apis/emphiresep/mods/receivable/updateReceivableTemplateRecreateProcess.ts", "src/apis/emphiresep/mods/subcontract/checkSubcontractIsQuoted.ts", "src/apis/emphiresep/mods/subcontract/deleteSubcontractById.ts", "src/apis/emphiresep/mods/subcontract/exporting.ts", "src/apis/emphiresep/mods/subcontract/getCorporationFromQys.ts", "src/apis/emphiresep/mods/subcontract/getLiabilityCs.ts", "src/apis/emphiresep/mods/subcontract/getQuotationByCustId.ts", "src/apis/emphiresep/mods/subcontract/getSealsFromQys.ts", "src/apis/emphiresep/mods/subcontract/getSignProcessFromQys.ts", "src/apis/emphiresep/mods/subcontract/getSubcontractDropdownList.ts", "src/apis/emphiresep/mods/subcontract/getSubcontractListByCustId.ts", "src/apis/emphiresep/mods/subcontract/index.ts", "src/apis/emphiresep/mods/subcontract/insertAndUpdateSubcontract.ts", "src/apis/emphiresep/mods/subcontract/querySubcontractById.ts", "src/apis/emphiresep/mods/subcontract/querySubcontractList.ts", "src/apis/emphiresep/mods/subcontract/querySubcontractLiteList.ts", "src/apis/emphiresep/mods/subcontract/querySubcontractServiceList.ts", "src/apis/emphiresep/mods/subcontract/transferSubcontract.ts", "src/apis/emphiresep/mods/subcontract/updateSubcontractFirstChoice.ts", "src/apis/emphiresep/mods/transfer/exportFile.ts", "src/apis/emphiresep/mods/transfer/getComboDropdownList.ts", "src/apis/emphiresep/mods/transfer/getCustPayEntityDropdownList.ts", "src/apis/emphiresep/mods/transfer/getCustPayEntityDropdownListEx.ts", "src/apis/emphiresep/mods/transfer/getTempTransferFeeList.ts", "src/apis/emphiresep/mods/transfer/getTransferFeeList.ts", "src/apis/emphiresep/mods/transfer/getTransferIdByContractId.ts", "src/apis/emphiresep/mods/transfer/getTransferInfoDropdownList.ts", "src/apis/emphiresep/mods/transfer/getTransferSalaryTempById.ts", "src/apis/emphiresep/mods/transfer/index.ts", "src/apis/emphiresep/mods/transfer/insertSalaryTempRecreateProcess.ts", "src/apis/emphiresep/mods/transfer/insertTransferInfo.ts", "src/apis/emphiresep/mods/transfer/insertTransferInfoAndSubcontract.ts", "src/apis/emphiresep/mods/transfer/queryTransferApprovalList.ts", "src/apis/emphiresep/mods/transfer/queryTransferInfoById.ts", "src/apis/emphiresep/mods/transfer/queryTransferInfoList.ts", "src/apis/emphiresep/mods/transfer/transferApprove.ts", "src/apis/emphiresep/mods/transfer/transferBack.ts", "src/apis/emphiresep/mods/transfer/updateTransferInfo.ts", "src/apis/emphiresep/mods/transfer/updateTransferSalaryTempInfo.ts", "src/apis/emphiresep/mods/updateOderEmpIdc/index.ts", "src/apis/emphiresep/mods/updateOderEmpIdc/pageQueryOrderEmpInfo.ts", "src/apis/emphiresep/mods/updateOderEmpIdc/updateBatchEmpIDC.ts", "src/apis/exfinance/api.d.ts", "src/apis/exfinance/baseClass.ts", "src/apis/exfinance/index.ts", "src/apis/exfinance/mods/exBill/createBill.ts", "src/apis/exfinance/mods/exBill/deleteOneCharges.ts", "src/apis/exfinance/mods/exBill/getAreaDropdownList.ts", "src/apis/exfinance/mods/exBill/getColForDetail.ts", "src/apis/exfinance/mods/exBill/getDelBillList.ts", "src/apis/exfinance/mods/exBill/getDetailForShow.ts", "src/apis/exfinance/mods/exBill/getLogList.ts", "src/apis/exfinance/mods/exBill/getOneCharges.ts", "src/apis/exfinance/mods/exBill/getRecListForLock.ts", "src/apis/exfinance/mods/exBill/index.ts", "src/apis/exfinance/mods/exBill/insertOnChargesModel.ts", "src/apis/exfinance/mods/exBill/lockExBill.ts", "src/apis/exfinance/mods/exBill/postCreateBill.ts", "src/apis/exfinance/mods/exBill/postDeleteOneCharges.ts", "src/apis/exfinance/mods/exBill/postGetAreaDropdownList.ts", "src/apis/exfinance/mods/exBill/postGetColForDetail.ts", "src/apis/exfinance/mods/exBill/postGetLogList.ts", "src/apis/exfinance/mods/exBill/postGetOneCharges.ts", "src/apis/exfinance/mods/exBill/postLockExBill.ts", "src/apis/exfinance/mods/exBill/toDownLoad.ts", "src/apis/exfinance/mods/index.ts", "src/apis/externalsupplier/api.d.ts", "src/apis/externalsupplier/baseClass.ts", "src/apis/externalsupplier/index.ts", "src/apis/externalsupplier/mods/disabilityImp/exportExcel.ts", "src/apis/externalsupplier/mods/disabilityImp/getDisabilityImp.ts", "src/apis/externalsupplier/mods/disabilityImp/getDisabilityImpNew.ts", "src/apis/externalsupplier/mods/disabilityImp/index.ts", "src/apis/externalsupplier/mods/disabilityImp/saveDisabilityImp.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/deletePdHsEmpFee.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/deletePdHsEmpFeeMonth.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/exportFeeChangeTask.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/exportFile.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/genExPdFeeMonth.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/getEmployeeFeeMonthDetailList.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/getExEmployeeOrderList.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/getExternalDropdownList.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/getExternalFrequencyDropdownList.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/getExternalQuotationDropdownList.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/getPdEmpFeeByEmpHireSepId.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/getPdEmployeeFeeMonthDetailList.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/getProviderCs.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/index.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/insertPdFeeChangeDetail.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/insertPdFeeChangeTask.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/insertPdHsEmpFee.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/queryEmpListForChangeTemplt.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/queryExEmpOrderListForEdit.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/queryExEmpOrderListForPer.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/queryExEmpOrderListForSepCon.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/queryPdEfTemplateById.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/queryPdFeeChangeTaskList.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/queryPdHsEmpFee.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/updateExQuotation.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/updateExternalFeeFrequency.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/updatePdFeeChangeDetail.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/updatePdFeeChangeTask.ts", "src/apis/externalsupplier/mods/externalEmpHireSep/updatePdHsEmpFee.ts", "src/apis/externalsupplier/mods/externalSupplier/getCustPayerDorpDownList.ts", "src/apis/externalsupplier/mods/externalSupplier/index.ts", "src/apis/externalsupplier/mods/externalSupplier/providerBankBatchDelete.ts", "src/apis/externalsupplier/mods/externalSupplier/providerBankList.ts", "src/apis/externalsupplier/mods/externalSupplier/providerBankSave.ts", "src/apis/externalsupplier/mods/externalSupplier/providerBankUpdate.ts", "src/apis/externalsupplier/mods/externalSupplier/setDefaultAcct.ts", "src/apis/externalsupplier/mods/filePay/getCoresFeeMonCount.ts", "src/apis/externalsupplier/mods/filePay/getDisbSecurityPayAmt.ts", "src/apis/externalsupplier/mods/filePay/getPrvdOrGroupFilePayAmt.ts", "src/apis/externalsupplier/mods/filePay/index.ts", "src/apis/externalsupplier/mods/filePay/postPayApprove.ts", "src/apis/externalsupplier/mods/filePay/selectFileCustDetial.ts", "src/apis/externalsupplier/mods/filePay/selectFileEmpDetial.ts", "src/apis/externalsupplier/mods/filePay/selectFilePayOfAssignerProvider.ts", "src/apis/externalsupplier/mods/filePay/startWorkFlow.ts", "src/apis/externalsupplier/mods/filePay/uptPayPass.ts", "src/apis/externalsupplier/mods/filePay/uptPayReject.ts", "src/apis/externalsupplier/mods/filePay/uptPayTerminal.ts", "src/apis/externalsupplier/mods/index.ts", "src/apis/externalsupplier/mods/outerPrvdManage/addPrvdGroupRelation.ts", "src/apis/externalsupplier/mods/outerPrvdManage/batchAssignedPrvdCSAuth.ts", "src/apis/externalsupplier/mods/outerPrvdManage/deleteBill.ts", "src/apis/externalsupplier/mods/outerPrvdManage/deleteBranch.ts", "src/apis/externalsupplier/mods/outerPrvdManage/deleteProviderGroup.ts", "src/apis/externalsupplier/mods/outerPrvdManage/deleteProviderGroupContact.ts", "src/apis/externalsupplier/mods/outerPrvdManage/deleteQuotation.ts", "src/apis/externalsupplier/mods/outerPrvdManage/exportExcel.ts", "src/apis/externalsupplier/mods/outerPrvdManage/getBranch.ts", "src/apis/externalsupplier/mods/outerPrvdManage/index.ts", "src/apis/externalsupplier/mods/outerPrvdManage/listBill.ts", "src/apis/externalsupplier/mods/outerPrvdManage/listProviderGroup.ts", "src/apis/externalsupplier/mods/outerPrvdManage/listProviderGroupContacts.ts", "src/apis/externalsupplier/mods/outerPrvdManage/listQuotation.ts", "src/apis/externalsupplier/mods/outerPrvdManage/pageQueryPrvdAssignCSAuth.ts", "src/apis/externalsupplier/mods/outerPrvdManage/pageQueryPrvdAssignCSAuthHis.ts", "src/apis/externalsupplier/mods/outerPrvdManage/queryPrvdGroupRelation.ts", "src/apis/externalsupplier/mods/outerPrvdManage/saveBill.ts", "src/apis/externalsupplier/mods/outerPrvdManage/saveBranch.ts", "src/apis/externalsupplier/mods/outerPrvdManage/saveOrUpdateProviderGroup.ts", "src/apis/externalsupplier/mods/outerPrvdManage/saveOrUpdateProviderGroupContact.ts", "src/apis/externalsupplier/mods/outerPrvdManage/savePrvdGroup.ts", "src/apis/externalsupplier/mods/outerPrvdManage/saveQuotation.ts", "src/apis/externalsupplier/mods/outerPrvdManage/takeEffectPrvdBill.ts", "src/apis/externalsupplier/mods/outerPrvdManage/takeEffectPrvdOffer.ts", "src/apis/externalsupplier/mods/outerPrvdManage/updatePrvdGroup.ts", "src/apis/externalsupplier/mods/outerPrvdManage/uptAssignCSAuth.ts", "src/apis/externalsupplier/mods/provider/exportExcel.ts", "src/apis/externalsupplier/mods/provider/get.ts", "src/apis/externalsupplier/mods/provider/index.ts", "src/apis/externalsupplier/mods/provider/list.ts", "src/apis/externalsupplier/mods/provider/remove.ts", "src/apis/externalsupplier/mods/provider/saveOrUpdate.ts", "src/apis/externalsupplier/mods/providerContract/addAgree.ts", "src/apis/externalsupplier/mods/providerContract/approve.ts", "src/apis/externalsupplier/mods/providerContract/back.ts", "src/apis/externalsupplier/mods/providerContract/delAgree.ts", "src/apis/externalsupplier/mods/providerContract/exportExcel.ts", "src/apis/externalsupplier/mods/providerContract/formalContract.ts", "src/apis/externalsupplier/mods/providerContract/index.ts", "src/apis/externalsupplier/mods/providerContract/insert.ts", "src/apis/externalsupplier/mods/providerContract/list.ts", "src/apis/externalsupplier/mods/providerContract/listAgree.ts", "src/apis/externalsupplier/mods/providerContract/listProviderApprove.ts", "src/apis/externalsupplier/mods/providerContract/terminal.ts", "src/apis/externalsupplier/mods/providerContract/update.ts", "src/apis/externalsupplier/mods/providerPay/exportEmpList.ts", "src/apis/externalsupplier/mods/providerPay/exportExcel.ts", "src/apis/externalsupplier/mods/providerPay/getPayAuditList.ts", "src/apis/externalsupplier/mods/providerPay/index.ts", "src/apis/externalsupplier/mods/providerPay/pageQueryPayItem.ts", "src/apis/externalsupplier/mods/providerPay/pageQueryPaySummary.ts", "src/apis/externalsupplier/mods/providerPay/pageQueryPrvdBill.ts", "src/apis/externalsupplier/mods/providerPay/postPayApprove.ts", "src/apis/externalsupplier/mods/providerPay/postPayment2BFS.ts", "src/apis/externalsupplier/mods/providerPay/queryPayAssignStatByAuditId.ts", "src/apis/externalsupplier/mods/providerPay/queryPayDetail.ts", "src/apis/externalsupplier/mods/providerPay/queryPayDetailStat.ts", "src/apis/externalsupplier/mods/providerPay/queryPrvdPayDetail.ts", "src/apis/externalsupplier/mods/providerPay/syncData.ts", "src/apis/externalsupplier/mods/providerPay/updatePayAudit.ts", "src/apis/externalsupplier/mods/providerPay/uptPayItemStatus.ts", "src/apis/externalsupplier/mods/providerPay/uptPayPass.ts", "src/apis/externalsupplier/mods/providerPay/uptPayReject.ts", "src/apis/externalsupplier/mods/providerPay/uptPayTerminal.ts", "src/apis/externalsupplier/mods/providerPay/uptSynBFSPayItemStatus.ts", "src/apis/finance/api.d.ts", "src/apis/finance/baseClass.ts", "src/apis/finance/index.ts", "src/apis/finance/mods/createBill/createBill.ts", "src/apis/finance/mods/createBill/distributeBillOrder.ts", "src/apis/finance/mods/createBill/getBillOrder.ts", "src/apis/finance/mods/createBill/getBillOrderLog.ts", "src/apis/finance/mods/createBill/index.ts", "src/apis/finance/mods/createBill/insertOrderBill.ts", "src/apis/finance/mods/createBill/postCreateBill.ts", "src/apis/finance/mods/createBill/postQueryOrderBillByBillId.ts", "src/apis/finance/mods/createBill/queryOrderBillByBillId.ts", "src/apis/finance/mods/createBill/toDownLoad.ts", "src/apis/finance/mods/createBill/updateOrderBill.ts", "src/apis/finance/mods/efEleSealInfo/cancelContract.ts", "src/apis/finance/mods/efEleSealInfo/downloadDoc.ts", "src/apis/finance/mods/efEleSealInfo/getEfEleSealInfo.ts", "src/apis/finance/mods/efEleSealInfo/index.ts", "src/apis/finance/mods/efEleSealInfo/saveEfEleSealInfo.ts", "src/apis/finance/mods/efEleSealInfo/selectEosCheckCount.ts", "src/apis/finance/mods/efEleSealInfo/selectHroCheckCount.ts", "src/apis/finance/mods/index.ts", "src/apis/finance/mods/invoiceJinRen/getFile.ts", "src/apis/finance/mods/invoiceJinRen/index.ts", "src/apis/finance/mods/invoiceJinRen/saveParDetails.ts", "src/apis/finance/mods/invoiceJinRen/selectInoviceInfo.ts", "src/apis/finance/mods/invoiceJinRen/selectInoviceInfoType.ts", "src/apis/finance/mods/invoiceJinRen/updateParInfoSynStauts.ts", "src/apis/finance/mods/invoiceMain/autoCreateOrInvoiceInfo.ts", "src/apis/finance/mods/invoiceMain/checkMailTitleName.ts", "src/apis/finance/mods/invoiceMain/createOrInvoiceInfoAndDetail.ts", "src/apis/finance/mods/invoiceMain/createOrInvoiceInfoAndDetails.ts", "src/apis/finance/mods/invoiceMain/delParInfos.ts", "src/apis/finance/mods/invoiceMain/delParInfosByInvoiceId.ts", "src/apis/finance/mods/invoiceMain/downloadFile.ts", "src/apis/finance/mods/invoiceMain/getApprovalDetailData.ts", "src/apis/finance/mods/invoiceMain/getBillingNumByTitle.ts", "src/apis/finance/mods/invoiceMain/getBusinessInfo.ts", "src/apis/finance/mods/invoiceMain/getChoiceParItemData.ts", "src/apis/finance/mods/invoiceMain/getCustPayerDorpDownList.ts", "src/apis/finance/mods/invoiceMain/getCustomerInfo.ts", "src/apis/finance/mods/invoiceMain/getEmailByCustPayerId.ts", "src/apis/finance/mods/invoiceMain/getInvoiceDetails.ts", "src/apis/finance/mods/invoiceMain/getInvoiceTypeByBranch.ts", "src/apis/finance/mods/invoiceMain/getInvoicesForInvaild.ts", "src/apis/finance/mods/invoiceMain/getInvoicesInfoPage.ts", "src/apis/finance/mods/invoiceMain/getItemForInvoiceInfo.ts", "src/apis/finance/mods/invoiceMain/getItemsForInvoice.ts", "src/apis/finance/mods/invoiceMain/getOrParInfoStatusData.ts", "src/apis/finance/mods/invoiceMain/getParDetailData.ts", "src/apis/finance/mods/invoiceMain/getRuleDifferenceType.ts", "src/apis/finance/mods/invoiceMain/getRuleShortfallType.ts", "src/apis/finance/mods/invoiceMain/getUnInoviceDetail.ts", "src/apis/finance/mods/invoiceMain/getUnionDataByReceiveId.ts", "src/apis/finance/mods/invoiceMain/getVerifyInfoInReceiveIds.ts", "src/apis/finance/mods/invoiceMain/getVerifyInfoInReceiveIdsExportData.ts", "src/apis/finance/mods/invoiceMain/getWorkFlowEx.ts", "src/apis/finance/mods/invoiceMain/index.ts", "src/apis/finance/mods/invoiceMain/postDelParInfos.ts", "src/apis/finance/mods/invoiceMain/postDownloadFile.ts", "src/apis/finance/mods/invoiceMain/postGetItemsForInvoice.ts", "src/apis/finance/mods/invoiceMain/postUpdateInvaildInvoice.ts", "src/apis/finance/mods/invoiceMain/quertVerifyInvoice.ts", "src/apis/finance/mods/invoiceMain/saveChoiceParItemData.ts", "src/apis/finance/mods/invoiceMain/saveParInfos.ts", "src/apis/finance/mods/invoiceMain/sendBusiness.ts", "src/apis/finance/mods/invoiceMain/sendBusiness2.ts", "src/apis/finance/mods/invoiceMain/syncJinDieData.ts", "src/apis/finance/mods/invoiceMain/terminalPro.ts", "src/apis/finance/mods/invoiceMain/toDownLoad.ts", "src/apis/finance/mods/invoiceMain/updateBillingNumById.ts", "src/apis/finance/mods/invoiceMain/updateInvaildInvoice.ts", "src/apis/finance/mods/invoiceMain/updateInvoiceInfoReStatus.ts", "src/apis/finance/mods/invoiceMain/updateOrInvoiceInfo.ts", "src/apis/finance/mods/invoiceMain/updateParStatus.ts", "src/apis/finance/mods/invoiceMain/uploadFile.ts", "src/apis/finance/mods/invoiceMain/writeBackFileInfo.ts", "src/apis/finance/mods/invoiceRule/commitInvoiceRule.ts", "src/apis/finance/mods/invoiceRule/delInvoiceRule.ts", "src/apis/finance/mods/invoiceRule/delInvoiceRule2.ts", "src/apis/finance/mods/invoiceRule/getInvoiceRuleList.ts", "src/apis/finance/mods/invoiceRule/getTitleItemNameList.ts", "src/apis/finance/mods/invoiceRule/getVerifyInvoiceRuleList.ts", "src/apis/finance/mods/invoiceRule/index.ts", "src/apis/finance/mods/invoiceRule/saveInvoiceRule.ts", "src/apis/finance/mods/invoiceRule/updateVerificationStatus.ts", "src/apis/finance/mods/invoiceSendMail/index.ts", "src/apis/finance/mods/invoiceSendMail/sendInvoiceMailBySendTime.ts", "src/apis/finance/mods/invoiceSf/index.ts", "src/apis/finance/mods/invoiceSf/mailNoPush.ts", "src/apis/finance/mods/invoiceSf/productPhotoPush.ts", "src/apis/finance/mods/receiptsDetail/createExecuteVerification.ts", "src/apis/finance/mods/receiptsDetail/createExecuteVerificationRisk.ts", "src/apis/finance/mods/receiptsDetail/createReceiptInfo.ts", "src/apis/finance/mods/receiptsDetail/createRiskProfig.ts", "src/apis/finance/mods/receiptsDetail/getAarrivalGathering.ts", "src/apis/finance/mods/receiptsDetail/getArrivalAmountInfo.ts", "src/apis/finance/mods/receiptsDetail/getCustPayerDorpDownList.ts", "src/apis/finance/mods/receiptsDetail/getInvoiceInfoByReceivableId.ts", "src/apis/finance/mods/receiptsDetail/getInvoicesInfoPage.ts", "src/apis/finance/mods/receiptsDetail/getItemForInvoiceInfo.ts", "src/apis/finance/mods/receiptsDetail/getItemsResult.ts", "src/apis/finance/mods/receiptsDetail/getItemsResultForAarrivalGathering.ts", "src/apis/finance/mods/receiptsDetail/getOverdrafPage.ts", "src/apis/finance/mods/receiptsDetail/getReceiptsDetailList.ts", "src/apis/finance/mods/receiptsDetail/getReceiptsDetails.ts", "src/apis/finance/mods/receiptsDetail/getReceivableAmountByContractIdAndCategoryForVerificationApproval.ts", "src/apis/finance/mods/receiptsDetail/getReciptsList.ts", "src/apis/finance/mods/receiptsDetail/getVerificationApporval.ts", "src/apis/finance/mods/receiptsDetail/getYskDetail.ts", "src/apis/finance/mods/receiptsDetail/index.ts", "src/apis/finance/mods/receiptsDetail/isFinancialCS.ts", "src/apis/finance/mods/receiptsDetail/queryReceivableInContractAndCategoryList.ts", "src/apis/finance/mods/receiptsDetail/receiptsTransTwo.ts", "src/apis/finance/mods/receiptsDetail/selectReceivableRecipt.ts", "src/apis/finance/mods/receiptsDetail/selectRiskCustEmp.ts", "src/apis/finance/mods/receiptsDetail/selectRiskFund.ts", "src/apis/finance/mods/receiptsDetail/selectRiskFundEmp.ts", "src/apis/finance/mods/receiptsDetail/toDownLoad.ts", "src/apis/finance/mods/receiptsDetail/toDownLoad2.ts", "src/apis/finance/mods/receiptsDetail/updateOrInvoiceInfo.ts", "src/apis/finance/mods/receiptsDetail/updateReceiptsDetailStatus.ts", "src/apis/finance/mods/receiptsDetail/updateReceiptsStatus.ts", "src/apis/finance/mods/receiptsDetail/updateVerificationStatus.ts", "src/apis/finance/mods/receivable/approveOnecharges.ts", "src/apis/finance/mods/receivable/billSendEmail.ts", "src/apis/finance/mods/receivable/cancelEleSealStatus.ts", "src/apis/finance/mods/receivable/checkBigCustomer.ts", "src/apis/finance/mods/receivable/checkInsertLockInfo.ts", "src/apis/finance/mods/receivable/checkOnecharges.ts", "src/apis/finance/mods/receivable/checkOnechargesTax.ts", "src/apis/finance/mods/receivable/checkUserDoOnechargesWithoutApproval.ts", "src/apis/finance/mods/receivable/createManualApply.ts", "src/apis/finance/mods/receivable/createReceivable.ts", "src/apis/finance/mods/receivable/createReceivableDetail.ts", "src/apis/finance/mods/receivable/deleteOneCharges.ts", "src/apis/finance/mods/receivable/getBillList.ts", "src/apis/finance/mods/receivable/getColForDetail.ts", "src/apis/finance/mods/receivable/getContractName.ts", "src/apis/finance/mods/receivable/getCountNumberFromRecBill.ts", "src/apis/finance/mods/receivable/getCustOnCharges.ts", "src/apis/finance/mods/receivable/getDelBillList.ts", "src/apis/finance/mods/receivable/getDetailForShow.ts", "src/apis/finance/mods/receivable/getEmpBillColForDetail.ts", "src/apis/finance/mods/receivable/getEmpBillDetailForShow.ts", "src/apis/finance/mods/receivable/getErbVerifyInvoiceStatusFromDetail.ts", "src/apis/finance/mods/receivable/getGroupList.ts", "src/apis/finance/mods/receivable/getIsReceiveAudit.ts", "src/apis/finance/mods/receivable/getIsReceiveAudits.ts", "src/apis/finance/mods/receivable/getLogList.ts", "src/apis/finance/mods/receivable/getModifyData.ts", "src/apis/finance/mods/receivable/getModifyHistory.ts", "src/apis/finance/mods/receivable/getOneCharges.ts", "src/apis/finance/mods/receivable/getOnechargesApproval.ts", "src/apis/finance/mods/receivable/getOnechargesApprovalDetail.ts", "src/apis/finance/mods/receivable/getOnechargesExport.ts", "src/apis/finance/mods/receivable/getOnechargesInProcess.ts", "src/apis/finance/mods/receivable/getOnechargesTaxAmt.ts", "src/apis/finance/mods/receivable/getRecListForLock.ts", "src/apis/finance/mods/receivable/getReceivableTemplate.ts", "src/apis/finance/mods/receivable/getSumDataOfDetail.ts", "src/apis/finance/mods/receivable/getTotalBillAmount.ts", "src/apis/finance/mods/receivable/getUnlockInfoList.ts", "src/apis/finance/mods/receivable/getUpdateEmp.ts", "src/apis/finance/mods/receivable/getmanualApprovalList.ts", "src/apis/finance/mods/receivable/index.ts", "src/apis/finance/mods/receivable/insertBillUnlockProcess.ts", "src/apis/finance/mods/receivable/insertGroupEmps.ts", "src/apis/finance/mods/receivable/insertLockInfo.ts", "src/apis/finance/mods/receivable/insertOnChargesModel.ts", "src/apis/finance/mods/receivable/insertOrUpdateGroups.ts", "src/apis/finance/mods/receivable/lockable.ts", "src/apis/finance/mods/receivable/lockables.ts", "src/apis/finance/mods/receivable/passDelBillProcess.ts", "src/apis/finance/mods/receivable/postCheckUserDoOnechargesWithoutApproval.ts", "src/apis/finance/mods/receivable/postGetColForDetail.ts", "src/apis/finance/mods/receivable/postGetReceivableTemplate.ts", "src/apis/finance/mods/receivable/queryGroUpEmplist.ts", "src/apis/finance/mods/receivable/queryUnlockInfo.ts", "src/apis/finance/mods/receivable/sendBigCustomer.ts", "src/apis/finance/mods/receivable/setEleSealStatus.ts", "src/apis/finance/mods/receivable/startDelBillProcess.ts", "src/apis/finance/mods/receivable/startOneCharges.ts", "src/apis/finance/mods/receivable/toDownLoad.ts", "src/apis/finance/mods/receivable/updateBillUnlock.ts", "src/apis/finance/mods/receivable/updateFileId.ts", "src/apis/finance/mods/receivable/updateReceivable.ts", "src/apis/finance/mods/specialCustomers/addSpecialCustomers.ts", "src/apis/finance/mods/specialCustomers/checkSpecialCustomers.ts", "src/apis/finance/mods/specialCustomers/deleteSpecialCustomers.ts", "src/apis/finance/mods/specialCustomers/exportSpecialCustomers.ts", "src/apis/finance/mods/specialCustomers/index.ts", "src/apis/finance/mods/specialCustomers/queryList.ts", "src/apis/finance/mods/ssReturnInfo/add.ts", "src/apis/finance/mods/ssReturnInfo/checkPayIsOk.ts", "src/apis/finance/mods/ssReturnInfo/index.ts", "src/apis/finance/mods/ssReturnInfo/pageQuery.ts", "src/apis/finance/mods/ssReturnInfo/queryImpResult.ts", "src/apis/finance/mods/ssReturnInfo/upt.ts", "src/apis/finance/mods/ssReturnInfoApp/approve.ts", "src/apis/finance/mods/ssReturnInfoApp/approveStartProcess.ts", "src/apis/finance/mods/ssReturnInfoApp/approveTerminal.ts", "src/apis/finance/mods/ssReturnInfoApp/getColForDetail.ts", "src/apis/finance/mods/ssReturnInfoApp/getDetailForShow.ts", "src/apis/finance/mods/ssReturnInfoApp/index.ts", "src/apis/finance/mods/ssReturnInfoApp/pageQueryApp.ts", "src/apis/finance/mods/ssReturnInfoApp/postGetColForDetail.ts", "src/apis/finance/mods/uploadCashRecord/getCashRecord.ts", "src/apis/finance/mods/uploadCashRecord/getCashRecordDetail.ts", "src/apis/finance/mods/uploadCashRecord/getTreatmentDetail.ts", "src/apis/finance/mods/uploadCashRecord/index.ts", "src/apis/finance/mods/uploadCashRecord/linkForCustomerBalance.ts", "src/apis/finance/mods/uploadCashRecord/toCustpayerBlack.ts", "src/apis/finance/mods/uploadCashRecord/toDownLoad.ts", "src/apis/finance/mods/uploadCashRecord/updateCashRecordDetail.ts", "src/apis/finance/mods/uploadCashRecord/updateRecordDetailR.ts", "src/apis/index.ts", "src/apis/information/api.d.ts", "src/apis/information/baseClass.ts", "src/apis/information/index.ts", "src/apis/information/mods/annualPay/exporter.ts", "src/apis/information/mods/annualPay/index.ts", "src/apis/information/mods/annualPay/insert.ts", "src/apis/information/mods/annualPay/select.ts", "src/apis/information/mods/annualPay/update.ts", "src/apis/information/mods/disabilityBenefitsContrller/calculate.ts", "src/apis/information/mods/disabilityBenefitsContrller/del.ts", "src/apis/information/mods/disabilityBenefitsContrller/index.ts", "src/apis/information/mods/disabilityBenefitsContrller/query.ts", "src/apis/information/mods/disabilityBenefitsContrller/save.ts", "src/apis/information/mods/fringeBenefits/exportCustFringeBenefits.ts", "src/apis/information/mods/fringeBenefits/exportCustMaterial.ts", "src/apis/information/mods/fringeBenefits/exportMultFringeBenefits.ts", "src/apis/information/mods/fringeBenefits/exportSingleFringeBenefits.ts", "src/apis/information/mods/fringeBenefits/getBusSubtypeDropdownList.ts", "src/apis/information/mods/fringeBenefits/getBusTypeDropdownList.ts", "src/apis/information/mods/fringeBenefits/getBusnameClassDropdownList.ts", "src/apis/information/mods/fringeBenefits/getDetail.ts", "src/apis/information/mods/fringeBenefits/getList.ts", "src/apis/information/mods/fringeBenefits/index.ts", "src/apis/information/mods/fringeBenefits/save.ts", "src/apis/information/mods/index.ts", "src/apis/information/mods/multPolicy/createSurSsVerticalByCust.ts", "src/apis/information/mods/multPolicy/getBoE4IP.ts", "src/apis/information/mods/multPolicy/getCalSsByCity.ts", "src/apis/information/mods/multPolicy/getCalSsByCust.ts", "src/apis/information/mods/multPolicy/getSurSsAcrossByCity.ts", "src/apis/information/mods/multPolicy/getSurSsAcrossByCust.ts", "src/apis/information/mods/multPolicy/getSurSsVerticalByCity.ts", "src/apis/information/mods/multPolicy/getSurSsVerticalCityData.ts", "src/apis/information/mods/multPolicy/getSurSsVerticalCtiysByCust.ts", "src/apis/information/mods/multPolicy/index.ts", "src/apis/information/mods/multPolicy/postGetBoE4IP.ts", "src/apis/information/mods/multPolicy/postGetSurSsAcrossByCust.ts", "src/apis/information/mods/multPolicy/postGetSurSsVerticalCtiysByCust.ts", "src/apis/information/mods/onceCharge/index.ts", "src/apis/information/mods/onceCharge/insertOnceCharge.ts", "src/apis/information/mods/onceCharge/postUpdate.ts", "src/apis/information/mods/onceCharge/queryOnceCharge.ts", "src/apis/information/mods/onceCharge/updateOnceCharge.ts", "src/apis/information/mods/policyDisabled/exporter.ts", "src/apis/information/mods/policyDisabled/index.ts", "src/apis/information/mods/policyDisabled/insert.ts", "src/apis/information/mods/policyDisabled/select.ts", "src/apis/information/mods/policyDisabled/update.ts", "src/apis/information/mods/policyLabel/getAllPolicyLabelDropdownList.ts", "src/apis/information/mods/policyLabel/getPolicyLabelDropdownList.ts", "src/apis/information/mods/policyLabel/index.ts", "src/apis/information/mods/policyLabel/insertPolicyLabel.ts", "src/apis/information/mods/policyLabel/queryPolicyLabel.ts", "src/apis/information/mods/policyLabel/updateState.ts", "src/apis/information/mods/policyLevel/checkDownLoad.ts", "src/apis/information/mods/policyLevel/deletePolicyLevel.ts", "src/apis/information/mods/policyLevel/exportTitleFile.ts", "src/apis/information/mods/policyLevel/getBusnameClassDropdownList.ts", "src/apis/information/mods/policyLevel/getDetail.ts", "src/apis/information/mods/policyLevel/getPolicyLevelDropdownList.ts", "src/apis/information/mods/policyLevel/getPolicyTitleIsUse.ts", "src/apis/information/mods/policyLevel/index.ts", "src/apis/information/mods/policyLevel/insertPolicyLevel.ts", "src/apis/information/mods/policyLevel/insertPolicyTitle.ts", "src/apis/information/mods/policyLevel/queryPolicyLevel.ts", "src/apis/information/mods/policyLevel/queryPolicyTitle.ts", "src/apis/information/mods/policyLevel/remove.ts", "src/apis/information/mods/policyLevel/updatePolicyLevel.ts", "src/apis/information/mods/policyLevel/updatePolicyTitle.ts", "src/apis/information/mods/policyLevel/updateState.ts", "src/apis/information/mods/policyProvinceUser/getDetail.ts", "src/apis/information/mods/policyProvinceUser/getProvinceDropdownList.ts", "src/apis/information/mods/policyProvinceUser/getProvinceList.ts", "src/apis/information/mods/policyProvinceUser/index.ts", "src/apis/information/mods/policyProvinceUser/insertPolicyProvinceUser.ts", "src/apis/information/mods/policyProvinceUser/queryPolicyProvinceUser.ts", "src/apis/information/mods/policyProvinceUser/remove.ts", "src/apis/information/mods/policyProvinceUser/updatePolicyProvinceUser.ts", "src/apis/information/mods/policyQuerySingle/getPolicyTitleDropdownList.ts", "src/apis/information/mods/policyQuerySingle/getTemplateInfoByTitleId.ts", "src/apis/information/mods/policyQuerySingle/index.ts", "src/apis/information/mods/policyQuerySingle/queryPolicyBatch.ts", "src/apis/information/mods/policyQuerySingle/queryPolicySingle.ts", "src/apis/information/mods/policyQuerySingle/queryPolicySingleSale.ts", "src/apis/information/mods/policyQuerySingle/savePdfDownLog.ts", "src/apis/information/mods/policySearchCust/exportSearchPolicyInfo.ts", "src/apis/information/mods/policySearchCust/getColForDetail.ts", "src/apis/information/mods/policySearchCust/getPolicyDropdownList.ts", "src/apis/information/mods/policySearchCust/getPolicyPdf.ts", "src/apis/information/mods/policySearchCust/index.ts", "src/apis/information/mods/policySearchCust/queryPolicySearchCust.ts", "src/apis/information/mods/policySearchCust/queryPolicySearchCustBatch.ts", "src/apis/information/mods/policySpecialArea/deleteById.ts", "src/apis/information/mods/policySpecialArea/enableData.ts", "src/apis/information/mods/policySpecialArea/getDetail.ts", "src/apis/information/mods/policySpecialArea/getDropdownList.ts", "src/apis/information/mods/policySpecialArea/getDropdownListByBranchId.ts", "src/apis/information/mods/policySpecialArea/getDropdownListDefaultValue.ts", "src/apis/information/mods/policySpecialArea/getListByIds.ts", "src/apis/information/mods/policySpecialArea/getProvinceCityNameByAreaId.ts", "src/apis/information/mods/policySpecialArea/index.ts", "src/apis/information/mods/policySpecialArea/insert.ts", "src/apis/information/mods/policySpecialArea/invalidData.ts", "src/apis/information/mods/policySpecialArea/queryPage.ts", "src/apis/information/mods/policySpecialArea/update.ts", "src/apis/information/mods/policyTemplate/checkDelTemplate.ts", "src/apis/information/mods/policyTemplate/checkSendPolicyTemplateUpdateRemind.ts", "src/apis/information/mods/policyTemplate/copyTemplate.ts", "src/apis/information/mods/policyTemplate/getTemplateFields.ts", "src/apis/information/mods/policyTemplate/getTemplateGroups.ts", "src/apis/information/mods/policyTemplate/index.ts", "src/apis/information/mods/policyTemplate/modifyFields.ts", "src/apis/information/mods/policyTemplate/modifyGroups.ts", "src/apis/information/mods/policyTemplate/modifyNameById.ts", "src/apis/information/mods/policyTemplate/modifyStateById.ts", "src/apis/information/mods/policyTemplate/previewPolicyTemplate.ts", "src/apis/information/mods/policyTemplate/previewPolicyTemplateAndVer.ts", "src/apis/information/mods/policyTemplate/queryPolicyTemplateList.ts", "src/apis/information/mods/policyTemplate/saveFields.ts", "src/apis/information/mods/policyTemplate/saveGroups.ts", "src/apis/information/mods/policyTemplate/savePolicyTemplate.ts", "src/apis/information/mods/policyTemplate/saveSortSeqNum.ts", "src/apis/information/mods/policyTemplate/sendPolicyTemplateUpdateRemind.ts", "src/apis/information/mods/policyTemplateInfo/addInfo.ts", "src/apis/information/mods/policyTemplateInfo/approved.ts", "src/apis/information/mods/policyTemplateInfo/checkAddNext.ts", "src/apis/information/mods/policyTemplateInfo/checkPublicPolicy.ts", "src/apis/information/mods/policyTemplateInfo/checkTemplateChange.ts", "src/apis/information/mods/policyTemplateInfo/commitApproval.ts", "src/apis/information/mods/policyTemplateInfo/exportPolicyInfo.ts", "src/apis/information/mods/policyTemplateInfo/getByInfoId.ts", "src/apis/information/mods/policyTemplateInfo/getCopyLastPolicyInfo.ts", "src/apis/information/mods/policyTemplateInfo/getInternalCity.ts", "src/apis/information/mods/policyTemplateInfo/getInternalCityByBranchId.ts", "src/apis/information/mods/policyTemplateInfo/getLastPolicyInfo.ts", "src/apis/information/mods/policyTemplateInfo/getPdf.ts", "src/apis/information/mods/policyTemplateInfo/getProvinceDropdownList.ts", "src/apis/information/mods/policyTemplateInfo/getProvinceNameByCityId.ts", "src/apis/information/mods/policyTemplateInfo/getTemplateTitlesByScope.ts", "src/apis/information/mods/policyTemplateInfo/getWithoutInternalCity.ts", "src/apis/information/mods/policyTemplateInfo/index.ts", "src/apis/information/mods/policyTemplateInfo/modifyInfo.ts", "src/apis/information/mods/policyTemplateInfo/publishPolicy.ts", "src/apis/information/mods/policyTemplateInfo/queryPolicyInfoApproveList.ts", "src/apis/information/mods/policyTemplateInfo/queryPolicyTemplateInfoList.ts", "src/apis/information/mods/policyTemplateInfo/remove.ts", "src/apis/information/mods/policyTemplateInfo/resendEmail.ts", "src/apis/information/mods/policyTemplateInfo/terminal.ts", "src/apis/information/mods/policyTemplateInfo/updatePdf.ts", "src/apis/information/mods/policyTemplateInfo/updatePolicyUpdateDate.ts", "src/apis/information/mods/servicePointPolicy/getServicePointPage.ts", "src/apis/information/mods/servicePointPolicy/index.ts", "src/apis/information/mods/singlePolicy/calculateInsurance.ts", "src/apis/information/mods/singlePolicy/exportCalculateInsurance.ts", "src/apis/information/mods/singlePolicy/exportPolicyDetail.ts", "src/apis/information/mods/singlePolicy/getBaseProportionTrialTable.ts", "src/apis/information/mods/singlePolicy/getFirstServicePointCity.ts", "src/apis/information/mods/singlePolicy/getPolicyDetail.ts", "src/apis/information/mods/singlePolicy/getProductRatio.ts", "src/apis/information/mods/singlePolicy/getServicePoint.ts", "src/apis/information/mods/singlePolicy/index.ts", "src/apis/information/mods/singlePolicy/postExportPolicyDetail.ts", "src/apis/information/mods/singlePolicy/postGetBaseProportionTrialTable.ts", "src/apis/information/mods/singlePolicy/postGetFirstServicePointCity.ts", "src/apis/information/mods/singlePolicy/postGetPolicyDetail.ts", "src/apis/information/mods/singlePolicy/postGetProductRatio.ts", "src/apis/information/mods/singlePolicy/postGetServicePoint.ts", "src/apis/information/mods/statutoryHoliday/index.ts", "src/apis/information/mods/statutoryHoliday/insertOnceCharge.ts", "src/apis/information/mods/statutoryHoliday/postUpdate.ts", "src/apis/information/mods/statutoryHoliday/queryStatutoryHoliday.ts", "src/apis/information/mods/statutoryHoliday/updateOnceCharge.ts", "src/apis/minio/api.d.ts", "src/apis/minio/baseClass.ts", "src/apis/minio/index.ts", "src/apis/minio/mods/index.ts", "src/apis/minio/mods/minIo/downloadFile.ts", "src/apis/minio/mods/minIo/downloadFilesAsZipStream.ts", "src/apis/minio/mods/minIo/index.ts", "src/apis/minio/mods/minIo/postDownloadFile.ts", "src/apis/minio/mods/minIo/uploadFile.ts", "src/apis/payroll/api.d.ts", "src/apis/payroll/baseClass.ts", "src/apis/payroll/index.ts", "src/apis/payroll/mods/backPay/backPayReport.ts", "src/apis/payroll/mods/backPay/index.ts", "src/apis/payroll/mods/backPay/pageQueryBackPay.ts", "src/apis/payroll/mods/backPay/selectBackPayResult.ts", "src/apis/payroll/mods/backSet/index.ts", "src/apis/payroll/mods/backSet/pageQuerybackSetBatch.ts", "src/apis/payroll/mods/backSet/pageQuerybackSetBatchDetail.ts", "src/apis/payroll/mods/backSet/toDownLoad.ts", "src/apis/payroll/mods/bankPassage/getBankPassage.ts", "src/apis/payroll/mods/bankPassage/index.ts", "src/apis/payroll/mods/bankcard/index.ts", "src/apis/payroll/mods/bankcard/postQueryByPsnId.ts", "src/apis/payroll/mods/bankcard/postUptSephireDt.ts", "src/apis/payroll/mods/bankcard/queryBankcardDetail.ts", "src/apis/payroll/mods/bankcard/queryBankcardInfoPage.ts", "src/apis/payroll/mods/bankcard/queryBankcardPayRollDetail.ts", "src/apis/payroll/mods/bankcard/queryByPsnId.ts", "src/apis/payroll/mods/bankcard/selectEmpCount.ts", "src/apis/payroll/mods/bankcard/uptIsImportSarly.ts", "src/apis/payroll/mods/bankcard/uptIsNotImportSarly.ts", "src/apis/payroll/mods/bankcard/uptSephireDt.ts", "src/apis/payroll/mods/bedcReceiptCom/addTask.ts", "src/apis/payroll/mods/bedcReceiptCom/getReceiptComReturnFlow.ts", "src/apis/payroll/mods/bedcReceiptCom/getReceiptComReturnList.ts", "src/apis/payroll/mods/bedcReceiptCom/index.ts", "src/apis/payroll/mods/bedcReceiptCom/page.ts", "src/apis/payroll/mods/bedcReceiptCom/view.ts", "src/apis/payroll/mods/bedcReceiptEmp/expListForReceipt.ts", "src/apis/payroll/mods/bedcReceiptEmp/index.ts", "src/apis/payroll/mods/bedcReceiptEmp/listForReceipt.ts", "src/apis/payroll/mods/bedcReceiptEmp/listReceiptEmp.ts", "src/apis/payroll/mods/bedcReceiptEmp/printReceiptApply.ts", "src/apis/payroll/mods/bedcReceiptEmp/printReceiptResult.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/getBatchInfo.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/getCalAnnuaWithholdAgentPage.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/getDetail.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/index.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/insertCalAnnuaWithholdAgent.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/isDeletedCalAnnuaWithholdAgent.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/postCalAnnualEmpImpControllerDownloadfile.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/queryCityList.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/querywithholdAgentNameDH.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/querywithholdAgentNameDLH.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/selCalAnnualWithholdAgent.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/selCalAnnualdata.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/selCalAnnualdataHis.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/selectServicePointCityCount.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/selectWithholdAgentCount.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/toDownLoad.ts", "src/apis/payroll/mods/calAnnuaWithholdAgent/updateCalAnnuaWithholdAgentisValid.ts", "src/apis/payroll/mods/calAnnualArea/delCalAnnualArea.ts", "src/apis/payroll/mods/calAnnualArea/getCalAnnualAreaList.ts", "src/apis/payroll/mods/calAnnualArea/index.ts", "src/apis/payroll/mods/calAnnualArea/insertCalAnnualArea.ts", "src/apis/payroll/mods/calAnnualCust/delCalAnnualCust.ts", "src/apis/payroll/mods/calAnnualCust/getCalAnnualCustById.ts", "src/apis/payroll/mods/calAnnualCust/getCalAnnualCustList.ts", "src/apis/payroll/mods/calAnnualCust/index.ts", "src/apis/payroll/mods/calAnnualCust/insertCalAnnualCust.ts", "src/apis/payroll/mods/calAnnualCust/isHaveCanAnnualEmp.ts", "src/apis/payroll/mods/calAnnualCust/updateCalAnnualCust.ts", "src/apis/payroll/mods/calAnnualDeclareImp/getDetail.ts", "src/apis/payroll/mods/calAnnualDeclareImp/index.ts", "src/apis/payroll/mods/calAnnualDeclareImp/toDownLoad.ts", "src/apis/payroll/mods/calAnnualEmp/delCalAnnualEmp.ts", "src/apis/payroll/mods/calAnnualEmp/getCalAnnualEmpByid.ts", "src/apis/payroll/mods/calAnnualEmp/getCalAnnualEmpHisList.ts", "src/apis/payroll/mods/calAnnualEmp/getCalAnnualEmpList.ts", "src/apis/payroll/mods/calAnnualEmp/index.ts", "src/apis/payroll/mods/calAnnualEmp/insertCalAnnualEmp.ts", "src/apis/payroll/mods/calAnnualEmp/isHaveCanAnnualData.ts", "src/apis/payroll/mods/calAnnualEmp/updateCalAnnualEmp.ts", "src/apis/payroll/mods/calAnnualResultImp/getDetail.ts", "src/apis/payroll/mods/calAnnualResultImp/index.ts", "src/apis/payroll/mods/calAnnualResultImp/toDownLoad.ts", "src/apis/payroll/mods/checkEmpType/checkEmpTypePage.ts", "src/apis/payroll/mods/checkEmpType/getAllEmpTypeList.ts", "src/apis/payroll/mods/checkEmpType/index.ts", "src/apis/payroll/mods/checkEmpType/queryCheckEmpTypeDetail.ts", "src/apis/payroll/mods/checkEmpType/queryCheckEmpTypeDetaill.ts", "src/apis/payroll/mods/checkEmpType/queryCheckHrieDtBatchWin.ts", "src/apis/payroll/mods/checkEmpType/uptCheckEmpType.ts", "src/apis/payroll/mods/checkHrieDt/checkHrieDtBatchPage.ts", "src/apis/payroll/mods/checkHrieDt/index.ts", "src/apis/payroll/mods/checkHrieDt/queryCheckHrieDtBatchWin.ts", "src/apis/payroll/mods/checkHrieDt/queryCheckHrieDtDetail.ts", "src/apis/payroll/mods/checkHrieDt/uptCheckHrieDt.ts", "src/apis/payroll/mods/custPayerBlacklist/add.ts", "src/apis/payroll/mods/custPayerBlacklist/disable.ts", "src/apis/payroll/mods/custPayerBlacklist/getSelectedPayer.ts", "src/apis/payroll/mods/custPayerBlacklist/index.ts", "src/apis/payroll/mods/custPayerBlacklist/page.ts", "src/apis/payroll/mods/custPayerBlacklist/toDownLoad.ts", "src/apis/payroll/mods/customerlist/addBlackListAcct.ts", "src/apis/payroll/mods/customerlist/addBlacklist.ts", "src/apis/payroll/mods/customerlist/deleteBlackListAcct.ts", "src/apis/payroll/mods/customerlist/getBlackListAcct.ts", "src/apis/payroll/mods/customerlist/getBlacklist.ts", "src/apis/payroll/mods/customerlist/getHroLog.ts", "src/apis/payroll/mods/customerlist/getWhitelist.ts", "src/apis/payroll/mods/customerlist/index.ts", "src/apis/payroll/mods/customerlist/syncWhitelist.ts", "src/apis/payroll/mods/customerlist/updateBlacklist.ts", "src/apis/payroll/mods/customerlist/updateWhitelist.ts", "src/apis/payroll/mods/dailyAccountFileCheck/download.ts", "src/apis/payroll/mods/dailyAccountFileCheck/exporting.ts", "src/apis/payroll/mods/dailyAccountFileCheck/index.ts", "src/apis/payroll/mods/dailyAccountFileCheck/postReconciliationQuery.ts", "src/apis/payroll/mods/dailyAccountFileCheck/queryReconDetailHisList.ts", "src/apis/payroll/mods/dailyAccountFileCheck/queryReconDetailList.ts", "src/apis/payroll/mods/dailyAccountFileCheck/queryReconHisList.ts", "src/apis/payroll/mods/dailyAccountFileCheck/queryReconList.ts", "src/apis/payroll/mods/dailyAccountFileCheck/recon.ts", "src/apis/payroll/mods/dailyAccountFileCheck/reconciliationQuery.ts", "src/apis/payroll/mods/declareFailFile/getDeclareFailFileList.ts", "src/apis/payroll/mods/declareFailFile/index.ts", "src/apis/payroll/mods/declareFailFile/insertDeclareFailFile.ts", "src/apis/payroll/mods/declareFailFile/updateDeclareFailFile.ts", "src/apis/payroll/mods/declarePush/getDeclarePush.ts", "src/apis/payroll/mods/declarePush/index.ts", "src/apis/payroll/mods/donationdeducItem/delDonationdeducItem.ts", "src/apis/payroll/mods/donationdeducItem/donationdeducItemPage.ts", "src/apis/payroll/mods/donationdeducItem/donationdeducItemPageDetailPage.ts", "src/apis/payroll/mods/donationdeducItem/donationdeducItemPageQuery.ts", "src/apis/payroll/mods/donationdeducItem/index.ts", "src/apis/payroll/mods/donationdeducItem/selectDonationdeducItemCount.ts", "src/apis/payroll/mods/donationdeducItem/toDownLoad.ts", "src/apis/payroll/mods/empDeclareReduce/empDeclareReduceDetailPage.ts", "src/apis/payroll/mods/empDeclareReduce/empDeclareReducePage.ts", "src/apis/payroll/mods/empDeclareReduce/index.ts", "src/apis/payroll/mods/empDeclareReduce/selEmpDeclareReduceTask.ts", "src/apis/payroll/mods/empDeclareReduce/selUptTaxEmpInfoResult.ts", "src/apis/payroll/mods/empDeclareReduce/subEmpDeclareReduce.ts", "src/apis/payroll/mods/empDeclareReduce/subEmpDeclareReduceTask.ts", "src/apis/payroll/mods/empDeclareReduce/toDownLoad.ts", "src/apis/payroll/mods/empMerge/buildTask.ts", "src/apis/payroll/mods/empMerge/confirmQuery.ts", "src/apis/payroll/mods/empMerge/exportEmpMergeData.ts", "src/apis/payroll/mods/empMerge/exportEmpMergeData2.ts", "src/apis/payroll/mods/empMerge/getEmpMergeDataList.ts", "src/apis/payroll/mods/empMerge/getEmpMergeTaskInfoList.ts", "src/apis/payroll/mods/empMerge/getEmpMergeTaskList.ts", "src/apis/payroll/mods/empMerge/getRoster.ts", "src/apis/payroll/mods/empMerge/index.ts", "src/apis/payroll/mods/empMerge/submitApply.ts", "src/apis/payroll/mods/empMerge/submitApplyAgain.ts", "src/apis/payroll/mods/empMerge/submitConfirm.ts", "src/apis/payroll/mods/empMerge/submitConfirmCheck.ts", "src/apis/payroll/mods/empMerge/syncEmpData.ts", "src/apis/payroll/mods/empMerge/toDownLoad.ts", "src/apis/payroll/mods/empSubmitInfo/getEmpSubmitInfoHisPage.ts", "src/apis/payroll/mods/empSubmitInfo/getEmpSubmitInfoPage.ts", "src/apis/payroll/mods/empSubmitInfo/index.ts", "src/apis/payroll/mods/empSubmitInfo/queryEmpSubmitInfo.ts", "src/apis/payroll/mods/empSubmitInfo/querySubmitTaskDetailInfo.ts", "src/apis/payroll/mods/empSubmitInfo/toDownLoad.ts", "src/apis/payroll/mods/empSubmitTask/getEmpSubmitTaskPage.ts", "src/apis/payroll/mods/empSubmitTask/index.ts", "src/apis/payroll/mods/empSubmitTask/querySubmitTaskDetailInfo.ts", "src/apis/payroll/mods/empSubmitTask/toDownLoad.ts", "src/apis/payroll/mods/empSubmitTaskHis/getEmpSubmitTaskHisPage.ts", "src/apis/payroll/mods/empSubmitTaskHis/index.ts", "src/apis/payroll/mods/empSubmitTaskHis/querySubmitTaskDetailInfo.ts", "src/apis/payroll/mods/empSubmitTaskHis/toDownLoad.ts", "src/apis/payroll/mods/employeeInfoAgentWageManage/getAgentWageEmployeeBankInfo.ts", "src/apis/payroll/mods/employeeInfoAgentWageManage/getEmpHistoryInfoById.ts", "src/apis/payroll/mods/employeeInfoAgentWageManage/getEmployeeHistoryInfoById.ts", "src/apis/payroll/mods/employeeInfoAgentWageManage/getWageAgentEmployeeBaseInfo.ts", "src/apis/payroll/mods/employeeInfoAgentWageManage/index.ts", "src/apis/payroll/mods/employeeInfoAgentWageManage/toDownLoad.ts", "src/apis/payroll/mods/employeeInfoAgentWageManage/updateEmployeeBaseInfo.ts", "src/apis/payroll/mods/endowmentItem/delEndowmentItem.ts", "src/apis/payroll/mods/endowmentItem/endowmentItemPage.ts", "src/apis/payroll/mods/endowmentItem/endowmentItemPageDetailPage.ts", "src/apis/payroll/mods/endowmentItem/endowmentItemPageQuery.ts", "src/apis/payroll/mods/endowmentItem/index.ts", "src/apis/payroll/mods/endowmentItem/selectEndowmentItemCount.ts", "src/apis/payroll/mods/endowmentItem/toDownLoad.ts", "src/apis/payroll/mods/eosWageData/getEosWageDataList.ts", "src/apis/payroll/mods/eosWageData/index.ts", "src/apis/payroll/mods/eosWageData/updateEosWageData.ts", "src/apis/payroll/mods/eosWageUpload/BatchupdateEosWageUploadDetail.ts", "src/apis/payroll/mods/eosWageUpload/UploadEosWageData.ts", "src/apis/payroll/mods/eosWageUpload/backCustConfrim.ts", "src/apis/payroll/mods/eosWageUpload/checkBackCustConfrim.ts", "src/apis/payroll/mods/eosWageUpload/checkEosUser.ts", "src/apis/payroll/mods/eosWageUpload/checkEosWageUploadDetail.ts", "src/apis/payroll/mods/eosWageUpload/checkIsNeedFujian.ts", "src/apis/payroll/mods/eosWageUpload/checkKeyNull.ts", "src/apis/payroll/mods/eosWageUpload/checkSendId.ts", "src/apis/payroll/mods/eosWageUpload/checkUser.ts", "src/apis/payroll/mods/eosWageUpload/checkZDSD.ts", "src/apis/payroll/mods/eosWageUpload/checkconType.ts", "src/apis/payroll/mods/eosWageUpload/delCustConFirm.ts", "src/apis/payroll/mods/eosWageUpload/delEosWageUploadDetail.ts", "src/apis/payroll/mods/eosWageUpload/downloadfileForEos.ts", "src/apis/payroll/mods/eosWageUpload/getCustomerEos.ts", "src/apis/payroll/mods/eosWageUpload/getDiffName.ts", "src/apis/payroll/mods/eosWageUpload/getEosWageDataListXz.ts", "src/apis/payroll/mods/eosWageUpload/getEosWageUploadDetailList.ts", "src/apis/payroll/mods/eosWageUpload/getEosWageUploadErrList.ts", "src/apis/payroll/mods/eosWageUpload/getEosWageUploadList.ts", "src/apis/payroll/mods/eosWageUpload/getEosWageUploadTempList.ts", "src/apis/payroll/mods/eosWageUpload/getIsLock.ts", "src/apis/payroll/mods/eosWageUpload/getLog.ts", "src/apis/payroll/mods/eosWageUpload/index.ts", "src/apis/payroll/mods/eosWageUpload/querySubmitUploadDetailForUpt.ts", "src/apis/payroll/mods/eosWageUpload/querySubmitUploadListDetailHis.ts", "src/apis/payroll/mods/eosWageUpload/queryUploadHisList.ts", "src/apis/payroll/mods/eosWageUpload/queryUploadinterfaceList.ts", "src/apis/payroll/mods/eosWageUpload/rollbackEosWageUploadDetail.ts", "src/apis/payroll/mods/eosWageUpload/selCityBywi.ts", "src/apis/payroll/mods/eosWageUpload/submitCust.ts", "src/apis/payroll/mods/eosWageUpload/submitFujian.ts", "src/apis/payroll/mods/eosWageUpload/submitXz.ts", "src/apis/payroll/mods/eosWageUpload/toDownLoad.ts", "src/apis/payroll/mods/eosWageUpload/updateEosWageData.ts", "src/apis/payroll/mods/eosWageUpload/updateEosWageDataStop.ts", "src/apis/payroll/mods/eosWageUpload/updateEosWageUploadDetail.ts", "src/apis/payroll/mods/eosWageUpload/updateEosWageUploadDetail2.ts", "src/apis/payroll/mods/exportTaxFile/exportTaxFileSummary.ts", "src/apis/payroll/mods/exportTaxFile/exportTaxFileinfo.ts", "src/apis/payroll/mods/exportTaxFile/index.ts", "src/apis/payroll/mods/flowTask/getAllFlowInfoTaskList.ts", "src/apis/payroll/mods/flowTask/getAllFlowTaskList.ts", "src/apis/payroll/mods/flowTask/getCollectionFlowHisList.ts", "src/apis/payroll/mods/flowTask/getCollectionFlowInfoTaskList.ts", "src/apis/payroll/mods/flowTask/getCollectionFlowTaskList.ts", "src/apis/payroll/mods/flowTask/index.ts", "src/apis/payroll/mods/flowTask/toDownLoad.ts", "src/apis/payroll/mods/flowTask/toDownLoad2.ts", "src/apis/payroll/mods/flowTask/toDownLoad3.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getAreaDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getBankDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getCountryDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getDegreeDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getPositionDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getProjectDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getProvincesDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getTaxReasonDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/getTaxTypeDropDownList.ts", "src/apis/payroll/mods/goldenTaxDropDownList/index.ts", "src/apis/payroll/mods/healthItem/delHealthItem.ts", "src/apis/payroll/mods/healthItem/healthItemPage.ts", "src/apis/payroll/mods/healthItem/healthItemPageDetailPage.ts", "src/apis/payroll/mods/healthItem/healthItemPageQuery.ts", "src/apis/payroll/mods/healthItem/index.ts", "src/apis/payroll/mods/healthItem/selectHealthItemCount.ts", "src/apis/payroll/mods/healthItem/toDownLoad.ts", "src/apis/payroll/mods/impPayRollSepDate/index.ts", "src/apis/payroll/mods/impPayRollSepDate/pageQueryBatchPayRollSpecialiat.ts", "src/apis/payroll/mods/impPayRollSepDate/pageQueryPayRollSEPDate.ts", "src/apis/payroll/mods/impPayRollSepDate/queryBatchPayRollDetail.ts", "src/apis/payroll/mods/impPayRollSepDate/sel.ts", "src/apis/payroll/mods/impPayRollSepDate/toDownLoad.ts", "src/apis/payroll/mods/index.ts", "src/apis/payroll/mods/payBatch/addReturnProcess.ts", "src/apis/payroll/mods/payBatch/againSendTransStsSync.ts", "src/apis/payroll/mods/payBatch/approve.ts", "src/apis/payroll/mods/payBatch/approveBackPrevious.ts", "src/apis/payroll/mods/payBatch/approveWageAgainSend.ts", "src/apis/payroll/mods/payBatch/backDisk.ts", "src/apis/payroll/mods/payBatch/backProcess.ts", "src/apis/payroll/mods/payBatch/beforeSendDiskCheck.ts", "src/apis/payroll/mods/payBatch/checkALLSyncStatus.ts", "src/apis/payroll/mods/payBatch/checkALLsuccessOrFailure.ts", "src/apis/payroll/mods/payBatch/checkAllChargebackByBatchId.ts", "src/apis/payroll/mods/payBatch/checkAllNotNullBankAcctByPayAuditId.ts", "src/apis/payroll/mods/payBatch/checkAllSuccessInSendBatch.ts", "src/apis/payroll/mods/payBatch/checkAllSuccessRecordByBatchId.ts", "src/apis/payroll/mods/payBatch/checkBankInfoUpdateDtByBatchId.ts", "src/apis/payroll/mods/payBatch/checkBothEqualRecordsByBatchId.ts", "src/apis/payroll/mods/payBatch/checkCommitExistSameEmpIdByPayAuditId.ts", "src/apis/payroll/mods/payBatch/checkConfirmDelayStatusByPayAudit.ts", "src/apis/payroll/mods/payBatch/checkEbankPartByPayBatchId.ts", "src/apis/payroll/mods/payBatch/checkExistSameEmpIdByPayAuditId.ts", "src/apis/payroll/mods/payBatch/checkExistsOffsetDetailByBatchId.ts", "src/apis/payroll/mods/payBatch/checkExistsSupplierPayByBatchId.ts", "src/apis/payroll/mods/payBatch/checkIFIncludeSending.ts", "src/apis/payroll/mods/payBatch/checkIFIncludeSendingByPayAuditId.ts", "src/apis/payroll/mods/payBatch/checkInvaildBankReport.ts", "src/apis/payroll/mods/payBatch/checkNameCodeBankIdIsNullByBatchId.ts", "src/apis/payroll/mods/payBatch/checkOnlyPayAuditIdInBatch.ts", "src/apis/payroll/mods/payBatch/checkPayBatchIdIsNotSend.ts", "src/apis/payroll/mods/payBatch/checkRepetitivePayName.ts", "src/apis/payroll/mods/payBatch/checkSameBankAcctByAuditId.ts", "src/apis/payroll/mods/payBatch/checkTaxFinishedByBatchId.ts", "src/apis/payroll/mods/payBatch/checkValidRecordInMiddleByBatchId.ts", "src/apis/payroll/mods/payBatch/checkWithholdAgentByAuditId.ts", "src/apis/payroll/mods/payBatch/commitEbankPartPayStatus.ts", "src/apis/payroll/mods/payBatch/createPayAudit.ts", "src/apis/payroll/mods/payBatch/createPaySendBatch.ts", "src/apis/payroll/mods/payBatch/createWageAgainSend.ts", "src/apis/payroll/mods/payBatch/delPaySendBatch.ts", "src/apis/payroll/mods/payBatch/doGetPayDetailBankInfo.ts", "src/apis/payroll/mods/payBatch/doWgPayrollApplyBatch.ts", "src/apis/payroll/mods/payBatch/getAgainSendDefaultSendWayByCustId.ts", "src/apis/payroll/mods/payBatch/getAllF10AmtBySendIds.ts", "src/apis/payroll/mods/payBatch/getAllF3AndF10AmtBySendIds.ts", "src/apis/payroll/mods/payBatch/getAppendPaySend.ts", "src/apis/payroll/mods/payBatch/getApplyTitleByPayAddress.ts", "src/apis/payroll/mods/payBatch/getAssignerApplyTitleByPayAddress.ts", "src/apis/payroll/mods/payBatch/getAssignerDepartmentIdByAuditId.ts", "src/apis/payroll/mods/payBatch/getBankInfoByBatchId.ts", "src/apis/payroll/mods/payBatch/getBankInfoByPayAddress.ts", "src/apis/payroll/mods/payBatch/getBankTypePCNameByInterBankPaybatchId.ts", "src/apis/payroll/mods/payBatch/getBaseDataByType.ts", "src/apis/payroll/mods/payBatch/getBatchDelayUploadFileById.ts", "src/apis/payroll/mods/payBatch/getBatchInSetSendResultList.ts", "src/apis/payroll/mods/payBatch/getBatchItemDetailBySendBatch.ts", "src/apis/payroll/mods/payBatch/getButtonUsedDetail.ts", "src/apis/payroll/mods/payBatch/getCheckBackPayApplyStatus.ts", "src/apis/payroll/mods/payBatch/getCheckUpdateSendWayStatus.ts", "src/apis/payroll/mods/payBatch/getClassIdBySends.ts", "src/apis/payroll/mods/payBatch/getConfirmStatusRecordsInBySendIds.ts", "src/apis/payroll/mods/payBatch/getConfirmStatusRecordsInCreateBySendIds.ts", "src/apis/payroll/mods/payBatch/getContractTypeBySsPayAuditId.ts", "src/apis/payroll/mods/payBatch/getDefaultBlackListAcct.ts", "src/apis/payroll/mods/payBatch/getDefaultPorviderBankAcct.ts", "src/apis/payroll/mods/payBatch/getDefaultSendWayByCustId.ts", "src/apis/payroll/mods/payBatch/getDefaultSendWayByCustIdInUpdate.ts", "src/apis/payroll/mods/payBatch/getEbankPartBatch.ts", "src/apis/payroll/mods/payBatch/getEbankPartBatchHistory.ts", "src/apis/payroll/mods/payBatch/getEbankPartCheckByMainId.ts", "src/apis/payroll/mods/payBatch/getEbankPartCheckDetailByCheckId.ts", "src/apis/payroll/mods/payBatch/getEbankPartMainHistoryList.ts", "src/apis/payroll/mods/payBatch/getEbankPartMainList.ts", "src/apis/payroll/mods/payBatch/getEbankPartMainListByBank.ts", "src/apis/payroll/mods/payBatch/getEbankPartMainListByStandard.ts", "src/apis/payroll/mods/payBatch/getEmpDataInWagePayAuditId.ts", "src/apis/payroll/mods/payBatch/getExistAllNeverStatusByPayBatchId.ts", "src/apis/payroll/mods/payBatch/getExistCheckPayStatusByCustIdAndAuditName.ts", "src/apis/payroll/mods/payBatch/getExistDelayPayDetailBySsPayAuditId.ts", "src/apis/payroll/mods/payBatch/getExistDifferentNameById.ts", "src/apis/payroll/mods/payBatch/getExistPayDetailOrBatchItemByPayAuditId.ts", "src/apis/payroll/mods/payBatch/getExpPayDataBySendBatch.ts", "src/apis/payroll/mods/payBatch/getExpPayDataBySendBatchByBankId.ts", "src/apis/payroll/mods/payBatch/getIfIncludeCustDataBySendIds.ts", "src/apis/payroll/mods/payBatch/getIsBlackListByCustId.ts", "src/apis/payroll/mods/payBatch/getNotSameNameFiveNumByPayBatchId.ts", "src/apis/payroll/mods/payBatch/getPayAuditDataList.ts", "src/apis/payroll/mods/payBatch/getPayBatchById.ts", "src/apis/payroll/mods/payBatch/getPayBatchItemInfoInSetBatchPayResult.ts", "src/apis/payroll/mods/payBatch/getPayDataBySendBatch.ts", "src/apis/payroll/mods/payBatch/getPayDetailTypeByCustId.ts", "src/apis/payroll/mods/payBatch/getPayRelevantResultByPaySends.ts", "src/apis/payroll/mods/payBatch/getPayrollEarlyWarningResult.ts", "src/apis/payroll/mods/payBatch/getPrintControlInfoByAuditId.ts", "src/apis/payroll/mods/payBatch/getQueryAddRepeatItemListById.ts", "src/apis/payroll/mods/payBatch/getQueryAgainSendApproveList.ts", "src/apis/payroll/mods/payBatch/getQueryAgainSendList.ts", "src/apis/payroll/mods/payBatch/getQueryPayBankDetailById.ts", "src/apis/payroll/mods/payBatch/getQueryPaySendStageList.ts", "src/apis/payroll/mods/payBatch/getQueryRepeatThisTimeDetailById.ts", "src/apis/payroll/mods/payBatch/getQueryRepeatUptBankcardHisListById.ts", "src/apis/payroll/mods/payBatch/getQuerySalaryPaymentApproveList.ts", "src/apis/payroll/mods/payBatch/getQuerySalaryPaymentList.ts", "src/apis/payroll/mods/payBatch/getQueryThisTimeDetailById.ts", "src/apis/payroll/mods/payBatch/getReceivableTemplateDropDownListById.ts", "src/apis/payroll/mods/payBatch/getRecordCountsByAuditId.ts", "src/apis/payroll/mods/payBatch/getSalaryItemColumnBySendBatch.ts", "src/apis/payroll/mods/payBatch/getSignBranchTitleSpeByDepartmentId.ts", "src/apis/payroll/mods/payBatch/getSupplierBankInfoByPayAddress.ts", "src/apis/payroll/mods/payBatch/getSupplierDefaultPorviderBankAcct.ts", "src/apis/payroll/mods/payBatch/getTaxAmtBySendIds.ts", "src/apis/payroll/mods/payBatch/getWgAgainSendDataById.ts", "src/apis/payroll/mods/payBatch/getWgPayBatchDataList.ts", "src/apis/payroll/mods/payBatch/getWithholdAgentByPayAuditId.ts", "src/apis/payroll/mods/payBatch/getpostscriptRmkList.ts", "src/apis/payroll/mods/payBatch/index.ts", "src/apis/payroll/mods/payBatch/insertAddWgPayBatchRepeatItem.ts", "src/apis/payroll/mods/payBatch/murongDiskNotify.ts", "src/apis/payroll/mods/payBatch/postSbMurongDiskNotify.ts", "src/apis/payroll/mods/payBatch/queryBatch.ts", "src/apis/payroll/mods/payBatch/queryBatchItem.ts", "src/apis/payroll/mods/payBatch/queryBatchWageData.ts", "src/apis/payroll/mods/payBatch/queryDelayBatchItem.ts", "src/apis/payroll/mods/payBatch/queryPaySend.ts", "src/apis/payroll/mods/payBatch/queryPaySendBySendIds.ts", "src/apis/payroll/mods/payBatch/resetPayExceptionRemark.ts", "src/apis/payroll/mods/payBatch/saveBtnOperateLog.ts", "src/apis/payroll/mods/payBatch/saveBtnOperateLogWithRemark.ts", "src/apis/payroll/mods/payBatch/saveConfirmPaySendFinish.ts", "src/apis/payroll/mods/payBatch/saveConfirmPaySendFinishBatch.ts", "src/apis/payroll/mods/payBatch/saveConfirmPaySendFinishBatch2.ts", "src/apis/payroll/mods/payBatch/savePayAuditByFundManager.ts", "src/apis/payroll/mods/payBatch/sendDisk.ts", "src/apis/payroll/mods/payBatch/setAgainEbankPart.ts", "src/apis/payroll/mods/payBatch/setApprovePaySendBatch.ts", "src/apis/payroll/mods/payBatch/setBackPayApply.ts", "src/apis/payroll/mods/payBatch/setBankReport.ts", "src/apis/payroll/mods/payBatch/setBatchPayResultByBatchId.ts", "src/apis/payroll/mods/payBatch/setBusinessRemind.ts", "src/apis/payroll/mods/payBatch/setChargebackByItemIds.ts", "src/apis/payroll/mods/payBatch/setConfirmDelayStatusToSsPayDetail.ts", "src/apis/payroll/mods/payBatch/setDelayByPayBatchId.ts", "src/apis/payroll/mods/payBatch/setEbankPartGenerate.ts", "src/apis/payroll/mods/payBatch/setFundSettlement.ts", "src/apis/payroll/mods/payBatch/setNerverSendByItemIds.ts", "src/apis/payroll/mods/payBatch/setPayBatchRepeatItemListById.ts", "src/apis/payroll/mods/payBatch/setRepeatSendBatch.ts", "src/apis/payroll/mods/payBatch/setSendingByItemIds.ts", "src/apis/payroll/mods/payBatch/setUpdateBankCardInEbankPart.ts", "src/apis/payroll/mods/payBatch/setUpdateEmpBankInfoBatch.ts", "src/apis/payroll/mods/payBatch/setUpdateEmpBankInfoBatchNewFunction.ts", "src/apis/payroll/mods/payBatch/terminal.ts", "src/apis/payroll/mods/payBatch/testMurongDiskNotify.ts", "src/apis/payroll/mods/payBatch/toDownLoad.ts", "src/apis/payroll/mods/payBatch/updateAllRepeatItemSendingStatus.ts", "src/apis/payroll/mods/payBatch/updateBatchStatus.ts", "src/apis/payroll/mods/payBatch/updateEmpBankInfo.ts", "src/apis/payroll/mods/payBatch/updateEmpBankInfoNewFunction.ts", "src/apis/payroll/mods/payBatch/updateFundSettlementResult.ts", "src/apis/payroll/mods/payBatch/updateSendWay.ts", "src/apis/payroll/mods/payBatchVirtual/approve.ts", "src/apis/payroll/mods/payBatchVirtual/checkAllDelayOrNeverRecordInBatchIds.ts", "src/apis/payroll/mods/payBatchVirtual/createPayAudit.ts", "src/apis/payroll/mods/payBatchVirtual/getAppendPaySend.ts", "src/apis/payroll/mods/payBatchVirtual/index.ts", "src/apis/payroll/mods/payBatchVirtual/queryPaySend.ts", "src/apis/payroll/mods/payBatchVirtual/saveBtnOperateLog.ts", "src/apis/payroll/mods/payRollArchives/checkDateForMonth.ts", "src/apis/payroll/mods/payRollArchives/getAllDropDownList.ts", "src/apis/payroll/mods/payRollArchives/getAllReceivableTemplateComboListById.ts", "src/apis/payroll/mods/payRollArchives/getContractInfoBySubContractId.ts", "src/apis/payroll/mods/payRollArchives/getEmpBankCardInClassList.ts", "src/apis/payroll/mods/payRollArchives/getPayRollArchivesEmpIdList.ts", "src/apis/payroll/mods/payRollArchives/getPayRollArchivesList.ts", "src/apis/payroll/mods/payRollArchives/getReceivableTemplateComboListById.ts", "src/apis/payroll/mods/payRollArchives/getSubContractInClassList.ts", "src/apis/payroll/mods/payRollArchives/index.ts", "src/apis/payroll/mods/payRollArchives/pageQueryEditBatch.ts", "src/apis/payroll/mods/payRollArchives/pageQueryFeedBackSendBatch.ts", "src/apis/payroll/mods/payRollArchives/pageQueryFristFeedBackSendBatch.ts", "src/apis/payroll/mods/payRollArchives/pageQuerySecondFeedBackSendBatch.ts", "src/apis/payroll/mods/payRollArchives/queryCountryListForCsd.ts", "src/apis/payroll/mods/payRollArchives/saveAllPsnFunction.ts", "src/apis/payroll/mods/payRollArchives/savePayRollArchives.ts", "src/apis/payroll/mods/payRollArchives/setInvalidWagePsn.ts", "src/apis/payroll/mods/payRollArchives/setInvalidWagePsnBatch.ts", "src/apis/payroll/mods/payRollArchives/toDownLoad.ts", "src/apis/payroll/mods/payRollArchives/updateAllPayRollFormat.ts", "src/apis/payroll/mods/payRollArchives/updateAllPsn.ts", "src/apis/payroll/mods/payRollArchives/updateAllWageReceivableTemplate.ts", "src/apis/payroll/mods/payRollArchives/updateAllWageTax.ts", "src/apis/payroll/mods/payRollClass/delPayRollClass.ts", "src/apis/payroll/mods/payRollClass/getAllDropDownList.ts", "src/apis/payroll/mods/payRollClass/getAssigneeCsIdByContractIdInHsSubcontract.ts", "src/apis/payroll/mods/payRollClass/getPayRollClassListByCustId.ts", "src/apis/payroll/mods/payRollClass/getPublicPayRollClassList.ts", "src/apis/payroll/mods/payRollClass/getReceivableTemplateComboListById.ts", "src/apis/payroll/mods/payRollClass/index.ts", "src/apis/payroll/mods/payRollClass/initData.ts", "src/apis/payroll/mods/payRollClass/insertHsSubContract.ts", "src/apis/payroll/mods/payRollClass/isHasCalculatedPayRollClass.ts", "src/apis/payroll/mods/payRollClass/isRepeatHsSubContractRecord.ts", "src/apis/payroll/mods/payRollClass/isRepeatPayRollClassNameRecordWithId.ts", "src/apis/payroll/mods/payRollClass/isRepeatPayRollClassNameRecordWithoutId.ts", "src/apis/payroll/mods/payRollClass/querySubcontractLiteList.ts", "src/apis/payroll/mods/payRollClass/save.ts", "src/apis/payroll/mods/payRollClass/setPayRollClassInValidStatus.ts", "src/apis/payroll/mods/payRollClass/setPayRollClassValidStatus.ts", "src/apis/payroll/mods/payRollClass/verify.ts", "src/apis/payroll/mods/payRollClassItem/approvalPayRollClassItem.ts", "src/apis/payroll/mods/payRollClassItem/commitVerifyPayRollClassItem.ts", "src/apis/payroll/mods/payRollClassItem/delPayRollClassItem.ts", "src/apis/payroll/mods/payRollClassItem/delPayRollClassItemInter.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemDisplayOrderList.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemHistoryList.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemHistoryListById.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemInter.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemList.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemListById.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemListByIsNoTaxWithADP.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemListByIsTax.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemListInFormula.ts", "src/apis/payroll/mods/payRollClassItem/getPayRollClassItemTempListById.ts", "src/apis/payroll/mods/payRollClassItem/getProductList.ts", "src/apis/payroll/mods/payRollClassItem/getQueryPayRollClassItemList.ts", "src/apis/payroll/mods/payRollClassItem/index.ts", "src/apis/payroll/mods/payRollClassItem/insertPayRollClassItem.ts", "src/apis/payroll/mods/payRollClassItem/insertPayRollClassItemInter.ts", "src/apis/payroll/mods/payRollClassItem/isHasCalculatedPayRollClassItem.ts", "src/apis/payroll/mods/payRollClassItem/isRepeatPayRollClassItemNameRecordWithId.ts", "src/apis/payroll/mods/payRollClassItem/isRepeatPayRollClassItemNameRecordWithoutId.ts", "src/apis/payroll/mods/payRollClassItem/postGetPayRollTempListById.ts", "src/apis/payroll/mods/payRollClassItem/rejectPayRollClassItem.ts", "src/apis/payroll/mods/payRollClassItem/saveDisplay.ts", "src/apis/payroll/mods/payRollClassItem/savePayRollClassItemInter.ts", "src/apis/payroll/mods/payRollClassItem/setDisplayOrderByItemId.ts", "src/apis/payroll/mods/payRollClassItem/toDownLoad.ts", "src/apis/payroll/mods/payRollClassItem/updatePayRollClassItem.ts", "src/apis/payroll/mods/payRollClassItem/updatePayRollClassItemInter.ts", "src/apis/payroll/mods/payRollClassItem/verifyPayRollClassItem.ts", "src/apis/payroll/mods/payRollResult/getWageDataList.ts", "src/apis/payroll/mods/payRollResult/index.ts", "src/apis/payroll/mods/payRollResult/setSendRemark.ts", "src/apis/payroll/mods/payRollResult/toDownLoad.ts", "src/apis/payroll/mods/payTaxBatch/checkALLIdCardInTax.ts", "src/apis/payroll/mods/payTaxBatch/checkContainFailureInTax.ts", "src/apis/payroll/mods/payTaxBatch/getPayTaxBatchMap.ts", "src/apis/payroll/mods/payTaxBatch/getThisTimeTaxDetailById.ts", "src/apis/payroll/mods/payTaxBatch/index.ts", "src/apis/payroll/mods/payTaxBatch/saveConfirmTaxFinish.ts", "src/apis/payroll/mods/payTaxBatch/savePayTaxBatch.ts", "src/apis/payroll/mods/payTaxBatch/setAnewTax.ts", "src/apis/payroll/mods/payTaxBatch/setExportTaxEmpInfo.ts", "src/apis/payroll/mods/payTaxBatch/setExportTaxFile.ts", "src/apis/payroll/mods/payTaxBatch/setUpdateTaxResult.ts", "src/apis/payroll/mods/payTaxBatch/toDownLoad.ts", "src/apis/payroll/mods/payTaxBatch/updateEmpBaseInfo.ts", "src/apis/payroll/mods/payrollSpecialiat/delPayrollSpecialiat.ts", "src/apis/payroll/mods/payrollSpecialiat/getPayRollDropDownList.ts", "src/apis/payroll/mods/payrollSpecialiat/getPayrollSpecialiatListByCustId.ts", "src/apis/payroll/mods/payrollSpecialiat/getPayrollSpecialiatListByCustIds.ts", "src/apis/payroll/mods/payrollSpecialiat/getPayrollSpecialiatPage.ts", "src/apis/payroll/mods/payrollSpecialiat/index.ts", "src/apis/payroll/mods/payrollSpecialiat/save.ts", "src/apis/payroll/mods/payrollSpecialiat/selectPayrollSpecialiatByIdCount.ts", "src/apis/payroll/mods/payrollSpecialiat/toDownLoad.ts", "src/apis/payroll/mods/refundQuery/getRefundDetail.ts", "src/apis/payroll/mods/refundQuery/getRefundLog.ts", "src/apis/payroll/mods/refundQuery/getRefundTask.ts", "src/apis/payroll/mods/refundQuery/getRefundTaskDetail.ts", "src/apis/payroll/mods/refundQuery/index.ts", "src/apis/payroll/mods/refundQuery/querySpEmp.ts", "src/apis/payroll/mods/refundQuery/toDownLoad.ts", "src/apis/payroll/mods/refundQuery/uptConfirmRefund.ts", "src/apis/payroll/mods/refundQuery/uptManual.ts", "src/apis/payroll/mods/refundQuery/uptNoNeedRefund.ts", "src/apis/payroll/mods/refundQuery/uptNoNeedReturn.ts", "src/apis/payroll/mods/reliefItem/delReliefItem.ts", "src/apis/payroll/mods/reliefItem/index.ts", "src/apis/payroll/mods/reliefItem/reliefItemPage.ts", "src/apis/payroll/mods/reliefItem/reliefItemPageDetailPage.ts", "src/apis/payroll/mods/reliefItem/reliefItemPageQuery.ts", "src/apis/payroll/mods/reliefItem/selectReliefItemCount.ts", "src/apis/payroll/mods/reliefItem/toDownLoad.ts", "src/apis/payroll/mods/rptProbationer/checkDateForMonth.ts", "src/apis/payroll/mods/rptProbationer/index.ts", "src/apis/payroll/mods/rptProbationer/pageQueryRptProbationerBatch.ts", "src/apis/payroll/mods/rptProbationer/pageQueryRptPsnSetFailureBatch.ts", "src/apis/payroll/mods/rptProbationer/postRptPsnSetFailureDownloadfile.ts", "src/apis/payroll/mods/rptProbationer/toDownLoad.ts", "src/apis/payroll/mods/salaryRewArea/add.ts", "src/apis/payroll/mods/salaryRewArea/delWgDeliveryArea.ts", "src/apis/payroll/mods/salaryRewArea/edit.ts", "src/apis/payroll/mods/salaryRewArea/index.ts", "src/apis/payroll/mods/salaryRewArea/noUseWgDeliveryArea.ts", "src/apis/payroll/mods/salaryRewArea/queryByPage.ts", "src/apis/payroll/mods/salaryRewArea/useWgDeliveryArea.ts", "src/apis/payroll/mods/sbHistory/downloadFile.ts", "src/apis/payroll/mods/sbHistory/downloadFileSend.ts", "src/apis/payroll/mods/sbHistory/index.ts", "src/apis/payroll/mods/sbHistory/queryHpInfo.ts", "src/apis/payroll/mods/sbHistory/querySbPage.ts", "src/apis/payroll/mods/send/checkExistsNeverStatusInSendId.ts", "src/apis/payroll/mods/send/checkExistsNotSingleHouseholdData.ts", "src/apis/payroll/mods/send/checkExistsSendIdInSsPayAudit.ts", "src/apis/payroll/mods/send/checkInvalidFormula.ts", "src/apis/payroll/mods/send/checkIsHaveTaxed.ts", "src/apis/payroll/mods/send/checkIsMergerTax.ts", "src/apis/payroll/mods/send/checkLockedReceivableTempl.ts", "src/apis/payroll/mods/send/checkNewBankCardTempl.ts", "src/apis/payroll/mods/send/checkReferencedReceivableTempl.ts", "src/apis/payroll/mods/send/checkRepetitiveSendName.ts", "src/apis/payroll/mods/send/dataImport.ts", "src/apis/payroll/mods/send/dataImportNew.ts", "src/apis/payroll/mods/send/getCheckWDMDetailList.ts", "src/apis/payroll/mods/send/getChoicePayRollSendList.ts", "src/apis/payroll/mods/send/getChoiceWageDataModifyList.ts", "src/apis/payroll/mods/send/getCustConfirmStatusBySendId.ts", "src/apis/payroll/mods/send/getCustSalaryLogoList.ts", "src/apis/payroll/mods/send/getDataInUpdateTaxMonth.ts", "src/apis/payroll/mods/send/getExpPayData.ts", "src/apis/payroll/mods/send/getGetSalaryItemColumns.ts", "src/apis/payroll/mods/send/getImpHisBySendId.ts", "src/apis/payroll/mods/send/getImportFailedInfoList.ts", "src/apis/payroll/mods/send/getMaxBillLockingDay.ts", "src/apis/payroll/mods/send/getMaxTaxMonthBySendId.ts", "src/apis/payroll/mods/send/getNoConfirmSend.ts", "src/apis/payroll/mods/send/getPayData.ts", "src/apis/payroll/mods/send/getPayDataInModify.ts", "src/apis/payroll/mods/send/getPaySendList.ts", "src/apis/payroll/mods/send/getSalaryItemColumn.ts", "src/apis/payroll/mods/send/getSelectMultiEmployee.ts", "src/apis/payroll/mods/send/getSelectWithholdAgent.ts", "src/apis/payroll/mods/send/getSendDetailDelayBySendId.ts", "src/apis/payroll/mods/send/getSendMailList.ts", "src/apis/payroll/mods/send/getSendMailResultList.ts", "src/apis/payroll/mods/send/getWageDataDetailInCountPay.ts", "src/apis/payroll/mods/send/getWageDataList.ts", "src/apis/payroll/mods/send/getsendIds.ts", "src/apis/payroll/mods/send/hasBillEnd.ts", "src/apis/payroll/mods/send/hasUnConfirmSend.ts", "src/apis/payroll/mods/send/hasUnValidInterface.ts", "src/apis/payroll/mods/send/index.ts", "src/apis/payroll/mods/send/insertSendImportHIs.ts", "src/apis/payroll/mods/send/isNotPayrollShowStatus.ts", "src/apis/payroll/mods/send/newUpdateNewWgPsn.ts", "src/apis/payroll/mods/send/payrollShowStatus.ts", "src/apis/payroll/mods/send/postGetSendMailListPage.ts", "src/apis/payroll/mods/send/postGetSendMailResultListPage.ts", "src/apis/payroll/mods/send/selectpayrollShowStatus.ts", "src/apis/payroll/mods/send/sendMailList.ts", "src/apis/payroll/mods/send/setCommitCustConfirmStatus.ts", "src/apis/payroll/mods/send/setSendRemark.ts", "src/apis/payroll/mods/send/setWageDataStatusBySendId.ts", "src/apis/payroll/mods/send/toDownLoad.ts", "src/apis/payroll/mods/send/updateAllTaxMonth.ts", "src/apis/payroll/mods/send/updateCustPayerSalary.ts", "src/apis/payroll/mods/send/updateCustPayerSalary1.ts", "src/apis/payroll/mods/send/updateNewBankCard.ts", "src/apis/payroll/mods/send/updateNewReceivableTempl.ts", "src/apis/payroll/mods/send/updateNewWgPsn.ts", "src/apis/payroll/mods/send/updateNewWgPsnByWageDataIds.ts", "src/apis/payroll/mods/send/updateNewWgPsnOther.ts", "src/apis/payroll/mods/send/updateSelectedBillYM.ts", "src/apis/payroll/mods/send/wageConfirm.ts", "src/apis/payroll/mods/send/wageDelete.ts", "src/apis/payroll/mods/send/wageUnConfirm.ts", "src/apis/payroll/mods/spEmployee/index.ts", "src/apis/payroll/mods/spEmployee/postList.ts", "src/apis/payroll/mods/spEmployee/querySpEmp.ts", "src/apis/payroll/mods/spEmployee/sendSMSValid.ts", "src/apis/payroll/mods/spEmployee/updateEnable.ts", "src/apis/payroll/mods/spEmployee/updateKey.ts", "src/apis/payroll/mods/spEmployee/updateSpEmp.ts", "src/apis/payroll/mods/specialDeduction/deductionDown1.ts", "src/apis/payroll/mods/specialDeduction/deductionDown2.ts", "src/apis/payroll/mods/specialDeduction/getDeductionTask.ts", "src/apis/payroll/mods/specialDeduction/getDeductionTaskDetail.ts", "src/apis/payroll/mods/specialDeduction/getDeductionTaskList.ts", "src/apis/payroll/mods/specialDeduction/getEmployee.ts", "src/apis/payroll/mods/specialDeduction/index.ts", "src/apis/payroll/mods/specialDeduction/toDownLoad.ts", "src/apis/payroll/mods/submissionGoldTax/checkTaskInitList.ts", "src/apis/payroll/mods/submissionGoldTax/getTaskInitList.ts", "src/apis/payroll/mods/submissionGoldTax/index.ts", "src/apis/payroll/mods/submissionGoldTax/readySubmitTaxDecForChu.ts", "src/apis/payroll/mods/submissionGoldTax/selReadySubmitTaxDecForChu.ts", "src/apis/payroll/mods/submissionGoldTax/selsubmitGoldTaxRes.ts", "src/apis/payroll/mods/submissionGoldTax/submissionGoldTax.ts", "src/apis/payroll/mods/submissionGoldTax/submissionGoldTaxCeshi.ts", "src/apis/payroll/mods/submissionGoldTax/toDownLoad.ts", "src/apis/payroll/mods/taxDeclaration/addOutTaxDeclaration.ts", "src/apis/payroll/mods/taxDeclaration/checkDeclarationAfter.ts", "src/apis/payroll/mods/taxDeclaration/checkYbffDeclaration.ts", "src/apis/payroll/mods/taxDeclaration/createTaxDeclaration.ts", "src/apis/payroll/mods/taxDeclaration/createTaxDeclarationDetailInfo.ts", "src/apis/payroll/mods/taxDeclaration/createTaxDeclarationInfo.ts", "src/apis/payroll/mods/taxDeclaration/createTaxDiffInfo.ts", "src/apis/payroll/mods/taxDeclaration/delOutTaxDeclaration.ts", "src/apis/payroll/mods/taxDeclaration/delSubmitTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/delTaxDeclaration.ts", "src/apis/payroll/mods/taxDeclaration/exportTaxDiffFile.ts", "src/apis/payroll/mods/taxDeclaration/exportTaxDiffFileinfo.ts", "src/apis/payroll/mods/taxDeclaration/exportTaxFileSummary.ts", "src/apis/payroll/mods/taxDeclaration/exportTaxFileinfo.ts", "src/apis/payroll/mods/taxDeclaration/index.ts", "src/apis/payroll/mods/taxDeclaration/isExistTaxDiff.ts", "src/apis/payroll/mods/taxDeclaration/isExistsTaxPay.ts", "src/apis/payroll/mods/taxDeclaration/isLockTaxDiff.ts", "src/apis/payroll/mods/taxDeclaration/isTaxDeclarationInfo.ts", "src/apis/payroll/mods/taxDeclaration/nextStep.ts", "src/apis/payroll/mods/taxDeclaration/readySubmitTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/selDelubmitTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/selIsDecUpdateHis.ts", "src/apis/payroll/mods/taxDeclaration/selNoTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/selOutEmpInfo.ts", "src/apis/payroll/mods/taxDeclaration/selOutTaxDeclarationPage.ts", "src/apis/payroll/mods/taxDeclaration/selRDecByDecLd.ts", "src/apis/payroll/mods/taxDeclaration/selReadySubmitTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/selSubmitTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/selSubmitTaxDecBack.ts", "src/apis/payroll/mods/taxDeclaration/selSubmitTaxDiff.ts", "src/apis/payroll/mods/taxDeclaration/selSubmitTaxDiffDetail.ts", "src/apis/payroll/mods/taxDeclaration/selTaxDeclarationBackPage.ts", "src/apis/payroll/mods/taxDeclaration/selTaxDeclarationFailPage.ts", "src/apis/payroll/mods/taxDeclaration/selTaxDeclarationPage.ts", "src/apis/payroll/mods/taxDeclaration/selTaxDiffByIdPage.ts", "src/apis/payroll/mods/taxDeclaration/selTaxDiffPage.ts", "src/apis/payroll/mods/taxDeclaration/selUptSubmitTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/selWithId.ts", "src/apis/payroll/mods/taxDeclaration/submitTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/submitTaxDecBack.ts", "src/apis/payroll/mods/taxDeclaration/submitTaxDiff.ts", "src/apis/payroll/mods/taxDeclaration/toDownLoad.ts", "src/apis/payroll/mods/taxDeclaration/uptSubmitTaxDec.ts", "src/apis/payroll/mods/taxDeclaration/zeroReturn.ts", "src/apis/payroll/mods/taxDeclarePhone/exportCust.ts", "src/apis/payroll/mods/taxDeclarePhone/index.ts", "src/apis/payroll/mods/taxDeclarePhone/selectTaxDeclarePhoneDetail.ts", "src/apis/payroll/mods/taxDownloadTask/downloadTaxFile.ts", "src/apis/payroll/mods/taxDownloadTask/downloadTaxFileHis.ts", "src/apis/payroll/mods/taxDownloadTask/expTaskHis.ts", "src/apis/payroll/mods/taxDownloadTask/getTaxDownloadTaskHis.ts", "src/apis/payroll/mods/taxDownloadTask/getTaxDownloadTaskPage.ts", "src/apis/payroll/mods/taxDownloadTask/index.ts", "src/apis/payroll/mods/taxDownloadTask/isTaxDownload.ts", "src/apis/payroll/mods/taxDownloadTask/queryDownloadTaskInfo.ts", "src/apis/payroll/mods/taxDownloadTask/taxDownloadTask.ts", "src/apis/payroll/mods/taxInvite/confirmTaxInvite.ts", "src/apis/payroll/mods/taxInvite/createTaxInvite.ts", "src/apis/payroll/mods/taxInvite/index.ts", "src/apis/payroll/mods/taxInvite/isTaxInvite.ts", "src/apis/payroll/mods/taxInvite/selTaxInviteDetailPage.ts", "src/apis/payroll/mods/taxInvite/selTaxInvitePage.ts", "src/apis/payroll/mods/taxInvite/toDownLoad.ts", "src/apis/payroll/mods/taxPay/applyPayRecord.ts", "src/apis/payroll/mods/taxPay/applyPayRecord2.ts", "src/apis/payroll/mods/taxPay/applyTaxBook.ts", "src/apis/payroll/mods/taxPay/back.ts", "src/apis/payroll/mods/taxPay/checkYbffDeclaration.ts", "src/apis/payroll/mods/taxPay/commitVerifyTaxPay.ts", "src/apis/payroll/mods/taxPay/doApprove.ts", "src/apis/payroll/mods/taxPay/downTaxBook.ts", "src/apis/payroll/mods/taxPay/getNoTaxPayPage.ts", "src/apis/payroll/mods/taxPay/getSuperOrgDropDownList.ts", "src/apis/payroll/mods/taxPay/getTaxPayApprovePage.ts", "src/apis/payroll/mods/taxPay/getTaxPayHisPage.ts", "src/apis/payroll/mods/taxPay/getTaxPayPage.ts", "src/apis/payroll/mods/taxPay/getUndoTaxBookResult.ts", "src/apis/payroll/mods/taxPay/index.ts", "src/apis/payroll/mods/taxPay/queryPayRecodeResult.ts", "src/apis/payroll/mods/taxPay/queryPayRecodeResult2.ts", "src/apis/payroll/mods/taxPay/queryTriPartyInfo.ts", "src/apis/payroll/mods/taxPay/selSubmitTaxDec.ts", "src/apis/payroll/mods/taxPay/selTaxPaySubmitTaxDec.ts", "src/apis/payroll/mods/taxPay/taxPaySubmitTaxDec.ts", "src/apis/payroll/mods/taxPay/toDownLoad.ts", "src/apis/payroll/mods/taxPay/undoTaxBook.ts", "src/apis/payroll/mods/taxPay/updateTaxPayTriParty.ts", "src/apis/payroll/mods/taxPay/uptTaxPayStatus.ts", "src/apis/payroll/mods/taxReFund/createTaxReFund.ts", "src/apis/payroll/mods/taxReFund/delTaxReFund.ts", "src/apis/payroll/mods/taxReFund/downloadTaxFile.ts", "src/apis/payroll/mods/taxReFund/index.ts", "src/apis/payroll/mods/taxReFund/returnZero.ts", "src/apis/payroll/mods/taxReFund/selInventoryList.ts", "src/apis/payroll/mods/taxReFund/selJBDList.ts", "src/apis/payroll/mods/taxReFund/selRfBankList.ts", "src/apis/payroll/mods/taxReFund/selRfCxtfsxf.ts", "src/apis/payroll/mods/taxReFund/selRfPayList.ts", "src/apis/payroll/mods/taxReFund/selRfSlipList.ts", "src/apis/payroll/mods/taxReFund/selRfSqtfsxf.ts", "src/apis/payroll/mods/taxReFund/selRfTfsxfqcxz.ts", "src/apis/payroll/mods/taxReFund/selRfTfsxfqcxzUpt.ts", "src/apis/payroll/mods/taxReFund/selTaxDeclarationPage.ts", "src/apis/payroll/mods/taxReFund/selTaxReFundHis.ts", "src/apis/payroll/mods/taxReFund/submitRfCxtfsxf.ts", "src/apis/payroll/mods/taxReFund/submitRfSqtfsxf.ts", "src/apis/payroll/mods/taxReFund/submitRfTfsxfqcxz.ts", "src/apis/payroll/mods/taxReFund/submitRfTfsxfqcxzUpt.ts", "src/apis/payroll/mods/taxReFund/toDownLoad.ts", "src/apis/payroll/mods/taxTaskInit/dedectionInit.ts", "src/apis/payroll/mods/taxTaskInit/doCheckTaxEmpTemp.ts", "src/apis/payroll/mods/taxTaskInit/doWgTaxInitBackUp.ts", "src/apis/payroll/mods/taxTaskInit/doWgTaxInitUpdate.ts", "src/apis/payroll/mods/taxTaskInit/empExport.ts", "src/apis/payroll/mods/taxTaskInit/expSubmittedResult.ts", "src/apis/payroll/mods/taxTaskInit/getTaskInitDetailList.ts", "src/apis/payroll/mods/taxTaskInit/getTaskInitList.ts", "src/apis/payroll/mods/taxTaskInit/index.ts", "src/apis/payroll/mods/taxTaskInit/insertTaskInit.ts", "src/apis/payroll/mods/taxTaskInit/submittedEmp.ts", "src/apis/payroll/mods/taxTaskInit/submittedResult.ts", "src/apis/payroll/mods/taxTaskInit/updateStatus.ts", "src/apis/payroll/mods/tranAccountSync/add.ts", "src/apis/payroll/mods/tranAccountSync/addAndSyns.ts", "src/apis/payroll/mods/tranAccountSync/disable.ts", "src/apis/payroll/mods/tranAccountSync/editQuery.ts", "src/apis/payroll/mods/tranAccountSync/editSave.ts", "src/apis/payroll/mods/tranAccountSync/getHroLog.ts", "src/apis/payroll/mods/tranAccountSync/index.ts", "src/apis/payroll/mods/tranAccountSync/page.ts", "src/apis/payroll/mods/tranAccountSync/queryById.ts", "src/apis/payroll/mods/tranAccountSync/sync.ts", "src/apis/payroll/mods/triParty/getTriPartyLastOne.ts", "src/apis/payroll/mods/triParty/getTriPartyPage.ts", "src/apis/payroll/mods/triParty/index.ts", "src/apis/payroll/mods/triParty/isDefaultTaxOrgan.ts", "src/apis/payroll/mods/triParty/isTaxOrgan.ts", "src/apis/payroll/mods/triParty/queryTaxOrganDTO.ts", "src/apis/payroll/mods/triParty/queryTriPartyInfo.ts", "src/apis/payroll/mods/triParty/selTriPartySubmitTaxDec.ts", "src/apis/payroll/mods/triParty/toDownLoad.ts", "src/apis/payroll/mods/triParty/triPartySubmitTax.ts", "src/apis/payroll/mods/triParty/updateTriParty.ts", "src/apis/payroll/mods/uploadTaxDeduction/downLoadFile.ts", "src/apis/payroll/mods/uploadTaxDeduction/exportExcel.ts", "src/apis/payroll/mods/uploadTaxDeduction/generateUploadTemplate.ts", "src/apis/payroll/mods/uploadTaxDeduction/getDeductionMonthList.ts", "src/apis/payroll/mods/uploadTaxDeduction/getUploadDeductionDetailList.ts", "src/apis/payroll/mods/uploadTaxDeduction/getWageClassList.ts", "src/apis/payroll/mods/uploadTaxDeduction/index.ts", "src/apis/payroll/mods/uploadTaxDeduction/insertDeductionData.ts", "src/apis/payroll/mods/uploadTaxDeduction/postGetWageClassList.ts", "src/apis/payroll/mods/uploadTaxDeduction/postSelRuningTask.ts", "src/apis/payroll/mods/uploadTaxDeduction/selRuningTask.ts", "src/apis/payroll/mods/uploadTaxDeduction/selectDeductionDetail.ts", "src/apis/payroll/mods/uploadTaxDeduction/selectSpecialDeduction.ts", "src/apis/payroll/mods/uploadTaxDeduction/selectUploadDeduction.ts", "src/apis/payroll/mods/uploadTaxDeduction/taxReportOutFile.ts", "src/apis/payroll/mods/uptGoldTaxEmpInfo/index.ts", "src/apis/payroll/mods/uptGoldTaxEmpInfo/selUptTaxEmpInfoDetailPage.ts", "src/apis/payroll/mods/uptGoldTaxEmpInfo/selUptTaxEmpInfoPage.ts", "src/apis/payroll/mods/uptGoldTaxEmpInfo/selUptTaxEmpInfoResult.ts", "src/apis/payroll/mods/wageData/approve.ts", "src/apis/payroll/mods/wageData/back.ts", "src/apis/payroll/mods/wageData/calcuageSysData.ts", "src/apis/payroll/mods/wageData/calcuageSysDataByIds.ts", "src/apis/payroll/mods/wageData/calculateBackById.ts", "src/apis/payroll/mods/wageData/calculateBackByIds.ts", "src/apis/payroll/mods/wageData/calculateById.ts", "src/apis/payroll/mods/wageData/calculateByIds.ts", "src/apis/payroll/mods/wageData/checkWithholdAgentRIByDataId.ts", "src/apis/payroll/mods/wageData/checkWithholdAgentRIBySendId.ts", "src/apis/payroll/mods/wageData/commitWageDataModifyApprove.ts", "src/apis/payroll/mods/wageData/delPayDataByIds.ts", "src/apis/payroll/mods/wageData/deleteWageDataModify.ts", "src/apis/payroll/mods/wageData/deleteWageDataModifyDetail.ts", "src/apis/payroll/mods/wageData/getCheckItemAmtLimitByDataId.ts", "src/apis/payroll/mods/wageData/getCheckItemAmtLimitBySendId.ts", "src/apis/payroll/mods/wageData/getCheckOffSetProcessRecordsByWageClassId.ts", "src/apis/payroll/mods/wageData/getCheckValidPwaWhiteRecordByDataId.ts", "src/apis/payroll/mods/wageData/getCheckValidPwaWhiteRecordBySendId.ts", "src/apis/payroll/mods/wageData/getEmployTypeBySendId.ts", "src/apis/payroll/mods/wageData/getIDCardNumBySendId.ts", "src/apis/payroll/mods/wageData/getNewSpecialDeduction.ts", "src/apis/payroll/mods/wageData/getSameSignBranchTitleById.ts", "src/apis/payroll/mods/wageData/getTaxTypeByDataId.ts", "src/apis/payroll/mods/wageData/getTaxTypeBySendId.ts", "src/apis/payroll/mods/wageData/getWageDataModifyApprove.ts", "src/apis/payroll/mods/wageData/getWageDataModifyList.ts", "src/apis/payroll/mods/wageData/getWageDataQuestion.ts", "src/apis/payroll/mods/wageData/index.ts", "src/apis/payroll/mods/wageData/insertWageData.ts", "src/apis/payroll/mods/wageData/terminal.ts", "src/apis/payroll/mods/wageData/updateWageDataById.ts", "src/apis/payroll/mods/wageData/updateWageDataList.ts", "src/apis/payroll/mods/wageData/updateWageDataModifyList.ts", "src/apis/payroll/mods/wageData/wageClcAll.ts", "src/apis/payroll/mods/wageDataInterface/WageDataInterfaceListById.ts", "src/apis/payroll/mods/wageDataInterface/checkRepetInterfaceName.ts", "src/apis/payroll/mods/wageDataInterface/checkRequiredInterfaceItem.ts", "src/apis/payroll/mods/wageDataInterface/checkRequiredPureInterfaceItem.ts", "src/apis/payroll/mods/wageDataInterface/checkValidClassItem.ts", "src/apis/payroll/mods/wageDataInterface/delWageDataInterface.ts", "src/apis/payroll/mods/wageDataInterface/delWageDataInterfaceItem.ts", "src/apis/payroll/mods/wageDataInterface/getAllDropDownList.ts", "src/apis/payroll/mods/wageDataInterface/getAllDropDownListInUpdate.ts", "src/apis/payroll/mods/wageDataInterface/getPayRollClassInAgentList.ts", "src/apis/payroll/mods/wageDataInterface/getWageDataInterfaceItemList.ts", "src/apis/payroll/mods/wageDataInterface/getWageDataInterfaceList.ts", "src/apis/payroll/mods/wageDataInterface/index.ts", "src/apis/payroll/mods/wageDataInterface/insertWageDataInterface.ts", "src/apis/payroll/mods/wageDataInterface/transferWageDataInterface.ts", "src/apis/payroll/mods/wageDataInterface/updateWageDataInterface.ts", "src/apis/payroll/mods/wechatDeclaration/downloadExcel.ts", "src/apis/payroll/mods/wechatDeclaration/generateUploadTemplate.ts", "src/apis/payroll/mods/wechatDeclaration/generateZipFile.ts", "src/apis/payroll/mods/wechatDeclaration/getWagePsnAddBatchList.ts", "src/apis/payroll/mods/wechatDeclaration/index.ts", "src/apis/payroll/mods/wechatDeclaration/insertWagePsnAddSupply.ts", "src/apis/payroll/mods/wechatDeclaration/queryWechatDeclarationList.ts", "src/apis/payroll/mods/wechatDeclaration/taxReportOutFile.ts", "src/apis/payroll/mods/wechatDeclaration/taxReportOutFile2.ts", "src/apis/payroll/mods/wgDeliveryArea/add.ts", "src/apis/payroll/mods/wgDeliveryArea/delWgDeliveryArea.ts", "src/apis/payroll/mods/wgDeliveryArea/edit.ts", "src/apis/payroll/mods/wgDeliveryArea/index.ts", "src/apis/payroll/mods/wgDeliveryArea/noUseWgDeliveryArea.ts", "src/apis/payroll/mods/wgDeliveryArea/queryByPage.ts", "src/apis/payroll/mods/wgDeliveryArea/useWgDeliveryArea.ts", "src/apis/payroll/mods/wgOffsetFlow/commitWageOffSetFlow.ts", "src/apis/payroll/mods/wgOffsetFlow/doApproveOffSetFlow.ts", "src/apis/payroll/mods/wgOffsetFlow/index.ts", "src/apis/payroll/mods/wgOffsetFlow/queryWgOffsetFlowDetailById.ts", "src/apis/payroll/mods/wgOffsetFlow/querywgOffsetFlow.ts", "src/apis/payroll/mods/wgOffsetFlow/querywgOffsetTaskPage.ts", "src/apis/payroll/mods/wgOffsetFlow/terminalWageOffSetFlow.ts", "src/apis/payroll/mods/wgOffsetFlow/uptIsAdjust.ts", "src/apis/payroll/mods/wgOffsetTask/allOffset.ts", "src/apis/payroll/mods/wgOffsetTask/deletewgOffsetTaskById.ts", "src/apis/payroll/mods/wgOffsetTask/index.ts", "src/apis/payroll/mods/wgOffsetTask/offsetReport.ts", "src/apis/payroll/mods/wgOffsetTask/queryPaySend.ts", "src/apis/payroll/mods/wgOffsetTask/querywgOffsetTask.ts", "src/apis/payroll/mods/wgOffsetTask/querywgOffsetTaskDetail.ts", "src/apis/payroll/mods/wgOffsetTask/querywgSend.ts", "src/apis/payroll/mods/wgOffsetTask/updateWgOffsetTaskDetail.ts", "src/apis/payroll/mods/withholdAgent/checkWiCityDv.ts", "src/apis/payroll/mods/withholdAgent/getAreaDropDownList.ts", "src/apis/payroll/mods/withholdAgent/getCityDropdownResult.ts", "src/apis/payroll/mods/withholdAgent/index.ts", "src/apis/payroll/mods/withholdAgent/quereyWihholdAgentRelation.ts", "src/apis/payroll/mods/withholdAgent/queryWithholdAgentList.ts", "src/apis/payroll/mods/withholdAgent/saveWithholdAgent.ts", "src/apis/payroll/mods/withholdAgent/selectDepartmentCodeCount.ts", "src/apis/payroll/mods/withholdAgent/selectDutyParagraphCount.ts", "src/apis/payroll/mods/withholdAgent/toDownLoad.ts", "src/apis/payroll/mods/withholdAgent/updateDeclarePassword.ts", "src/apis/payroll/mods/withholdAgent/updateStatus.ts", "src/apis/payroll/mods/withholdAgent/updateWithholdAgentRelation.ts", "src/apis/payroll/mods/withholdAgentWhite/getValidWithholdAgentWhiteRecordByIds.ts", "src/apis/payroll/mods/withholdAgentWhite/getWithholdAgentWhite.ts", "src/apis/payroll/mods/withholdAgentWhite/getWithholdAgentsByPsnIds.ts", "src/apis/payroll/mods/withholdAgentWhite/index.ts", "src/apis/payroll/mods/withholdAgentWhite/insert.ts", "src/apis/payroll/mods/withholdAgentWhite/invalid.ts", "src/apis/payroll/mods/withholdAgentWhite/isHave.ts", "src/apis/poi/api.d.ts", "src/apis/poi/baseClass.ts", "src/apis/poi/index.ts", "src/apis/poi/mods/index.ts", "src/apis/poi/mods/quitPoiDownload/exportTemplate.ts", "src/apis/poi/mods/quitPoiDownload/fetchTemplateInfoByCityId.ts", "src/apis/poi/mods/quitPoiDownload/getSsGroupIdByEmp.ts", "src/apis/poi/mods/quitPoiDownload/getSsGroupIdByEmpZero.ts", "src/apis/poi/mods/quitPoiDownload/getTemplateDataByUuid.ts", "src/apis/poi/mods/quitPoiDownload/importUser.ts", "src/apis/poi/mods/quitPoiDownload/index.ts", "src/apis/poi/mods/quitPoiDownload/postExportTemplate.ts", "src/apis/report/api.d.ts", "src/apis/report/baseClass.ts", "src/apis/report/index.ts", "src/apis/report/mods/ddCust/exportCust.ts", "src/apis/report/mods/ddCust/index.ts", "src/apis/report/mods/ddCust/queryDDCust.ts", "src/apis/report/mods/evacuateWaring/index.ts", "src/apis/report/mods/evacuateWaring/insertEvacuateWaring.ts", "src/apis/report/mods/evacuateWaring/queryEvacuateWaring.ts", "src/apis/report/mods/evacuateWaring/toDownLoad.ts", "src/apis/report/mods/evacuateWaring/updateCstContract.ts", "src/apis/report/mods/inDecMember/getDepartmentDropdownList.ts", "src/apis/report/mods/inDecMember/getServiceProductDropdownList.ts", "src/apis/report/mods/inDecMember/index.ts", "src/apis/report/mods/inDecMember/initAreaBranchData.ts", "src/apis/report/mods/index.ts", "src/apis/report/mods/payrollDisabilityBenefitsReport/getListCount.ts", "src/apis/report/mods/payrollDisabilityBenefitsReport/getPayrollDisabilityBenefitsList.ts", "src/apis/report/mods/payrollDisabilityBenefitsReport/index.ts", "src/apis/report/mods/payrollDisabilityBenefitsReport/toDownLoad.ts", "src/apis/report/mods/reportComplain/approve.ts", "src/apis/report/mods/reportComplain/back.ts", "src/apis/report/mods/reportComplain/backPrevious.ts", "src/apis/report/mods/reportComplain/createRptComplaint.ts", "src/apis/report/mods/reportComplain/index.ts", "src/apis/report/mods/reportComplain/queryRptComplaintNoteBy.ts", "src/apis/report/mods/reportComplain/queryRptComplaintPage.ts", "src/apis/report/mods/reportComplain/queryRptComplaintPageApp.ts", "src/apis/report/mods/reportComplain/saveRptComplaint.ts", "src/apis/report/mods/reportComplain/toDownLoad.ts", "src/apis/report/mods/reportComplain/updateRptComplaint.ts", "src/apis/report/mods/reportFinance/customerDailyVersion.ts", "src/apis/report/mods/reportFinance/generateSpecialBillPrc.ts", "src/apis/report/mods/reportFinance/getAllBillTemplateFromDetail.ts", "src/apis/report/mods/reportFinance/getAuditOrNotifyCountNumberFromDetail.ts", "src/apis/report/mods/reportFinance/getBPXDropdownListByUserId.ts", "src/apis/report/mods/reportFinance/getBPXReceivevableNUm.ts", "src/apis/report/mods/reportFinance/getBPXTempltListByCustIdAndCode.ts", "src/apis/report/mods/reportFinance/getReceivableTempltCount.ts", "src/apis/report/mods/reportFinance/getUserDropdownListByBranch.ts", "src/apis/report/mods/reportFinance/index.ts", "src/apis/report/mods/reportFinance/queryBillProviderNums.ts", "src/apis/report/mods/reportFinance/queryRptServfeeReturn.ts", "src/apis/report/mods/reportFinance/toDownLoad.ts", "src/apis/report/mods/reportQaPayroll/index.ts", "src/apis/report/mods/reportQaPayroll/queryMaxRoleByUser.ts", "src/apis/report/mods/reportQaPayroll/queryMaxRoleByUserEx.ts", "src/apis/report/mods/reportQc/fetchClientVisitedRatio.ts", "src/apis/report/mods/reportQc/fetchUserMaxRoleByUserId.ts", "src/apis/report/mods/reportQc/getCityDropdownListByArea.ts", "src/apis/report/mods/reportQc/getEmpFeeProductForShow.ts", "src/apis/report/mods/reportQc/getEmpFeeProductTitleForShow.ts", "src/apis/report/mods/reportQc/getMydServerPath.ts", "src/apis/report/mods/reportQc/index.ts", "src/apis/report/mods/reportQc/insertBoLog.ts", "src/apis/report/mods/reportQc/postGetMydServerPath.ts", "src/apis/report/mods/reportQc/queryClientVisitedPage.ts", "src/apis/report/mods/reportQc/queryEmpFeeProductPage.ts", "src/apis/report/mods/reportQc/queryProductReport.ts", "src/apis/report/mods/reportQc/queryQAMonthDataNumber.ts", "src/apis/report/mods/wageSureData/getWageSureData.ts", "src/apis/report/mods/wageSureData/index.ts", "src/apis/rpa/api.d.ts", "src/apis/rpa/baseClass.ts", "src/apis/rpa/index.ts", "src/apis/rpa/mods/call/callHistory.ts", "src/apis/rpa/mods/call/exportDetail.ts", "src/apis/rpa/mods/call/exporting.ts", "src/apis/rpa/mods/call/generateSendList.ts", "src/apis/rpa/mods/call/getRecallStatusFromRedis.ts", "src/apis/rpa/mods/call/index.ts", "src/apis/rpa/mods/call/manualStartTask.ts", "src/apis/rpa/mods/call/postQueryHireDetailList.ts", "src/apis/rpa/mods/call/queryAndInsertDetail.ts", "src/apis/rpa/mods/call/queryHireCallList.ts", "src/apis/rpa/mods/call/queryHireDetailList.ts", "src/apis/rpa/mods/call/recall.ts", "src/apis/rpa/mods/call/updateBatchList.ts", "src/apis/rpa/mods/faq/DropdownList.ts", "src/apis/rpa/mods/faq/batchList.ts", "src/apis/rpa/mods/faq/download.ts", "src/apis/rpa/mods/faq/exporting.ts", "src/apis/rpa/mods/faq/faqTempList.ts", "src/apis/rpa/mods/faq/index.ts", "src/apis/rpa/mods/faq/list.ts", "src/apis/rpa/mods/faq/postUpload.ts", "src/apis/rpa/mods/faq/putPut.ts", "src/apis/rpa/mods/faq/restart.ts", "src/apis/rpa/mods/faq/tempExport.ts", "src/apis/rpa/mods/faq/updateInfos.ts", "src/apis/rpa/mods/faqCallBack/index.ts", "src/apis/rpa/mods/faqCallBack/queryFaqByCity.ts", "src/apis/rpa/mods/file/generalDownloadFile.ts", "src/apis/rpa/mods/file/generalDownloadFileByPath.ts", "src/apis/rpa/mods/file/index.ts", "src/apis/rpa/mods/hireFromApp/index.ts", "src/apis/rpa/mods/hireFromApp/queryContractInfo.ts", "src/apis/rpa/mods/hireFromApp/queryEmpAccount.ts", "src/apis/rpa/mods/hireFromApp/queryHireAssociationList.ts", "src/apis/rpa/mods/index.ts", "src/apis/rpa/mods/medicalLevel/add.ts", "src/apis/rpa/mods/medicalLevel/del.ts", "src/apis/rpa/mods/medicalLevel/index.ts", "src/apis/rpa/mods/medicalLevel/list.ts", "src/apis/rpa/mods/rpaJob/add.ts", "src/apis/rpa/mods/rpaJob/index.ts", "src/apis/rpa/mods/rpaJob/list.ts", "src/apis/rpa/mods/rpaJob/runJob.ts", "src/apis/rpa/mods/rpaSzSocialRule/exporting.ts", "src/apis/rpa/mods/rpaSzSocialRule/index.ts", "src/apis/rpa/mods/rpaSzSocialRule/insertRpaSzSocialRule.ts", "src/apis/rpa/mods/rpaSzSocialRule/queryRpaSzSocialRuleList.ts", "src/apis/rpa/mods/rpaSzSocialRule/updateBatchList.ts", "src/apis/rpa/mods/rpaSzSocialRule/updateRpaSzSocialRule.ts", "src/apis/rpa/mods/ssCompanyCode/add.ts", "src/apis/rpa/mods/ssCompanyCode/del.ts", "src/apis/rpa/mods/ssCompanyCode/edit.ts", "src/apis/rpa/mods/ssCompanyCode/index.ts", "src/apis/rpa/mods/ssCompanyCode/info.ts", "src/apis/rpa/mods/ssCompanyCode/list.ts", "src/apis/rpa/mods/ssCompanyCode/queryByDetailId.ts", "src/apis/sale/api.d.ts", "src/apis/sale/baseClass.ts", "src/apis/sale/index.ts", "src/apis/sale/mods/contract/checkContractMeetSvcStop.ts", "src/apis/sale/mods/contract/checkContractMeetSvcStopCount.ts", "src/apis/sale/mods/contract/delContractQuotRelation.ts", "src/apis/sale/mods/contract/eosUserStop.ts", "src/apis/sale/mods/contract/getContractById.ts", "src/apis/sale/mods/contract/getContractIdByCust.ts", "src/apis/sale/mods/contract/getContractList.ts", "src/apis/sale/mods/contract/getContractListForTransfer.ts", "src/apis/sale/mods/contract/getContractProLine.ts", "src/apis/sale/mods/contract/getContractQuos.ts", "src/apis/sale/mods/contract/getContractVersion.ts", "src/apis/sale/mods/contract/getCrmContractCsList.ts", "src/apis/sale/mods/contract/getCurrentSales.ts", "src/apis/sale/mods/contract/getDropDownList.ts", "src/apis/sale/mods/contract/getSalersByCustId.ts", "src/apis/sale/mods/contract/getSignFlagManual.ts", "src/apis/sale/mods/contract/getSub.ts", "src/apis/sale/mods/contract/getSubEx.ts", "src/apis/sale/mods/contract/index.ts", "src/apis/sale/mods/contract/initData.ts", "src/apis/sale/mods/contract/save.ts", "src/apis/sale/mods/contract/saveContractVersion.ts", "src/apis/sale/mods/contract/saveCstContract.ts", "src/apis/sale/mods/contract/sel2.ts", "src/apis/sale/mods/contract/selectList.ts", "src/apis/sale/mods/contract/sendContractStopMail.ts", "src/apis/sale/mods/contract/toDownLoad.ts", "src/apis/sale/mods/contract/updateCstContract.ts", "src/apis/sale/mods/contract/updateReturnBackInfo.ts", "src/apis/sale/mods/contract/updateSignFlagManual.ts", "src/apis/sale/mods/contract/uptContractFlagManual.ts", "src/apis/sale/mods/contract/uptContractSvcStop.ts", "src/apis/sale/mods/contract/uptContractToCRM.ts", "src/apis/sale/mods/contract/uptStatus.ts", "src/apis/sale/mods/contractVersion/checkOnlyValidCustomer.ts", "src/apis/sale/mods/contractVersion/delCustomerRecord.ts", "src/apis/sale/mods/contractVersion/index.ts", "src/apis/sale/mods/contractVersion/isDeletedContractVersion.ts", "src/apis/sale/mods/contractVersion/save.ts", "src/apis/sale/mods/contractVersion/sel.ts", "src/apis/sale/mods/contractVersion/setInValidCustomerRecord.ts", "src/apis/sale/mods/contractVersion/setValidCustomerRecord.ts", "src/apis/sale/mods/contractVersion/setValidCustomerRecordNoCheck.ts", "src/apis/sale/mods/contractVersion/toDownLoad.ts", "src/apis/sale/mods/contractVersion/updateContractVersion.ts", "src/apis/sale/mods/custArchives/deleteArchives.ts", "src/apis/sale/mods/custArchives/deleteShareCust.ts", "src/apis/sale/mods/custArchives/index.ts", "src/apis/sale/mods/custArchives/insertCustArchives.ts", "src/apis/sale/mods/custArchives/saveAllShareCust.ts", "src/apis/sale/mods/custArchives/saveShareCust.ts", "src/apis/sale/mods/custArchives/selShareAchrives.ts", "src/apis/sale/mods/custArchives/selShareCust.ts", "src/apis/sale/mods/custArchives/selectArchives.ts", "src/apis/sale/mods/custUseRec/approved.ts", "src/apis/sale/mods/custUseRec/back.ts", "src/apis/sale/mods/custUseRec/commitApproval.ts", "src/apis/sale/mods/custUseRec/expUseRec.ts", "src/apis/sale/mods/custUseRec/getCustomer.ts", "src/apis/sale/mods/custUseRec/index.ts", "src/apis/sale/mods/custUseRec/insertCustUseRec.ts", "src/apis/sale/mods/custUseRec/page.ts", "src/apis/sale/mods/custUseRec/queryApproveList.ts", "src/apis/sale/mods/custUseRec/removeCustUseRec.ts", "src/apis/sale/mods/custUseRec/terminal.ts", "src/apis/sale/mods/custUseRec/updateCustUseRec.ts", "src/apis/sale/mods/customer/addCustAtt.ts", "src/apis/sale/mods/customer/addSvcReport.ts", "src/apis/sale/mods/customer/checkAssCs.ts", "src/apis/sale/mods/customer/delCustAtt.ts", "src/apis/sale/mods/customer/delCustInter.ts", "src/apis/sale/mods/customer/delSvcReport.ts", "src/apis/sale/mods/customer/deleteShareCust.ts", "src/apis/sale/mods/customer/getCustIdByCode.ts", "src/apis/sale/mods/customer/getCustInter.ts", "src/apis/sale/mods/customer/getCustProLine.ts", "src/apis/sale/mods/customer/getCustomerDetailById.ts", "src/apis/sale/mods/customer/getEosUser.ts", "src/apis/sale/mods/customer/getExportOptions.ts", "src/apis/sale/mods/customer/getGroupIdByCustId.ts", "src/apis/sale/mods/customer/index.ts", "src/apis/sale/mods/customer/initData.ts", "src/apis/sale/mods/customer/postGetCustIdByCode.ts", "src/apis/sale/mods/customer/postGetGroupIdByCustId.ts", "src/apis/sale/mods/customer/save.ts", "src/apis/sale/mods/customer/saveAllShareCust.ts", "src/apis/sale/mods/customer/saveCustInter.ts", "src/apis/sale/mods/customer/saveCustMaterial.ts", "src/apis/sale/mods/customer/saveShareCust.ts", "src/apis/sale/mods/customer/sel.ts", "src/apis/sale/mods/customer/selCustAtt.ts", "src/apis/sale/mods/customer/selCustMaterialList.ts", "src/apis/sale/mods/customer/selMaterial.ts", "src/apis/sale/mods/customer/selProjectMembers.ts", "src/apis/sale/mods/customer/selShareCust.ts", "src/apis/sale/mods/customer/selShareSvcReport.ts", "src/apis/sale/mods/customer/selSvcReport.ts", "src/apis/sale/mods/customer/setWageCalculateType.ts", "src/apis/sale/mods/customer/updateEosUser.ts", "src/apis/sale/mods/customer/uptInternalCust.ts", "src/apis/sale/mods/customer/uptSvcReportPubulish.ts", "src/apis/sale/mods/customerInvoiceExp/addCustomerInvoiceExp.ts", "src/apis/sale/mods/customerInvoiceExp/index.ts", "src/apis/sale/mods/customerInvoiceExp/selInvoiceExp.ts", "src/apis/sale/mods/customerInvoiceExp/selInvoiceRoute.ts", "src/apis/sale/mods/customerInvoiceExp/selInvoiceRouteEos.ts", "src/apis/sale/mods/customerPayer/defaultCustomerPayerSJ.ts", "src/apis/sale/mods/customerPayer/delCustomerPayerSJList.ts", "src/apis/sale/mods/customerPayer/getCustPayerDropdownList.ts", "src/apis/sale/mods/customerPayer/index.ts", "src/apis/sale/mods/customerPayer/queryCustPayerHisList.ts", "src/apis/sale/mods/customerPayer/queryCustPayerList.ts", "src/apis/sale/mods/customerPayer/queryCustPayerSalaryHisList.ts", "src/apis/sale/mods/customerPayer/queryCustPayerSalaryList.ts", "src/apis/sale/mods/customerPayer/queryCustPayerSalaryTempList.ts", "src/apis/sale/mods/customerPayer/queryCustPayerTempList.ts", "src/apis/sale/mods/customerPayer/saveCustPayerSalaryTempList.ts", "src/apis/sale/mods/customerPayer/saveCustPayerTempList.ts", "src/apis/sale/mods/customerPayer/saveCustomerPayerInterList.ts", "src/apis/sale/mods/customerPayer/saveCustomerPayerSJList.ts", "src/apis/sale/mods/customerPayer/selCustomerPayerSJ.ts", "src/apis/sale/mods/customerPayer/uptApproveCustPayerSalaryTemp.ts", "src/apis/sale/mods/customerPayer/uptApproveCustPayerTemp.ts", "src/apis/sale/mods/customerPayer/uptCustPayerInfo.ts", "src/apis/sale/mods/customerPayer/uptCustPayerSalaryTempReject.ts", "src/apis/sale/mods/customerPayer/uptCustPayerSalaryTempToApprove.ts", "src/apis/sale/mods/customerPayer/uptCustPayerSync.ts", "src/apis/sale/mods/customerPayer/uptCustPayerTempReject.ts", "src/apis/sale/mods/customerPayer/uptCustPayerTempToApprove.ts", "src/apis/sale/mods/customerPayer/uptCustomerPayerSJList.ts", "src/apis/sale/mods/customerPayrollRecord/checkOnlyValidCustomer.ts", "src/apis/sale/mods/customerPayrollRecord/delCustomerRecord.ts", "src/apis/sale/mods/customerPayrollRecord/getCustomer.ts", "src/apis/sale/mods/customerPayrollRecord/index.ts", "src/apis/sale/mods/customerPayrollRecord/save.ts", "src/apis/sale/mods/customerPayrollRecord/sel.ts", "src/apis/sale/mods/customerPayrollRecord/setInValidCustomerRecord.ts", "src/apis/sale/mods/customerPayrollRecord/setValidCustomerRecord.ts", "src/apis/sale/mods/customerRecord/checkOnlyValidCustomer.ts", "src/apis/sale/mods/customerRecord/delCustomerRecord.ts", "src/apis/sale/mods/customerRecord/delCustomerRecordNoCheck.ts", "src/apis/sale/mods/customerRecord/getCustomer.ts", "src/apis/sale/mods/customerRecord/index.ts", "src/apis/sale/mods/customerRecord/save.ts", "src/apis/sale/mods/customerRecord/sel.ts", "src/apis/sale/mods/customerRecord/selNoCheck.ts", "src/apis/sale/mods/customerRecord/setInValidCustomerRecord.ts", "src/apis/sale/mods/customerRecord/setInValidCustomerRecordNoCheck.ts", "src/apis/sale/mods/customerRecord/setValidCustomerRecord.ts", "src/apis/sale/mods/customerRecord/setValidCustomerRecordNoCheck.ts", "src/apis/sale/mods/groupCompany/delGroupCompany.ts", "src/apis/sale/mods/groupCompany/exportCompany.ts", "src/apis/sale/mods/groupCompany/exportCust.ts", "src/apis/sale/mods/groupCompany/getAllEmployee.ts", "src/apis/sale/mods/groupCompany/getCustomer.ts", "src/apis/sale/mods/groupCompany/getCustomerByGroupId.ts", "src/apis/sale/mods/groupCompany/getGroupCompany.ts", "src/apis/sale/mods/groupCompany/index.ts", "src/apis/sale/mods/groupCompany/insertGroupCompany.ts", "src/apis/sale/mods/groupCompany/postGetAllEmployee.ts", "src/apis/sale/mods/groupCompany/postGetCustomerByGroupId.ts", "src/apis/sale/mods/groupCompany/saveCustomer.ts", "src/apis/sale/mods/index.ts", "src/apis/sale/mods/marketActivityInfo/delMarketActivityInfo.ts", "src/apis/sale/mods/marketActivityInfo/index.ts", "src/apis/sale/mods/marketActivityInfo/queryMarketActivityInfo.ts", "src/apis/sale/mods/marketActivityInfo/saveMarketActivityInfo.ts", "src/apis/sale/mods/marketActivityInfo/updateStatus.ts", "src/apis/sale/mods/productManage/checkNotReferencedProviderById.ts", "src/apis/sale/mods/productManage/delInsuranceProvider.ts", "src/apis/sale/mods/productManage/delInsuranceType.ts", "src/apis/sale/mods/productManage/delProduct.ts", "src/apis/sale/mods/productManage/delProductInterList.ts", "src/apis/sale/mods/productManage/delQuotationTemplateAreas.ts", "src/apis/sale/mods/productManage/delQuotationTemplateInfect.ts", "src/apis/sale/mods/productManage/delSubProduct.ts", "src/apis/sale/mods/productManage/getAllDropDownList.ts", "src/apis/sale/mods/productManage/getInsuranceProvider.ts", "src/apis/sale/mods/productManage/getInsuranceType.ts", "src/apis/sale/mods/productManage/getProductDropdownListBySSGoup.ts", "src/apis/sale/mods/productManage/getProductDropdownListBySSGoupRe.ts", "src/apis/sale/mods/productManage/getProductInQuotation.ts", "src/apis/sale/mods/productManage/getProductInQuotationTemplate.ts", "src/apis/sale/mods/productManage/getProductInterList.ts", "src/apis/sale/mods/productManage/getQuotationTemplateAreas.ts", "src/apis/sale/mods/productManage/getQuotationTemplateInfect.ts", "src/apis/sale/mods/productManage/getSubProduct.ts", "src/apis/sale/mods/productManage/index.ts", "src/apis/sale/mods/productManage/initData.ts", "src/apis/sale/mods/productManage/insert.ts", "src/apis/sale/mods/productManage/insertInsuranceProvider.ts", "src/apis/sale/mods/productManage/insertProductData.ts", "src/apis/sale/mods/productManage/insertSubProduct.ts", "src/apis/sale/mods/productManage/invalidInsuranceType.ts", "src/apis/sale/mods/productManage/invalidProduct.ts", "src/apis/sale/mods/productManage/invalidSubProduct.ts", "src/apis/sale/mods/productManage/postGetInsuranceProvider.ts", "src/apis/sale/mods/productManage/postGetInsuranceType.ts", "src/apis/sale/mods/productManage/postGetSubProduct.ts", "src/apis/sale/mods/productManage/postInitData.ts", "src/apis/sale/mods/productManage/postQueryProduct.ts", "src/apis/sale/mods/productManage/publishInsuranceType.ts", "src/apis/sale/mods/productManage/publishProduct.ts", "src/apis/sale/mods/productManage/publishSubProduct.ts", "src/apis/sale/mods/productManage/queryInsuranceTypeById.ts", "src/apis/sale/mods/productManage/queryProduct.ts", "src/apis/sale/mods/productManage/queryProductByQuotationId.ts", "src/apis/sale/mods/productManage/queryProductDropdownList.ts", "src/apis/sale/mods/productManage/saveInsuranceType.ts", "src/apis/sale/mods/productManage/saveProductInterList.ts", "src/apis/sale/mods/productManage/saveQuotationTemplateAreas.ts", "src/apis/sale/mods/productManage/saveQuotationTemplateInfect.ts", "src/apis/sale/mods/productManage/toDownLoad.ts", "src/apis/sale/mods/productManage/update.ts", "src/apis/sale/mods/productManage/updateProductData.ts", "src/apis/sale/mods/productTypeManage/deleteDepart.ts", "src/apis/sale/mods/productTypeManage/getProductTypeDTOList.ts", "src/apis/sale/mods/productTypeManage/index.ts", "src/apis/sale/mods/productTypeManage/postQueryProductType.ts", "src/apis/sale/mods/productTypeManage/postSaveProductType.ts", "src/apis/sale/mods/productTypeManage/queryRepeatRecord.ts", "src/apis/sale/mods/productTypeManage/queryTypeInProductRecord.ts", "src/apis/sale/mods/quotation/abandonQuotation.ts", "src/apis/sale/mods/quotation/abandonQuotationGroup.ts", "src/apis/sale/mods/quotation/approveQuotation.ts", "src/apis/sale/mods/quotation/approveQuotationEx.ts", "src/apis/sale/mods/quotation/back.ts", "src/apis/sale/mods/quotation/createQuotation.ts", "src/apis/sale/mods/quotation/createQuotationGroup.ts", "src/apis/sale/mods/quotation/delQuotation.ts", "src/apis/sale/mods/quotation/delQuotationGroup.ts", "src/apis/sale/mods/quotation/deleteBpoProjectRule.ts", "src/apis/sale/mods/quotation/doApprove.ts", "src/apis/sale/mods/quotation/effectQuotation.ts", "src/apis/sale/mods/quotation/effectQuotationGroup.ts", "src/apis/sale/mods/quotation/getApproveStatusEx.ts", "src/apis/sale/mods/quotation/getCurrentSales.ts", "src/apis/sale/mods/quotation/getDuplicateBpoProjectRuleCount.ts", "src/apis/sale/mods/quotation/getQuotation.ts", "src/apis/sale/mods/quotation/getQuotationCrm.ts", "src/apis/sale/mods/quotation/getQuotationData.ts", "src/apis/sale/mods/quotation/getQuotationDropdownList.ts", "src/apis/sale/mods/quotation/getQuotationEx.ts", "src/apis/sale/mods/quotation/getQuotationGroup.ts", "src/apis/sale/mods/quotation/getQuotationGroupData.ts", "src/apis/sale/mods/quotation/getQuotationTemplEx.ts", "src/apis/sale/mods/quotation/getValidOuotation.ts", "src/apis/sale/mods/quotation/index.ts", "src/apis/sale/mods/quotation/insertBpoProjectRule.ts", "src/apis/sale/mods/quotation/queryBpoProjectRule.ts", "src/apis/sale/mods/quotation/queryQuoteLadderList.ts", "src/apis/sale/mods/quotation/revalidationQuotation.ts", "src/apis/sale/mods/quotation/selectQuotationByPrimaryKey.ts", "src/apis/sale/mods/quotation/selectQuotationItemDetailEx.ts", "src/apis/sale/mods/quotation/selectUser.ts", "src/apis/sale/mods/quotation/terminal.ts", "src/apis/sale/mods/quotation/toDownLoad.ts", "src/apis/sale/mods/quotation/updateApproveQuotation.ts", "src/apis/sale/mods/quotation/updateBpoProjectRule.ts", "src/apis/sale/mods/quotation/updateQuotation.ts", "src/apis/sale/mods/quotation/updateQuotationGroup.ts", "src/apis/sale/mods/quotationTempl/del.ts", "src/apis/sale/mods/quotationTempl/delQuotationTemplTrdCost.ts", "src/apis/sale/mods/quotationTempl/delQuotationTemplateAreas.ts", "src/apis/sale/mods/quotationTempl/delQuotationTemplateInfect.ts", "src/apis/sale/mods/quotationTempl/delSocialSecurityTempl.ts", "src/apis/sale/mods/quotationTempl/getAllDropDownList.ts", "src/apis/sale/mods/quotationTempl/getBatchInfo.ts", "src/apis/sale/mods/quotationTempl/getCustomer.ts", "src/apis/sale/mods/quotationTempl/getOuotationTemplateById.ts", "src/apis/sale/mods/quotationTempl/getProductTypeDropDownList.ts", "src/apis/sale/mods/quotationTempl/getProviderDropDownList.ts", "src/apis/sale/mods/quotationTempl/getQuoTemplSsecrityList.ts", "src/apis/sale/mods/quotationTempl/getQuoTemplSsecrityListWithCustId.ts", "src/apis/sale/mods/quotationTempl/getQuoTemplateInsuranceDataList.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplCFG.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplInsuranceTypeList.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplItem.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplItemList.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplTrdCost.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplate.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplate1700ById.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplateAreas.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplateById.ts", "src/apis/sale/mods/quotationTempl/getQuotationTemplateInfect.ts", "src/apis/sale/mods/quotationTempl/getSocialSecurityTemplProduct.ts", "src/apis/sale/mods/quotationTempl/index.ts", "src/apis/sale/mods/quotationTempl/insert.ts", "src/apis/sale/mods/quotationTempl/insertQuotationTempl.ts", "src/apis/sale/mods/quotationTempl/insertQuotationTempl1700.ts", "src/apis/sale/mods/quotationTempl/invalidQuotationTempl.ts", "src/apis/sale/mods/quotationTempl/publishQuotationTempl.ts", "src/apis/sale/mods/quotationTempl/saveQuotationProductLine.ts", "src/apis/sale/mods/quotationTempl/saveQuotationTemplTrdCost.ts", "src/apis/sale/mods/quotationTempl/saveQuotationTemplateAreas.ts", "src/apis/sale/mods/quotationTempl/saveQuotationTemplateCFG.ts", "src/apis/sale/mods/quotationTempl/saveQuotationTemplateInfect.ts", "src/apis/sale/mods/quotationTempl/saveQuotationTemplateInsuranceType.ts", "src/apis/sale/mods/quotationTempl/saveQuotationTemplateItem.ts", "src/apis/sale/mods/quotationTempl/saveSsecurityTempl.ts", "src/apis/sale/mods/quotationTempl/update.ts", "src/apis/sale/mods/quotationTempl/updateQuotationTempl.ts", "src/apis/sale/mods/quotationTempl/updateQuotationTempl1700.ts", "src/apis/sale/mods/withholdAgentCustNoCheck/addWithholdAgentCustNoCheck.ts", "src/apis/sale/mods/withholdAgentCustNoCheck/deleteWithholdAgentCustNoCheck.ts", "src/apis/sale/mods/withholdAgentCustNoCheck/exportCust.ts", "src/apis/sale/mods/withholdAgentCustNoCheck/index.ts", "src/apis/sale/mods/withholdAgentCustNoCheck/listWithholdAgentCustNoCheck.ts", "src/apis/sale/mods/workPlaceHealth/index.ts", "src/apis/sale/mods/workPlaceHealth/selectList.ts", "src/apis/sale/mods/workPlaceHealth/toDownLoad.ts", "src/apis/supplier/api.d.ts", "src/apis/supplier/baseClass.ts", "src/apis/supplier/index.ts", "src/apis/supplier/mods/index.ts", "src/apis/supplier/mods/supplierTask/addSupplierTask.ts", "src/apis/supplier/mods/supplierTask/backWageSupplierPay.ts", "src/apis/supplier/mods/supplierTask/commitWageSupplierPay.ts", "src/apis/supplier/mods/supplierTask/delSupplierTask.ts", "src/apis/supplier/mods/supplierTask/doApproveWageSupplierPay.ts", "src/apis/supplier/mods/supplierTask/expEbankTemplate.ts", "src/apis/supplier/mods/supplierTask/expTaskHis.ts", "src/apis/supplier/mods/supplierTask/expWageSupplierNonPayTask.ts", "src/apis/supplier/mods/supplierTask/getSupplierTaskPage.ts", "src/apis/supplier/mods/supplierTask/index.ts", "src/apis/supplier/mods/supplierTask/postQueryWageSupplierNonPay.ts", "src/apis/supplier/mods/supplierTask/postQueryWageSupplierNonPayTask.ts", "src/apis/supplier/mods/supplierTask/queryWageSupplierDetail.ts", "src/apis/supplier/mods/supplierTask/queryWageSupplierNonPay.ts", "src/apis/supplier/mods/supplierTask/queryWageSupplierNonPayTask.ts", "src/apis/supplier/mods/supplierTask/terminalWageSupplierPay.ts", "src/apis/supplier/mods/supplierTask/updateWageSupplierDto.ts", "src/apis/supplier/mods/supplierTask/updateWageSupplierPayBill.ts", "src/apis/supplier/mods/supplierTask/updateWgSupplierPayStatusById.ts", "src/apis/sysmanage/api.d.ts", "src/apis/sysmanage/baseClass.ts", "src/apis/sysmanage/index.ts", "src/apis/sysmanage/mods/clientCompany/addBatchClientCompany.ts", "src/apis/sysmanage/mods/clientCompany/count.ts", "src/apis/sysmanage/mods/clientCompany/del.ts", "src/apis/sysmanage/mods/clientCompany/index.ts", "src/apis/sysmanage/mods/clientCompany/resetPwd.ts", "src/apis/sysmanage/mods/clientCompany/save.ts", "src/apis/sysmanage/mods/clientCompany/sel.ts", "src/apis/sysmanage/mods/clientCompany/selCustomClientCompany.ts", "src/apis/sysmanage/mods/clientCompany/toDownLoad.ts", "src/apis/sysmanage/mods/index.ts", "src/apis/sysmanage/mods/messagePublish/getAllMainList.ts", "src/apis/sysmanage/mods/messagePublish/getFaqList.ts", "src/apis/sysmanage/mods/messagePublish/getMessagePublishList.ts", "src/apis/sysmanage/mods/messagePublish/getNewestUpdateList.ts", "src/apis/sysmanage/mods/messagePublish/getNewestUpdateSubList.ts", "src/apis/sysmanage/mods/messagePublish/getRecipient.ts", "src/apis/sysmanage/mods/messagePublish/index.ts", "src/apis/sysmanage/mods/messagePublish/queryRoleList.ts", "src/apis/sysmanage/mods/messagePublish/queryRoleListEx.ts", "src/apis/sysmanage/mods/messagePublish/saveFaq.ts", "src/apis/sysmanage/mods/messagePublish/saveMessagePublish.ts", "src/apis/sysmanage/mods/messagePublish/saveNewestUpdateList.ts", "src/apis/sysmanage/mods/messagePublish/saveNewestUpdateSub.ts", "src/apis/sysmanage/mods/messagePublish/sendRemind.ts", "src/apis/sysmanage/mods/messagePublish/updateFaqList.ts", "src/apis/sysmanage/mods/messagePublish/updateMessagePublish.ts", "src/apis/sysmanage/mods/messagePublish/updateMessagePublishList.ts", "src/apis/sysmanage/mods/messagePublish/updateNewestUpdateList.ts", "src/apis/sysmanage/mods/messagePublish/updateNewestUpdateSub.ts", "src/apis/sysmanage/mods/messagePublish/updateNewestUpdateSubList.ts", "src/apis/sysmanage/mods/olTask/addTask.ts", "src/apis/sysmanage/mods/olTask/getTask.ts", "src/apis/sysmanage/mods/olTask/index.ts", "src/apis/sysmanage/mods/olTask/taskFinish.ts", "src/apis/sysmanage/mods/olTask/taskStop.ts", "src/apis/sysmanage/mods/olTask/toDownLoad.ts", "src/apis/sysmanage/mods/olTask/updateTask.ts", "src/apis/sysmanage/mods/privacyVersion/getPageOfProxy.ts", "src/apis/sysmanage/mods/privacyVersion/index.ts", "src/apis/sysmanage/mods/privacyVersion/insertPrivacy.ts", "src/apis/sysmanage/mods/privacyVersion/updatePrivacy.ts", "src/apis/sysmanage/mods/proxySetting/deleteMimicSetting.ts", "src/apis/sysmanage/mods/proxySetting/deleteProxySetting.ts", "src/apis/sysmanage/mods/proxySetting/getAllAccountList.ts", "src/apis/sysmanage/mods/proxySetting/getPageOfMimic.ts", "src/apis/sysmanage/mods/proxySetting/getPageOfProxy.ts", "src/apis/sysmanage/mods/proxySetting/index.ts", "src/apis/sysmanage/mods/proxySetting/insertAndUpdateMimic.ts", "src/apis/sysmanage/mods/proxySetting/insertProxySetting.ts", "src/apis/sysmanage/mods/proxySetting/isRepeatProxyRecord.ts", "src/apis/sysmanage/mods/proxySetting/postDeleteProxySetting.ts", "src/apis/sysmanage/mods/proxySetting/queryRepeatRecordMimic.ts", "src/apis/sysmanage/mods/proxySetting/updateProxySetting.ts", "src/apis/sysmanage/mods/support/createCollectionProposal.ts", "src/apis/sysmanage/mods/support/getData.ts", "src/apis/sysmanage/mods/support/getSupportDate.ts", "src/apis/sysmanage/mods/support/getSupportDateById.ts", "src/apis/sysmanage/mods/support/getSupportDateBySupportId.ts", "src/apis/sysmanage/mods/support/getSupportUserDate.ts", "src/apis/sysmanage/mods/support/getsqlExecutant.ts", "src/apis/sysmanage/mods/support/index.ts", "src/apis/sysmanage/mods/support/insertDataByEntity.ts", "src/apis/sysmanage/mods/support/postGetSupportUserDate.ts", "src/apis/sysmanage/mods/support/queryCollectionProposal.ts", "src/apis/sysmanage/mods/support/saveDataByEntity.ts", "src/apis/sysmanage/mods/support/supportBack.ts", "src/apis/sysmanage/mods/support/supportFinsh.ts", "src/apis/sysmanage/mods/support/toDownLoad.ts", "src/apis/sysmanage/mods/support/toolsDataByEntity.ts", "src/apis/sysmanage/mods/support/updateCollectionProposal.ts", "src/apis/sysmanage/mods/sysCompanyContacts/batchDelete.ts", "src/apis/sysmanage/mods/sysCompanyContacts/index.ts", "src/apis/sysmanage/mods/sysCompanyContacts/list.ts", "src/apis/sysmanage/mods/sysCompanyContacts/save.ts", "src/apis/sysmanage/mods/sysCompanyContacts/update.ts", "src/apis/sysmanage/mods/sysContacts/deleteSysContacts.ts", "src/apis/sysmanage/mods/sysContacts/exporting.ts", "src/apis/sysmanage/mods/sysContacts/index.ts", "src/apis/sysmanage/mods/sysContacts/postSelect.ts", "src/apis/sysmanage/mods/sysContacts/saveSysContacts.ts", "src/apis/sysmanage/mods/sysContacts/selectSysContacts.ts", "src/apis/sysmanage/mods/sysContacts/updateSysContacts.ts", "src/apis/sysmanage/mods/sysUserGroup/addUserGroupDetail.ts", "src/apis/sysmanage/mods/sysUserGroup/index.ts", "src/apis/sysmanage/mods/sysUserGroup/page.ts", "src/apis/sysmanage/mods/sysUserGroup/remove.ts", "src/apis/sysmanage/mods/sysUserGroup/updateUserGroupDetail.ts", "src/apis/sysmanage/mods/sysUserGroupDetail/index.ts", "src/apis/sysmanage/mods/sysUserGroupDetail/listGroupUser.ts", "src/apis/sysmanage/mods/sysUserGroupDetail/listUserGroupDetail.ts", "src/apis/sysmanage2/api.d.ts", "src/apis/sysmanage2/baseClass.ts", "src/apis/sysmanage2/index.ts", "src/apis/sysmanage2/mods/clientCompany/addBatchClientCompany.ts", "src/apis/sysmanage2/mods/clientCompany/count.ts", "src/apis/sysmanage2/mods/clientCompany/del.ts", "src/apis/sysmanage2/mods/clientCompany/index.ts", "src/apis/sysmanage2/mods/clientCompany/resetPwd.ts", "src/apis/sysmanage2/mods/clientCompany/save.ts", "src/apis/sysmanage2/mods/clientCompany/sel.ts", "src/apis/sysmanage2/mods/clientCompany/selCustomClientCompany.ts", "src/apis/sysmanage2/mods/clientCompany/toDownLoad.ts", "src/apis/sysmanage2/mods/index.ts", "src/apis/sysmanage2/mods/messagePublish/getAllMainList.ts", "src/apis/sysmanage2/mods/messagePublish/getFaqList.ts", "src/apis/sysmanage2/mods/messagePublish/getMessagePublishList.ts", "src/apis/sysmanage2/mods/messagePublish/getNewestUpdateList.ts", "src/apis/sysmanage2/mods/messagePublish/getNewestUpdateSubList.ts", "src/apis/sysmanage2/mods/messagePublish/getRecipient.ts", "src/apis/sysmanage2/mods/messagePublish/index.ts", "src/apis/sysmanage2/mods/messagePublish/queryRoleList.ts", "src/apis/sysmanage2/mods/messagePublish/queryRoleListEx.ts", "src/apis/sysmanage2/mods/messagePublish/saveFaq.ts", "src/apis/sysmanage2/mods/messagePublish/saveMessagePublish.ts", "src/apis/sysmanage2/mods/messagePublish/saveNewestUpdateList.ts", "src/apis/sysmanage2/mods/messagePublish/saveNewestUpdateSub.ts", "src/apis/sysmanage2/mods/messagePublish/sendRemind.ts", "src/apis/sysmanage2/mods/messagePublish/updateFaqList.ts", "src/apis/sysmanage2/mods/messagePublish/updateMessagePublish.ts", "src/apis/sysmanage2/mods/messagePublish/updateMessagePublishList.ts", "src/apis/sysmanage2/mods/messagePublish/updateNewestUpdateList.ts", "src/apis/sysmanage2/mods/messagePublish/updateNewestUpdateSub.ts", "src/apis/sysmanage2/mods/messagePublish/updateNewestUpdateSubList.ts", "src/apis/sysmanage2/mods/olTask/addTask.ts", "src/apis/sysmanage2/mods/olTask/getTask.ts", "src/apis/sysmanage2/mods/olTask/index.ts", "src/apis/sysmanage2/mods/olTask/taskFinish.ts", "src/apis/sysmanage2/mods/olTask/taskStop.ts", "src/apis/sysmanage2/mods/olTask/toDownLoad.ts", "src/apis/sysmanage2/mods/olTask/updateTask.ts", "src/apis/sysmanage2/mods/privacyVersion/getPageOfProxy.ts", "src/apis/sysmanage2/mods/privacyVersion/index.ts", "src/apis/sysmanage2/mods/privacyVersion/insertPrivacy.ts", "src/apis/sysmanage2/mods/privacyVersion/updatePrivacy.ts", "src/apis/sysmanage2/mods/proxySetting/deleteMimicSetting.ts", "src/apis/sysmanage2/mods/proxySetting/deleteProxySetting.ts", "src/apis/sysmanage2/mods/proxySetting/getAllAccountList.ts", "src/apis/sysmanage2/mods/proxySetting/getPageOfMimic.ts", "src/apis/sysmanage2/mods/proxySetting/getPageOfProxy.ts", "src/apis/sysmanage2/mods/proxySetting/index.ts", "src/apis/sysmanage2/mods/proxySetting/insertAndUpdateMimic.ts", "src/apis/sysmanage2/mods/proxySetting/insertProxySetting.ts", "src/apis/sysmanage2/mods/proxySetting/isRepeatProxyRecord.ts", "src/apis/sysmanage2/mods/proxySetting/postDeleteProxySetting.ts", "src/apis/sysmanage2/mods/proxySetting/queryRepeatRecordMimic.ts", "src/apis/sysmanage2/mods/proxySetting/updateProxySetting.ts", "src/apis/sysmanage2/mods/support/createCollectionProposal.ts", "src/apis/sysmanage2/mods/support/getData.ts", "src/apis/sysmanage2/mods/support/getSupportDate.ts", "src/apis/sysmanage2/mods/support/getSupportDateById.ts", "src/apis/sysmanage2/mods/support/getSupportDateBySupportId.ts", "src/apis/sysmanage2/mods/support/getSupportUserDate.ts", "src/apis/sysmanage2/mods/support/getsqlExecutant.ts", "src/apis/sysmanage2/mods/support/index.ts", "src/apis/sysmanage2/mods/support/insertDataByEntity.ts", "src/apis/sysmanage2/mods/support/postGetSupportUserDate.ts", "src/apis/sysmanage2/mods/support/queryCollectionProposal.ts", "src/apis/sysmanage2/mods/support/saveDataByEntity.ts", "src/apis/sysmanage2/mods/support/supportBack.ts", "src/apis/sysmanage2/mods/support/supportFinsh.ts", "src/apis/sysmanage2/mods/support/toDownLoad.ts", "src/apis/sysmanage2/mods/support/toolsDataByEntity.ts", "src/apis/sysmanage2/mods/support/updateCollectionProposal.ts", "src/apis/sysmanage2/mods/sysCompanyContacts/batchDelete.ts", "src/apis/sysmanage2/mods/sysCompanyContacts/index.ts", "src/apis/sysmanage2/mods/sysCompanyContacts/list.ts", "src/apis/sysmanage2/mods/sysCompanyContacts/save.ts", "src/apis/sysmanage2/mods/sysCompanyContacts/update.ts", "src/apis/sysmanage2/mods/sysContacts/deleteSysContacts.ts", "src/apis/sysmanage2/mods/sysContacts/exporting.ts", "src/apis/sysmanage2/mods/sysContacts/index.ts", "src/apis/sysmanage2/mods/sysContacts/postSelect.ts", "src/apis/sysmanage2/mods/sysContacts/saveSysContacts.ts", "src/apis/sysmanage2/mods/sysContacts/selectSysContacts.ts", "src/apis/sysmanage2/mods/sysContacts/updateSysContacts.ts", "src/apis/sysmanage2/mods/sysUserGroup/addUserGroupDetail.ts", "src/apis/sysmanage2/mods/sysUserGroup/index.ts", "src/apis/sysmanage2/mods/sysUserGroup/page.ts", "src/apis/sysmanage2/mods/sysUserGroup/remove.ts", "src/apis/sysmanage2/mods/sysUserGroup/updateUserGroupDetail.ts", "src/apis/sysmanage2/mods/sysUserGroupDetail/index.ts", "src/apis/sysmanage2/mods/sysUserGroupDetail/listGroupUser.ts", "src/apis/sysmanage2/mods/sysUserGroupDetail/listUserGroupDetail.ts", "src/apis/wechat/api.d.ts", "src/apis/wechat/baseClass.ts", "src/apis/wechat/index.ts", "src/apis/wechat/mods/index.ts", "src/apis/wechat/mods/wechatHiresep/checkCouldCreateEntry.ts", "src/apis/wechat/mods/wechatHiresep/checkCouldEditEntry.ts", "src/apis/wechat/mods/wechatHiresep/delImage.ts", "src/apis/wechat/mods/wechatHiresep/editHiresepMain.ts", "src/apis/wechat/mods/wechatHiresep/exportEntryTemplateByLowcode.ts", "src/apis/wechat/mods/wechatHiresep/fetchHosInfo.ts", "src/apis/wechat/mods/wechatHiresep/fetchUUID.ts", "src/apis/wechat/mods/wechatHiresep/getDynamicFileItem.ts", "src/apis/wechat/mods/wechatHiresep/getDynamicItem.ts", "src/apis/wechat/mods/wechatHiresep/getUniqueHiresepMain.ts", "src/apis/wechat/mods/wechatHiresep/index.ts", "src/apis/wechat/mods/wechatHiresep/submiteHiresepMain.ts", "src/apis/wechat/mods/wechatHiresep/uploadBatchEntry.ts", "src/apis/wechat/mods/wechatPhoto/allPhotos.ts", "src/apis/wechat/mods/wechatPhoto/index.ts", "src/apis/wechat/mods/wechatPhoto/uploadPhoto.ts", "src/apis/wechat/mods/wechatSepcailCity/fetchSocialCreditInfo.ts", "src/apis/wechat/mods/wechatSepcailCity/index.ts", "src/apis/welfaremanage/api.d.ts", "src/apis/welfaremanage/baseClass.ts", "src/apis/welfaremanage/index.ts", "src/apis/welfaremanage/mods/adjTask/adjTaskProduct.ts", "src/apis/welfaremanage/mods/adjTask/checkAdjList.ts", "src/apis/welfaremanage/mods/adjTask/delSsAdjTaskProductRatio.ts", "src/apis/welfaremanage/mods/adjTask/downloadCollectExcel.ts", "src/apis/welfaremanage/mods/adjTask/downloadEosCollectExcel.ts", "src/apis/welfaremanage/mods/adjTask/downloadExcel.ts", "src/apis/welfaremanage/mods/adjTask/downloadImpResult.ts", "src/apis/welfaremanage/mods/adjTask/expAdjEmailRecordList.ts", "src/apis/welfaremanage/mods/adjTask/exportFile.ts", "src/apis/welfaremanage/mods/adjTask/exportFileEmp.ts", "src/apis/welfaremanage/mods/adjTask/fillAdjTaskStartMonth.ts", "src/apis/welfaremanage/mods/adjTask/generateAdjList.ts", "src/apis/welfaremanage/mods/adjTask/generateUploadTemplate.ts", "src/apis/welfaremanage/mods/adjTask/getAdjTaskProductList.ts", "src/apis/welfaremanage/mods/adjTask/getAdjTaskProductTempList.ts", "src/apis/welfaremanage/mods/adjTask/getCustEmpCount.ts", "src/apis/welfaremanage/mods/adjTask/getDetailEmpList.ts", "src/apis/welfaremanage/mods/adjTask/getEmpCheckCount.ts", "src/apis/welfaremanage/mods/adjTask/index.ts", "src/apis/welfaremanage/mods/adjTask/passAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/postCheckAdjList.ts", "src/apis/welfaremanage/mods/adjTask/postDownloadCollectExcel.ts", "src/apis/welfaremanage/mods/adjTask/postDownloadEosCollectExcel.ts", "src/apis/welfaremanage/mods/adjTask/postDownloadExcel.ts", "src/apis/welfaremanage/mods/adjTask/postDownloadImpResult.ts", "src/apis/welfaremanage/mods/adjTask/postGenerateAdjList.ts", "src/apis/welfaremanage/mods/adjTask/postGenerateUploadTemplate.ts", "src/apis/welfaremanage/mods/adjTask/postGetCustEmpCount.ts", "src/apis/welfaremanage/mods/adjTask/postGetDetailEmpList.ts", "src/apis/welfaremanage/mods/adjTask/postGetEmpCheckCount.ts", "src/apis/welfaremanage/mods/adjTask/postLockAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/postQueryAdjTaskRangeList.ts", "src/apis/welfaremanage/mods/adjTask/postQueryPfProductInfo.ts", "src/apis/welfaremanage/mods/adjTask/postReportDeclareList.ts", "src/apis/welfaremanage/mods/adjTask/postReportDetailEmpList.ts", "src/apis/welfaremanage/mods/adjTask/postReportOutFile.ts", "src/apis/welfaremanage/mods/adjTask/postReportPolicyWord.ts", "src/apis/welfaremanage/mods/adjTask/postSubmitApproveAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/postSubmitApproveAdjTask1.ts", "src/apis/welfaremanage/mods/adjTask/postSubmitApproveAdjTaskPolicy.ts", "src/apis/welfaremanage/mods/adjTask/postUnlockAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/postUploadAdjList.ts", "src/apis/welfaremanage/mods/adjTask/postUploadCheckList.ts", "src/apis/welfaremanage/mods/adjTask/postUploadCollect.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjEmailRecordList.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjTaskApprovalList.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjTaskEmpForFinish.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjTaskEmpList.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjTaskList.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjTaskProductList.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjTaskProductListForModify.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjTaskRangeList.ts", "src/apis/welfaremanage/mods/adjTask/queryAdjTaskTemp.ts", "src/apis/welfaremanage/mods/adjTask/queryCollectAdjTaskList.ts", "src/apis/welfaremanage/mods/adjTask/queryColumnName.ts", "src/apis/welfaremanage/mods/adjTask/queryPfProductInfo.ts", "src/apis/welfaremanage/mods/adjTask/queryProductRatio.ts", "src/apis/welfaremanage/mods/adjTask/queryProductRatioAdjust.ts", "src/apis/welfaremanage/mods/adjTask/queryProductRatioAdjustTemp.ts", "src/apis/welfaremanage/mods/adjTask/queryProductRatioR.ts", "src/apis/welfaremanage/mods/adjTask/queryProductRatioRTemp.ts", "src/apis/welfaremanage/mods/adjTask/queryProductRatioU.ts", "src/apis/welfaremanage/mods/adjTask/queryProductRatioUTemp.ts", "src/apis/welfaremanage/mods/adjTask/querySsPfExist.ts", "src/apis/welfaremanage/mods/adjTask/rejectAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/reportDeclareList.ts", "src/apis/welfaremanage/mods/adjTask/reportDetailEmpList.ts", "src/apis/welfaremanage/mods/adjTask/reportOutFile.ts", "src/apis/welfaremanage/mods/adjTask/reportPolicyWord.ts", "src/apis/welfaremanage/mods/adjTask/saveAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/saveSsAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/saveSsAdjTaskProduct.ts", "src/apis/welfaremanage/mods/adjTask/saveSsAdjTaskProduct1.ts", "src/apis/welfaremanage/mods/adjTask/saveSsAdjTaskProductRatio.ts", "src/apis/welfaremanage/mods/adjTask/selImpResult.ts", "src/apis/welfaremanage/mods/adjTask/sendAdjMailDetail.ts", "src/apis/welfaremanage/mods/adjTask/terminateAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/terminatePassAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/terminateRejectAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/toDownLoad.ts", "src/apis/welfaremanage/mods/adjTask/updateAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/updateCollectEmp.ts", "src/apis/welfaremanage/mods/adjTask/updateOrderAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/updateSocAdjTask.ts", "src/apis/welfaremanage/mods/adjTask/uploadAdjList.ts", "src/apis/welfaremanage/mods/adjTask/uploadCheckList.ts", "src/apis/welfaremanage/mods/adjTask/uploadCollect.ts", "src/apis/welfaremanage/mods/ebmtransact/aPrintReceipt.ts", "src/apis/welfaremanage/mods/ebmtransact/backTransactInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/delHp.ts", "src/apis/welfaremanage/mods/ebmtransact/deleteTransactInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/doTransactInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/doTransactInfo60tO70.ts", "src/apis/welfaremanage/mods/ebmtransact/exportExcel.ts", "src/apis/welfaremanage/mods/ebmtransact/getBusTypeDropdownList.ts", "src/apis/welfaremanage/mods/ebmtransact/getBusinessSubTypeBytransactId.ts", "src/apis/welfaremanage/mods/ebmtransact/getBusinessSubTypeList.ts", "src/apis/welfaremanage/mods/ebmtransact/getBzMateralsByTransactId.ts", "src/apis/welfaremanage/mods/ebmtransact/getCmpAppServerUrl.ts", "src/apis/welfaremanage/mods/ebmtransact/getData.ts", "src/apis/welfaremanage/mods/ebmtransact/getEmpBankInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/getEmpInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/getMateralsByTransactId.ts", "src/apis/welfaremanage/mods/ebmtransact/getMateralsByTransactId2.ts", "src/apis/welfaremanage/mods/ebmtransact/getMateralsList.ts", "src/apis/welfaremanage/mods/ebmtransact/getSsProcessInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/getTransactDateByBusId.ts", "src/apis/welfaremanage/mods/ebmtransact/getTransactLogByBusId.ts", "src/apis/welfaremanage/mods/ebmtransact/getTransacterDropdownList.ts", "src/apis/welfaremanage/mods/ebmtransact/index.ts", "src/apis/welfaremanage/mods/ebmtransact/insertDataByEntity.ts", "src/apis/welfaremanage/mods/ebmtransact/insertEbmBusinessSubTypeHis.ts", "src/apis/welfaremanage/mods/ebmtransact/insertEbmMateralsHis.ts", "src/apis/welfaremanage/mods/ebmtransact/insertTransactInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/insertTransactLog.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetBusTypeDropdownList.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetBusinessSubTypeBytransactId.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetBusinessSubTypeList.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetBzMateralsByTransactId.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetCmpAppServerUrl.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetEmpBankInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetMateralsByTransactId.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetMateralsByTransactId2.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetMateralsList.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetTransactDateByBusId.ts", "src/apis/welfaremanage/mods/ebmtransact/postGetTransactLogByBusId.ts", "src/apis/welfaremanage/mods/ebmtransact/selHp.ts", "src/apis/welfaremanage/mods/ebmtransact/stopTransactInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/updateInfo.ts", "src/apis/welfaremanage/mods/ebmtransact/updateTransactInfo.ts", "src/apis/welfaremanage/mods/fileManagement/deleteEmpFileById.ts", "src/apis/welfaremanage/mods/fileManagement/exporting.ts", "src/apis/welfaremanage/mods/fileManagement/index.ts", "src/apis/welfaremanage/mods/fileManagement/insertEmpFile.ts", "src/apis/welfaremanage/mods/fileManagement/queryEmpFileCountByEmpId.ts", "src/apis/welfaremanage/mods/fileManagement/queryEmpFileDetailList.ts", "src/apis/welfaremanage/mods/fileManagement/queryEmpFileList.ts", "src/apis/welfaremanage/mods/fileManagement/queryEmpForAddList.ts", "src/apis/welfaremanage/mods/fileManagement/queryEmpOrderByEmpId.ts", "src/apis/welfaremanage/mods/fileManagement/queryEmployeeFileReport.ts", "src/apis/welfaremanage/mods/fileManagement/updateEmpFile.ts", "src/apis/welfaremanage/mods/index.ts", "src/apis/welfaremanage/mods/laborContract/approved.ts", "src/apis/welfaremanage/mods/laborContract/batchLaborFile.ts", "src/apis/welfaremanage/mods/laborContract/bigEosReCancel.ts", "src/apis/welfaremanage/mods/laborContract/bigEosReCommit.ts", "src/apis/welfaremanage/mods/laborContract/commitLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/dayComparePrecise.ts", "src/apis/welfaremanage/mods/laborContract/delLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/doProcessEosAssigneeCsRemind.ts", "src/apis/welfaremanage/mods/laborContract/downloadEleContractFile.ts", "src/apis/welfaremanage/mods/laborContract/eleContractSend.ts", "src/apis/welfaremanage/mods/laborContract/finish.ts", "src/apis/welfaremanage/mods/laborContract/generateUploadTemplate.ts", "src/apis/welfaremanage/mods/laborContract/getAllDropDownList.ts", "src/apis/welfaremanage/mods/laborContract/getAutoColumnInfo.ts", "src/apis/welfaremanage/mods/laborContract/getBatchInfo.ts", "src/apis/welfaremanage/mods/laborContract/getBigEosLaborContractApproveHistoryList.ts", "src/apis/welfaremanage/mods/laborContract/getBigEosLaborContractApproveList.ts", "src/apis/welfaremanage/mods/laborContract/getBigEosLaborContractList.ts", "src/apis/welfaremanage/mods/laborContract/getBigEosReLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/getDropDownList.ts", "src/apis/welfaremanage/mods/laborContract/getEleContractInfo.ts", "src/apis/welfaremanage/mods/laborContract/getEosAssigneeCsRemindDataList.ts", "src/apis/welfaremanage/mods/laborContract/getFileListByEleContractId.ts", "src/apis/welfaremanage/mods/laborContract/getHsLaborctEosRenew.ts", "src/apis/welfaremanage/mods/laborContract/getLabQysHuazhuSyn.ts", "src/apis/welfaremanage/mods/laborContract/getLabQysHuazhuSynLog.ts", "src/apis/welfaremanage/mods/laborContract/getLaborBatchFile.ts", "src/apis/welfaremanage/mods/laborContract/getLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/getLaborContractByEmpAddId.ts", "src/apis/welfaremanage/mods/laborContract/getLaborContractByEosReLaborContractId.ts", "src/apis/welfaremanage/mods/laborContract/getLaborContractBylaborContractId.ts", "src/apis/welfaremanage/mods/laborContract/getLaborContractExp.ts", "src/apis/welfaremanage/mods/laborContract/getLaborContractHistory.ts", "src/apis/welfaremanage/mods/laborContract/getLaborContractUploadFileById.ts", "src/apis/welfaremanage/mods/laborContract/getLcExpireRenewalDataList.ts", "src/apis/welfaremanage/mods/laborContract/getLcUploadFileDataList.ts", "src/apis/welfaremanage/mods/laborContract/getPreSignUrl.ts", "src/apis/welfaremanage/mods/laborContract/getSelectEmployee.ts", "src/apis/welfaremanage/mods/laborContract/getSignUrl.ts", "src/apis/welfaremanage/mods/laborContract/getViewUrl.ts", "src/apis/welfaremanage/mods/laborContract/getWorkitemIdByInsId.ts", "src/apis/welfaremanage/mods/laborContract/haveTosignedLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/importDataProcess.ts", "src/apis/welfaremanage/mods/laborContract/index.ts", "src/apis/welfaremanage/mods/laborContract/insertLaborContractHistory.ts", "src/apis/welfaremanage/mods/laborContract/insertLaborContractToQys.ts", "src/apis/welfaremanage/mods/laborContract/postGenerateUploadTemplate.ts", "src/apis/welfaremanage/mods/laborContract/queryCorporationBySubcontractId.ts", "src/apis/welfaremanage/mods/laborContract/querySignProcess.ts", "src/apis/welfaremanage/mods/laborContract/querySignProcessBySubcontractId.ts", "src/apis/welfaremanage/mods/laborContract/reEleContractSend.ts", "src/apis/welfaremanage/mods/laborContract/retrieveEosLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/retrieveEosLaborContractCheck.ts", "src/apis/welfaremanage/mods/laborContract/retrieveLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/saveLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/selImpResult.ts", "src/apis/welfaremanage/mods/laborContract/sendSmsForAppScene.ts", "src/apis/welfaremanage/mods/laborContract/setManualHireCall.ts", "src/apis/welfaremanage/mods/laborContract/setSameSignDt.ts", "src/apis/welfaremanage/mods/laborContract/terminal.ts", "src/apis/welfaremanage/mods/laborContract/terminateLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/toDownLoad.ts", "src/apis/welfaremanage/mods/laborContract/updateEleContract.ts", "src/apis/welfaremanage/mods/laborContract/updateHsLaborctEosRenew.ts", "src/apis/welfaremanage/mods/laborContract/updateHsLaborctEosRenewSigning.ts", "src/apis/welfaremanage/mods/laborContract/updateReturnEleContract.ts", "src/apis/welfaremanage/mods/laborContract/uptLaborContract.ts", "src/apis/welfaremanage/mods/laborContract/uptReturn.ts", "src/apis/welfaremanage/mods/laborContractFileBatch/UploadFile.ts", "src/apis/welfaremanage/mods/laborContractFileBatch/batchUploadFile.ts", "src/apis/welfaremanage/mods/laborContractFileBatch/fileBatchReport.ts", "src/apis/welfaremanage/mods/laborContractFileBatch/getFileListByEleContractId.ts", "src/apis/welfaremanage/mods/laborContractFileBatch/index.ts", "src/apis/welfaremanage/mods/laborContractFileBatch/insertHsLaborFileBatch.ts", "src/apis/welfaremanage/mods/laborContractFileBatch/selectFileBatchItemList.ts", "src/apis/welfaremanage/mods/laborContractFileBatch/selectFileBatchList.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/continueinfo.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/downloadFile.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/empinfo.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/entryinfo.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/index.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/leaveinfo.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/postDownloadFile.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/transinfo.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/updateEmpNo.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/updateQysAndHroInfoToHuazhu.ts", "src/apis/welfaremanage/mods/laborQysHuazhu/updateQysAndSyncToHauzhu.ts", "src/apis/welfaremanage/mods/laborSupplyHz/Syn.ts", "src/apis/welfaremanage/mods/laborSupplyHz/abort.ts", "src/apis/welfaremanage/mods/laborSupplyHz/auditPass.ts", "src/apis/welfaremanage/mods/laborSupplyHz/index.ts", "src/apis/welfaremanage/mods/laborSupplyHz/insertHzSupply.ts", "src/apis/welfaremanage/mods/laborSupplyHz/queryHzSupply.ts", "src/apis/welfaremanage/mods/laborSupplyHz/queryHzSupplyCertList.ts", "src/apis/welfaremanage/mods/laborSupplyHz/queryHzSupplyList.ts", "src/apis/welfaremanage/mods/laborSupplyHz/queryLaborContractAddEmpList.ts", "src/apis/welfaremanage/mods/laborSupplyHz/reject.ts", "src/apis/welfaremanage/mods/laborSupplyHz/submitHzSupply.ts", "src/apis/welfaremanage/mods/laborSupplyHz/updateHzSupply.ts", "src/apis/welfaremanage/mods/laborSupplyHz/updateSubmitHzSupply.ts", "src/apis/welfaremanage/mods/laborSupplyHz/uploadAndSyn.ts", "src/apis/welfaremanage/mods/laborSupplyHz/uploadHzSupplyFile.ts", "src/apis/welfaremanage/mods/retireManage/add.ts", "src/apis/welfaremanage/mods/retireManage/applyRetire.ts", "src/apis/welfaremanage/mods/retireManage/del.ts", "src/apis/welfaremanage/mods/retireManage/getSelectEmployee.ts", "src/apis/welfaremanage/mods/retireManage/index.ts", "src/apis/welfaremanage/mods/retireManage/pageRetireRecord.ts", "src/apis/welfaremanage/mods/retireManage/sel.ts", "src/apis/welfaremanage/mods/retireManage/toDownLoad.ts", "src/apis/welfaremanage/mods/retireManage/upt.ts", "src/apis/welfaremanage/mods/retirement/exporting.ts", "src/apis/welfaremanage/mods/retirement/getCurrentFeeList.ts", "src/apis/welfaremanage/mods/retirement/getCurrentMaterialList.ts", "src/apis/welfaremanage/mods/retirement/getIsGiftedCount.ts", "src/apis/welfaremanage/mods/retirement/getOriginalMaterialList.ts", "src/apis/welfaremanage/mods/retirement/index.ts", "src/apis/welfaremanage/mods/retirement/insertRetirement.ts", "src/apis/welfaremanage/mods/retirement/queryRetirementList.ts", "src/apis/welfaremanage/mods/retirement/updateRetirement.ts", "src/apis/welfaremanage/mods/rpaCertificate/approve.ts", "src/apis/welfaremanage/mods/rpaCertificate/cityList.ts", "src/apis/welfaremanage/mods/rpaCertificate/codeToId.ts", "src/apis/welfaremanage/mods/rpaCertificate/custAdd.ts", "src/apis/welfaremanage/mods/rpaCertificate/custDel.ts", "src/apis/welfaremanage/mods/rpaCertificate/doapprove.ts", "src/apis/welfaremanage/mods/rpaCertificate/download.ts", "src/apis/welfaremanage/mods/rpaCertificate/empAdd.ts", "src/apis/welfaremanage/mods/rpaCertificate/empBatchAdd.ts", "src/apis/welfaremanage/mods/rpaCertificate/empBatchEmpList.ts", "src/apis/welfaremanage/mods/rpaCertificate/empBatchList.ts", "src/apis/welfaremanage/mods/rpaCertificate/empBatchStart.ts", "src/apis/welfaremanage/mods/rpaCertificate/empDel.ts", "src/apis/welfaremanage/mods/rpaCertificate/empDetail.ts", "src/apis/welfaremanage/mods/rpaCertificate/empDetailView.ts", "src/apis/welfaremanage/mods/rpaCertificate/empDetailViewUrl.ts", "src/apis/welfaremanage/mods/rpaCertificate/empList.ts", "src/apis/welfaremanage/mods/rpaCertificate/empUpdateStatus.ts", "src/apis/welfaremanage/mods/rpaCertificate/index.ts", "src/apis/welfaremanage/mods/rpaCertificate/list.ts", "src/apis/welfaremanage/mods/rpaCertificate/postEmpStartRpac.ts", "src/apis/welfaremanage/mods/rpaCertificate/reStart.ts", "src/apis/welfaremanage/mods/rpaCertificate/terminal.ts", "src/apis/welfaremanage/mods/rpaPegging/exportFile.ts", "src/apis/welfaremanage/mods/rpaPegging/fromSocial.ts", "src/apis/welfaremanage/mods/rpaPegging/index.ts", "src/apis/welfaremanage/mods/rpaPegging/list.ts", "src/apis/welfaremanage/mods/rpaPegging/restartRpac.ts", "src/apis/welfaremanage/mods/rpaPegging/send.ts", "src/apis/welfaremanage/mods/rpaStampInfo/index.ts", "src/apis/welfaremanage/mods/rpaStampInfo/list.ts", "src/apis/welfaremanage/mods/rpaStampInfo/update.ts", "src/apis/welfaremanage/mods/rpaTaxation/batchUpdate.ts", "src/apis/welfaremanage/mods/rpaTaxation/cityProcessConfig.ts", "src/apis/welfaremanage/mods/rpaTaxation/comparison.ts", "src/apis/welfaremanage/mods/rpaTaxation/exportCityConfig.ts", "src/apis/welfaremanage/mods/rpaTaxation/exportFile.ts", "src/apis/welfaremanage/mods/rpaTaxation/gen.ts", "src/apis/welfaremanage/mods/rpaTaxation/index.ts", "src/apis/welfaremanage/mods/rpaTaxation/list.ts", "src/apis/welfaremanage/mods/rpaTaxation/listProduct.ts", "src/apis/welfaremanage/mods/rpaTaxation/notmatchList.ts", "src/apis/welfaremanage/mods/rpaTaxation/postExportCityConfig.ts", "src/apis/welfaremanage/mods/rpaTaxation/queryOrgDown.ts", "src/apis/welfaremanage/mods/rpaTaxation/send.ts", "src/apis/welfaremanage/mods/rpaTaxation/syncName.ts", "src/apis/welfaremanage/mods/rpaTaxation/syncSalary.ts", "src/apis/welfaremanage/mods/rpaTaxation/update.ts", "src/apis/welfaremanage/mods/rpaTaxation/updateProduct.ts", "src/apis/welfaremanage/mods/rpaTaxation/uploadCityConfigFile.ts", "src/apis/welfaremanage/mods/rpaTaxation/uploadFile.ts", "src/apis/welfaremanage/mods/rpacCallBack/callBack.ts", "src/apis/welfaremanage/mods/rpacCallBack/cityDropDownList.ts", "src/apis/welfaremanage/mods/rpacCallBack/getSsBatchInfo.ts", "src/apis/welfaremanage/mods/rpacCallBack/getSsBatchSecretKey.ts", "src/apis/welfaremanage/mods/rpacCallBack/index.ts", "src/apis/welfaremanage/mods/rpacCallBack/queryOrgDown.ts", "src/apis/welfaremanage/mods/rpacCallBack/startRpacJob.ts", "src/apis/welfaremanage/mods/rpacCallBack/updateRpaDeatilInfo.ts", "src/apis/welfaremanage/mods/rpacCallBack/updateSsBatchInfo.ts", "src/apis/welfaremanage/mods/secondCustPayer/deleteById.ts", "src/apis/welfaremanage/mods/secondCustPayer/exportExcel.ts", "src/apis/welfaremanage/mods/secondCustPayer/generateUploadTemplate.ts", "src/apis/welfaremanage/mods/secondCustPayer/index.ts", "src/apis/welfaremanage/mods/secondCustPayer/postGenerateUploadTemplate.ts", "src/apis/welfaremanage/mods/secondCustPayer/selectList.ts", "src/apis/welfaremanage/mods/secondCustPayer/updateSelective.ts", "src/apis/welfaremanage/mods/siBackPay/add.ts", "src/apis/welfaremanage/mods/siBackPay/del.ts", "src/apis/welfaremanage/mods/siBackPay/index.ts", "src/apis/welfaremanage/mods/siBackPay/pageSelSIBackPayBase.ts", "src/apis/welfaremanage/mods/siBackPay/sel.ts", "src/apis/welfaremanage/mods/siBackPay/selSIBackPayItem.ts", "src/apis/welfaremanage/mods/siBackPay/toDownLoad.ts", "src/apis/welfaremanage/mods/simpleBusiness/delIfsItem.ts", "src/apis/welfaremanage/mods/simpleBusiness/delImptask.ts", "src/apis/welfaremanage/mods/simpleBusiness/exportExcel.ts", "src/apis/welfaremanage/mods/simpleBusiness/generateUploadTemplate.ts", "src/apis/welfaremanage/mods/simpleBusiness/index.ts", "src/apis/welfaremanage/mods/simpleBusiness/postGenerateUploadTemplate.ts", "src/apis/welfaremanage/mods/simpleBusiness/postSelIfsItem.ts", "src/apis/welfaremanage/mods/simpleBusiness/queryAuthority.ts", "src/apis/welfaremanage/mods/simpleBusiness/saveIfsItem.ts", "src/apis/welfaremanage/mods/simpleBusiness/saveImpTask.ts", "src/apis/welfaremanage/mods/simpleBusiness/selBusinessImp.ts", "src/apis/welfaremanage/mods/simpleBusiness/selBusinessImpByEmp.ts", "src/apis/welfaremanage/mods/simpleBusiness/selBusinessImpById.ts", "src/apis/welfaremanage/mods/simpleBusiness/selIfs.ts", "src/apis/welfaremanage/mods/simpleBusiness/selIfsItem.ts", "src/apis/welfaremanage/mods/simpleBusiness/selIfsItemBySubtypeId.ts", "src/apis/welfaremanage/mods/simpleBusiness/selImpTask.ts", "src/apis/welfaremanage/mods/simpleBusiness/updateBusinessImp.ts", "src/apis/welfaremanage/mods/simpleBusiness/updateImpTask.ts", "src/apis/welfaremanage/mods/socialBatch/downloadFile.ts", "src/apis/welfaremanage/mods/socialBatch/exportExcel.ts", "src/apis/welfaremanage/mods/socialBatch/getStampFileTypeRemind.ts", "src/apis/welfaremanage/mods/socialBatch/imageInfoList.ts", "src/apis/welfaremanage/mods/socialBatch/index.ts", "src/apis/welfaremanage/mods/socialBatch/insert.ts", "src/apis/welfaremanage/mods/socialBatch/list.ts", "src/apis/welfaremanage/mods/socialBatch/listCityTemplate.ts", "src/apis/welfaremanage/mods/socialBatch/postSocialDetailList.ts", "src/apis/welfaremanage/mods/socialBatch/querySendCount.ts", "src/apis/welfaremanage/mods/socialBatch/restart.ts", "src/apis/welfaremanage/mods/socialBatch/restartBatch.ts", "src/apis/welfaremanage/mods/socialBatch/restartRpac.ts", "src/apis/welfaremanage/mods/socialBatch/restartStamp.ts", "src/apis/welfaremanage/mods/socialBatch/rpacBatchStatus.ts", "src/apis/welfaremanage/mods/socialBatch/rpacToken.ts", "src/apis/welfaremanage/mods/socialBatch/setBatchCount.ts", "src/apis/welfaremanage/mods/socialBatch/startByBatchIds.ts", "src/apis/welfaremanage/mods/socialBatch/unlock.ts", "src/apis/welfaremanage/mods/socialBatch/uploadFile.ts", "src/apis/welfaremanage/mods/socialManage/addSSLock.ts", "src/apis/welfaremanage/mods/socialManage/allowanceApprove.ts", "src/apis/welfaremanage/mods/socialManage/applyProcess.ts", "src/apis/welfaremanage/mods/socialManage/approve.ts", "src/apis/welfaremanage/mods/socialManage/back.ts", "src/apis/welfaremanage/mods/socialManage/createOtherPayAudit.ts", "src/apis/welfaremanage/mods/socialManage/createPayAllowwanceAudit.ts", "src/apis/welfaremanage/mods/socialManage/createPayAudit.ts", "src/apis/welfaremanage/mods/socialManage/createPaySet.ts", "src/apis/welfaremanage/mods/socialManage/createPayTreatmentAudit.ts", "src/apis/welfaremanage/mods/socialManage/createRiskApprove.ts", "src/apis/welfaremanage/mods/socialManage/createRiskPayAudit.ts", "src/apis/welfaremanage/mods/socialManage/delBudget.ts", "src/apis/welfaremanage/mods/socialManage/doGetAllProcess.ts", "src/apis/welfaremanage/mods/socialManage/doGetSocialProcessBatchList.ts", "src/apis/welfaremanage/mods/socialManage/doWgDataBatch.ts", "src/apis/welfaremanage/mods/socialManage/getAllAmt.ts", "src/apis/welfaremanage/mods/socialManage/getAllowwancePayAuditDataListEx.ts", "src/apis/welfaremanage/mods/socialManage/getApplyTitleByPayAddress.ts", "src/apis/welfaremanage/mods/socialManage/getAssignerProviderDetailForShow.ts", "src/apis/welfaremanage/mods/socialManage/getAssignerProviderDetailForShowByAssignerProvider.ts", "src/apis/welfaremanage/mods/socialManage/getBaseEmployeeInfoById.ts", "src/apis/welfaremanage/mods/socialManage/getBillList.ts", "src/apis/welfaremanage/mods/socialManage/getColForDetail.ts", "src/apis/welfaremanage/mods/socialManage/getColForDetailByAssignerProvider.ts", "src/apis/welfaremanage/mods/socialManage/getContractDetailForShow.ts", "src/apis/welfaremanage/mods/socialManage/getContractDetailForShowByAssignerProvider.ts", "src/apis/welfaremanage/mods/socialManage/getContractForShowByFile.ts", "src/apis/welfaremanage/mods/socialManage/getCustDetailForShow.ts", "src/apis/welfaremanage/mods/socialManage/getCustDetailForShowByAssignerProvider.ts", "src/apis/welfaremanage/mods/socialManage/getCustPayer.ts", "src/apis/welfaremanage/mods/socialManage/getDetailForShow.ts", "src/apis/welfaremanage/mods/socialManage/getDetailForShowByAssignerProvider.ts", "src/apis/welfaremanage/mods/socialManage/getEmployeeFeeMonthDetailList.ts", "src/apis/welfaremanage/mods/socialManage/getLateFeeQuery.ts", "src/apis/welfaremanage/mods/socialManage/getLateFeeQueryTitle.ts", "src/apis/welfaremanage/mods/socialManage/getMakeUpPayQuery.ts", "src/apis/welfaremanage/mods/socialManage/getMakeUpPayQueryTitle.ts", "src/apis/welfaremanage/mods/socialManage/getMon.ts", "src/apis/welfaremanage/mods/socialManage/getPayAllowwanceDetail.ts", "src/apis/welfaremanage/mods/socialManage/getPayAllowwanceDetailCashId.ts", "src/apis/welfaremanage/mods/socialManage/getPayAllowwanceDetailStatus.ts", "src/apis/welfaremanage/mods/socialManage/getPayAllowwanceFile.ts", "src/apis/welfaremanage/mods/socialManage/getPayAuditApprovedDataById.ts", "src/apis/welfaremanage/mods/socialManage/getPayAuditDataById.ts", "src/apis/welfaremanage/mods/socialManage/getPayAuditDataList.ts", "src/apis/welfaremanage/mods/socialManage/getPayAuditDataListEx.ts", "src/apis/welfaremanage/mods/socialManage/getPayResult.ts", "src/apis/welfaremanage/mods/socialManage/getPayTreatmentDetail.ts", "src/apis/welfaremanage/mods/socialManage/getProductBase.ts", "src/apis/welfaremanage/mods/socialManage/getSocialApply.ts", "src/apis/welfaremanage/mods/socialManage/getSocialAssignerProviderPay.ts", "src/apis/welfaremanage/mods/socialManage/getSocialReduction.ts", "src/apis/welfaremanage/mods/socialManage/getSocialSecurityList.ts", "src/apis/welfaremanage/mods/socialManage/getSocialSecurityPopupList.ts", "src/apis/welfaremanage/mods/socialManage/getStartMon.ts", "src/apis/welfaremanage/mods/socialManage/getTreatmentDetail.ts", "src/apis/welfaremanage/mods/socialManage/getTreatmentPayAuditDataListEx.ts", "src/apis/welfaremanage/mods/socialManage/index.ts", "src/apis/welfaremanage/mods/socialManage/insertOtherPayMid.ts", "src/apis/welfaremanage/mods/socialManage/insertPayAuditData.ts", "src/apis/welfaremanage/mods/socialManage/newProcessFail.ts", "src/apis/welfaremanage/mods/socialManage/noNeedDo.ts", "src/apis/welfaremanage/mods/socialManage/otherPayApprove.ts", "src/apis/welfaremanage/mods/socialManage/queryChangeProductDropDownList.ts", "src/apis/welfaremanage/mods/socialManage/queryCityDropDownList.ts", "src/apis/welfaremanage/mods/socialManage/queryHsEmpFee.ts", "src/apis/welfaremanage/mods/socialManage/queryHsEmpFeeMonth.ts", "src/apis/welfaremanage/mods/socialManage/queryMakeUp.ts", "src/apis/welfaremanage/mods/socialManage/queryNewTypeIdDropDownList.ts", "src/apis/welfaremanage/mods/socialManage/queryProcessInfo.ts", "src/apis/welfaremanage/mods/socialManage/queryProvinceDropDownList.ts", "src/apis/welfaremanage/mods/socialManage/queryRatio.ts", "src/apis/welfaremanage/mods/socialManage/queryRemit.ts", "src/apis/welfaremanage/mods/socialManage/queryRemitMon.ts", "src/apis/welfaremanage/mods/socialManage/querySsAndFundPayDetail.ts", "src/apis/welfaremanage/mods/socialManage/querySsPackageIdDropDownList.ts", "src/apis/welfaremanage/mods/socialManage/queryStopTypeDropDownList.ts", "src/apis/welfaremanage/mods/socialManage/rePay.ts", "src/apis/welfaremanage/mods/socialManage/reSet.ts", "src/apis/welfaremanage/mods/socialManage/returnBack.ts", "src/apis/welfaremanage/mods/socialManage/rollImpBudget.ts", "src/apis/welfaremanage/mods/socialManage/rpaSocialProcess.ts", "src/apis/welfaremanage/mods/socialManage/rpaSocialStop.ts", "src/apis/welfaremanage/mods/socialManage/saveBudget.ts", "src/apis/welfaremanage/mods/socialManage/saveSocialChange.ts", "src/apis/welfaremanage/mods/socialManage/saveSocialProcess.ts", "src/apis/welfaremanage/mods/socialManage/saveSocialProcessBatch.ts", "src/apis/welfaremanage/mods/socialManage/saveSocialUpdateAll.ts", "src/apis/welfaremanage/mods/socialManage/saveSocialUpdateBase.ts", "src/apis/welfaremanage/mods/socialManage/saveSynchronousOrder.ts", "src/apis/welfaremanage/mods/socialManage/saveUploadBudget.ts", "src/apis/welfaremanage/mods/socialManage/selectAllAmtPay.ts", "src/apis/welfaremanage/mods/socialManage/selectAllAmtPaySet.ts", "src/apis/welfaremanage/mods/socialManage/selectBudget.ts", "src/apis/welfaremanage/mods/socialManage/selectEmpMaintainAcct.ts", "src/apis/welfaremanage/mods/socialManage/selectFileAmt.ts", "src/apis/welfaremanage/mods/socialManage/selectFilePayDetail.ts", "src/apis/welfaremanage/mods/socialManage/selectGroupProductPaySet.ts", "src/apis/welfaremanage/mods/socialManage/selectLockMon.ts", "src/apis/welfaremanage/mods/socialManage/selectLockMonCheck.ts", "src/apis/welfaremanage/mods/socialManage/selectLockMonEx.ts", "src/apis/welfaremanage/mods/socialManage/selectMakeupAmtPay.ts", "src/apis/welfaremanage/mods/socialManage/selectMakeupAmtPaySet.ts", "src/apis/welfaremanage/mods/socialManage/selectPaySet.ts", "src/apis/welfaremanage/mods/socialManage/selectRemitAmtPay.ts", "src/apis/welfaremanage/mods/socialManage/selectRemitAmtPaySet.ts", "src/apis/welfaremanage/mods/socialManage/selectSocialChange.ts", "src/apis/welfaremanage/mods/socialManage/selectSocialPayCust.ts", "src/apis/welfaremanage/mods/socialManage/selectSocialProcessOld.ts", "src/apis/welfaremanage/mods/socialManage/selectSocialStop.ts", "src/apis/welfaremanage/mods/socialManage/selectSsCustAcct.ts", "src/apis/welfaremanage/mods/socialManage/selectSsPayAuditByparm.ts", "src/apis/welfaremanage/mods/socialManage/selectSsProviderAcct.ts", "src/apis/welfaremanage/mods/socialManage/socialGroup.ts", "src/apis/welfaremanage/mods/socialManage/ssLockQuery.ts", "src/apis/welfaremanage/mods/socialManage/syncData.ts", "src/apis/welfaremanage/mods/socialManage/terminal.ts", "src/apis/welfaremanage/mods/socialManage/toDownLoad.ts", "src/apis/welfaremanage/mods/socialManage/treatmentApprove.ts", "src/apis/welfaremanage/mods/socialManage/treatmentBack.ts", "src/apis/welfaremanage/mods/socialManage/treatmentTterminal.ts", "src/apis/welfaremanage/mods/socialManage/updateBudget.ts", "src/apis/welfaremanage/mods/socialManage/updateOtherPayResult.ts", "src/apis/welfaremanage/mods/socialManage/updatePayFlags.ts", "src/apis/welfaremanage/mods/socialManage/updatePayResult.ts", "src/apis/welfaremanage/mods/socialManage/updateSocialProgressWx.ts", "src/apis/welfaremanage/mods/socialManage/uptOrderSsGroupSvc.ts", "src/apis/welfaremanage/mods/socialManage/uptOrderSsGroupSvcList.ts", "src/apis/welfaremanage/mods/socialManage/uptOrderSsGroupSvcReduceList.ts", "src/apis/welfaremanage/mods/socialManage/uptOrderSsGroupSvcReduceListBeijing.ts", "src/apis/welfaremanage/mods/socialManage/uptSSUnLock.ts", "src/apis/welfaremanage/mods/socialManage/uptSocialStop.ts", "src/apis/welfaremanage/mods/socialSecurity/approve.ts", "src/apis/welfaremanage/mods/socialSecurity/back.ts", "src/apis/welfaremanage/mods/socialSecurity/batchUpdateProductRatio.ts", "src/apis/welfaremanage/mods/socialSecurity/deleteProductRatioById.ts", "src/apis/welfaremanage/mods/socialSecurity/deleteSocialSecurityGroupById.ts", "src/apis/welfaremanage/mods/socialSecurity/deleteSocialSecurityWelfareById.ts", "src/apis/welfaremanage/mods/socialSecurity/getDuplicateProductRatioCount.ts", "src/apis/welfaremanage/mods/socialSecurity/getDuplicateSsGroupCount.ts", "src/apis/welfaremanage/mods/socialSecurity/getDuplicateSsWelfareCount.ts", "src/apis/welfaremanage/mods/socialSecurity/getIncludedCityList.ts", "src/apis/welfaremanage/mods/socialSecurity/getSsGroupDropdownList.ts", "src/apis/welfaremanage/mods/socialSecurity/getSsPfSpecialist.ts", "src/apis/welfaremanage/mods/socialSecurity/index.ts", "src/apis/welfaremanage/mods/socialSecurity/insertProductRatio.ts", "src/apis/welfaremanage/mods/socialSecurity/insertSocialSecurityGroup.ts", "src/apis/welfaremanage/mods/socialSecurity/insertSocialSecurityWelfare.ts", "src/apis/welfaremanage/mods/socialSecurity/postDeleteProductRatioById.ts", "src/apis/welfaremanage/mods/socialSecurity/postDeleteSocialSecurityGroupById.ts", "src/apis/welfaremanage/mods/socialSecurity/postDeleteSocialSecurityWelfareById.ts", "src/apis/welfaremanage/mods/socialSecurity/postGetIncludedCityList.ts", "src/apis/welfaremanage/mods/socialSecurity/postGetSsGroupDropdownList.ts", "src/apis/welfaremanage/mods/socialSecurity/postGetSsPfSpecialist.ts", "src/apis/welfaremanage/mods/socialSecurity/postQueryProductRatioDetailList.ts", "src/apis/welfaremanage/mods/socialSecurity/postQueryProductRatioDetailListTemp.ts", "src/apis/welfaremanage/mods/socialSecurity/postQuerySsGroupProductListForOrder.ts", "src/apis/welfaremanage/mods/socialSecurity/queryBaseByProductRatioItem.ts", "src/apis/welfaremanage/mods/socialSecurity/queryProductDropdownList.ts", "src/apis/welfaremanage/mods/socialSecurity/queryProductRatioById.ts", "src/apis/welfaremanage/mods/socialSecurity/queryProductRatioByProductId.ts", "src/apis/welfaremanage/mods/socialSecurity/queryProductRatioDetailList.ts", "src/apis/welfaremanage/mods/socialSecurity/queryProductRatioDetailListTemp.ts", "src/apis/welfaremanage/mods/socialSecurity/queryProductRatioDropdownList.ts", "src/apis/welfaremanage/mods/socialSecurity/queryProductRatioList.ts", "src/apis/welfaremanage/mods/socialSecurity/queryProductRatioListApp.ts", "src/apis/welfaremanage/mods/socialSecurity/querySocialSecurityGroupList.ts", "src/apis/welfaremanage/mods/socialSecurity/querySocialSecurityWelfareDetailList.ts", "src/apis/welfaremanage/mods/socialSecurity/querySocialSecurityWelfareDetailListForView.ts", "src/apis/welfaremanage/mods/socialSecurity/querySocialSecurityWelfareList.ts", "src/apis/welfaremanage/mods/socialSecurity/querySocialSecurityWelfareListForOrder.ts", "src/apis/welfaremanage/mods/socialSecurity/querySsGroupDetailList.ts", "src/apis/welfaremanage/mods/socialSecurity/querySsGroupDropdownList.ts", "src/apis/welfaremanage/mods/socialSecurity/querySsGroupListForOrder.ts", "src/apis/welfaremanage/mods/socialSecurity/querySsGroupProductListForOrder.ts", "src/apis/welfaremanage/mods/socialSecurity/setDefaultWelfarePackage.ts", "src/apis/welfaremanage/mods/socialSecurity/terminal.ts", "src/apis/welfaremanage/mods/socialSecurity/toDownLoad.ts", "src/apis/welfaremanage/mods/socialSecurity/updateProductRatio.ts", "src/apis/welfaremanage/mods/socialSecurity/updateSocialSecurityGroup.ts", "src/apis/welfaremanage/mods/socialSecurity/updateSocialSecurityWelfare.ts", "src/apis/welfaremanage/mods/socialSecurityReport/delSsReportsConfsByIds.ts", "src/apis/welfaremanage/mods/socialSecurityReport/getReportConfBySSGroup.ts", "src/apis/welfaremanage/mods/socialSecurityReport/index.ts", "src/apis/welfaremanage/mods/socialSecurityReport/querySsReportConfByParam.ts", "src/apis/welfaremanage/mods/socialSecurityReport/saveSsReportConfs.ts", "src/apis/welfaremanage/mods/ssAcct/checkSSAcctExists.ts", "src/apis/welfaremanage/mods/ssAcct/index.ts", "src/apis/welfaremanage/mods/ssAcct/pageInit.ts", "src/apis/welfaremanage/mods/ssAcct/pageQueryCustAcct.ts", "src/apis/welfaremanage/mods/ssAcct/pageQueryEmpAcct.ts", "src/apis/welfaremanage/mods/ssAcct/pageQueryProAcct.ts", "src/apis/welfaremanage/mods/ssAcct/pageQuerySSImpBatch.ts", "src/apis/welfaremanage/mods/ssAcct/postCheckSSAcctExists.ts", "src/apis/welfaremanage/mods/ssAcct/postPageInit.ts", "src/apis/welfaremanage/mods/ssAcct/saveCust.ts", "src/apis/welfaremanage/mods/ssAcct/saveEmp.ts", "src/apis/welfaremanage/mods/ssAcct/savePro.ts", "src/apis/welfaremanage/mods/ssAcct/uptEmpAcctStatus.ts", "src/apis/welfaremanage/mods/ssCommon/getGetSSGroupByUserAndTypeId.ts", "src/apis/welfaremanage/mods/ssCommon/getMaxRoleGradeByUserId.ts", "src/apis/welfaremanage/mods/ssCommon/getSSGroup.ts", "src/apis/welfaremanage/mods/ssCommon/getSSGroupByUser.ts", "src/apis/welfaremanage/mods/ssCommon/index.ts", "src/apis/welfaremanage/mods/ssCommon/selForPartyByDeptId.ts", "src/apis/welfaremanage/mods/ssCommon/selForPartyByDeptInde.ts", "src/apis/welfaremanage/mods/ssCommon/selForPartyByMap.ts", "src/apis/welfaremanage/mods/ssCommon/selProductForPartyByMap.ts", "src/apis/welfaremanage/mods/ssCommon/selectWithholdAgent.ts", "src/apis/welfaremanage/mods/ssCommon/selectWithholdAgentForOne.ts", "src/apis/welfaremanage/mods/ssEntryRelate/index.ts", "src/apis/welfaremanage/mods/ssEntryRelate/selectList.ts", "src/apis/welfaremanage/mods/ssEntryRelate/updateSelective.ts", "src/apis/welfaremanage/mods/ssPaymentResult/getEmpList.ts", "src/apis/welfaremanage/mods/ssPaymentResult/getEntList.ts", "src/apis/welfaremanage/mods/ssPaymentResult/index.ts", "src/apis/welfaremanage/mods/ssPaymentResult/updatePaymentVoucher.ts", "src/apis/welfaremanage/mods/ssPaymentResult/updateTaxPaymentCertificate.ts", "src/apis/welfaremanage/mods/ssRemitSyncToOrder/index.ts", "src/apis/welfaremanage/mods/ssRemitSyncToOrder/querySSProcessInfoOprList.ts", "src/apis/welfaremanage/mods/ssRemitSyncToOrder/querySSRemitSyncToOrderList.ts", "src/apis/welfaremanage/mods/ssRemitSyncToOrder/setSSProcessInfoOprDisable.ts", "src/apis/welfaremanage/mods/ssRemitSyncToOrder/setSSRemitSyncToOrder.ts", "src/apis/welfaremanage/mods/sysSmsLog/getSysSmsLog.ts", "src/apis/welfaremanage/mods/sysSmsLog/index.ts", "src/apis/welfaremanage/mods/sysSmsLog/testSysSmsLog.ts", "src/apis/welfaremanage/mods/sysSmsLog/toDownLoad.ts", "src/apis/welfaremanage/mods/sysSmsShield/del.ts", "src/apis/welfaremanage/mods/sysSmsShield/edit.ts", "src/apis/welfaremanage/mods/sysSmsShield/index.ts", "src/apis/welfaremanage/mods/sysSmsShield/query.ts", "src/apis/welfaremanage/mods/sysSmsShield/save.ts", "src/apis/welfaremanage/mods/sysSmsShield/toDownLoad.ts", "src/apis/welfaremanage/mods/sysSmsTemplt/del.ts", "src/apis/welfaremanage/mods/sysSmsTemplt/edit.ts", "src/apis/welfaremanage/mods/sysSmsTemplt/index.ts", "src/apis/welfaremanage/mods/sysSmsTemplt/query.ts", "src/apis/welfaremanage/mods/sysSmsTemplt/queryField.ts", "src/apis/welfaremanage/mods/sysSmsTemplt/save.ts", "src/apis/welfaremanage/mods/sysSmsTemplt/toDownLoad.ts", "src/apis/workflow/api.d.ts", "src/apis/workflow/baseClass.ts", "src/apis/workflow/index.ts", "src/apis/workflow/mods/index.ts", "src/apis/workflow/mods/remind/createRemind.ts", "src/apis/workflow/mods/remind/exportExcel.ts", "src/apis/workflow/mods/remind/findRemind.ts", "src/apis/workflow/mods/remind/index.ts", "src/apis/workflow/mods/remind/initRemindType.ts", "src/apis/workflow/mods/remind/postCreateRemind.ts", "src/apis/workflow/mods/remind/postInitRemindType.ts", "src/apis/workflow/mods/remind/updateRemindList.ts", "src/apis/workflow/mods/workFlow/getActivitySet.ts", "src/apis/workflow/mods/workFlow/getActivitySetEx.ts", "src/apis/workflow/mods/workFlow/getApproveProcess.ts", "src/apis/workflow/mods/workFlow/getWorkitemList.ts", "src/apis/workflow/mods/workFlow/getWorkitemListCount.ts", "src/apis/workflow/mods/workFlow/getprocessDefCodeActivityCn.ts", "src/apis/workflow/mods/workFlow/index.ts", "src/apis/workflow/mods/workFlow/insertActivitySet.ts", "src/apis/workflow/mods/workFlow/postGetActivitySetEx.ts", "src/apis/workflow/mods/workFlow/postGetWorkitemList.ts", "src/apis/workflow/mods/workFlow/postGetWorkitemListCount.ts", "src/apis/workflow/mods/workFlow/postGetprocessDefCodeActivityCn.ts", "src/apis/workflow/mods/workFlow/toDownLoad.ts", "src/apis/workflow/mods/workFlow/updateActivitySet.ts", "src/apis/workflow/mods/workFlow/updateActivitySetFail.ts", "src/apis/workflow/mods/workFlow/updateActivitySetPub.ts", "src/apis/workflow/mods/workFlowFactory/copyProcessDef.ts", "src/apis/workflow/mods/workFlowFactory/delProcessDef.ts", "src/apis/workflow/mods/workFlowFactory/getAllProcessIns.ts", "src/apis/workflow/mods/workFlowFactory/getApproveProcess.ts", "src/apis/workflow/mods/workFlowFactory/getProcessDef.ts", "src/apis/workflow/mods/workFlowFactory/getProcessDefById.ts", "src/apis/workflow/mods/workFlowFactory/getProcessIns.ts", "src/apis/workflow/mods/workFlowFactory/getProcessInsById.ts", "src/apis/workflow/mods/workFlowFactory/getValidRole.ts", "src/apis/workflow/mods/workFlowFactory/getWorkItemById.ts", "src/apis/workflow/mods/workFlowFactory/getWorkflowIns.ts", "src/apis/workflow/mods/workFlowFactory/getWorkflowInsBy.ts", "src/apis/workflow/mods/workFlowFactory/getWorkflowInsByProcessInsId.ts", "src/apis/workflow/mods/workFlowFactory/index.ts", "src/apis/workflow/mods/workFlowFactory/insertCreateProcessDef.ts", "src/apis/workflow/mods/workFlowFactory/processDefPublish.ts", "src/apis/workflow/mods/workFlowFactory/selectAllProcessDefByMap.ts", "src/apis/workflow/mods/workFlowFactory/updateProcessDef.ts", "src/apis/workflow/mods/workFlowFactory/updateProcessDefInfo.ts", "src/apis/workflow/mods/workFlowFactory/uploadImage.ts"]