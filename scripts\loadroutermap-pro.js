'use strict';
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
exports.__esModule = true;
/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-29 09:38:37
 * @lastTime: 2019-10-09 10:32:14
 * @LastAuthor: 侯成
 * @message:
 * 这个脚本实际上在打酱油，没有实际意义。
 * `config\router.config.ts` 里面有下两句:
 *    `export const routerMap = require('./routers/routerMap.json');`
 *    `const routerData = require('./routers/all.json');`
 * 这样就造成了对与 `routerMap.json` 和 `all.json` 两个文件的刚性依赖。
 * 但这不是什么大问题，`loadFuncs.ts` 每次运行都会生成这两个文件。只需要在每次`start`前运行`loadFuncs`就可以。`yarn start:local`就是这样配置的。
 *
 * 但是问题在于，任何一个新键的项目，git拉取项目后，`yarn install`完成之后，将立刻执行`umi g tmp`，此时上述两个文件缺失，将会造成报错。
 * 当然，这对于前端开发人人员不是问题，直接跑`yarn start`行了。
 * 麻烦在于服务器端，这个错误无关紧要，但看起来很致命，难以解释。所以这个脚本的目的在于，运行`umi g tmp`之前，先生成这两个文件。
 * 毫无疑问，在`yarn start:local`之后，这两个文件将被重新生成并覆盖。所以这个脚本运行了个寂寞。
 *
 */

var fs_1 = __importDefault(require('fs'));
var BASE_DIR = __filename.replace(/\\/g, '/').split('/scripts/')[0];
var componentFileDir = BASE_DIR + '/config/routers';

var sourceFileAll = componentFileDir + '/zefualt-all.json';
var targetFileAll = componentFileDir + '/all.json';
var sourceFile = componentFileDir + '/zefalut-routerMap.json';
var targetFile = componentFileDir + '/routerMap.json';

var main = function () {
  fs_1['default'].copyFile(sourceFile, targetFile, function (err) {
    if (err) {
      console.log('something wrong in was happened');
    } else {
      console.log('copy routerMap.json succeed');
    }
  });
  fs_1['default'].copyFile(sourceFileAll, targetFileAll, function (err) {
    if (err) {
      console.log('something wrong in all.json was happened');
    } else {
      console.log('copy al.json succeed');
    }
  });
};
main();
