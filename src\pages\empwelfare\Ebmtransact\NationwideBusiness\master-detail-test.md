# 主从表关联功能测试

## 测试目标
验证业务内容与业务节点的主从表关联关系是否正常工作。

## 测试环境
- 主表：业务内容（BusinessContent）
- 从表：业务节点（BusinessNode）
- 关联字段：businessContentId

## 功能测试用例

### 1. 主表选择测试

#### 1.1 初始状态
- [ ] 页面加载时，业务节点列表为空
- [ ] 业务节点的"新增"按钮为禁用状态
- [ ] 业务节点维护标题不显示业务内容信息

#### 1.2 选择业务内容
- [ ] 点击业务内容列表中的任意一行
- [ ] 业务节点列表自动查询对应数据
- [ ] 业务节点的"新增"按钮变为可用状态
- [ ] 业务节点维护标题显示当前选中的业务内容名称

#### 1.3 切换业务内容
- [ ] 选择不同的业务内容记录
- [ ] 业务节点列表自动更新为新选中业务内容的节点
- [ ] 标题中的业务内容名称同步更新

### 2. 从表操作测试

#### 2.1 新增业务节点
- [ ] 未选择业务内容时，"新增"按钮禁用
- [ ] 选择业务内容后，"新增"按钮可用
- [ ] 点击"新增"按钮，弹窗正常打开
- [ ] 表单中业务内容字段自动填充且为只读
- [ ] 提交成功后，业务节点列表自动刷新
- [ ] 新增的节点正确关联到选中的业务内容

#### 2.2 修改业务节点
- [ ] 选择业务节点记录后，"修改"按钮可用
- [ ] 点击"修改"按钮，弹窗正常打开
- [ ] 表单预填充选中记录的数据
- [ ] 业务内容字段为只读状态
- [ ] 提交成功后，业务节点列表自动刷新

#### 2.3 删除业务节点
- [ ] 选择业务节点记录后，"删除"按钮可用
- [ ] 删除确认提示正常显示
- [ ] 删除成功后，业务节点列表自动刷新

### 3. 数据关联测试

#### 3.1 关联字段传递
- [ ] 新增业务节点时，businessContentId正确传递
- [ ] API请求参数包含正确的businessContentId
- [ ] 保存成功后，数据库中的关联关系正确

#### 3.2 查询过滤
- [ ] 选择业务内容A，只显示属于A的业务节点
- [ ] 选择业务内容B，只显示属于B的业务节点
- [ ] 查询参数正确包含businessContentId过滤条件

### 4. 用户体验测试

#### 4.1 状态提示
- [ ] 未选择业务内容时，有明确的提示信息
- [ ] 选中业务内容后，标题显示当前上下文
- [ ] 按钮状态根据选择情况正确变化

#### 4.2 操作流程
- [ ] 操作流程符合用户直觉
- [ ] 主从表切换响应及时
- [ ] 错误情况有友好提示

### 5. 边界情况测试

#### 5.1 空数据处理
- [ ] 业务内容为空时，页面正常显示
- [ ] 选中业务内容无对应节点时，显示空列表
- [ ] 删除最后一个业务节点后，列表正确更新

#### 5.2 异常情况处理
- [ ] 网络请求失败时，有错误提示
- [ ] 数据加载中有loading状态
- [ ] 操作失败时，状态正确回滚

## 技术验证

### 1. useWritable 配置
```tsx
const wriTableSub = useWritable({
  service: API.welfaremanage.ebmtransact.getData,
});
```
- [ ] useWritable实例创建成功
- [ ] service配置正确
- [ ] 实例方法可正常调用

### 2. 选择事件处理
```tsx
const handleBusinessContentSelect = (record: any) => {
  setSelectedBusinessContent(record);
  if (record?.businessContentId) {
    wriTableSub.request({ businessContentId: record.businessContentId });
  } else {
    wriTableSub.request({ businessContentId: null });
  }
};
```
- [ ] 选择事件正确触发
- [ ] 状态更新正确
- [ ] 从表查询参数正确

### 3. CachedPage 配置
```tsx
// 主表配置
<CachedPage
  onSelectSingleRow={handleBusinessContentSelect}
  // ...
/>

// 从表配置
<CachedPage
  wriTable={wriTableSub}
  // ...
/>
```
- [ ] onSelectSingleRow事件绑定正确
- [ ] wriTable属性传递正确
- [ ] 主从表独立工作正常

## 性能测试

### 1. 查询性能
- [ ] 主表查询响应时间正常
- [ ] 从表查询响应时间正常
- [ ] 切换选择时无明显延迟

### 2. 内存使用
- [ ] 长时间使用无内存泄漏
- [ ] 组件卸载时正确清理
- [ ] 状态管理合理

## 兼容性测试

### 1. 浏览器兼容性
- [ ] Chrome浏览器正常工作
- [ ] Firefox浏览器正常工作
- [ ] Edge浏览器正常工作

### 2. 屏幕适配
- [ ] 大屏幕显示正常
- [ ] 小屏幕显示正常
- [ ] 响应式布局正确

## 测试数据

### 业务内容测试数据
```javascript
[
  {
    businessContentId: 'BC001',
    businessContentName: '工伤认定申请',
    businessTypeName: '社保业务',
    businessProjectName: '工伤业务'
  },
  {
    businessContentId: 'BC002', 
    businessContentName: '生育津贴申请',
    businessTypeName: '社保业务',
    businessProjectName: '生育业务'
  }
]
```

### 业务节点测试数据
```javascript
[
  {
    businessNodeId: 'BN001',
    businessNodeName: '材料审核',
    businessContentId: 'BC001'
  },
  {
    businessNodeId: 'BN002',
    businessNodeName: '现场勘查',
    businessContentId: 'BC001'
  },
  {
    businessNodeId: 'BN003',
    businessNodeName: '资格审查',
    businessContentId: 'BC002'
  }
]
```

## 测试完成标准
- 所有功能测试用例通过
- 技术验证项目通过
- 性能测试达标
- 兼容性测试通过
- 无严重bug和用户体验问题
