<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-09-17 12:02:38
 * @LastAuthor: 侯成
 * @LastTime: 2019-09-18 13:41:27
 * @message: 
 -->
# 请求后处理
在API请求后，需要检查响应的结果，对可能的错误进行处理。以下提供一种范式：

```tsx
// src\pages\emphiresep\SubcontractManage\index.tsx
import { resError, resErrorMsg, resOkMsg } from '@/utils/methods/message';

haddleSubcontractView = () => {
  const { selectedRows } = this.state;
  const subcontractId = selectedRows[0].subcontractId;
  const { dispatch } = this.props;
  API.sale.subcontract.getSubcontract.request({ id: subcontractId }).then(res => {
    if (resError(res)) return resErrorMsg(res);
    const subContract = responseData(res) as TSubcontractDTO;
    dispatch({
      type: 'subcontract/setScTempForm',
      payload: subContract,
    });
    this.toggleSubcontractView();
    this.toggleSubcontractView();
  });
};
```

`resError`, `resErrorMsg`, `resOkMsg` 是定义在`utils/methods/message`中的方法。基本定义如下：

```tsx
// src\utils\methods\message.ts
export type CommonResponse<T = object> {
  code?: number;
  data?: T;
  message?: string;
}

export const resError = (res: CommonResponse) => boolean;

export const resErrorMsg = (res: CommonResponse | undefined, msg?: string) => void;

export const resOkMsg = resErrorMsg;
```
* `resError` 用于检查`API` 的响应结果, 当`res` 不存在，或`code` 不为 `CODE_SUCCESS` 时，返回 `false`。
* `resErrorMsg`  用于返回错误信息。`msg` 为可选参数，若不传入，将使用 `API` 接口提供的错误信息。
* `resOkMsg` 在请求成功且需要提示的场景下使用，本质与`resErrorMsg` 相同，重取别名，是为了避免语义混淆。

```ts
export const msgErr = (msg?: string, dur?: number) => message.error(msg, dur || duration)
export const msgOk = (msg?: string, dur?: number) => message.success(msg, dur || duration)
```

* `msgErr`  用于返回错误信息。`msg` 为可选参数，若不传入。
* `msgOk` 在请求成功且需要提示的场景下使用，本质与`resErrorMsg` 相同，重取别名，是为了避免语义混淆。
