declare namespace defs {
  export namespace sysmanage {
    export class AllMainQuery {
      /** isShow */
      isShow: string;

      /** publishStatus */
      publishStatus: string;

      /** status */
      status: string;

      /** userId */
      userId: string;
    }

    export class BaseEntity {
      /** add */
      add: boolean;

      /** 申请人Id */
      applyBy: string;

      /** 申请时间 */
      applyDt: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 客户付款方ID */
      clientCompanyId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** 客户编号 */
      custCode: string;

      /** 客户Id */
      custId: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 电子业务提醒 */
      eleBusinessRemind: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 开放雇员登录 */
      haveemp: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 离职员工是否查看 */
      isLookDimission: string;

      /** 是否查看电子业务 */
      isLookEleBusiness: string;

      /** 是否查看电子合同0 否  1 是 */
      isLookEleContract: string;

      /** 查看公积金 */
      isLookFund: string;

      /** 是否查看电子离职证明：0 否  1 是 */
      isLookQuitCertificate: string;

      /** 是否查看电子离职材料：0 否  1 是 */
      isLookQuitMaterial: string;

      /** 查看工资 */
      isLookSalary: string;

      /** 查看社保 */
      isLookSocial: string;

      /** 允许个税抵扣微信申报 */
      isTaxApply: string;

      /** 状态 */
      isValid: string;

      /** 劳动合同短信提醒 */
      laborContractRemind: string;

      /** 离职证明短信提醒 */
      leavingCertificateRemind: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 离职材料短信提醒 */
      resignMaterialsRemind: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class BaseSysCompanyContactsDTO {
      /** 地址 */
      address: string;

      /** 城市ID */
      cityId: number;

      /** 公司/分公司/办事处名称 */
      companyName: string;

      /** 传真 */
      fax: string;

      /** 电话 */
      phone: string;

      /** 邮编 */
      postcode: string;
    }

    export class ClientCompanyQuery {
      /** 创建时间到 */
      createDtEd: string;

      /** 创建时间从 */
      createDtSt: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 申请日期从 */
      dtFrom: string;

      /** 申请日期到 */
      dtTo: string;

      /** endIndex */
      endIndex: number;

      /** 开房雇员登录 */
      haveemp: string;

      /** 状态 */
      isValid: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class CustClientCompany {
      /** add */
      add: boolean;

      /** approveDate */
      approveDate: string;

      /** approveStartDate */
      approveStartDate: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractCode */
      contractCode: string;

      /** contractId */
      contractId: string;

      /** contractName */
      contractName: string;

      /** contractSubType */
      contractSubType: string;

      /** contractType */
      contractType: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** liabilityCs */
      liabilityCs: string;

      /** liabilityCsName */
      liabilityCsName: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** providerId */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class CustomClientCompanyQuery {
      /** 合同审批通过时间 */
      approveDate: string;

      /** 合同审批开始时间 */
      approveStartDate: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同名称 */
      contractName: string;

      /** 合同小类 */
      contractSubType: string;

      /** 合同大类 */
      contractType: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class DepartmentForMess {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_DEPARTMENT_FOR_MESS.DEPARTMENT_FOR_MESS_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      departmentForMessId: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_DEPARTMENT_FOR_MESS.DEPARTMENT_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      departmentId: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_DEPARTMENT_FOR_MESS.MAIN_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      mainId: string;

      /** messType */
      messType: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_DEPARTMENT_FOR_MESS.TYPE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      type: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class Faq {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** createByName */
      createByName: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_FAQ.FAQ_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      faqId: string;

      /** fileName */
      fileName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_FAQ.FILE_PATH           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** status */
      status: string;

      /** statusName */
      statusName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_FAQ.TITLE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      title: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_FAQ.VALID_BY           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      validBy: string;

      /** validByName */
      validByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_FAQ.VALID_DT           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      validDt: string;
    }

    export class FilterEntity {
      /** add */
      add: boolean;

      /** areaId */
      areaId: number;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchId */
      branchId: number;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** ${comment} */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** ${comment} */
      groupName: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** ${comment} */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** sysUserGroupDetailList */
      sysUserGroupDetailList: Array<defs.sysmanage.SysUserGroupDetail>;

      /** 修改人 */
      updateBy: string;

      /** ${comment} */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** ${comment} */
      userGroupId: number;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class HashMap<T0 = any, T1 = any> {}

    export class MainMap {
      /** departmentForMessData */
      departmentForMessData: Array<defs.sysmanage.DepartmentForMess>;

      /** notexistUserData */
      notexistUserData: Array<defs.sysmanage.NotexistUser>;

      /** roleForMessData */
      roleForMessData: Array<defs.sysmanage.RoleForMess>;
    }

    export class MessagePublish {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** child */
      child: object;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** createByName */
      createByName: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.DETAIL           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      detail: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** fileName */
      fileName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.FILE_PATH           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.INNER_CODE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      innerCode: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.IS_SHOW           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      isShow: string;

      /** isShowName */
      isShowName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.MESSAGE_PUBLISH_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      messagePublishId: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.PUBLISH_BY           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      publishBy: string;

      /** publishByName */
      publishByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.PUBLISH_DT           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      publishDt: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.PUBLISH_STATUS           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      publishStatus: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.RECEIVE_BY           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      receiveBy: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.TITLE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      title: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.TYPE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      type: string;

      /** typeName */
      typeName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class MessagePublishDTO {
      /** child */
      child: object;

      /** createByName */
      createByName: string;

      /** departmentForMessData */
      departmentForMessData: Array<defs.sysmanage.DepartmentForMess>;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.DETAIL           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      detail: string;

      /** fileName */
      fileName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.FILE_PATH           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      filePath: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.INNER_CODE           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      innerCode: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.IS_SHOW           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      isShow: string;

      /** isShowName */
      isShowName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.MESSAGE_PUBLISH_ID           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      messagePublishId: string;

      /** notexistUserData */
      notexistUserData: Array<defs.sysmanage.NotexistUser>;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.PUBLISH_BY           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      publishBy: string;

      /** publishByName */
      publishByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.PUBLISH_DT           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      publishDt: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.PUBLISH_STATUS           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      publishStatus: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.RECEIVE_BY           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      receiveBy: string;

      /** roleForMessData */
      roleForMessData: Array<defs.sysmanage.RoleForMess>;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.TITLE           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      title: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_MESSAGE_PUBLISH.TYPE           @ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      type: string;

      /** typeName */
      typeName: string;

      /** userId */
      userId: string;
    }

    export class MessagePulishQuery {
      /** endIndex */
      endIndex: number;

      /** 更新日期从 */
      explainDtFrom: string;

      /** 更新日期到 */
      explainDtTo: string;

      /** 更新List */
      gridData: Array<defs.sysmanage.MessagePublish>;

      /** innerCode */
      innerCode: string;

      /** isShow */
      isShow: string;

      /** messagePublishId */
      messagePublishId: string;

      /** notMessagePublishId */
      notMessagePublishId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 推送日期从 */
      publishDtFrom: string;

      /** 推送日期到 */
      publishDtTo: string;

      /** publishStatus */
      publishStatus: string;

      /** startIndex */
      startIndex: number;

      /** title */
      title: string;

      /** type */
      type: string;

      /** 更新编号 */
      updateCode: string;

      /** 更新说明 */
      updateExplain: string;

      /** 更新标题 */
      updateTitle: string;

      /** 更新类型 */
      updateType: string;

      /** userId */
      userId: string;
    }

    export class MimicQuery {
      /** 改变的list */
      list: Array<defs.sysmanage.ProxySettingDTO>;

      /** 判断重复list */
      tempList: Array<defs.sysmanage.ProxySettingDTO>;
    }

    export class NewestUpdate {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE.DEPLOY_DT           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      deployDt: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** explainDt */
      explainDt: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE.NEWEST_UPDATE_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      newestUpdateId: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE.STATUS           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      status: string;

      /** statusName */
      statusName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE.UPDATE_EXPLAIN           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      updateExplain: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class NewestUpdateSub {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE_SUB.EXPLAIN           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      explain: string;

      /** fileName */
      fileName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE_SUB.FILE_PATH           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE_SUB.NEWEST_UPDATE_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      newestUpdateId: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE_SUB.NEWEST_UPDATE_SUB_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      newestUpdateSubId: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** realFilePath */
      realFilePath: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE_SUB.REMIND_LIST           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      remindList: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE_SUB.UPDATE_CODE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      updateCode: string;

      /** 修改日期 */
      updateDt: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE_SUB.UPDATE_TITLE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      updateTitle: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NEWEST_UPDATE_SUB.UPDATE_TYPE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      updateType: string;

      /** updateTypeName */
      updateTypeName: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class NewsUpdateQuery {
      /** 发布日期从 */
      deployDtFrom: string;

      /** 发布日期到 */
      deployDtTo: string;

      /** endIndex */
      endIndex: number;

      /** 日期从 */
      explainDtFrom: string;

      /** 日期到 */
      explainDtTo: string;

      /** 注意：保存和更新的数据都放这个参数里，前面都属都不用，---不知道谁弄的要转个map */
      gridData: Array<defs.sysmanage.NewestUpdate>;

      /** 信息id */
      newestUpdateId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** 状态 */
      status: string;

      /** 更新编号 */
      updateCode: string;

      /** 更新内容 */
      updateExplain: string;

      /** 更新标题 */
      updateTitle: string;

      /** 更新状态 */
      updateType: string;
    }

    export class NewsUpdateSubQuery {
      /** endIndex */
      endIndex: number;

      /** 批量更新时用的参数 */
      gridData: Array<defs.sysmanage.NewestUpdateSub>;

      /** newestUpdateId */
      newestUpdateId: string;

      /** newestUpdateSubId */
      newestUpdateSubId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class NotexistUser {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NOTEXIST_USER.MAIN_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      mainId: string;

      /** messType */
      messType: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NOTEXIST_USER.NOTEXIST_USER_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      notexistUserId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NOTEXIST_USER.TYPE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      type: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_NOTEXIST_USER.USER_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      userIdEx: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }

    export class PrivacyDTO {
      /** isDeleted */
      isDeleted: number;

      /** isValid */
      isValid: number;

      /** privacyContent */
      privacyContent: string;

      /** privacyId */
      privacyId: number;

      /** privacyVersion */
      privacyVersion: number;

      /** updateBy */
      updateBy: string;
    }

    export class ProxySettingDTO {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 部门名称 */
      departmentGrade: string;

      /** 部门id */
      departmentId: string;

      /** 部门名称 */
      departmentName: string;

      /** 部门名称 */
      deptName: string;

      /** 结束日期 */
      endDate: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 所属大区 */
      governingArea: string;

      /** 所属分公司 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** mockAuth */
      mockAuth: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** 部门名称 */
      proxyDeptName: string;

      /** 员工姓名 */
      proxyRealName: string;

      /** 代理类型 */
      proxyType: string;

      /** 被代理用户ID */
      proxyUserId: string;

      /** 用户名称 */
      proxyUserName: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 用户姓名 */
      realName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 关系ID */
      serialId: string;

      /** 开始日期从 */
      startDateFrom: string;

      /** 开始日期到 */
      startDateTo: string;

      /** 状态 */
      status: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户ID */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 用户名称 */
      userName: string;
    }

    export class ProxySettingQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 代理姓名 */
      proxyRealName: string;

      /** 代理帐号 */
      proxyUserName: string;

      /** 姓名 */
      realName: string;

      /** 开始 */
      startDateFrom: string;

      /** 截至 */
      startDateTo: string;

      /** startIndex */
      startIndex: number;

      /** 登录帐号 */
      userName: string;
    }

    export class ResetCompanyPswDTO {
      /** custId */
      custId: string;

      /** pwd */
      pwd: string;
    }

    export class RoleForMess {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_ROLE_FOR_MESS.MAIN_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      mainId: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_ROLE_FOR_MESS.ROLE_FOR_MESS_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      roleForMessId: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_ROLE_FOR_MESS.ROLE_ID           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      roleId: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column SYS_ROLE_FOR_MESS.TYPE           ibatorgenerated Tue Sep 11 18:14:11 CST 2012 */
      type: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SendMainQuery {
      /** endIndex */
      endIndex: number;

      /** mainId */
      mainId: string;

      /** mainMap */
      mainMap: defs.sysmanage.MainMap;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** receString */
      receString: string;

      /** remindDesc */
      remindDesc: string;

      /** startIndex */
      startIndex: number;
    }

    export class Support {
      /** activityDefId */
      activityDefId: string;

      /** add */
      add: boolean;

      /** answerer */
      answerer: string;

      /** answererId */
      answererId: string;

      /** areaname */
      areaname: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** deadDT */
      deadDT: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** fileId */
      fileId: string;

      /** fileName */
      fileName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** historyResult */
      historyResult: string;

      /** id */
      id: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processTime */
      processTime: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** questDT */
      questDT: string;

      /** questLevel */
      questLevel: string;

      /** questProduct */
      questProduct: string;

      /** questProductType */
      questProductType: string;

      /** questProvider */
      questProvider: string;

      /** questSeverity */
      questSeverity: string;

      /** questType */
      questType: string;

      /** questUserName */
      questUserName: string;

      /** questerId */
      questerId: string;

      /** questerName */
      questerName: string;

      /** questionTitle */
      questionTitle: string;

      /** realName */
      realName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** result */
      result: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** sqlExecutant */
      sqlExecutant: string;

      /** sqlExecutantDt */
      sqlExecutantDt: string;

      /** sqls */
      sqls: string;

      /** state */
      state: string;

      /** statementName */
      statementName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** supportContent */
      supportContent: string;

      /** supportDeadDATE */
      supportDeadDATE: string;

      /** supportEvaluate */
      supportEvaluate: string;

      /** supportTime */
      supportTime: string;

      /** supportType */
      supportType: string;

      /** supportUserId */
      supportUserId: string;

      /** supportUserName */
      supportUserName: string;

      /** tempResult */
      tempResult: string;

      /** tools */
      tools: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** workItemId */
      workItemId: string;

      /** workflowId */
      workflowId: string;
    }

    export class SupportQuery {
      /** answererId */
      answererId: string;

      /** area */
      area: string;

      /** countSqlName */
      countSqlName: string;

      /** endIndex */
      endIndex: number;

      /** evaluate */
      evaluate: string;

      /** isDeleted */
      isDeleted: string;

      /** nowrealname */
      nowrealname: string;

      /** 页数 */
      pageNum: number;

      /** pageQuerySqlName */
      pageQuerySqlName: string;

      /** 每页记录数 */
      pageSize: number;

      /** processDefId */
      processDefId: string;

      /** provider */
      provider: string;

      /** questDTEnd */
      questDTEnd: string;

      /** questDTStart */
      questDTStart: string;

      /** questEntDeadDT */
      questEntDeadDT: string;

      /** questLevel */
      questLevel: string;

      /** questProduct */
      questProduct: string;

      /** questProductType */
      questProductType: string;

      /** questSeverity */
      questSeverity: string;

      /** questStartDeadDT */
      questStartDeadDT: string;

      /** questerId */
      questerId: string;

      /** questionId */
      questionId: string;

      /** questionTitle */
      questionTitle: string;

      /** startIndex */
      startIndex: number;

      /** state */
      state: string;

      /** supportContent */
      supportContent: string;

      /** supportType */
      supportType: string;

      /** userId */
      userId: string;

      /** userid */
      userid: string;
    }

    export class SysCollectionProposal {
      /** add */
      add: boolean;

      /** areaName */
      areaName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** collectionProposal */
      collectionProposal: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** createDtEd */
      createDtEd: string;

      /** createDtSt */
      createDtSt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** estimateExecuteDt */
      estimateExecuteDt: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** feedback */
      feedback: string;

      /** fileId */
      fileId: string;

      /** fileName */
      fileName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** hpExecuteDt */
      hpExecuteDt: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processInsId */
      processInsId: string;

      /** proposalAreaId */
      proposalAreaId: string;

      /** proposalBy */
      proposalBy: string;

      /** proposalByName */
      proposalByName: string;

      /** proposalCode */
      proposalCode: string;

      /** proposalDetail */
      proposalDetail: string;

      /** proposalProvider */
      proposalProvider: string;

      /** proposalStatus */
      proposalStatus: string;

      /** proposalTitle */
      proposalTitle: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** providerName */
      providerName: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** queryType */
      queryType: string;

      /** reply */
      reply: string;

      /** replyDetail */
      replyDetail: string;

      /** replyDt */
      replyDt: string;

      /** replyName */
      replyName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** stype */
      stype: string;

      /** subType */
      subType: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** sysFunctionId */
      sysFunctionId: string;

      /** sysFunctionName */
      sysFunctionName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** workitemId */
      workitemId: string;
    }

    export class SysCompanyContactsDTO {
      /** 地址 */
      address: string;

      /** 城市ID */
      cityId: number;

      /** 主键ID */
      companyContactsId: number;

      /** 公司/分公司/办事处名称 */
      companyName: string;

      /** 传真 */
      fax: string;

      /** 电话 */
      phone: string;

      /** 邮编 */
      postcode: string;
    }

    export class SysCompanyContactsQueryDTO {
      /** 城市ID */
      cityId: number;

      /** 公司/分公司/办事处名称 */
      companyName: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class SysUserGroup {
      /** add */
      add: boolean;

      /** areaId */
      areaId: number;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchId */
      branchId: number;

      /** branchName */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** ${comment} */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** detailCount */
      detailCount: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** ${comment} */
      groupName: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** ${comment} */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** ${comment} */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** ${comment} */
      userGroupId: number;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SysUserGroupDetail {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** ${comment} */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** email */
      email: string;

      /** empCode */
      empCode: string;

      /** empId */
      empId: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** ${comment} */
      isDeleted: string;

      /** 是否接收薪资支付审批邮件 */
      isPayEmail: string;

      /** 是否接收社保支付审批邮件 */
      isSocialEmail: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** realName */
      realName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** ${comment} */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** ${comment} */
      userGroupDetailId: number;

      /** ${comment} */
      userGroupId: number;

      /** ${comment} */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class Task {
      /** add */
      add: boolean;

      /** approveStatus */
      approveStatus: string;

      /** approveStatusText */
      approveStatusText: string;

      /** areaName */
      areaName: string;

      /** authority */
      authority: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchName */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** countNum */
      countNum: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** createDtEnd */
      createDtEnd: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** dealBy */
      dealBy: string;

      /** dealName */
      dealName: string;

      /** del */
      del: boolean;

      /** detailComtent */
      detailComtent: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** fileId */
      fileId: string;

      /** fileName */
      fileName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** olTaskId */
      olTaskId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** priority */
      priority: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processExplain */
      processExplain: string;

      /** processInsId */
      processInsId: string;

      /** processingDt */
      processingDt: string;

      /** processingDtEnd */
      processingDtEnd: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** queryType */
      queryType: string;

      /** questUserName */
      questUserName: string;

      /** questerName */
      questerName: string;

      /** raiseBy */
      raiseBy: string;

      /** realName */
      realName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** taskTitle */
      taskTitle: string;

      /** taskType */
      taskType: string;

      /** taskTypeText */
      taskTypeText: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class sysContactsDTO {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 城市名称 */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 办公电话 */
      contactTel: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 一级部门 */
      deptFirst: string;

      /** 二级部门 */
      deptSecond: string;

      /** 三级部门 */
      deptThird: string;

      /** 邮箱 */
      eMail: string;

      /** 职员代码 */
      empCode: string;

      /** 姓名 */
      empName: string;

      /** 职位名称 */
      empPosition: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 主键id */
      sysContactsId: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class sysContactsQuery {
      /** 所在工作城市 */
      cityName: string;

      /** 一级部门 */
      deptFirst: string;

      /** 姓名 */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }
  }
}

declare namespace API {
  export namespace sysmanage {
    /**
     * Client Company Controller
     */
    export namespace clientCompany {
      /**
        * 批量增加客户端信息
批量增加客户端信息
        * /clientCompany/addBatchClientCompany
        */
      export namespace addBatchClientCompany {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<defs.sysmanage.CustClientCompany>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.sysmanage.CustClientCompany>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 通过客户id查询是否存在
通过客户id查询是否存在
        * /clientCompany/count
        */
      export namespace count {
        export class Params {
          /** custId */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据Id删除客户端
根据Id删除客户端
        * /clientCompany/del
        */
      export namespace del {
        export class Params {
          /** ids */
          ids: string;
        }

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出客户端信息
导出客户端信息
        * /clientCompany/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 重置密码
重置密码
        * /clientCompany/resetPwd
        */
      export namespace resetPwd {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.ResetCompanyPswDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ResetCompanyPswDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ResetCompanyPswDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增客户端
新增客户端
        * /clientCompany/save
        */
      export namespace save {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.BaseEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询客户端
查询客户端
        * /clientCompany/sel
        */
      export namespace sel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.ClientCompanyQuery;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ClientCompanyQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ClientCompanyQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询客户端公司
查询客户端公司
        * /clientCompany/selCustomClientCompany
        */
      export namespace selCustomClientCompany {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CustomClientCompanyQuery;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.CustomClientCompanyQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.CustomClientCompanyQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 最新更新维护
     */
    export namespace messagePublish {
      /**
        * 获取主页数据
获取主页数据
        * /messagePublish/getAllMainList
        */
      export namespace getAllMainList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.AllMainQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.AllMainQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询faq记录
查询faq记录
        * /messagePublish/getFaqList
        */
      export namespace getFaqList {
        export class Params {
          /** limit */
          limit: number;
          /** map */
          map?: object;
          /** start */
          start: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询信息发布记录
查询信息发布记录
        * /messagePublish/getMessagePublishList
        */
      export namespace getMessagePublishList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.MessagePulishQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.MessagePulishQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询最新更新记录
查询最新更新记录
        * /messagePublish/getNewestUpdateList
        */
      export namespace getNewestUpdateList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.NewsUpdateQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.NewsUpdateQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询最新更新子表记录
查询最新更新子表记录
        * /messagePublish/getNewestUpdateSubList
        */
      export namespace getNewestUpdateSubList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.NewsUpdateSubQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.NewsUpdateSubQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取接收人
获取接收人
        * /messagePublish/getRecipient
        */
      export namespace getRecipient {
        export class Params {
          /** mainId */
          mainId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取接收人数据
获取接收人数据
        * /messagePublish/queryRoleList
        */
      export namespace queryRoleList {
        export class Params {
          /** userId */
          userId: string;
          /** userId2 */
          userId2: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * queryRoleListEx
       * /messagePublish/queryRoleListEx
       */
      export namespace queryRoleListEx {
        export class Params {
          /** roleName */
          roleName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存faq记录
保存faq记录
        * /messagePublish/saveFaq
        */
      export namespace saveFaq {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.Faq,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Faq,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存信息发布记录
保存信息发布记录
        * /messagePublish/saveMessagePublish
        */
      export namespace saveMessagePublish {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.MessagePublishDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.MessagePublishDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存最新更新记录
保存最新更新记录
        * /messagePublish/saveNewestUpdateList
        */
      export namespace saveNewestUpdateList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.NewsUpdateQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.NewsUpdateQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存最新更新子表记录
保存最新更新子表记录
        * /messagePublish/saveNewestUpdateSub
        */
      export namespace saveNewestUpdateSub {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.NewestUpdateSub,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.NewestUpdateSub,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 发送提醒
发送提醒
        * /messagePublish/sendRemind
        */
      export namespace sendRemind {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.SendMainQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SendMainQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量更新faq记录
批量更新faq记录
        * /messagePublish/updateFaqList
        */
      export namespace updateFaqList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新信息发布记录
更新信息发布记录
        * /messagePublish/updateMessagePublish
        */
      export namespace updateMessagePublish {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.MessagePublishDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.MessagePublishDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量更新信息发布记录
批量更新信息发布记录
        * /messagePublish/updateMessagePublishList
        */
      export namespace updateMessagePublishList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.MessagePulishQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.MessagePulishQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量更新最新更新记录
批量更新最新更新记录
        * /messagePublish/updateNewestUpdateList
        */
      export namespace updateNewestUpdateList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.NewsUpdateQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.NewsUpdateQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新最新更新子表记录
更新最新更新子表记录
        * /messagePublish/updateNewestUpdateSub
        */
      export namespace updateNewestUpdateSub {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.NewestUpdateSub,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.NewestUpdateSub,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量最新更新子表记录
批量最新更新子表记录
        * /messagePublish/updateNewestUpdateSubList
        */
      export namespace updateNewestUpdateSubList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.NewsUpdateSubQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.NewsUpdateSubQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 在线任务
     */
    export namespace olTask {
      /**
        * 添加审批任务
添加审批任务
        * /olTask/addTask
        */
      export namespace addTask {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出信息
导出信息
        * /olTask/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * getTask
       * /olTask/getTask
       */
      export namespace getTask {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * taskFinish
       * /olTask/taskFinish
       */
      export namespace taskFinish {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * taskStop
       * /olTask/taskStop
       */
      export namespace taskStop {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * updateTask
       * /olTask/updateTask
       */
      export namespace updateTask {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Task,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Privacy Version Controller
     */
    export namespace privacyVersion {
      /**
        * 新增
新增
        * /privacy/insertPrivacy
        */
      export namespace insertPrivacy {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.PrivacyDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.PrivacyDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.PrivacyDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询结果
根据条件分页查询结果
        * /privacy/queryPagePrivacy
        */
      export namespace getPageOfProxy {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 生效/删除
生效/删除
        * /privacy/updatePrivacy
        */
      export namespace updatePrivacy {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.BaseEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.PrivacyDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.PrivacyDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 模拟人列表维护
     */
    export namespace proxySetting {
      /**
        * 删除模拟人
删除模拟人
        * /proxySetting/deleteMimicSetting
        */
      export namespace deleteMimicSetting {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除代理设置
删除代理设置
        * /proxySetting/deleteProxySetting
        */
      export namespace deleteProxySetting {
        export class Params {
          /** ids */
          ids: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除代理设置
删除代理设置
        * /proxySetting/deleteProxySetting
        */
      export namespace postDeleteProxySetting {
        export class Params {
          /** ids */
          ids: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询模拟人列表
查询模拟人列表
        * /proxySetting/getAllAccountList
        */
      export namespace getAllAccountList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ProxySettingQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ProxySettingQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询模拟人
查询模拟人
        * /proxySetting/getPageOfMimic
        */
      export namespace getPageOfMimic {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ProxySettingQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ProxySettingQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件得到代理设置的分页查询结果
根据条件得到代理设置的分页查询结果
        * /proxySetting/getPageOfProxy
        */
      export namespace getPageOfProxy {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ProxySettingQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ProxySettingQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 插入或者更新模拟人
插入或者更新模拟人
        * /proxySetting/insertAndUpdateMimic
        */
      export namespace insertAndUpdateMimic {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.MimicQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.MimicQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增代理设置
新增代理设置
        * /proxySetting/insertProxySetting
        */
      export namespace insertProxySetting {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.ProxySettingDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ProxySettingDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 校验代理用户+被代理用户在一个时间段内唯一
校验代理用户+被代理用户在一个时间段内唯一
        * /proxySetting/isRepeatProxyRecord
        */
      export namespace isRepeatProxyRecord {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ProxySettingDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ProxySettingDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取所有重复的菜单
获取所有重复的菜单
        * /proxySetting/queryRepeatRecordMimic
        */
      export namespace queryRepeatRecordMimic {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.sysmanage.ProxySettingDTO>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.sysmanage.ProxySettingDTO>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改代理设置
修改代理设置
        * /proxySetting/updateProxySetting
        */
      export namespace updateProxySetting {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.ProxySettingDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ProxySettingDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 在线支持
     */
    export namespace support {
      /**
        * 创建系统意见与建议收集
创建系统意见与建议收集
        * /support/createCollectionProposal
        */
      export namespace createCollectionProposal {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.SysCollectionProposal,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SysCollectionProposal,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出套餐信息
导出套餐信息
        * /support/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页获取在线支持
分页获取在线支持
        * /support/getData
        */
      export namespace getData {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.SupportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SupportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据userId得到在线支持信息
根据userId得到在线支持信息
        * /support/getSupportDate
        */
      export namespace getSupportDate {
        export class Params {
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<
            Array<defs.sysmanage.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据Id号得到在线支持信息
根据Id号得到在线支持信息
        * /support/getSupportDateById
        */
      export namespace getSupportDateById {
        export class Params {
          /** id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Support;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据supportId得到在线支持信息
根据supportId得到在线支持信息
        * /support/getSupportDateBySupportId
        */
      export namespace getSupportDateBySupportId {
        export class Params {
          /** supportId */
          supportId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Support;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据roleId得到人员信息
根据roleId得到人员信息
        * /support/getSupportUserDate
        */
      export namespace getSupportUserDate {
        export class Params {
          /** roleId */
          roleId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<
            Array<defs.sysmanage.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据roleId得到人员信息
根据roleId得到人员信息
        * /support/getSupportUserDate
        */
      export namespace postGetSupportUserDate {
        export class Params {
          /** roleId */
          roleId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<
            Array<defs.sysmanage.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得技术支持人员信息
获得技术支持人员信息
        * /support/getsqlExecutant
        */
      export namespace getsqlExecutant {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<
            Array<defs.sysmanage.HashMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 添加在线支持
添加在线支持
        * /support/insertDataByEntity
        */
      export namespace insertDataByEntity {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * queryCollectionProposal
       * /support/queryCollectionProposal
       */
      export namespace queryCollectionProposal {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.SysCollectionProposal,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SysCollectionProposal,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 提交在线支持
提交在线支持
        * /support/saveDataByEntity
        */
      export namespace saveDataByEntity {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 回退在线支持
回退在线支持
        * /support/supportBack
        */
      export namespace supportBack {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 结束在线支持
结束在线支持
        * /support/supportFinsh
        */
      export namespace supportFinsh {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 处理在线支持
处理在线支持
        * /support/toolsDataByEntity
        */
      export namespace toolsDataByEntity {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.Support,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * update系统意见与建议收集
update系统意见与建议收集
        * /support/updateCollectionProposal
        */
      export namespace updateCollectionProposal {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.SysCollectionProposal,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SysCollectionProposal,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 全国公司通讯录
     */
    export namespace sysCompanyContacts {
      /**
        * 批量删除全国公司通讯录
批量删除全国公司通讯录
        * /sys/company/contacts/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 全国公司通讯录ID，逗号分隔 */
          ids: string;
        }

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询全国公司通讯录列表
查询全国公司通讯录列表
        * /sys/company/contacts/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<
            Array<defs.sysmanage.SysCompanyContactsDTO>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.SysCompanyContactsQueryDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SysCompanyContactsQueryDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增全国公司通讯录
新增全国公司通讯录
        * /sys/company/contacts/save
        */
      export namespace save {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.BaseSysCompanyContactsDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.BaseSysCompanyContactsDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改全国公司通讯录
修改全国公司通讯录
        * /sys/company/contacts/update
        */
      export namespace update {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.SysCompanyContactsDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SysCompanyContactsDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 全国通讯录人员管理
     */
    export namespace sysContacts {
      /**
        * 批量删除通讯录人员
批量删除通讯录人员
        * /sys/contacts/delete
        */
      export namespace deleteSysContacts {
        export class Params {
          /** 主键id集合 */
          sysContactsIds: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出通讯录人员
导出通讯录人员
        * /sys/contacts/export
        */
      export namespace exporting {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存通讯录人员
保存通讯录人员
        * /sys/contacts/save
        */
      export namespace saveSysContacts {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.sysContactsDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.sysContactsDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询通讯录人员
查询通讯录人员
        * /sys/contacts/select
        */
      export namespace selectSysContacts {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询通讯录人员
查询通讯录人员
        * /sys/contacts/select
        */
      export namespace postSelect {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.sysContactsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.sysContactsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新通讯录人员
更新通讯录人员
        * /sys/contacts/update
        */
      export namespace updateSysContacts {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.sysContactsDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.sysContactsDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Sys User Group Controller
     */
    export namespace sysUserGroup {
      /**
        * 保存
入参：{groupName：账户组名，sysUserGroupDetailList：【{userId ,realName}】 }
        * /sysUserGroup/addUserGroupDetail
        */
      export namespace addUserGroupDetail {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询账号组
入参：{groupName}返回：{groupName 账户组名，detailCount 关联账号数}
        * /sysUserGroup/page
        */
      export namespace page {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.sysmanage.CommonResponse<defs.sysmanage.Page>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.sysmanage.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除
入参 {userGroupId}
        * /sysUserGroup/remove
        */
      export namespace remove {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.SysUserGroup,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SysUserGroup,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改
入参：{groupName：账户组名，sysUserGroupDetailList：【{userId ,realName}】 }
        * /sysUserGroup/updateUserGroupDetail
        */
      export namespace updateUserGroupDetail {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Sys User Group Detail Controller
     */
    export namespace sysUserGroupDetail {
      /**
        * 首页列出组下的用户
入参：{无}，返回：{realName 姓名 ,userName 账号,deptName 部门 ,branckName 分公司,areaName 所属大区}
        * /sysUserGroupDetail/listGroupUser
        */
      export namespace listGroupUser {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.SysUserGroupDetail,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SysUserGroupDetail,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 联动子表
入参：{userGroupId}  返回：【{realName 姓名 ,userName 账号,deptName 部门 ,branckName 分公司,areaName 所属大区}】
        * /sysUserGroupDetail/listUserGroupDetail
        */
      export namespace listUserGroupDetail {
        export class Params {}

        export type Response<T> = defs.sysmanage.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.sysmanage.SysUserGroupDetail,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.sysmanage.SysUserGroupDetail,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
