import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employee/getEmpDeptCount
     * @desc 获取员工在部门下的个数
获取员工在部门下的个数
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = '';
export const url = '/rhro-service-1.0/employee/getEmpDeptCount:GET';
export const initialUrl = '/rhro-service-1.0/employee/getEmpDeptCount';
export const cacheKey = '_employee_getEmpDeptCount_GET';
export async function request(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/getEmpDeptCount`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/getEmpDeptCount`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
