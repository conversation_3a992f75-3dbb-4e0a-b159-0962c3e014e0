```sh
.
├── azure-pipelines.yml
├── build-pre.sh
├── config                      # 全局配置
│   ├── config.ts
│   ├── defaultSettings.ts
│   ├── locales
│   ├── plugin.config.ts
│   ├── pont
│   ├── router.config.ts
│   └── routers
├── doc                         # 项目文档
│   ├── implements
│   ├── modules
│   ├── projects
│   └── technics
├── package.json                # 前端项目配置文件
├── README.md                   # 项目说明文档
├── scripts                     # 独立脚本
│   ├── generateMock.js
│   ├── getPrettierFiles.js
│   ├── lint-prettier.js
│   ├── loadFuncs.js
│   ├── loadFuncs-pro.js
│   ├── loadFuncs.ts
│   └── prettier.js
├── src                         # 工程开发主目录
│   ├── apis                      # pont生成api调用
│   │   ├── admin
│   │   └── sale
│   ├── app.ts                    # 项目运行入口
│   ├── assets                    # 样式文件目录
│   │   ├── css
│   │   │   └── index.less
│   │   └── welcome.jpg
│   ├── components                # 公共组件
│   ├── e2e                       # 自动化工程测试
│   │   ├── baseLayout.e2e.js
│   │   └── topMenu.e2e.js
│   ├── global.less
│   ├── global.tsx
│   ├── layouts                   # 页面元素外框架
│   │   ├── BasicLayout.less
│   │   ├── BasicLayout.tsx
│   │   ├── BlankLayout.tsx
│   │   ├── Footer.tsx
│   │   ├── Header.less
│   │   ├── Header.tsx
│   │   ├── MenuContext.tsx
│   │   ├── UserLayout.less
│   │   └── UserLayout.tsx
│   ├── locales                   # 国际化
│   │   ├── en-US
│   │   ├── en-US.ts
│   │   ├── zh-CN
│   │   └── zh-CN.ts
│   ├── manifest.json
│   ├── models                   # 全局共用dva models
│   │   ├── clientLogin.1.tsbk
│   │   ├── clientLogin.ts
│   │   ├── connect.d.ts
│   │   ├── global.ts
│   │   ├── list.ts
│   │   ├── login.ts
│   │   ├── menu.ts
│   │   ├── project.ts
│   │   ├── routeWash.tsbk
│   │   ├── setting.ts
│   │   └── user.ts
│   ├── pages                     # 页面文件主目录
│   │   ├── 404.tsx
│   │   ├── Authorized.tsx
│   │   ├── document.ejs
│   │   ├── Exception
│   │   ├── Gateway.tsx
│   │   ├── Sales
│   │   │   ├── cargo               # 一级模块公共配置
│   │   │   │   ├── models.ts
│   │   │   │   └── types.ts
│   │   │   ├── ContractManage
│   │   │   ├── models              # 一级模块共用dva models
│   │   │   │   └── contract.ts
│   │   │   └── QuotationForm
│   │   └── Welcome.tsx
│   ├── services                  # api调用合集（将弃用）
│   │   ├── api.ts
│   │   ├── baseData
│   │   │   ├── cityInfo.ts
│   │   │   ├── countryInfo.ts
│   │   │   └── provinceInfo.ts
│   │   ├── types.ts
│   │   └── user.ts
│   ├── service-worker.js
│   ├── typings.d.ts              # typescript公共类型声明
│   └── utils                     # 公共方法
│       ├── authority.test.ts
│       ├── authority.ts
│       ├── Authorized.ts
│       ├── extractRouters.ts
│       ├── getPageTitle.ts
│       ├── message.ts
│       ├── pagenation.ts
│       ├── pontFetch.ts
│       ├── request.ts
│       ├── resolveData.ts
│       ├── searchparams
│       │   ├── encode.ts
│       │   ├── index.ts
│       │   └── utils.ts
│       ├── settings.ts
│       ├── utils.less
│       ├── utils.test.ts
│       └── utils.ts
├── start.sh
├── tests                       # 自动化测试
│   └── run-tests.js
├── tsconfig.json               # typescript配置
├── tslint.yml
├── typings.d.ts                # typescript公共类型声明
├── yarn.lock
└── yarn-registery.sh           # 更新yarn代理源
```