<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-08-28 12:05:54
 * @lastTime: 2019-08-29 10:20:51
 * @LastAuthor: 侯成
 * @message: 
 -->
# 可编辑表单
在本项目中，拥有有大量的行内编辑场景，支持行内输入非常简单，困难在于单元格的验证。本组件致力于解决此问题。

![](../assets/images/2.5-writable.png)

## 组件实现
```tsx
// src\components\Writable\index.tsx
// 类型定义
interface WritableProps<T> {
  tableKey: string;
  columns: WritableColumnProps<T & TableLine>[];
  outerForm: WrappedFormUtils<any>;
  handdleDelete?: (rows: T[]) => void;
  rules?: TFormRules<ValidationRule>;
  initData?: Array<T>;
  scroll?: { x?: string OR number OR boolean; y?: string OR number OR boolean };
}

// 具体实现
class Writable<T> extends React.Component<WritableProps<T>, WritableStates<T>> {
  constructor(props: WritableProps<T>) {
    super(props);
    const { columns: tableColumns, initData } = props;
    const writableColumns = this.initializeColumns(initializeHiden(tableColumns));
    const genHashoptions = this.initializeHashOptions(writableColumns);
    const rows = this.initializeMarker(initData, genHashoptions);
    this.state = {
      writableColumns,
      genHashoptions,
      rows,
      deletedRows: [],
      selectedRowKeys: [],
    };
  }
  // ... ...
  render() {
    const { scroll } = this.props;
    const { writableColumns, rows, deletedRows, selectedRowKeys } = this.state;
    const rowSelection: TableRowSelection<T> = {
      selectedRowKeys: selectedRowKeys,
      onChange: this.handleRowSelectChange,
    };
    return (<div>
    // ... ...
    </div>)
}
```
属性 | 说明 | 类型 | 默认值
----|------|----- | ----
tableKey| 必填。表格在外层数据对象中所属的键名。  | string | -
columns| 必填。表格列的配置。  | `WritableColumnProps<T & TableLine>[]` | -
outerForm| 必填。需要时应当传入父组件的form属性，以提供数据收集和表单验证功能。 | `WrappedFormUtils<any>` | `undefined`
handdleDelete| 选填。删除选中行时的回调函数，返回当前背删除的行数据对象。  | `(rows: T[]) => void` | -
rules| 选填。表格表单中，单元格的数据验证规则。  | `TFormRules<ValidationRule>` | -
initData| 选填。表格初始化时的默认数据，通常在修改数据时用到。  | `TFormRules<ValidationRule>` | -
scroll| 选填。`antd`的`table`组件所支持的属性，用于设定表格滚动条。  | `{ x?: string OR number OR boolean; y?: string OR number OR boolean }` | -

**columns**二级属性说明
类型定义
```tsx
const gridInputs = {
  disabled: disabledInput,
  hidden: hiddenInput,
  plain: plainInput,
  float: floatInput,
  number: numberInput,
  month: monthPicker,
};

type InputRender =
  | keyof typeof gridInputs
  | ((props: InlineItemProps, inputProps?: Object) => JSX.Element);

interface WritableColumnProps<T> extends StandardTableColumnProps<T> {
  inputRender?: InputRender;
  inputProps?: Object;
}
```
属性说明
属性 | 说明 | 类型 | 默认值
----|------|----- | ----
已包含属性| 参见`antd`中的`Table`组件所支持的属性 | `InputRender` | -
inputRender| 选填。单元格的输入框类型，通常只需传入已定义的类型名字。若`gridInputs`未包含所需的输入框时，也可以直接编写实现方法。 | `InputRender` | -
inputProps| 选填。单元格的输入框所配套的相关参数。参见`antd`中的`Input`组件所支持的参数。 | `Object` | -

## 数据后处理
可编辑表格在`form.validateFields`拿到数据之后，需经过后处理，得到真正的嵌套结构。

1. 使用
```tsx
// src\pages\basedata\socialsecurity\ProductRatio\ProRatioForm.tsx
const handdleConfirm = () => {
  form.validateFields((err, fieldsValue) => {
    const archived = archiveSubItems(fieldsValue);
    API.basedata.productRatio.update.request(archived).then(res => {
      if (!res || res.code !== CODE_SUCCESS)
        return msgCall({ code: 404, msg: res.message || '新增社保产品比例失败' });
    });
    toggleProRatioForm();
  });
};
```

2. 设计概要
```ts
// src\components\Writable\index.tsx
const archiveSubItems = <T extends TableLine>(data: T) => T;
```

## 可编辑表单的使用
1. 基础属性设置


```tsx
// src\pages\basedata\socialsecurity\ProductRatio\ProRatioForm.tsx

import Writable, { archiveSubItems, WritableColumnProps, gridInput } from '@/components/Writable';

type TProductRatioItemDTO = defs.basedata.ProductRatioItemDTO;

// Writable Columns Rules 的定义
const ProRatioItemsRules: TFormRules<ValidationRule> = {
  startMon: [
    {
      required: true,
      message: '请选择起始月',
    },
  ],
  endMon: [
    {
      required: true,
      message: '请选择截至月',
    },
    {
      validator: undefined,
    },
  ],
  epMaxBase: [
    {
      required: true,
      message: '请输入企业最高基数',
    },
  ],
  annualAvgSalary: [
    {
      validator: validateFloat(2),
    },
  ]
};
// Writable Columns 的定义
const ProRatioItemsColumns: WritableColumnProps<TProductRatioItemDTO>[] = [
  {
    title: '编号',
    dataIndex: 'productRatioId',
    inputRender: 'disabled',
    width: 120,
    fixed: 'left',
  },
  {
    title: '起始月',
    dataIndex: 'startMon',
    // 此例用于演示自定义`inputRender`如何编写
    inputRender: (props: InlineItemProps, inputProps?: Object) => {
      // console.log("props in startMon.inputRender:", props)
      // console.log("inputProps in startMon.inputRender:", inputProps)
      const inputProp = { placeholder: '请输入', format: MothPickerFormat, ...inputProps };
      const { options } = props;
      if (options && options.initialValue)
        props.options!.initialValue = toMoment(options.initialValue, inputProp.format);
      return gridInput(props, <MonthPicker {...inputProp} />);
    },
    width: 120,
    fixed: 'left',
  },
  {
    title: '截至月',
    dataIndex: 'endMon',
    inputRender: 'month',
    width: 120,
    fixed: 'left',
  },
  {
    title: '年度平均工资',
    dataIndex: 'annualAvgSalary',
    inputRender: 'number',
  },
  {
    title: '年度最低工资',
    dataIndex: 'annualMinSalary',
    inputRender: 'number',
  },
  {
    title: '详情ID',
    dataIndex: 'detailId',
    inputRender: 'hidden',
    colSpan: 0,
  },
];
```
2. 组件使用


```tsx
// src\pages\basedata\socialsecurity\ProductRatio\ProRatioForm.tsx

import Writable, { archiveSubItems, WritableColumnProps, gridInput } from '@/components/Writable';

type TProductRatioItemDTO = defs.basedata.ProductRatioItemDTO;

const ProRatioFormF: React.FC<ProRatioFormPorps> = props => {

  // 有些验证方法，需要借助 `form`, `this`等只能在调用者内部才能定义的方法
  const DateRangeCheck = (rule: any, value: moment.Moment, callback: Function) => {
    const { field }: { field: string } = rule;
    const startMon = field.replace('endMon', 'startMon');
    const startMonVal = form.getFieldValue(startMon);

    if (!value || momentToStamp(value) < momentToStamp(startMonVal)) {
      callback('结束月份不应小于开始月份');
    } else {
      callback();
    }
  };

  const handdleDelete = (rows: TProductRatioItemDTO[]) => {
    // console.log('rows in ProRatioForm.handdleDelete:', rows);
  };
  // 直接使用序列指派`ProRatioItemsRules`中相关单元的验证方法
  ProRatioItemsRules.endMon[1].validator = DateRangeCheck;
  const handdleConfirm = () => {
    form.validateFields((err, fieldsValue) => {
      const archived = archiveSubItems(fieldsValue);
      // ... ...
      toggleProRatioForm();
    });
  };
  const renderForm = () => {
    return (
      <Form layout="inline" title="社保产品比例" className={styles.popFormBase}>
        // ... ...
      </Form>
    );
  };

  return (
    <Modal
      destroyOnClose
      title={title}
      width={1200}
      visible={onProRatioForm}
      onOk={handdleConfirm}
      onCancel={() => toggleProRatioForm()}
    >
      {renderForm()}
      <Writable<TProductRatioItemDTO>
        tableKey="productRatioItemDTOs"
        outerForm={form}
        // handdleDelete={handdleDelete}
        rules={ProRatioItemsRules}
        columns={ProRatioItemsColumns}
        initData={productRatioForm.productRatioItemDTOs!}
        scroll={\{ x: 1300 \}}
      />
    </Modal>
  );
};
```
可以看到，相比普通的`Standard`组件，`Writable`在使用时，主要是`columns`的定义中多一个`inputRender`属性，此属性指定了最终渲染出的单元格的类型。


## 可编辑表格与弹窗选择的结合

使用示例：
```tsx
// src\pages\emphiresep\SubcontractManage\AddForms\SplitService.tsx

export const itemsColumns: WritableColumnProps<TSubcontractServiceDTO>[] = [
  {
    title: '拆分方分公~司',
    dataIndex: 'svcProviderId',
  },
  {
    title: '拆分方客服',
    dataIndex: 'csId',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    inputRender: 'text',
  },
];

class SplitServiceF extends React.Component<SplitServiceProps> {
  handleSubmit = () => {
    const { form, dispatch, forward } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      const archived = archiveSubItems(fieldsValue);
      const lines = archived[tableKey] as object[];
      if (!lines || lines.length === 0)
        return resErrorMsg(undefined, '未添加任何拆分方信息，拒绝提交');
      const values = { ...fieldsValue, ...archived };
      dispatch!({
        type: 'subcontract/setScSplitServiceForm',
        payload: values,
      });
      forward(stepForward.forward);
    });
  };

  forgeInputRender = (
    itemsColumnses: WritableColumnProps<TSubcontractServiceDTO>[],
  ): WritableColumnProps<TSubcontractServiceDTO>[] => {
    const { scSplitServiceForm } = this.props;
    const svcProvider = (
      props: InlineItemProps,
      inputProps?: Object,
      record?: TSubcontractServiceDTO,
    ) => {
      const { dataIndex } = props;
      const siblingKey = dataIndex.replace('svcProviderId', '');
      const viewKey = siblingKey + 'svcProviderName';
      const areaIdKey = siblingKey + 'areaId';
      const branchIdKey = siblingKey + 'branchId';
      const inputProp = { placeholder: '请选择', ...inputProps };
      return gridInput(
        props,
        <BranchPop
          modalwidth={800}
          rowKey={dataIndex}
          viewKey={viewKey}
          rowValue={`${dataIndex}-${areaIdKey}-${branchIdKey}`}
          keyMap={\{
            [dataIndex]: 'branchId',
            [viewKey]: 'branchName',
            [areaIdKey]: 'areaId',
            [branchIdKey]: 'branchId',
          \}}
          outerForm={this.props.form}
          fieldOptions={\{ ...props.options, initialValue: scSplitServiceForm![viewKey] }\}
          rowValueData={\{
            [dataIndex]: scSplitServiceForm![dataIndex],
            [viewKey]: scSplitServiceForm![viewKey],
          }\}
          {...inputProp}
        />,
      );
    };

    const cs = (props: InlineItemProps, inputProps?: Object) => {
      const { dataIndex, outerForm } = props;
      const viewKey = dataIndex.replace('csId', 'csName');
      const inputProp = { placeholder: '请选择', ...inputProps };
      const siblingKey = dataIndex.replace('csId', '');
      const areaIdKey = siblingKey + 'areaId';
      const svcProviderNameKey = siblingKey + 'svcProviderName';
      const deptIdKey = siblingKey + 'deptId';
      const svcProviderNameVlaue = outerForm.getFieldValue(svcProviderNameKey);
      const areaIdVlaue = outerForm.getFieldValue(areaIdKey);
      return gridInput(
        props,
        <CsPop
          modalwidth={800}
          rowKey={dataIndex}
          viewKey={viewKey}
          rowValue={`${dataIndex}-${deptIdKey}`}
          fixedValues={\{
            [svcProviderNameKey]: svcProviderNameVlaue,
            [areaIdKey]: areaIdVlaue,
          }\}
          keyMap={\{
            [dataIndex]: 'userId',
            [viewKey]: 'chnName',
            [svcProviderNameKey]: 'branchName',
            [areaIdKey]: 'areaId',
            [deptIdKey]: 'deptId',
          }\}
          outerForm={this.props.form}
          fieldOptions={\{ ...props.options, initialValue: scSplitServiceForm![viewKey] }\}
          rowValueData={\{
            [dataIndex]: scSplitServiceForm![dataIndex],
            [viewKey]: scSplitServiceForm![viewKey],
          }\}
          {...inputProp}
        />,
      );
    };
    const newItemsColumns = [...itemsColumnses];
    newItemsColumns[0].inputRender = svcProvider;
    newItemsColumns[1].inputRender = cs;
    return newItemsColumns;
  };

  renderForm() {
    const { form, scSplitServiceForm } = this.props;
    const newItemsColumns = this.forgeInputRender(itemsColumns);
    const splitServices = scSplitServiceForm![tableKey] as TSubcontractServiceDTO[];
    return (
      <Writable<TSubcontractServiceDTO>
        tableKey={tableKey}
        outerForm={form}
        rules={scServiceItemsRules}
        columns={newItemsColumns}
        initData={splitServices}
      />
    );
  }

  render() {
    const { forward } = this.props;
    return (
      <Card>
        {this.renderForm()}
        // ... ...
      </Card>
    );
  }
}
```

* `itemsColumns` 是单行表单的列定义。
* `forgeInputRender` 是配置表格内弹窗的核心方法。 `svcProvider` 和 `cs` 是两个弹窗的设置示例。可以看到，主要工作在于设置`rowValue` 和 `keyMap` ，所有传入`Pop` 组件的属性，需在 `keyMap` ` 中设置正确的别名映射关系。
