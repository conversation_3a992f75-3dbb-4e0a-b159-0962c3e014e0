{"agentWageIdCardNumManage": "孙尚阳 纯代发人员证件号码修改 | ./payroll/AgentWageIdCardNumManage/index", "editEmpBankCard": "孙尚阳 纯代发人员银行卡信息维护 | ./payroll/EditEmpBankCard/index", "ndhsqj.QueryCalAnnuaData": "严小强 年度汇算清缴 汇算清缴数据查询 | ./payroll/CalAnnual/QueryCalAnnuaData", "ndhsqj.CalAnnualDeclareImp": "严小强 年度汇算清缴 申报结果导入 | ./payroll/CalAnnual/CalAnnualDeclareImp", "ndhsqj.CalAnnualResultImp": "严小强 年度汇算清缴 汇算结果导入 | ./payroll/CalAnnual/CalAnnualResultImp", "ndhsqj.CalAnnualImportEmp": "严小强 年度汇算清缴 汇算清缴人员导入 | ./payroll/CalAnnual/CalAnnualImportEmp", "ndhsqj.KJYWRGL": "孙尚阳 汇算扣缴义务人管理 | ./payroll/ndhsqj/KJYWRGL/index", "ndhsqj.hsqjkhgl": "孙尚阳 汇算清缴客户管理 | ./payroll/ndhsqj/hsqjkhgl/index", "deduction.queryUploadDeductionDetail": "孙尚阳 雇员专项抵扣月度查询 | ./payroll/deduction/QueryUploadDeductionDetail/index", "deduction.taxReportHugeCust": "孙尚阳 薪资档案增员报表（大户） | ./payroll/deduction/TaxReportHugeCust/index", "deduction.taxReportProvider": "孙尚阳 薪资档案增员报表（供应商） | ./payroll/deduction/TaxReportProvider/index", "deduction.taxReportIndependent": "孙尚阳 薪资档案增员报表（单立户） | ./payroll/deduction/TaxReportIndependent/index", "deduction.custTaxReport": "孙尚阳 雇员专项抵扣报表（按客户） | ./payroll/deduction/CustTaxReport/index", "deduction.taxReportSupply": "孙尚阳 增员补充报表 | ./payroll/deduction/TaxReportSupply/index", "deduction.uploadTaxDeductionLarge": "孙尚阳 上传专项抵扣数据（大户） | ./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionLarge", "deduction.uploadTaxDeductionSingle": "孙尚阳 上传专项抵扣数据（单立户） | ./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionSingle", "deduction.uploadTaxDeductionSupplier": "孙尚阳 上传专项抵扣数据（供应商） | ./payroll/deduction/UploadTaxDeduction/UploadTaxDeductionSupplier", "deduction.specialDeductionReport": "孙尚阳 专项抵扣上传报表 | ./payroll/deduction/SpecialDeductionReport/index", "queryPayRollClass": "孙尚阳 薪资类别 | ./payroll/QueryPayRollClass/index", "payRollClassItemManage": "孙尚阳 薪资项目维护 | ./payroll/PayRollClassItemManage/index", "queryPayRollClassItem": "孙尚阳 薪资项目查询 | ./payroll/QueryPayRollClassItem/index", "queryTaxRate": "陈国祥 税率表查看 | ./payroll/taxrate/QueryTaxRate", "queryWithholdAgent": "陈国祥 扣缴义务人维护 | ./payroll/withholdAgent/QueryWithholdAgent", "bankcardQuery": "陈国祥 银行卡及小合同信息查询 | ./payroll/bankcardqry/BankcardQuery", "XZFFPC.HFRYBB": "孙尚阳 缓发人员报表 | ./payroll/XZFFPC/HFRYBB/index", "XZFFPC.YBFFRYBB": "孙尚阳 永不发放人员报表 | ./payroll/XZFFPC/YBFFRYBB/index", "XZFFPCXN.XJFFPCXN": "严小强 新建发放批次(虚拟) | ./payroll/XZFFPCXN/XJFFPCXN/index", "bswjdc": "孙尚阳 报税文件导出 | ./payroll/bswjdc/index", "BatchFeedBackSendResult": "严小强 网银包生成情况汇总表 | ./payroll/BatchFeedBackSendResult", "EmployeeInfoAgentWageManage": "严小强 纯代发人员信息维护 | ./payroll/EmployeeInfoAgentWageManage", "QueryPayRollResult": "严小强 薪资结果查询 | ./payroll/QueryPayRollResult", "QueryPaySendStage": "严小强 薪资发放阶段查询 | ./payroll/QueryPaySendStage", "rptPsnSetFailureManages": "严小强 自动失效薪资档案报表 | ./payroll/rptPsnSetFailureManages", "XZDFS.SENDMAIL": "孙尚阳 发送薪资单 | ./payroll/XZDFS/SENDMAIL/index", "distributePayrollSpecialiat": "孙尚阳 分配薪资专员 | ./payroll/DistributePayrollSpecialiat/index", "queryEosWageData": "孙尚阳 易OS薪资数据 | ./payroll/QueryEosWageData/index", "XZDFS.SENDMAILRESULT": "孙尚阳 发送历史查询 | ./payroll/XZDFS/SENDMAILRESULT/index", "XZFFPCXN.SZFFJGXN": "严小强 设置发放结果(虚拟) | ./payroll/XZFFPCXN/SZFFJGXN/index", "XZFFPCXN.XZZFCXXN": "孙尚阳 薪资支付查询(虚拟) | ./payroll/XZFFPCXN/XZZFCXXN/index", "XZFFPCXN.HFRYBBXN": "孙尚阳 缓发人员报表(虚拟) | ./payroll/XZFFPCXN/HFRYBBXN/index", "XZFFPCXN.TaxPayRefund": "孙尚阳 单立户发放结果反馈 | ./payroll/XZFFPCXN/TaxPayRefund/index", "archives.CheckHrieDtManages": "孙尚阳 核查受雇日期 | ./payroll/archives/CheckHrieDtManages/index", "archives.QueryCheckHireDt": "孙尚阳 核查受雇日期查询 | ./payroll/archives/QueryCheckHireDt/index", "archives.CheckEmpTypeManages": "孙尚阳 核查从业类型 | ./payroll/archives/CheckEmpTypeManages/index", "archives.QueryEmpType": "孙尚阳 核查从业类型查询 | ./payroll/archives/QueryEmpType/index", "DataInterfaceManage": "赵煜颢 数据导入 | ./payroll/DataInterfaceManage", "PureDataInterfaceManage": "赵煜颢 纯代发导入接口 | ./payroll/PureDataInterfaceManage", "PayRollArchivesManage": "严小强 薪资档案 | ./payroll/PayRollArchivesManage/index", "CountPayManage": "严小强 薪资计算 | ./payroll/CountPayManage/index", "XZFFPC.XJFFPC": "严小强 新建发放批次 | ./payroll/XZFFPC/XJFFPC/index", "XZFFPC.QueryPayResult": "孙尚阳 薪资支付查询 | ./payroll/XZFFPC/QueryPayResult/index", "XZFFPC.firstFeedBackSendManages": "孙尚阳 首次反馈发放结果 | ./payroll/XZFFPC/FirstFeedBackSendManages/index", "XZFFPC.secondFeedBackSendManages": "刘夏梅 二次发放结果反馈 | ./payroll/XZFFPC/SecondFeedBackSendManages/index", "XZFFPC.updateSuccessManages": "孙尚阳 发放成功记录调整 | ./payroll/XZFFPC/UpdateSuccessManages/index", "XZFFPC.updateBatchPayRollSpecialiat": "刘夏梅 发放批次变更薪资专员 | ./payroll/XZFFPC/UpdateBatchPayRollSpecialiat/index", "XZFFPC.SetPayResult": "严小强 设置发放结果 | ./payroll/XZFFPC/SetPayResult/index", "XZFFPC.QueryWageAgainSend": "刘夏梅 二次发放查询 | ./payroll/XZFFPC/QueryWageAgainSend/index", "send.PayTaxBatchManage": "严小强 薪资报税批次 报税批次管理 | ./payroll/send/PayTaxBatchManage", "XZFFPC.WhiteList": "刘夏梅 银企直连客户白名单维护 | ./payroll/XZFFPC/WhiteList/index", "XZFFPC.BlackList": "刘夏梅 银企直连客户黑名单维护 | ./payroll/XZFFPC/BlackList/index", "XZFFPC.SpUser": "刘夏梅 银企直连送盘人员维护 | ./payroll/XZFFPC/SpUser/index", "XZFFPC.RefundQuery": "刘夏梅 退票查询 | ./payroll/XZFFPC/RefundQuery/index", "XZFFPC.RefundTaskQuery": "刘夏梅 退票任务查询 | ./payroll/XZFFPC/RefundTaskQuery/index", "withholdAgentLimitMaintance": "刘夏梅 扣缴义务人限额维护 | ./payroll/WithholdAgentLimitMaintance", "payTaxes": "孙尚阳 税优惠人员信息维护（6W） | ./payroll/PayTaxes", "JSDJ.uptEmpInfoManages": "孙尚阳 金税对接 人员信息变更 | ./payroll/JSDJ/uptEmpInfoManages", "JSDJ.empDeclareReduce": "孙尚阳 金税对接 人员报离 | ./payroll/JSDJ/empDeclareReduce", "JSDJ.QueryEmpSubmitInfo": "孙尚阳 报税人员信息查询 | ./payroll/JSDJ/QueryEmpSubmitInfo/index", "JSDJ.QueryEmpSubmitTask": "孙尚阳 人员报送任务查询 | ./payroll/JSDJ/QueryEmpSubmitTask/index", "JSDJ.ReliefItemUpload": "孙尚阳 减免事项上传 | ./payroll/JSDJ/ReliefItemUpload/index", "JSDJ.QueryReliefItem": "孙尚阳 减免事项明细查询 | ./payroll/JSDJ/QueryReliefItem/index", "JSDJ.HealthItemUpload": "孙尚阳 商业健康保险附表上传 | ./payroll/JSDJ/HealthItemUpload/index", "JSDJ.QueryHealthItem": "孙尚阳 商业健康保险附表查询  | ./payroll/JSDJ/QueryHealthItem/index", "JSDJ.EndowmentItemUpload": "孙尚阳 税延养老保险附表上传 | ./payroll/JSDJ/EndowmentItemUpload/index", "JSDJ.QueryEndowmentItem": "孙尚阳 税延养老保险附表查询  | ./payroll/JSDJ/QueryEndowmentItem/index", "JSDJ.DonationdeducItemUpload": "孙尚阳 捐赠扣除附表上传  | ./payroll/JSDJ/DonationdeducItemUpload/index", "JSDJ.QueryDonationdeducItem": "孙尚阳 捐赠扣除附表查询  | ./payroll/JSDJ/QueryDonationdeducItem/index", "JSDJ.TriPartyQuery": "孙尚阳 三方协议获取任务查询  | ./payroll/JSDJ/TriPartyQuery/index", "JSDJ.TaxPayQuery.DH": "孙尚阳 税款缴纳查询（大户）  | ./payroll/JSDJ/TaxPayQuery/DH", "JSDJ.TaxPayQuery.DLH": "孙尚阳 税款缴纳查询（单立户）  | ./payroll/JSDJ/TaxPayQuery/DLH", "JSDJ.TaxPayQuery.GYS": "孙尚阳 税款缴纳查询（供应商）  | ./payroll/JSDJ/TaxPayQuery/GYS", "JSDJ.UngenerateTaxPayMission": "孙尚阳 未生成税款缴纳任务列表  | ./payroll/JSDJ/UngenerateTaxPayMission/index", "JSDJ.TaxDiffManages": "孙尚阳 薪资核算任务查询  | ./payroll/JSDJ/TaxDiffManages/index", "JSDJ.TaxPaymentDownload.DH": "孙尚阳 完税证明下载（大户）  | ./payroll/JSDJ/TaxPaymentDownload/DH", "JSDJ.TaxPaymentDownload.DL": "孙尚阳 完税证明下载（单立户）  | ./payroll/JSDJ/TaxPaymentDownload/DLH", "JSDJ.TaxPaymentDownload.WB": "孙尚阳 完税证明下载（供应商）  | ./payroll/JSDJ/TaxPaymentDownload/GYS", "JSDJ.TaxDeclarationManages.DH": "孙尚阳 金税对接 个税申报（大户） | ./payroll/JSDJ/TaxDeclarationManages/DH", "JSDJ.TaxDeclarationManages.DL": "孙尚阳 金税对接 个税申报（单立户） | ./payroll/JSDJ/TaxDeclarationManages/DLH", "JSDJ.TaxDeclarationManages.WB": "孙尚阳 金税对接 个税申报（供应商） | ./payroll/JSDJ/TaxDeclarationManages/GYS", "JSDJ.UngenerateTaxPay": "孙尚阳 金税对接 未生成个税申报任务列表 | ./payroll/JSDJ/UngenerateTaxPay", "JSDJ.SpecialDeduction": "孙尚阳 金税对接 专项附加扣除任务查询 | ./payroll/JSDJ/AttachPay", "JSDJ.TaxInviteManages": "孙尚阳 确认人员 |  ./payroll/JSDJ/TaxConfirmInviteesManages", "XZFFPC.dailyAccount": "孙尚阳 每日对账查询 | ./payroll/XZFFPC/DailyAccount/index", "updateXZDALZDate": "孙尚阳 扣缴义务人限额维护 | ./payroll/UpdateXZDALZDate", "JSDJ.WithholdAgentWhite": "孙尚阳 金税对接 扣缴义务人白名单 | ./payroll/JSDJ/WithholdAgentWhite", "JSDJ.payTaxSearch": "孙尚阳 金税对接 6W直接扣除人员任务查询 | ./payroll/JSDJ/payTaxSearch", "JSDJ.goldenTax.QueryEndowmentItem": "孙尚阳 金税对接 6W直接扣除人员获取及确认 | ./payroll/JSDJ/payTaxConfrim", "JSDJ.ApplyFeedback": "孙尚阳 金税对接 申报反馈任务查询 | ./payroll/JSDJ/ApplyFeedback", "JSDJ.ExcludeTaxEmp": "孙尚阳 金税对接 排除报税人员列表 | ./payroll/JSDJ/ExcludeTaxEmp", "JSDJ.inital.Init": "孙尚阳 金税对接 金税对接初始化 | ./payroll/JSDJ/initial/Init", "JSDJ.inital.employee": "孙尚阳 金税对接 金税对接人员初始化 | ./payroll/JSDJ/initial/Employee", "JSDJ.inital.TaxData": "孙尚阳 金税对接 金税对接报税数据初始化 | ./payroll/JSDJ/initial/TaxData", "phoneReminder": "孙尚阳 报税手机号码补充明细查询 | ./payroll/phoneReminder", "FSCD.XZCDRWCX": "刘夏梅 薪资冲抵任务查询 | ./payroll/FSCD/XZCDRWCX", "FSCD.XZCDLCCX": "刘夏梅 薪资冲抵流程查询 | ./payroll/FSCD/XZCDLCCX", "XZFFPC.XXFFPCFKCKXX": "严小强 线下发放批次反馈出款信息 | ./payroll/XZFFPC/XXFFPCFKCKXX", "QueryEosWageUpload.QueryEosWage": "严小强 标准客户薪资导入（查询） | ./payroll/QueryEosWageUpload/StandardCustomeSalaryImport/index", "QueryEosWageUpload.QueryEosWageKf": "严小强 标准客户薪资导入（客服） | ./payroll/QueryEosWageUpload/QueryEosWageKf/index", "QueryEosWageUpload.QueryEosWageXz": "严小强 标准客户薪资导入（薪资） | ./payroll/QueryEosWageUpload/QueryEosWageXz/index", "JSDJ.TaxDownloadTask.DH": "孙尚阳 金税对接 申报明细获取（大户） | ./payroll/JSDJ/TaxDownloadTask/DH", "JSDJ.TaxDownloadTask.DL": "孙尚阳 金税对接 申报明细获取（单立户） | ./payroll/JSDJ/TaxDownloadTask/DLH", "JSDJ.TaxDownloadTask.WB": "孙尚阳 金税对接 申报明细获取（供应商） | ./payroll/JSDJ/TaxDownloadTask/GYS", "LSDZHD.NoFlowPayerMaintenance": "刘夏梅 不获取流水付款方维护 | ./payroll/LSDZHD/NoFlowPayerMaintenance/index", "LSDZHD.FlowReceiptMaintenance": "刘夏梅 流水回单账号维护 | ./payroll/LSDZHD/FlowReceiptMaintenance/index", "LSDZHD.batchReceiptDownload": "严小强 批量回单下载 | ./payroll/LSDZHD/BatchReceiptDownload/index", "LSDZHD.queryReceiptByEmployee": "严小强 按照雇员查询回单 | ./payroll/LSDZHD/QueryReceiptByEmployee/index", "LSDZHD.employeeReceiptTaskQuery": "严小强 雇员回单任务查询 | ./payroll/LSDZHD/EmployeeReceiptTaskQuery/index", "LSDZHD.getTask.autoGetTask": "孙尚阳 自动流水获取任务 | ./payroll/LSDZHD/getTask/autoGetTask/index", "LSDZHD.getTask.getAssociation": "孙尚阳 回单及流水关联关系获取 | ./payroll/LSDZHD/getTask/getAssociation/index", "LSDZHD.autoFlowUploadRecord": "孙尚阳 自动流水上传记录查询 | ./payroll/LSDZHD/AutoFlowUploadRecord/index", "send.ExportTaxBureauDataLarge": "刘夏梅 薪资报税批次 根据名单导出税局要求数据（大户） | ./payroll/send/ExportTaxBureauDataLarge", "send.ExportTaxBureauDataSingle": "刘夏梅 薪资报税批次 根据名单导出税局要求数据（单立户） | ./payroll/send/ExportTaxBureauDataSingle", "send.ExportTaxBureauDataSupplier": "刘夏梅 薪资报税批次 根据名单导出税局要求数据（供应商） | ./payroll/send/ExportTaxBureauDataSupplier", "JSDJ.TaxPayRefund.DH": "孙尚阳 退付手续费（大户）  | ./payroll/JSDJ/ServiceCharge/DH", "JSDJ.TaxPayRefund.DL": "孙尚阳 退付手续费（单立户）  | ./payroll/JSDJ/ServiceCharge/DLH", "JSDJ.TaxPayRefund.WB": "孙尚阳 退付手续费（供应商）  | ./payroll/JSDJ/ServiceCharge/GYS", "XZFFPC.querySpqywh": "严小强 送盘区域维护  | ./payroll/XZFFPC/QuerySpqywh/index", "XZFFPC.queryXzzfqywh": "严小强 薪资支付审核区域维护  | ./payroll/XZFFPC/QueryXzzfqywh/index"}