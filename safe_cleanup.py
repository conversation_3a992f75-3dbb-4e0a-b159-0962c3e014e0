#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Safe Cleanup Script for Windows C: Drive

This script performs safe cleanup operations on the Windows C: drive,
including temporary files, browser cache, and recycle bin cleanup.
"""

import os
import sys
import glob
import shutil
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('safe_cleanup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def get_temp_directories():
    """Get list of temporary directories to clean"""
    temp_dirs = []
    
    # System temporary directory
    temp_dirs.append(os.environ.get('TEMP', ''))
    temp_dirs.append(os.environ.get('TMP', ''))
    
    # Windows temporary directory
    win_temp = os.path.join(os.environ.get('SYSTEMROOT', 'C:\\Windows'), 'Temp')
    if os.path.exists(win_temp):
        temp_dirs.append(win_temp)
    
    # User temporary directories
    user_profile = os.environ.get('USERPROFILE', '')
    if user_profile:
        user_temp = os.path.join(user_profile, 'AppData', 'Local', 'Temp')
        if os.path.exists(user_temp):
            temp_dirs.append(user_temp)
    
    # Filter out empty paths
    return [d for d in temp_dirs if d and os.path.exists(d)]

def clean_temporary_files():
    """Clean temporary files from system and user directories"""
    logger.info("Starting temporary files cleanup...")
    temp_dirs = get_temp_directories()
    total_cleaned = 0
    
    for temp_dir in temp_dirs:
        logger.info(f"Cleaning temporary files in: {temp_dir}")
        try:
            # Count files before cleanup
            file_count_before = sum([len(files) for r, d, files in os.walk(temp_dir)])
            
            # Clean files (but not directories)
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        # Skip files that are in use
                        if not os.access(file_path, os.W_OK):
                            continue
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        total_cleaned += file_size
                    except (PermissionError, OSError) as e:
                        # Skip files that can't be deleted
                        logger.debug(f"Skipped file {file_path}: {e}")
                        continue
            
            # Count files after cleanup
            file_count_after = sum([len(files) for r, d, files in os.walk(temp_dir)])
            logger.info(f"Temporary files cleanup in {temp_dir}: {file_count_before - file_count_after} files removed")
            
        except Exception as e:
            logger.error(f"Error cleaning temporary files in {temp_dir}: {e}")
    
    logger.info(f"Temporary files cleanup completed. Total cleaned: {total_cleaned / (1024*1024):.2f} MB")
    return total_cleaned

def clean_browser_cache():
    """Clean browser cache files"""
    logger.info("Starting browser cache cleanup...")
    total_cleaned = 0
    
    # Browser cache directories
    user_profile = os.environ.get('USERPROFILE', '')
    if not user_profile:
        logger.warning("Could not determine user profile directory")
        return total_cleaned
    
    browser_cache_paths = [
        os.path.join(user_profile, 'AppData', 'Local', 'Google', 'Chrome', 'User Data', 'Default', 'Cache'),
        os.path.join(user_profile, 'AppData', 'Local', 'Google', 'Chrome', 'User Data', 'Default', 'GPUCache'),
        os.path.join(user_profile, 'AppData', 'Local', 'Mozilla', 'Firefox', 'Profiles'),
        os.path.join(user_profile, 'AppData', 'Local', 'Microsoft', 'Edge', 'User Data', 'Default', 'Cache'),
    ]
    
    for cache_path in browser_cache_paths:
        if not os.path.exists(cache_path):
            continue
            
        logger.info(f"Cleaning browser cache in: {cache_path}")
        
        try:
            # For Firefox, we need to find the profile directories
            if 'Firefox' in cache_path:
                profiles = glob.glob(os.path.join(cache_path, '*', 'cache2'))
                for profile_cache in profiles:
                    if os.path.exists(profile_cache):
                        cache_size = sum(os.path.getsize(os.path.join(dirpath, filename)) 
                                       for dirpath, dirnames, filenames in os.walk(profile_cache) 
                                       for filename in filenames)
                        shutil.rmtree(profile_cache, ignore_errors=True)
                        total_cleaned += cache_size
                        logger.info(f"Cleaned Firefox cache: {cache_size / (1024*1024):.2f} MB")
            else:
                # For other browsers, clean the entire cache directory
                cache_size = sum(os.path.getsize(os.path.join(dirpath, filename)) 
                               for dirpath, dirnames, filenames in os.walk(cache_path) 
                               for filename in filenames)
                shutil.rmtree(cache_path, ignore_errors=True)
                total_cleaned += cache_size
                logger.info(f"Cleaned browser cache: {cache_size / (1024*1024):.2f} MB")
                
        except Exception as e:
            logger.error(f"Error cleaning browser cache in {cache_path}: {e}")
    
    logger.info(f"Browser cache cleanup completed. Total cleaned: {total_cleaned / (1024*1024):.2f} MB")
    return total_cleaned

def clean_recycle_bin():
    """Clean recycle bin"""
    logger.info("Starting recycle bin cleanup...")
    total_cleaned = 0
    
    try:
        # Use Windows built-in command to empty recycle bin
        import subprocess
        result = subprocess.run(['powershell', '-Command', 'Clear-RecycleBin -Force'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("Recycle bin cleared successfully")
        else:
            logger.warning(f"Could not clear recycle bin: {result.stderr}")
    except Exception as e:
        logger.error(f"Error cleaning recycle bin: {e}")
    
    logger.info(f"Recycle bin cleanup completed. Total cleaned: {total_cleaned / (1024*1024):.2f} MB")
    return total_cleaned

def is_safe_to_delete(file_path):
    """Check if a file is safe to delete"""
    # Critical system directories that should never be deleted
    critical_paths = [
        os.environ.get('SYSTEMROOT', 'C:\\Windows'),
        os.path.join(os.environ.get('SYSTEMROOT', 'C:\\Windows'), 'System32'),
        os.path.join(os.environ.get('SYSTEMROOT', 'C:\\Windows'), 'SysWOW64'),
        'C:\\Program Files',
        'C:\\Program Files (x86)',
    ]
    
    # Normalize paths for comparison
    normalized_path = os.path.normpath(file_path).lower()
    
    for critical_path in critical_paths:
        if critical_path and normalized_path.startswith(os.path.normpath(critical_path).lower()):
            return False
    
    return True

def main():
    """Main function to run safe cleanup"""
    logger.info("Starting safe cleanup mode for Windows C: drive")
    logger.info("=" * 50)
    
    # Check if running on Windows
    if os.name != 'nt':
        logger.error("This script is designed for Windows systems only")
        return
    
    # Check if running with administrator privileges
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            logger.warning("Not running with administrator privileges. Some cleanup operations may be limited.")
    except:
        logger.warning("Could not determine administrator status. Some cleanup operations may be limited.")
    
    total_cleaned = 0
    
    try:
        # Clean temporary files
        temp_cleaned = clean_temporary_files()
        total_cleaned += temp_cleaned
        
        # Clean browser cache
        cache_cleaned = clean_browser_cache()
        total_cleaned += cache_cleaned
        
        # Clean recycle bin
        recycle_cleaned = clean_recycle_bin()
        total_cleaned += recycle_cleaned
        
    except KeyboardInterrupt:
        logger.info("Cleanup interrupted by user")
        return
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        return
    
    logger.info("=" * 50)
    logger.info(f"Safe cleanup completed. Total space cleaned: {total_cleaned / (1024*1024):.2f} MB")
    logger.info("System should continue to operate normally.")

if __name__ == "__main__":
    main()