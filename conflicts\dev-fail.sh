###
 # @Author: 侯成
 # @Email: <EMAIL>
 # @Date: 2021-02-24 16:47:00
 # @LastAuthor: 侯成
 # @LastTime: 2021-02-24 16:51:27
 # @message: message
### 

cp: cannot stat 'src/pages/payroll/PayRollArchivesManage/Forms/MissionHistoryDetail.tsx': No such file or directory
cp: cannot stat 'src/pages/Mrpay/index.tsx': No such file or directory
cp: cannot stat 'src/pages/payroll/PayRollArchivesManage/Forms/MissionHistory.tsx': No such file or directory
cp: cannot stat 'src/pages/User/clientLogin.less': No such file or directory
cp: cannot stat 'src/pages/payroll/JSDJ/TaxDeclarationManages/components/AddExportModal.tsx': No such file or directory
cp: cannot stat 'src/pages/payroll/JSDJ/TaxDeclarationManages/components/MaintainExport.tsx': No such file or directory
cp: cannot stat 'src/pages/payroll/JSDJ/TaxDeclarationManages/components/SendHistory.tsx': No such file or directory
cp: cannot stat 'src/pages/payroll/JSDJ/TaxDeclarationManages/components/ShowFailureModal.tsx': No such file or directory
devs.sh: line 231: }/SocialApplyReceivable.tsx: No such file or directory
cp: failed to get attributes of 'src/utils/welfaremanage': No such file or directory

cp --path src/pages/empwelfare/Socialmanage/SocialApply/SocialApplyReceivable.tsx ../featurejs/