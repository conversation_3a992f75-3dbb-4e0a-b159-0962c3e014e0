import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/role/insertRoleUser
     * @desc 插入用户的角色
插入用户的角色
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.CommonResponse();
export const url = '/rhro-service-1.0/role/insertRoleUser:POST';
export const initialUrl = '/rhro-service-1.0/role/insertRoleUser';
export const cacheKey = '_role_insertRoleUser_POST';
export async function request(
  data: Array<defs.admin.RoleUser>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/role/insertRoleUser`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: Array<defs.admin.RoleUser>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/role/insertRoleUser`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
