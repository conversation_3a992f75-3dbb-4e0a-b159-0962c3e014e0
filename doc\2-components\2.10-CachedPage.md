<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-07-13 10:36:02
 * @LastAuthor: 侯成
 * @LastTime: 2020-07-13 11:22:34
 * @message: message
-->

# 组件 CachedPage

## 调用示例

```tsx
const columns: any[] = [
  {
    title: '所在省份',
    dataIndex: 'provinceId',
  },
  {
    title: '城市',
    dataIndex: 'cityName',
  },
  {
    title: '城市英文名称',
    dataIndex: 'cityEnglishName',
  },
  {
    title: '所属上级城市',
    dataIndex: 'governingCityName',
  },
  {
    title: '城市拼音码',
    dataIndex: 'pinyinCode',
  },
  {
    title: '城市区号',
    dataIndex: 'cityCode',
  },
  {
    title: '是否被使用',
    dataIndex: 'cityCode1',
  },
  {
    title: '城市系数',
    dataIndex: 'cityFactor',
  },
];
const formColumns = [];

const CityInfo: React.FC<CityInfoprops> = (props) => {
  const onAdd = () => {
    searchNew();
  };
  const renderButtons = (option) => {
    const { request } = option;
    return (
      <ButtonCol>
        <Button onClick={ononAddClick}>新增</Button>
      </ButtonCol>
    );
  };

  const onShift = (action: 'add' | 'update' | 'delete', pload: POJO[] | POJO) => {};

  <CachedPage
    onShift={onShift}
    renderButtons={renderButtons}
    rowKey="c
  ityName"
    columns={columns}
    formColumns={formColumns}
    service={service}
  />;
};

const CachedPage: React.FC<CityInfoprops> = (props) => {
  const { onShift } = props;
  const search = () => {
    // handdle searh
  };

  return (
    <Card>
      <CachedForm onShift={onShift} search={search} formColumns={formColumns} />
      <CachedTable
        rowKey="cityName"
        rowSelectType="radio"
        search={search}
        columns={columns}
        service={API.basedata.city.queryCityList}
      />
    </Card>
  );
};
```

## CachedForm 实现建议

```tsx
const CachedForm: React.FC<CachedTableProps> = (props) => {
  const { service, formColumns, renderButtons, ...rest } = props;
  const [option] = StandardTable.useStandardTable({ service });
  return (
    <React.fragment>
      <EnumrateForm initialValues={option} formColumns={formColumns} />

      {renderButtons ? renderButtons(options) : <EnumrateButon />}
    </React.fragment>
  );
};
```

## CachedTable 实现建议

```tsx
const CachedTable: React.FC<CachedTableProps> = (props) => {
  const { service, ...rest } = props;
  const [table] = StandardTable.useStandardTable({ service });
  return <StandardTable {...rest} {...(table as any)} />;
};
```

## EnumrateForm 实现建议

```tsx
const EnumrateForm: React.FC<CachedTableProps> = (props) => {
  const { formColumns, ...rest } = props;
  const [table] = StandardTable.useStandardTable({ service });
  return (
    <React.fragment>
      <RowElement>
        {formColumns.map((item: EditeFormProps) => {
          return <ColumnForms key={item.fieldName} {...item} />;
        })}
      </RowElement>
    </React.fragment>
  );
};
```

## EnumrateButon 实现建议

```tsx
const EnumrateButon: React.FC<CachedTableProps> = (props) => {
  const { buttonColumns, ...rest } = props;
  const [table] = StandardTable.useStandardTable({ service });
  return (
    <React.fragment>
      <RowElement>
        {buttonColumns.map((item: EditeFormProps) => {
          return <ColumnForms key={item.fieldName} {...item} />;
        })}
      </RowElement>
    </React.fragment>
  );
};
```
