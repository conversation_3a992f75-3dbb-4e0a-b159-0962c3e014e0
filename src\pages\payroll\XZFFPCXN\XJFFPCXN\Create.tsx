import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { WritableInstance } from '@/components/Writable/hooks/useWritable';
import { msgErr } from '@/utils/methods/message';
import { stdMonthFormatMoDash } from '@/utils/methods/times';
import { getCurrentUser } from '@/utils/model';
import { WritableColumnProps } from '@/utils/writable/types';
import { Button, Modal, Typography } from 'antd';
import { isEmpty } from 'lodash';
import React, { useState } from 'react';
import PaySendDetail from './components/PaySendDetail';
import UpdatePayRollSendVirtualBatchWin from './components/UpdatePayRollSendVirtualBatchWin';

interface CreateProps {
  onAdded: (param: string) => void;
}

let _options: WritableInstance | undefined = undefined;
const Create = (props: CreateProps) => {
  const service = API.payroll.payBatchVirtual.queryPaySend;

  const currentUser = getCurrentUser();
  const [singleRow, setSingleRow] = useState<POJO>({});

  const [addVisible, setAddVisible] = useState<boolean>(false);

  const [detailVisible, setDetailVisible] = useState<boolean>(false);

  const [nextParam, setNextParam] = useState<POJO>({});

  const sendStatusList = new Map<string, string>([
    ['0', '未发放'],
    ['1', '发放中'],
    ['2', '部分发放'],
    ['3', '发放完成'],
  ]);

  const formColumns: EditeFormProps[] = [
    {
      label: '客户账单年月',
      fieldName: 'billMonth',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '薪资计税年月',
      fieldName: 'taxMonth',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '客户',
      fieldName: 'custId',
      inputRender: () => <CustomerPop />,
    },
    {
      label: '工资所属年月起',
      rules: [{ required: true, message: '请选择工资所属年月起' }],
      fieldName: 'sendMonthFrom',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '工资所属年月止',
      rules: [{ required: true, message: '请选择工资所属年月止' }],
      fieldName: 'sendMonthTo',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '发放名称', dataIndex: 'disburseName', width: 200 },
    { title: '薪资所属年月', dataIndex: 'sendMonth', width: 200 },
    { title: '客户账单年月', dataIndex: 'billMonth', width: 200 },
    { title: '薪资计税年月', dataIndex: 'taxMonth', width: 200 },
    { title: '客户编号', dataIndex: 'custCode', width: 200 },
    { title: '客户名称', dataIndex: 'custName', width: 200 },
    { title: '薪资类别', dataIndex: 'wageClassName', width: 200 },
    { title: '数据确认日期', dataIndex: 'confirmDt', width: 200 },
    { title: '确认人', dataIndex: 'confirmBy', width: 200 },
    {
      title: '发放状态',
      dataIndex: 'disburseBatchStatus',
      width: 200,
      render: (val: string) => sendStatusList.get(val),
    },
    {
      title: '查看明细',
      dataIndex: 'a',
      width: 200,
      render: (_, r) => {
        return <Typography.Link onClick={() => onViewDetail(r)}>查看</Typography.Link>;
      },
    },
  ];

  const onViewDetail = (record: POJO) => {
    setSingleRow(record);
    setDetailVisible(true);
  };

  const handleQueries = (val: POJO) => {
    delete val.pageNum;
    delete val.pageSize;
    return val;
  };

  const onAdd = () => {
    const rows = _options?.selectedRows;
    if (isEmpty(rows)) return msgErr('请先选择数据');
    const dict: POJO<boolean> = {
      [rows![0].custId]: true,
    };
    let isNotSame = false;
    for (let i = 0; i < rows!.length; i++) {
      const item = rows![i];
      if (!dict[item.custId]) return msgErr('选择的薪资发放记录必须属于同一个客户');
      if (item.createUser != currentUser.profile.userId) isNotSame = true;
    }
    if (isNotSame) {
      Modal.confirm({
        content: '计算与发放非同一人，是否仍继续操作',
        onOk: () => afterChecked(rows!),
      });
    } else {
      afterChecked(rows!);
    }
  };

  const afterChecked = (rows: POJO[]) => {
    const paySends = rows.map((r) => r.wageSendId).join(',');
    setNextParam({
      paySends,
      custId: rows[0].custId,
    });
    setAddVisible(true);
  };

  const renderButtons = (options: WritableInstance) => {
    _options = options;
    return (
      <React.Fragment>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={onAdd}>新增</Button>
      </React.Fragment>
    );
  };

  const renderModals = () => {
    return (
      <React.Fragment>
        <UpdatePayRollSendVirtualBatchWin
          visible={addVisible}
          isInputText
          params={nextParam}
          hideHandle={(name) => {
            setAddVisible(false);
            setNextParam({});
            if (name) {
              _options?.request();
              props.onAdded(name);
            }
          }}
        />
        <PaySendDetail
          visible={detailVisible}
          hideHandle={() => setDetailVisible(false)}
          data={singleRow}
        />
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      <CachedPage
        cardProps={{ bordered: false, bodyStyle: { padding: 0 } }}
        service={service}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        handleQueries={handleQueries}
        notShowPagination
        cached
        scroll={{ x: 'max-content', y: 500 }}
      />
      {renderModals()}
    </React.Fragment>
  );
};

export default Create;
