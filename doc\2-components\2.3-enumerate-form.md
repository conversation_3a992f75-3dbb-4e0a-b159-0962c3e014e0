<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-24 14:57:52
 * @lastTime: 2019-07-25 16:53:42
 * @LastAuthor: Do not edit
 * @message: 
 -->
# 枚举输入框

## 输入框批量渲染
在表单定义中，存在大量模板代码，本组致力于件提供一种批量渲染表格输入框的方法。

基础定义：

```tsx
// src/components/Forms/EnumerateForm.tsx

interface IFormItem {
  title: string;
  dataIndex: string;
  options?: GetFieldDecoratorOptions;
}

interface EnumerateFormProps extends FormComponentProps {
  formItems: IFormItem[];
  rowLength?: number;
  inputWidth?: number;
  columnWidth?: number;
}
const EnumerateFormF: React.FC<EnumerateFormProps> = props => {}
```
参数说明：
* `formItems` 必填。待渲染的表单数据,类型是`IFormItem[]`。
* `rowLength` 选填。默认3。表单中每一行所包含的输入框的个数。
* `columnWidth` 选填。表单中每一列的宽度。默认值是`24 / rowLength`。
当前只能渲染`Input`输入框，其他类型的支持，期待后来人补全。
