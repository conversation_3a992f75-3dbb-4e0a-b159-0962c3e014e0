<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-26 10:42:12
 * @lastTime: 2019-08-28 12:00:55
 * @LastAuthor: 侯成
 * @message: 
 -->
# 基础选择器
本组件作为作为选择基础数据的标准控件，同时可以进行二次分装，生成更多的特异性组件。

基本结构

目录 | 级别 |说明 
----| :------: | ---------
 `src\components\Selectors\BaseSelectors.tsx` | 一级，元组件 | 选择器核心实现
 `src\components\Selectors\BaseDataSelectors.tsx` | 二级，衍生公共组件 | 基础数据选择
 `src\components\Selectors\FuncSelectors.tsx` | 一级，公共组件 | MapToSelctor 聚集地
 `src\components\Selectors\CodeToView.tsx` | 一级，公共组件 | 通过 Id 获取详情
 `src\components\Selectors\index.tsx` | 一级，公共组件 | 公共导出集合地


## 基础声明
```tsx
// src/components/Selectors/Selectors.tsx

interface CommonProps extends SelectProps {
  keyType?: 'string' | 'number';
  params?: object;
  fixedParams?: object;
  code?: TOptionsValue;
  nestedKey?: string;
  keyPairs?: TListKeyPairs;
  service: IServiceType;
  dispatch?: Dispatch;
}

export interface BaseSelectorsProps<T extends TOptionsValue> extends CommonProps {
  listQueries?: IListQueries<T>;
}

const BaseSelectorsComp: <T extends TOptionsValue>(props: BaseSelectorsProps<T>) =>
  React.ReactElement<BaseSelectorsProps<T>> =
  <T extends TOptionsValue>(props: BaseSelectorsProps<T>) => {

  const { params, fixedParams, code, nestedKey, keyPairs, listQueries, service, dispatch,...origin
  } = props;

  // ... ...

};

const BaseSelectors: <T extends TOptionsValue>(props: BaseSelectorsProps<T>) =>
  React.ReactElement<BaseSelectorsProps<T>> =
  connect(({ cache }: ConnectState) => ({
    listQueries: cache.listQueries,
  }))(BaseSelectorsComp);
```

可以看到，`BaseSelectors` 作为一个元组件，包含大量较为抽象的参数，直接使用较为困难。以下是属性解释：

### 属性说明

属性 | 说明 | 类型 | 默认值
----|------|----- | ----
keyType | 数据中id的类型，默认为 `number`，若要使用`string`格式，请显式指明。 | ` 'number' OR 'string'` | 'number'
params | 调用接口时所传递的参数。 | `object` | -
fixedParams | 调用固定参数，永远不会被`params`中的数据覆写，通常在衍生组件封装时传入。 | `object` | -
code | 代号，传入后组件将不再是下拉选择控件，而是直接显示详情名称。 | `TOptionsValue` | -
keyPairs | 键值对，后端数据中，键和值对应的键名。 | `[string, string]` | [’key', 'value']
nestedKey | 嵌套键，后端接口返回结果中的嵌套结构对应的位置。 | `string` | -
service | `API`中的接口方法。 | `IServiceType` | -
listQueries | `dva`管理的属性，无需说明。 | `IListQueries&lt;T&gt;` | -
...origin | 普通`Select`组件所接受的全部参数。 | `SelectProps` | -

详细释义：
组件中有一个必填项：`service`。其他为选填。
* `keyPairs`: 键值对，若接口返回数据为：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "baseDataCode": 104,
      "baseDataName": "北区"
    },
    {
      "baseDataCode": 103,
      "baseDataName": "西区"
    },
    {
      "baseDataCode": 102,
      "baseDataName": "南区"
    },
    {
      "baseDataCode": 101,
      "baseDataName": "东区"
    }
  ],
  "bizCode": 0
}
```
则`keyPairs`的值为`['baseDataCode', 'baseDataName']`

* `nestedKey`： 嵌套键。若接口返回数据如下：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "baseData": {
      "areas": [
        {
          "baseDataCode": 104,
          "baseDataName": "北区"
        },
        {
          "baseDataCode": 103,
          "baseDataName": "西区"
        },
        {
          "baseDataCode": 102,
          "baseDataName": "南区"
        },
        {
          "baseDataCode": 101,
          "baseDataName": "东区"
        }
      ]
    }
  },
  "bizCode": 0
}
```
则`nestedKey`的值为 `'baseData:areas'`

## 使用
对`BaseSelectors`进行封装。

实现基础数选择据器：
```tsx
const BaseDataSelectorsComp: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
  ref: TRef,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    keyPairs: ['baseDataCode', 'baseDataName'],
    service: API.basedata.baseData.listByType,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
```
关于`keyPairs`的值。

——

实现大区下拉：
```tsx
const AreaSelectorsComp: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
  ref: TRef,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseData.deptList,
    keyPairs: ['baseDataCode', 'baseDataName'],
    fixedParams: { grade: 2, type: 1 },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
```
关于`fixedParams`的值。

——

实现城市选择器：
```tsx
const CitySelectorComp: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
  ref: TRef,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.city.postCityDropdown,
    showSearch: true
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
```
传递属于`Select`的属性——`showSearch`。

——

实体公司选择器：
```tsx
const EntitySelectorComp: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
  ref: TRef,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.entity.queryEntityList,
    nestedKey: 'list',
    fixedParams: { pageNum: 0, pageSize: 0 },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
```
关于`nestedKey`的值。

### 在业务中使用

使用`CitySelector`城市选择器。

```tsx

<Row gutter={\{ md: 6, lg: 24, xl: 48 \}\}>

    // ... ...

  <Col md={6} sm={24}>
    <FormItem label="城市666">
      {getFieldDecorator('cityId', { initialValue: fixedValue.cityId })(
        <CitySelector
          placeholder="请选择"
          disabled={fixedValue.cityId !== undefined}
        />,
      )}
    </FormItem>
  </Col>
</Row>
```

——

直接使用`BaseSelectors`元组件，**不推荐**。

```tsx
const productSelectorProps: BaseSelectorsProps<number> = {
  service: API.basedata.product.dropDownSsgroupList,
  fixedParams: { ssGroupId: -1 },
  showSearch: true
};

<Row gutter={\{ md: 6, lg: 24, xl: 48 \}\}>

    // ... ...

  <Col md={6} sm={24}>
    <FormItem label="产品选择">
      {getFieldDecorator('cityId', { initialValue: fixedValue.cityId })(
        <BaseSelectors<number>
        service={API.basedata.product.dropDownSsgroupList}
        fixedParams={\{ ssGroupId: -1 }\}
        showSearch
        />
      )}
    </FormItem>
  </Col>
</Row>
```
