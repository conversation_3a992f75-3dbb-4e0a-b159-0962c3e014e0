'use strict';

const { access } = require('fs');

var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
exports.__esModule = true;
/*
 * @Author: zhujianghua
 * @Email: <EMAIL>
 * @Date: 2020-11-27 18:21:43
 * @LastAuthor: 侯成
 * @LastTime: 2021-03-09 18:04:45
 * @message:
 */
var fs_1 = __importDefault(require('fs'));
var BASE_DIR = __filename.replace(/\\/g, '/').split('/scripts/loadFuncs')[0];

var readDir = function (path) {
  var dirs = fs_1['default'].readdirSync(path).filter(function (e) {
    return e.startsWith('comp-');
  });
  var list = [];
  dirs.forEach(function (name) {
    var p = path + '/' + name;
    var json = JSON.parse(fs_1['default'].readFileSync(p).toString());
    var moduleName = name.replace('comp-', '').replace('.json', '');
    list = list.concat(processOne(json, moduleName));
  });
  var routeMap = {};
  list.forEach(function (item) {
    var path = item.path;
    routeMap[path] = item.component;
  });
  fs_1['default'].writeFileSync(path + '/all.json', JSON.stringify(list, null, 4));
  fs_1['default'].writeFileSync(path + '/routerMap.json', JSON.stringify(routeMap, null, 4));
};

var processOne = function (json, name) {
  return Object.keys(json).map(function (key) {
    var value = json[key].split('|');
    var splits = value[0].trim().split(' ');
    return {
      path: '/' + name + '/' + key.replace(/\./gi, '/'),
      component: value[1].replace(/\s*/gi, ''),
      pageName: splits[splits.length - 1].trim(),
      author: splits[0].trim(),
    };
  });
};
var generateVersionid = () => {
  fs_1['default'].writeFileSync(
    BASE_DIR + '/config/version.json',
    JSON.stringify(
      {
        versionid: Date.now(),
      },
      null,
      4,
    ),
  );
};

var copyFiles = function (sorce, target) {
  fs_1['default'].copyFile(sorce, target, function (err) {
    if (err) {
      console.log(err);
      console.log(`尝试复制文件${sorce}时发生错误。`);
    } else {
      console.log(`复制文件${sorce}成功。`);
    }
  });
};

var copyFileApis = function () {
  // console.log('process.env.APP_MODE in copyFile:', process.env.APP_MODE)
  const MODE = process.env.APP_MODE || 'local';
  console.log(
    `当前的环境模式为：APP_MODE="${MODE}"，如果在服务器上环境为local，那么APP_MODE很可能没有正确设置。`,
  );
  const apiSorce = `${BASE_DIR}/src/assets/apis/index_${MODE}.ts`;
  const apiGate = `${BASE_DIR}/src/assets/apis/index.ts`;
  copyFiles(apiSorce, apiGate);
};

var copyFileProxy = function () {
  const zefault = `${BASE_DIR}/config/locales/config-zefault.ts`;
  const local = `${BASE_DIR}/config/locales/config-local.ts`;
  access(local, (err) => {
    console.log(err);
    err ? copyFiles(zefault, local) : '';
  });
};

function main() {
  readDir(BASE_DIR + '/config/routers');
  generateVersionid();
  copyFileApis();
  copyFileProxy();
}

main();
