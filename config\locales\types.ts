/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-29 09:38:29
 * @lastTime: 2019-10-29 09:50:12
 * @LastAuthor: yanlin.shen
 * @message:
 */

export interface LocaleConfig {
  WEB_DOMIAN?: string;
  API_DOMIAN?: string;
}

export interface IApiConf {
  [key: string]: string;
}

export const apiConf: IApiConf = {
  chroBase: 'chroBase',
  chroAdmin: 'chroAdmin',
  chroBaseData: 'chroBaseData',
  chroQuery: 'chroQuery',
  chroSale: 'chroSale',
  chroOrder: 'chroOrder',
  chroImports: 'chroImports',
  chroPayroll: 'chroPayroll',
  chroFinance: 'chroFinance',
  chroReport: 'chroReport',
};

export const devDomain = 'http://*************';
export const testDomain = 'http://*************';

// 备用端口：8080
export const apiPort = '8080';
// export const apiPort: IApiConf = {
//   [apiConf.chroBase]: '8080',
//   [apiConf.chroAdmin]: '8080',
//   [apiConf.chroBaseData]: '8080',
//   [apiConf.chroQuery]: '8080',
//   [apiConf.chroSale]: '8080',
//   [apiConf.chroOrder]: '8080',
//   [apiConf.chroImports]: '8080',
//   [apiConf.chroPayroll]: '8080',
//   [apiConf.chroFinance]: '8080',
//   [apiConf.chroReport]: '8080',
// };

export const apiProxy = '/rhro-service-1.0';
// export const apiProxy: IApiConf = {
//   [apiConf.chroBase]: '/rhro-service-1.0',
//   [apiConf.chroAdmin]: '/rhro-service-1.0',
//   [apiConf.chroBaseData]: '/rhro-service-1.0',
//   [apiConf.chroQuery]: '/rhro-service-1.0',
//   [apiConf.chroSale]: '/rhro-service-1.0',
//   [apiConf.chroOrder]: '/rhro-service-1.0',
//   [apiConf.chroImports]: '/rhro-service-1.0',
//   [apiConf.chroPayroll]: '/rhro-service-1.0',
//   [apiConf.chroFinance]: '/rhro-service-1.0',
//   [apiConf.chroReport]: '/rhro-service-1.0',
// };

export const apiService = '/rhro-service-1.0/';
// export const apiService: IApiConf = {
//   [apiConf.chroBase]: '/rhro-service-1.0/',
//   [apiConf.chroAdmin]: '/rhro-service-1.0/',
//   [apiConf.chroBaseData]: '/rhro-service-1.0/',
//   [apiConf.chroQuery]: '/rhro-service-1.0/',
//   [apiConf.chroSale]: '/rhro-service-1.0/',
//   [apiConf.chroOrder]: '/rhro-service-1.0/',
//   [apiConf.chroImports]: '/rhro-service-1.0/',
//   [apiConf.chroPayroll]: '/rhro-service-1.0/',
//   [apiConf.chroFinance]: '/rhro-service-1.0/',
//   [apiConf.chroReport]: '/rhro-service-1.0/',
// };

export const apiDev = `${devDomain}:${apiPort}`;
/* export const apiDev: IApiConf = {
  [apiConf.chroBase]: `${devDomain}:${apiPort}`,
  [apiConf.chroAdmin]: `${devDomain}:${apiPort}`,
  [apiConf.chroBaseData]: `${devDomain}:${apiPort}`,
  [apiConf.chroQuery]: `${devDomain}:${apiPort}`,
  [apiConf.chroSale]: `${devDomain}:${apiPort}`,
  [apiConf.chroOrder]: `${devDomain}:${apiPort}`,
  [apiConf.chroImports]: `${devDomain}:${apiPort}`,
  [apiConf.chroPayroll]: `${devDomain}:${apiPort}`,
  [apiConf.chroFinance]: `${devDomain}:${apiPort}`,
  [apiConf.chroReport]: `${devDomain}:${apiPort}`,
}; */

export const apiTest = `${testDomain}:${apiPort}`;
// export const apiTest: IApiConf = {
//   [apiConf.chroBase]: `${testDomain}:${apiPort}`,
//   [apiConf.chroAdmin]: `${testDomain}:${apiPort}`,
//   [apiConf.chroBaseData]: `${testDomain}:${apiPort}`,
//   [apiConf.chroQuery]: `${testDomain}:${apiPort}`,
//   [apiConf.chroSale]: `${testDomain}:${apiPort}`,
//   [apiConf.chroOrder]: `${testDomain}:${apiPort}`,
//   [apiConf.chroImports]: `${testDomain}:${apiPort}`,
//   [apiConf.chroPayroll]: `${testDomain}:${apiPort}`,
//   [apiConf.chroFinance]: `${testDomain}:${apiPort}`,
//   [apiConf.chroReport]: `${testDomain}:${apiPort}`,
// };
