<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-11-08 16:06:41
 * @LastAuthor: 侯成
 * @LastTime: 2019-11-08 16:37:09
 * @message: message
 -->
# 名称展示组件
在前后端交互中，经常出现后端`API` 返回某项数据的主键id，单前端需要展示对应名称的情况。本组件致力于解决此类问题。

## 基础声明
```tsx
interface CodeToViewProps {
  params: IRequestParams;
  pk?: string;
  viewKey: string;
  service: IServiceType;
}

const CodeToViewComp: React.FC<CodeToViewProps & CodeToViewPatch> = props => {
  const { params, viewKey, service, pkQueries, pk, dispatch } = props;
  // ... ...
  return <span>{data[viewKey] || '--'}</span>;
}

const CodeToView: React.FunctionComponent<CodeToViewProps & CodeToViewPatch> = connect(
  ({ cache }: ConnectState) => ({
    pkQueries: cache.pkQueries,
  }),
)(CodeToViewComp);
```
`CodeToView` 是一个元组件，依赖的参数较多。

### 属性说明

属性 | 说明 | 类型 | 默认值
----|------|----- | ----
paramsAll | 用于请求后端API的参数。可通过 `service` 查看。 | `IRequestParams` | -
pk | 请求后端API时，主键的名称。可通过 `service` 查看。默认为`'id'` | `string` | `'id'`
viewKey | 后端API返回的数据，详情名称所对应的的键。 | `string` | -
service | `API`中的接口方法。 | `IServiceType` | -

## 使用
对`CodeToView`进行封装。
```tsx

interface UserIdToNameProps {
  id: number | undefined;
  params?: IRequestParams;
}

const UserIdToName: React.FC<UserIdToNameProps> = props => {
  const { id, params } = props;
  if (!id) return <span>--</span>;
  return (
    <CodeToView
      params={\{ id, ...params }\}
      service={API.admin.user.getUser}
      viewKey="chnName"
      {...props}
    />
  );
};
```
`UserIdToNameProps` 中定义的属性:
 * `id`，是后端API所需的主键，可通过查看 `service` 得到特定的键名。
 * `params`，是使用`UserIdToName`可额外传入的请求参数，可通过查看 `service` 得到特定的参数定义。
