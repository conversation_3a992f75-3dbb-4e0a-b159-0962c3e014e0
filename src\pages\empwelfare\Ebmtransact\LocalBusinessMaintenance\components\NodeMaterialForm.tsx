import React, { useEffect, useState } from 'react';
import { Form, Button, message, Select, Upload } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';
import { isOrNoMap } from '../../NationwideBusiness';
import { UploadOutlined } from '@ant-design/icons';

interface NodeMaterialFormProps {
  modal: [boolean, CallableFunction];
  listOptions: any;
  initialInfo?: any;
}

const NodeMaterialForm: React.FC<NodeMaterialFormProps> = ({ modal, listOptions, initialInfo }) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;

  const [form] = Form.useForm();
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (visible) {
      if (initialInfo && Object.keys(initialInfo).length > 0) {
        setIsEdit(true);
        form.setFieldsValue(initialInfo);
      } else {
        setIsEdit(false);
        form.resetFields();
      }
    }
  }, [visible, initialInfo, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      // 确保包含业务节点ID
      const submitData = {
        ...values,
        businessNodeId: initialInfo?.businessNodeId,
      };
      // TODO: 调用API保存数据
      // console.log('节点材料提交:', submitData);
      message.success(isEdit ? '修改成功' : '新增成功');
      setVisible(false);
      form.resetFields();
      // 刷新列表，传递当前的业务节点ID
      listOptions.request({ businessNodeId: initialInfo?.businessNodeId });
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button key="submit" type="primary" onClick={handleSubmit}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '材料编号',
      fieldName: 'materialCode',
      inputRender: 'string',
      inputProps: { disabled: isEdit },
    },
    {
      label: '材料名称',
      fieldName: 'materialName',
      inputRender: 'string',
      inputProps: { disabled: isEdit },
    },
    {
      label: '是否原件',
      fieldName: 'isOriginal',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
      inputProps: { disabled: isEdit },
    },
    {
      label: '材料数量',
      fieldName: 'quantity',
      inputRender: 'number',
    },
    {
      label: '是否返还材料',
      fieldName: 'returnMaterial',
      inputRender: () => mapToSelectors(isOrNoMap, { allowClear: true }),
    },
    {
      label: '用印签字方',
      fieldName: 'signatureParty',
      inputRender: 'select',
      inputProps: {
        options: [
          { label: '易才章', value: '1' },
          { label: '客户章', value: '2' },
          { label: '人员签名', value: '3' },
        ],
      },
    },
    {
      label: '材料模板',
      fieldName: 'template',
      inputRender: () => (
        <Upload>
          <Button icon={<UploadOutlined />}>点击上传</Button>
        </Upload>
      ),
    },
  ];

  // 设置只读字段
  const readOnlyFields = isEdit
    ? {
        materialCode: true,
        materialName: true,
        isOriginal: true,
      }
    : undefined;

  return (
    <Codal
      title={isEdit ? '修改节点材料' : '新增节点材料'}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        setVisible(false);
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="60%"
    >
      <FormElement3 form={form}>
        <EnumerateFields
          formColumns={formColumns}
          outerForm={form}
          colNumber={1}
          readOnlyFields={readOnlyFields}
        />
      </FormElement3>
    </Codal>
  );
};

export default NodeMaterialForm;