const processObjectMap = new Map<string, string>([
  ['1', '客户'],
  ['2', '员工'],
]);
const busTypeMap = new Map<string, string>([
  ['1', '社保业务'],
  ['2', '公积金业务'],
  ['3', '人力资源收费服务'],
]);
const busSourceMap = new Map<string, string>([
  ['1', 'HRO提交'],
  ['2', '客户端提交'],
  ['3', '微信端提交'],
]);
const transactPropertyMap = new Map<string, string>([
  ['1', '流程业务'],
  ['2', '单次业务'],
]);
const transactTypeMap = new Map<string, string>([
  ['1', '易才办理'],
  ['2', '员工办理'],
]);
const statusMap = new Map<string, string>([
  ['1', '办理中'],
  ['2', '办理完成'],
  ['3', '取消'],
  ['4', '终止'],
]);
const resultMap = new Map<string, string>([
  ['1', '成功'],
  ['2', '失败'],
]);

/** 客户规模 */
export const customSizeMap = new Map<string, string>([
  ['1', '大型客户'],
  ['2', '中型客户'],
  ['3', '小型客户'],
]);

/** 业务状态 */
export const busStatusMap = new Map<string, string>([
  ['1', '待确认'],
  ['2', '办理中'],
  ['3', '办理完成'],
]);

/** 办理对象 */
export const transactObjectMap = new Map<number | string, string>([
  ['1', '客户'],
  ['2', '员工'],
]);

/** 办理结果 */
export const busResultMap = new Map<string, string>([
  ['1', '成功'],
  ['2', '失败'],
]);

/** 微信端进度查询 */
export const wechatProgressQueryMap = new Map<number | string, string>([
  ['1', '开通'],
  ['2', '不开通'],
]);

export {
  processObjectMap,
  busTypeMap,
  busSourceMap,
  transactPropertyMap,
  transactTypeMap,
  statusMap,
  resultMap,
};
