# 登录功能实现

## 问题描述

通过用户名密码，调用接口验证用户输入，获取校验结果，校验通过后跳转至主页。

### 实现方法

接口地址　`/login`

1.页面主体：

```js
// src/pages/User/Login.tsx
interface UserLoginProps extends Required<ConnectProps> {
  breadcrumbNameMap: { [path: string]: MenuDataItem };
}
interface UserLoginState {
  name: string;
  username: string;
  imgsource: string;
  captcha: string;
  password: string;
}

@connect()
class UserLogin extends React.Component<UserLoginProps, UserLoginState> {}

<Form onSubmit={this.handleSubmit}>// ...</Form>;
```

2.方法主体:

```tsx
// src/pages/User/Login.tsx
handleSubmit = (e: React.FormEvent) => {
  e.preventDefault();
  const { dispatch, route } = this.props;
  const { routes } = route!;
  dispatch!({
    type: 'login/login',
    payload: {
      username: this.state.username,
      password: this.state.password,
      captcha: this.state.captcha,
    },
  });
};
```

3.方法调用:

```ts
// src/models/login.ts
*userLogin({ payload }, { call, put, select }) {
  // ...
  yield put({type: 'login',payload: carryOn, loginMethod,});
}

*login({ payload, loginMethod }, { call, put, select }) {
  // ...
  const res = yield call(loginMethod, payload);
  // ...
  yield put({ type: 'changeLoginStatus', payload: loginStatus });
  // ...
  if (res.code === CODE_SUCCESS) yield put(rediectTo());
},
```

当接口返回数据正确时。`changeLoginStatus` 方法会存储用户登录状态，`rediectTo` 方法通过解析当前`url`地址中`redirect`参数来获得接下来将要访问的页面，无参数时将重定向至根路径“`/`”。

所有访问都将通过 `src/pages/Gateway`组件，在此组件中将校验用户登录状态。未登录的用户，将会被重定向至`/user/login`页面，此组件将导致后续问题。

```tsx
// src/pages/Gateway.tsx
// 获取登录状态
const isLogin = login.status === '200';
// connect方法将从 stroe 中获取 login 对象，继而传入此组件。
export default connect(({ menu: menuModel, login }: ConnectState) => ({
  routerData: menuModel.routerData,
  login,
}))(GatewayComp);
```

### 遗漏情况

当用户刷新浏览器时，标签进程重启，内存中所有数据将丢失，store 中 login 对象将重置为登录状态。具体表现为在任意页面刷新浏览器，将被重定向至`user/login`页面。通过右键->打开新标签时，将会遇到同样情形。

#### 问题再分析

1.流程图

```mermaid
graph LR
  start[访问主页] --> input[全局网关]
  input --> conditionA{本地状态}
  conditionA -- YES --> printA[放行]
  conditionA -- NO --> conditionB{远程状态}
  conditionB -- YES --> printB[放行]
  conditionB -- NO --> printC[登录页]
  printA --> stop[主页]
  printB --> stop
```

见末尾 图 1.1 此流程的核心问题在于，组件访问是即时加载的，而查询服务器登录状态是异步返回的。亦即，在后端返回数据之前，页面已经开始加载，所以实际流程是先放行，后将校验。这一目的将通过 `PromiseRender`组件实现。

#### 方法实现

页面主体：

```tsx
// src/pages/Gateway.tsx
const GatewayComp: React.FC<GatewayProps> = ({ children, location, login }) => {
  const isLogin = login.status === '200';

  let onNoMatch = <Redirect to="/user/login" />;
  let authority: Authority = login.type;
  if (!isLogin) {
    const redirect =
      location!.pathname === '/dashboard/analysis'
        ? null
        : stringify({
            redirect: window.location.href,
          });
    onNoMatch = <Redirect to={redirect ? `/user/login?${redirect}` : '/user/login'} />;
    authority = userLoginRequired();
  }
  return (
    <Gateway authority={authority} noMatch={onNoMatch}>
      {children}
    </Gateway>
  );
};
```

`login`对象由 `dva` 的 `connect` 方法传入，来源于 `store.state.login`，`isLogin`代表当前登录状态。

`children`是已登录用户将访问的组件，`onNoMatch`是当前用户权限校验失败时重定向的页面，`authority` 是用户的权限状态，第一处设置的值用户类型`login.type`，用户类型包括内部用户、外部用户等。

当本地登录状态为`false`时，`onNoMatch`被重置为 `/user/login?${redirect}`， `redirect`是重定向参数，将在远程校验通过转入，它由`stringify`方法转义当前访问的目标地址获得。

`userLoginRequired`是请求后端接口的方法，返回由`request.get("/switchBranchCheck")`返回的`Promise`对象。

`Gateway`实现：

```tsx
// src/components/Gateway/Gateway.tsx
const GateWay: IAuthorized = ({ children, authority, noMatch }) => {
  const childrenRender = typeof children === 'undefined' ? null : children;
  return CheckPermissions(authority, childrenRender, noMatch) as React.ReactElement;
};

const CheckPermissions = (
  authority?: Authority,
  target?: React.ReactNode,
  ExceptionComp?: React.ReactNode,
) => runCheck(authority, CURRENT, target, ExceptionComp);

const runCheck = (
  authority?: Authority,
  currentAuthority?: string | string[],
  target?: React.ReactNode,
  ExceptionComp?: React.ReactNode,
): React.ReactNode | null => {
  // ... ...
  if (authority instanceof Promise) {
    // authority 是 Promise对象时
    return <PromiseRender ok={target} error={ExceptionComp} promise={authority} />;
  }
  // ... ...
  throw new Error('unsupported parameters');
};
```

在`GateWay`中接收三个参数，`children, authority, noMatch`。各参数最终被传入`runCheck`方法，并重命名为：`target, authority, ExceptionComp`。

`runCheck`接收四个参数，多出的`currentAuthority`来源与全局配置，值为所有用户类型。当`authority` 为`Promise` 对象时，一个`PromiseRender` 组件将被返回。

在当前的场景中，用户本地未登录时，`GatewayComp` 组件将最终返回一个`PromiseRender`组件，并直接加载。查询后端登录状态的接口调用，将在此组件中，等待返回值。

`PromiseRender`实现：

```tsx
// src/components/Gateway/PromiseRender.tsx
export interface PromiseRenderPorps {
  error?: AnyComponent | React.ReactNode;
  ok?: AnyComponent | React.ReactNode;
  promise: Promise<any>;
}

interface PromiseRenderState {
  component?: AnyComponent | null;
}

export default class PromiseRender extends React.Component<PromiseRenderPorps, PromiseRenderState> {
  // ... ...
  setRenderComponent(props: PromiseRenderPorps) {
    const ok = this.checkIsInstantiation(props.ok);
    const error = this.checkIsInstantiation(props.error);
    props.promise
      .then((res: any) => {
        res.code === CODE_SUCCESS
          ? this.setState({ component: ok })
          : this.setState({ component: error });
      })
      .catch(() => {
        this.setState({
          component: error,
        });
      });
  }
  // ... ...
}
```

在 `setRenderComponent` 方法中 `props.promise.then()`在等待返回数据。每一次浏览器开始运行，将生成全新的`SESSION_KEY`，并在一个生命周期内（程序退出之前）保持不变，这一 KEY 值将被自动添加至`HTTP headers`之上。后端 HTTP 服务器会自动创建`SESSION`对象，并通过`SESSION_KEY`访问此对象。此对象通常存储在内存中，一段时间后清除，时间由 HTTP 服务器自行决定。接口返回数据示例：

```json
// 后端session有效时：
{
  "code": 200,
  "message": "success",
  "data": {
    "functions": [],
    "roleList": [],
    "user": {
      "chnName": "猪头啊",
      "userId": 1000,
      "userName": "admin",
      "avatar": "https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",
    },
    "isNeedUptPwd": false,
    "token": "eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiJiNjIzY2I2Yy1iNjRhLTQ4N2QtOTg1MS1hMGQ0Njc5NDM3N2IiLCJpYXQiOjE1NjEwODE0MDgsInN1YiI6ImFkbWluIiwiaXNzIjoiY3RnIiwiZXhwIjoxNTYzNjczNDA4fQ.AhtEi3-BUM3No1FqegzNc7fn2sp71N8064JpBjM5O4niq2c1NlDp19BerO8ddSglOAotQeVIHwh59xajifBC2Q"
  }
}
// 后端session无效时：
{
  "code": 400,
  "message": "fail",
}
```

数据处理

```tsx
// src/components/Gateway/PromiseRender.tsx
props.promise.then((res: any) => {
  res.code === 200 ? this.setState({ component: ok }) : this.setState({ component: error });
});
```

当返回结果`code`为 200 时，名为`ok`的组件将被加载，其值来源于`GatewayCopm`中的`children`对象。当`code`为 400 时，名为`error`的组件将被加载，其值来源于`GatewayCopm`中的`noMatch`对象。

自此登录状态校验结束。

图 1.1

#### 流程图示例

```mermaid
graph LR
    start[开始] --> input[输入A,B,C]
    input --> conditionA{A是否大于B}
    conditionA -- YES --> conditionC{A是否大于C}
    conditionA -- NO --> conditionB{B是否大于C}
    conditionC -- YES --> printA[输出A]
    conditionC -- NO --> printC[输出C]
    conditionB -- YES --> printB[输出B]
    conditionB -- NO --> printC[输出C]
    printA --> stop[结束]
    printC --> stop
    printB --> stop
```
