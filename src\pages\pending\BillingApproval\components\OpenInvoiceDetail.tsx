import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import {
  ContractTypeSelector,
  GetBaseSelector,
  mapToSelectors,
  renderListToSelectors,
} from '@/components/Selectors';
import { useWritable, Writable, WritableInstance } from '@/components/Writable';
import { formatAmount } from '@/utils/methods/format';
import {
  balanceTypeMap,
  invoiceTypeMap,
  jsSysMap,
  parModeMap,
  parModeMap2,
  parStatusMap,
  parTypeMap,
  receiptsTypeMap,
} from '@/utils/settings/finance/upload';
import { WritableColumnProps } from '@/utils/writable/types';
import { Button, Table, Form, Input, Typography } from 'antd';
import React, { useState, useEffect } from 'react';
import { isEmpty } from 'lodash';
import { msgErr, msgOk } from '@/utils/methods/message';
import { AsyncButton } from '@/components/Forms/Confirm';
import { tableIndexKey, tableLineState, tableLineStateMap } from '@/utils/settings/forms';
import { Calculator } from '@/utils/methods/calculator';
import ChoiceParItem from './ChoiceParItem';
import VerifyInvoiceDetail from './VerifyInvoiceDetail';
import ParDetail from './ParDetail';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import { numberRegx } from '@/utils/forms/validate';
import IconFont from '@/components/IconFont';
import '../style.less';
import SelectInvalidPar from './SelectInvalidPar';

export interface OpenInvoiceDetailDataProps {
  invoiceId?: string;
  custId?: string;
  dataTransObj?: {
    groupId?: string;
    groupName?: string;
    custCode?: string;
    custName?: string;
    receivableIds?: string;
    billYm?: string;
    finReceivableYm?: string;
    headName?: string;
    invoiceMemo?: string;
    mailType?: string;
    invoiceEmail?: string;
    invoiceAmt?: string;
    orInvoiceInfo?: POJO;
  };
}

interface OpenInvoiceDetailProps {
  parButtonIsVisible?: boolean;
  signBranchTitle?: string;
  isbillingNumEdit?: boolean;
  invoiceId?: string;
  data?: OpenInvoiceDetailDataProps;
  wriTable: WritableInstance;
  wriTable2: WritableInstance;
  wriTable3: WritableInstance;
  loading: boolean;
  requestTable?: () => void;
  saved?: (billingId: string) => void;
  list1: POJO[];
  list2: POJO[];
  list3: POJO[];
  // 单独为审批加了一个参数控制票面记录、摘要信息是否可以修改，RHRO-5533
  isApprove?: boolean;
  // 客户资质 2 为小规模纳税人 小规模纳税人不能开具专票，请修改
  custAptitude: string;
}

const OpenInvoiceDetail = (props: OpenInvoiceDetailProps) => {
  const { isApprove } = props;
  const [form] = Form.useForm();

  const service = API.finance.invoiceMain.getApprovalDetailData;
  const service2 = { ...service, cacheKey: `${service.cacheKey} 2` };
  const service3 = { ...service, cacheKey: `${service.cacheKey} 3` };

  const [vatr, setVatr] = useState<Map<number, string>>(new Map());

  const [isEdit, setIsEdit] = useState<boolean>(true);

  const [parItemVisible, setParItemVisible] = useState<boolean>(false);
  const [verifyVisible, setVerifyVisible] = useState<boolean>(false);
  const [parId, setParId] = useState<string>('');

  const [billList, setBillList] = useState<POJO[]>([]);

  const [selectPar, setSelectPar] = useState<POJO | undefined>();

  const columns2: WritableColumnProps<any>[] = [
    {
      title: '发票类型',
      dataIndex: 'parType',
      rules: [{ required: true, message: '请选择发票类型' }],
      inputRender: ({ serial, record }) => {
        const disabled = isApprove ? false : !!record.parId;
        return mapToSelectors(parTypeMap, {
          disabled,
          onChange: (v) => onChange(v, serial, record.invoiceType),
        });
      },
    },
    {
      title: '开票形式',
      dataIndex: 'invoiceType',
      rules: [{ required: true, message: '请选择发票类型' }],
      inputRender: ({ serial, record }) => {
        const disabled = isApprove ? false : !!record.parId;
        return mapToSelectors(invoiceTypeMap, {
          disabled,
          onChange: (v) => {
            if (v != 3) {
              props.wriTable2.updateRows({
                [tableIndexKey]: serial,
                invoiceType: v,
                parMode: '',
                receiptsType: '',
                balanceType: '',
              });
            } else {
              props.wriTable2.updateRows({
                [tableIndexKey]: serial,
                invoiceType: v,
                parMode: '',
              });
            }
          },
        });
      },
    },
    {
      title: '大合同类型',
      dataIndex: 'serviceType',
      rules: [{ required: true, message: '请选择大合同类型' }],
      inputRender: ({ serial, record }) => {
        const disabled = isApprove ? false : !!record.parId;
        return (
          <ContractTypeSelector
            onChange={async (serviceType) => {
              // 开票形式为全电票、且开票方式为差额（含税）时，根据对应分公司开票规则中的"凭证类型"默认带入显示，可以修
              const res = await API.finance.invoiceMain.getRuleDifferenceType.requests({
                branchId: props.data?.dataTransObj?.orInvoiceInfo?.departmentId,
                invoiceType: record.parType || 1,
                contractType: serviceType,
              });
              const res2 = await API.finance.invoiceMain.getRuleShortfallType.requests({
                branchId: props.data?.dataTransObj?.orInvoiceInfo?.departmentId,
                invoiceType: record.parType || 1,
                contractType: serviceType,
                custAptitude: props?.custAptitude,
                custId: props.data?.custId,
              });
              props.wriTable2.updateRows({
                [tableIndexKey]: serial,
                serviceType,
                receiptsType: res || null,
                balanceType: res2 || null,
              });
            }}
            disabled={disabled}
          />
        );
      },
    },
    {
      title: '开票方式',
      dataIndex: 'parMode',
      width: 200,
      rules: [{ required: true, message: '请选择开票方式' }],
      inputRender: ({ serial, record }) => {
        const disabled = isApprove ? false : !!record.parId;
        const _newMap = record.invoiceType === 3 ? parModeMap2 : parModeMap;
        return mapToSelectors(_newMap, {
          disabled,
          onChange: (parMode) => parModeChange(serial, record, parMode),
        });
      },
    },
    {
      title: '凭证类型',
      dataIndex: 'receiptsType',
      width: 200,
      inputRender: ({ record }) => {
        const disabled = (isApprove ? false : !!record.parId) || record.invoiceType != 3;
        return mapToSelectors(receiptsTypeMap, { disabled });
      },
    },
    {
      title: '差额类型',
      dataIndex: 'balanceType',
      width: 200,
      inputRender: ({ record }) => {
        const disabled = (isApprove ? false : !!record.parId) || record.invoiceType != 3;
        return mapToSelectors(balanceTypeMap, { disabled });
      },
    },
    {
      title: '票面备注',
      dataIndex: 'parRemark',
      inputRender: ({ record }) => {
        const disabled = isApprove ? false : !!record.parId;
        return <Input disabled={disabled} />;
      },
    },
    {
      title: '是否进入金税系统',
      dataIndex: 'jsSys',
      rules: [{ required: true, message: '请选择是否进入金税系统' }],
      inputRender: ({ record }) => {
        let disabled = isApprove ? false : !!record.parId;
        if (record.invalidInvoiceNo) disabled = true;
        return mapToSelectors(jsSysMap, { disabled });
      },
    },
    {
      title: '票面金额',
      dataIndex: 'parAmount',
      rules: [{ required: true, message: '请填写票面金额', pattern: numberRegx }],
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    {
      title: '包含项目',
      dataIndex: 'link',
      render: (_, record) => (
        <Typography.Link
          onClick={(e) => {
            e.preventDefault();
            const row = (props.wriTable2.getRow(record[tableIndexKey]) || {}) as POJO;
            if ((!row.parId || isApprove) && row.serviceType) {
              setParItemVisible(true);
            }
          }}
        >
          选择
        </Typography.Link>
      ),
      rules: [{ required: true, message: '请选择包含项目' }],
    },
    props.parButtonIsVisible && {
      title: '发票状态',
      dataIndex: 'parStatus',
      inputRender: () => mapToSelectors(parStatusMap, { allowClear: true, disabled: true }),
    },
    {
      title: '引用的发票号',
      dataIndex: 'invalidInvoiceNo',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
  ].filter((i) => i);

  const columns3: WritableColumnProps<any>[] = [
    {
      title: '摘要名称',
      dataIndex: 'itemName',
      rules: [{ required: true, message: '请选择摘要名称' }],
      inputRender: ({ text }) => (
        <Input
          disabled={isApprove ? false : !isEdit}
          suffix={
            <IconFont
              onClick={() => (isApprove ? true : isEdit) && setVerifyVisible(true)}
              type="iconziyuan6"
              style={{ color: 'rgba(0,0,0,.35)' }}
            />
          }
        ></Input>
      ),
    },
    {
      title: '税率',
      dataIndex: 'vatr',
      inputRender: () => mapToSelectors(vatr, { disabled: isApprove ? false : !isEdit }),
    },
    {
      title: '金额',
      dataIndex: 'amount',
      inputRender: () => <Input disabled={isApprove ? false : !isEdit} onBlur={amountChange} />,
    },
    {
      title: '增值税',
      dataIndex: 'vat',
      rules: [{ pattern: numberRegx, message: '请输入增值税' }],
      inputRender: () => <Input disabled={isApprove ? false : !isEdit} onBlur={amountChange} />,
    },
    {
      title: '差额',
      dataIndex: 'payBalance',
      rules: [{ pattern: numberRegx, message: '请输入差额' }],
      inputRender: () => <Input disabled={isApprove ? false : !isEdit} onBlur={amountChange} />,
    },
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '开票机号',
      fieldName: 'billingId',
      colNumber: 1,
      inputRender: () =>
        renderListToSelectors(billList, ['key', 'shortName'], {
          allowClear: true,
          disabled: !props.isbillingNumEdit,
        }),
    },
  ];

  useEffect(() => {
    requestVatr();
  }, []);

  const onChange = async (v: number, serial: number, invoiceType: number) => {
    const bizCode = await getInvoiceType(v);
    if (bizCode) {
      props.wriTable2.updateRows({
        [tableIndexKey]: serial,
        invoiceType: bizCode,
        parMode: v == 1 && invoiceType != 3 ? 2 : 3,
      });
    } else {
      props.wriTable2.updateRows({
        [tableIndexKey]: serial,
        parMode: v == 1 && invoiceType != 3 ? 2 : 3,
      });
    }
    resetItemName();
  };

  const resetItemName = () => {
    const rows = (props.wriTable3.getList().all || []) as POJO[];
    const newRows = rows.map((i) => {
      return {
        ...i,
        itemName: null,
        productTypeId: null,
      };
    });
    props.wriTable3.updateRows(newRows);
  };

  const getInvoiceType = async (v: number) => {
    const param: POJO = { id: props.data?.dataTransObj?.orInvoiceInfo?.payeeId, parType: v };
    const res = await API.finance.invoiceMain.getInvoiceTypeByBranch.request(param);
    return res.bizCode;
  };

  const requestVatr = async () => {
    const res = await API.basedata.baseDataCls.getDorpDownList.requests({
      statementName: 'baseData.baseDataDropDownListByMap',
      type: '058',
    });
    const list = res.list as POJO[];
    const map = new Map<number, string>();
    list.forEach((e) => map.set(+e.key, e.shortName));
    setVatr(map);
  };

  useEffect(() => {
    if (isEmpty(props?.wriTable2.selectedSingleRow)) {
      const row = props?.wriTable2.data.list?.[0];
      if (!row) return;
      const parItems = (row.parItems || []) as POJO[];
      props.wriTable3.setNewData(parItems);
      props.wriTable2.setSelectedSingleRow(row);
      setIsEdit(!row.parId);
    }
  }, [props?.wriTable2.selectedSingleRow]);
  useEffect(() => {
    requestBilling();
    form.setFieldsValue({ billingId: props.data?.dataTransObj?.orInvoiceInfo?.billingId });
  }, [props.signBranchTitle]);

  const requestBilling = async () => {
    const res = await API.finance.invoiceMain.getBillingNumByTitle.requests({
      titleId: props.signBranchTitle || '',
    });
    setBillList(res.list || []);
  };

  /** 开票方式改变 */
  const parModeChange = async (serial: number, record: POJO, parMode: number) => {
    const row = props.wriTable2.selectedSingleRow;
    if (isEmpty(row)) return;
    if (parMode == 3 && record.invoiceType == 3) {
      // 开票形式为全电票、且开票方式为差额（含税）时，根据对应分公司开票规则中的"凭证类型"默认带入显示，可以修
      const res = await API.finance.invoiceMain.getRuleDifferenceType.requests({
        branchId: props.data?.dataTransObj?.orInvoiceInfo?.departmentId,
        invoiceType: record.parType,
        contractType: record.serviceType,
      });
      const res2 = await API.finance.invoiceMain.getRuleShortfallType.requests({
        branchId: props.data?.dataTransObj?.orInvoiceInfo?.departmentId,
        invoiceType: record.parType || 1,
        contractType: record.serviceType,
        custAptitude: props?.custAptitude,
        custId: props.data?.custId,
      });
      props.wriTable2.updateRows({
        [tableIndexKey]: serial,
        parMode,
        parItems: [],
        parAmount: 0,
        parItemKeys: [],
        receiptsType: res || null,
        balanceType: res2 || null,
      });
    } else {
      props.wriTable2.updateRows({
        [tableIndexKey]: serial,
        parMode,
        parItems: [],
        parAmount: 0,
        parItemKeys: [],
      });
    }

    props.wriTable3.setNewData([]);
  };

  /** 金额发生改变 */
  const amountChange = () => {
    let sum = 0;
    const rows = (props.wriTable3.getList().all || []) as POJO[];
    rows.forEach((e) => {
      sum = Calculator.add(sum, e.amount || 0);
      sum = Calculator.add(sum, e.vat || 0);
    });
    const selected = props.wriTable2.selectedSingleRow;
    const t2 =
      props.wriTable2.getList().all.find((e) => (e[tableIndexKey] = selected[tableIndexKey])) || {};
    props.wriTable3.updateRows(rows);
    props.wriTable2.updateRows({ ...t2, parAmount: sum.toFixed(2), parItems: rows });
  };

  /** 添加行 */
  const onAdd = async () => {
    const invoiceRemark = props.data?.dataTransObj?.orInvoiceInfo?.invoiceRemark;
    const invoiceTypeName = props.data?.dataTransObj?.orInvoiceInfo?.invoiceTypeName;
    const allCount = props.wriTable2.getList().allCount;
    let visibleData = (props.wriTable.getList().visible || []).map((e) => e.serviceType);
    props.list1.forEach((e) => visibleData.push(e.serviceType));
    props.list2.forEach((e) => visibleData.push(e.serviceType));
    props.list3.forEach((e) => visibleData.push(e.serviceType));
    visibleData = Array.from(new Set(visibleData));
    const serviceType = visibleData.length === 1 ? visibleData[0] : undefined;
    if (allCount <= 0 && invoiceTypeName == '专票') {
      const type = await getInvoiceType(1);
      if (type == 3) {
        // 开票形式为全电票、且开票方式为差额（含税）时，根据对应分公司开票规则中的"凭证类型"默认带入显示，可以修
        const res = await API.finance.invoiceMain.getRuleDifferenceType.requests({
          branchId: props.data?.dataTransObj?.orInvoiceInfo?.departmentId,
          invoiceType: 1,
          contractType: serviceType,
        });
        const res2 = await API.finance.invoiceMain.getRuleShortfallType.requests({
          branchId: props.data?.dataTransObj?.orInvoiceInfo?.departmentId,
          invoiceType: 1,
          contractType: serviceType,
          custAptitude: props?.custAptitude,
          custId: props.data?.custId,
        });
        props.wriTable2.addRows({
          parRemark: invoiceRemark,
          parType: 1,
          jsSys: 1,
          invoiceType: type,
          parMode: 3,
          serviceType,
          receiptsType: res || null,
          balanceType: res2 || null,
        });
      } else {
        props.wriTable2.addRows({
          parRemark: invoiceRemark,
          parType: 1,
          jsSys: 1,
          invoiceType: type,
          parMode: 2,
          serviceType,
        });
      }
    } else {
      const type = await getInvoiceType(2);
      props.wriTable2.addRows({
        parRemark: invoiceRemark,
        parType: 2,
        jsSys: 1,
        invoiceType: type,
        parMode: 3,
        serviceType,
      });
    }
  };

  /** 删除行 */
  const onDel = async () => {
    const rows = props.wriTable2.selectedRows as POJO[];
    if (isEmpty(rows)) return msgErr('您尚未选择任何数据!');
    const next = rows.filter((e: POJO) => !e.parId);
    if (next.length) props.wriTable2.deleteRows(next);
    props.wriTable3.setNewData([]);
    // 审批可以删除
    if (isApprove) {
      const ids = rows.filter((e: POJO) => !!e.parId).map((e) => e.parId);
      if (ids.length === 0) return;
      await API.finance.invoiceMain.delParInfos.requests({ ids: String(ids) });
      props.requestTable?.();
    }
  };

  /** 作废 */
  const onUpdate = async () => {
    const rows = props.wriTable2.selectedRows as POJO[];
    if (isEmpty(rows)) return msgErr('您尚未选择任何数据!');
    const ids = rows.map((e) => e.parId);
    await API.finance.invoiceMain.updateParStatus.requests({ ids, parStatus: '4' });
    props.requestTable?.();
  };

  /** 明细 */
  const onDetail = () => {
    const row = props.wriTable2.selectedSingleRow as POJO;
    if (isEmpty(row)) return msgErr('请选择数据');
    if (row.parId) setParId(row.parId);
  };

  const fillItems = (data: POJO, row: POJO) => {
    const rs = data.rs as POJO[];
    const a: POJO[] = [];
    const b: POJO[] = [];
    rs.forEach((r) => {
      a.push({ ...r });
      b.push(r.parItemKey);
    });
    props.wriTable2.updateRows({
      [tableIndexKey]: row[tableIndexKey],
      parItems: rs,
      parAmount: data.sum,
      parItemKeys: b,
    });
    props.wriTable3.setNewData(rs);
  };

  const onSelectSingleRow = (row: POJO) => {
    const old = props.wriTable2.selectedSingleRow;
    if (row[tableIndexKey] === old?.[tableIndexKey]) return;
    const right = props.wriTable3.getList().all || [];
    const leftAll = props.wriTable2.getList().visible;
    if (leftAll.some((e) => e[tableIndexKey] === old?.[tableIndexKey])) {
      props.wriTable2.updateRows({
        [tableIndexKey]: old?.[tableIndexKey],
        parItems: right,
      });
    }
    props.wriTable3.resetFields();
    const parItems = (row.parItems || []) as POJO[];
    props.wriTable3.setNewData(parItems);
    props.wriTable2.setSelectedSingleRow(row);
    setIsEdit(!row.parId);
  };

  /** 保存 */
  const onSave = async () => {
    let sum = 0;
    const lists = await props.wriTable2.validateFields();
    const list = (lists.full || []) as POJO[];
    const lists2 = await props.wriTable3.validateFields();
    const list2 = (lists2.all || []) as POJO[];
    // 审批时,获取更改过的list,走一次删除，然后当做新增的保存
    const listUpdate = lists.updated;
    for (let i = 0; i < list.length; i++) {
      const r = list[i];
      if (r.invoiceType === 3 && r.parMode == 3 && (!r.receiptsType || r.receiptsType == ''))
        return msgErr('开票形式为全电票，开票方式为差额(含税)时凭证类型必填');
      if (r.invoiceType === 3 && r.parMode == 3 && (!r.balanceType || r.balanceType == '')) {
        return msgErr('开票形式为全电票，开票方式为差额(含税)时差额类型必填');
      }
      if (r.parType == 1 && props.custAptitude === '2')
        return msgErr('小规模纳税人不能开具专票，请修改');
      if (r.parStatus?.toString() !== '4') sum = Calculator.add(sum, r.parAmount || 0);
    }
    // list.forEach((r) => {
    //   if (r.parType == 1 && props.custAptitude === '2')
    //     return msgErr('小规模纳税人不能开具专票，请修改');
    //   if (r.parStatus?.toString() !== '4') sum = Calculator.add(sum, r.parAmount || 0);
    // });
    if (sum - +(props.data?.dataTransObj?.invoiceAmt || 0) !== 0)
      return msgErr('票面金额不等于开票总额!');
    const billingId = form.getFieldValue('billingId');
    if (!billingId) return msgErr('开票机号不能为空!');
    const leftSerial = props.wriTable2.selectedSingleRow[tableIndexKey];
    let addArray: POJO[] = [];
    for (let i = 0; i < list.length; i++) {
      const r = list[i];
      if (r[tableIndexKey] === leftSerial) {
        r.parItems = list2;
      }
      const children = r.parItems || [];
      for (let j = 0; j < children.length; j++) {
        if (!children[j].itemName) return msgErr('摘要名称为必填!');
      }
      if (!r.parId) {
        r.parStatus = '1';
        r.invoiceId = props.data?.invoiceId;
        addArray.push(r);
      }
    }
    if (props.parButtonIsVisible) {
      const param = {
        invoiceId: props.invoiceId as string,
        custPayerId: props.data?.dataTransObj?.orInvoiceInfo?.custPayerId,
        infos: addArray,
      };
      const res = await API.finance.invoiceMain.sendBusiness2.requests(param);
      if (res.isSuccess == '0') {
        msgOk('提交成功!');
        props.requestTable?.();
        props.saved?.(billingId);
      } else {
        return msgErr(res.msg);
      }
    } else {
      // 审批时新增
      if (isApprove && listUpdate.length !== 0) {
        // 清空parId
        // 判断修改的list里面parItemKey是否存在，不存在报错：无法自动拆分数据，请驳回后由客服重新提交开票申请
        const newL: any[] = [];
        for (let i = 0; i < listUpdate.length; i++) {
          const item = listUpdate[i];
          if (item[tableIndexKey] === leftSerial) {
            item.parItems = list2;
          }
          for (let j = 0; j < item.parItems.length; j++) {
            if (!item.parItems[j].itemName) return msgErr('摘要名称为必填!');
            if (!item.parItems[j].parItemKey)
              return msgErr('无法自动拆分数据，请驳回后由客服重新提交开票申请!');
          }
          newL.push({ ...item, parId: null });
        }
        addArray = addArray.concat(newL);
      }
      const res = await API.finance.invoiceMain.saveParInfos.requests({
        custPayerId: props.data?.dataTransObj?.orInvoiceInfo?.custPayerId as string,
        infos: addArray,
      });
      if (res.isSuccess == '0') {
        msgOk('提交成功!');
        if (isApprove && lists.updated.length !== 0) {
          // 先保存后删除
          const ids = lists.updated.map((e) => e.parId);
          await API.finance.invoiceMain.delParInfos.requests({ ids: String(ids) });
        }
        props.requestTable?.();
      } else {
        msgErr(res.msg);
        return;
      }
      props.saved?.(billingId);
    }
    const obj = {
      invoiceId: props.invoiceId,
      billingId,
    };
    await API.finance.invoiceMain.updateBillingNumById.requests(obj);
  };

  /** 引用作废发票号 */
  const onReferNo = async () => {
    const row = props.wriTable2.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请先选择数据');
    if (row.clientOperation === tableLineStateMap.add) {
      const _exists = {};
      (props.wriTable2.getList().added || []).forEach((e: POJO) => {
        if (e.invalidInvoiceNo) _exists[e.invalidInvoiceNo] = true;
      });
      const rowData = props.wriTable2.getRow(row[tableIndexKey]);
      const data = {
        custId: props.data?.custId,
        custPayerId: props.data?.dataTransObj?.orInvoiceInfo?.custPayerId,
        parType: rowData.parType, // 发票类型
        invoiceType: rowData.invoiceType, // 开票形式
        serviceType: rowData.serviceType, // 大合同类型
        parMode: rowData.parMode, // 开票方式
        parAmount: rowData.parAmount, // 票面金额
        exists: _exists,
      };
      setSelectPar(data);
    } else {
      return msgErr('只能对新建未保存的票面信息进行操作');
    }
  };

  const uptParInfo = (no: string, parRemark: string) => {
    const row = props.wriTable2.selectedSingleRow;
    props.wriTable2.updateRows({
      [tableIndexKey]: row[tableIndexKey],
      invalidInvoiceNo: no,
      jsSys: 0,
      parStatus: 2,
      parRemark: row.parRemark || parRemark,
    });
  };

  const renderButton = () => {
    const judgeButton = (visible: boolean | undefined) => {
      return !visible ? (
        <>
          <Button onClick={onAdd}>添加</Button>
          <AsyncButton style={{ marginLeft: 8 }} onClick={onDel}>
            删除
          </AsyncButton>
          {renderBill()}
          <AsyncButton style={{ marginLeft: 8 }} onClick={onSave}>
            保存
          </AsyncButton>
          <AsyncButton style={{ marginLeft: 8 }} onClick={onReferNo}>
            引用作废发票号
          </AsyncButton>
        </>
      ) : (
        <>
          <AuthButtons funcCode="addParInfo">
            <Button onClick={onAdd}>添加</Button>
          </AuthButtons>
          <AuthButtons funcCode="delParInfo">
            <Button style={{ marginLeft: 8 }} onClick={onDel}>
              删除
            </Button>
          </AuthButtons>
          <AuthButtons funcCode="updateParStatus">
            <AsyncButton style={{ marginLeft: 8 }} onClick={onUpdate}>
              作废
            </AsyncButton>
          </AuthButtons>
          <AuthButtons funcCode="detailParInfo">
            <Button style={{ marginLeft: 8 }} onClick={onDetail}>
              明细
            </Button>
          </AuthButtons>
          {renderBill()}
          <AsyncButton style={{ marginLeft: 8 }} onClick={onSave}>
            保存
          </AsyncButton>
          <AsyncButton style={{ marginLeft: 8 }} onClick={onReferNo}>
            引用作废发票号
          </AsyncButton>
        </>
      );
    };
    return <div>{judgeButton(props.parButtonIsVisible)}</div>;
  };

  const renderSummary2 = (list: POJO[]) => {
    const next = list.filter((e) => e[tableLineState] !== tableLineStateMap.delete);
    if (next.length <= 0) return null;
    let a = 0;
    next.forEach((r) => (a = Calculator.add(a, r.parAmount || 0)));
    return (
      <Table.Summary.Row>
        <Table.Summary.Cell index={0}>合计</Table.Summary.Cell>
        <Table.Summary.Cell index={1} />
        <Table.Summary.Cell index={2} />
        <Table.Summary.Cell index={3} />
        <Table.Summary.Cell index={4} />
        <Table.Summary.Cell index={5} />
        <Table.Summary.Cell index={6} />
        <Table.Summary.Cell index={7} />
        <Table.Summary.Cell index={8} />
        <Table.Summary.Cell index={9} />
        <Table.Summary.Cell index={10} />
        <Table.Summary.Cell index={11}>{formatAmount(a.toString())}</Table.Summary.Cell>
        <Table.Summary.Cell index={12} />
        <Table.Summary.Cell index={13} />
        <Table.Summary.Cell index={14} />
      </Table.Summary.Row>
    );
  };

  const renderSummary3 = (list: POJO[]) => {
    if (list.length <= 0) return null;
    let a = 0;
    let b = 0;
    let c = 0;
    list.forEach((r) => {
      a = Calculator.add(a, r.amount || 0);
      b = Calculator.add(b, r.vat || 0);
      c = Calculator.add(c, r.payBalance || 0);
    });
    return (
      <Table.Summary.Row>
        <Table.Summary.Cell index={0}>合计</Table.Summary.Cell>
        <Table.Summary.Cell index={1} />
        <Table.Summary.Cell index={2} />
        <Table.Summary.Cell index={3} />
        <Table.Summary.Cell index={4} />
        <Table.Summary.Cell index={5}>{formatAmount(a.toString())}</Table.Summary.Cell>
        <Table.Summary.Cell index={6}>{formatAmount(b.toString())}</Table.Summary.Cell>
        <Table.Summary.Cell index={7}>{formatAmount(c.toString())}</Table.Summary.Cell>
      </Table.Summary.Row>
    );
  };

  const renderTable = () => {
    return (
      <div style={{ display: 'flex', flexDirection: 'row', marginTop: 5, marginBottom: 5 }}>
        <div style={{ width: '49%', marginRight: 10 }} id="preview" className="maskClass">
          <Writable
            title={() => '票面信息'}
            service={service2}
            columns={columns2}
            notShowPagination
            noAddButton
            noDeleteButton
            wriTable={props.wriTable2}
            summary={renderSummary2}
            loading={props.loading}
            onSelectSingleRow={onSelectSingleRow}
          />
        </div>
        <div style={{ width: '50%' }} id="abstract" className="maskClass">
          <Writable
            title={() => '对应摘要'}
            service={service3}
            columns={columns3}
            data={{ list: [], pagination: {} }}
            notShowPagination
            noAddButton
            noDeleteButton
            wriTable={props.wriTable3}
            summary={renderSummary3}
          />
        </div>
      </div>
    );
  };

  const renderBill = () => {
    return (
      <FormElement3 form={form} style={{ display: 'inline-block', width: '600px' }}>
        <EnumerateFields formColumns={formColumns} colNumber={4} outerForm={form} />
      </FormElement3>
    );
  };

  const renderModals = () => {
    const rows = (props.wriTable2?.getList().full || []) as POJO[];
    const selected = props.wriTable2?.selectedSingleRow || {};
    const serial = selected[tableIndexKey];
    const row = (props.wriTable2?.getRow(serial) || {}) as POJO;
    let p: POJO[] = [];
    rows.forEach((r) => (p = p.concat(r.parItemKeys || [])));
    return (
      <React.Fragment>
        <ChoiceParItem
          visible={parItemVisible}
          hideHandle={(data) => {
            setParItemVisible(false);
            if (data) fillItems(data, row);
          }}
          data={{
            invoiceId: props.data?.invoiceId,
            parItemKeys: p,
            serviceType: row.serviceType,
            parMode: row.parMode,
            parId: row.parId,
            parType: row.parType,
            branchId: props.data?.dataTransObj?.orInvoiceInfo?.departmentId,
            custAptitude: props.custAptitude,
          }}
        />
        <VerifyInvoiceDetail
          visible={verifyVisible}
          signBranchTitle={props.data?.dataTransObj?.orInvoiceInfo?.signBranchTitle}
          hideHandle={(data) => {
            setVerifyVisible(false);
            if (data) {
              const sel = props.wriTable3.selectedSingleRow[tableIndexKey];
              props.wriTable3.updateRows({
                [tableIndexKey]: sel,
                itemName: data.shortName,
                productTypeId: data.key,
              });
            }
          }}
        />
        <ParDetail visible={parId !== ''} hideHandle={() => setParId('')} parId={parId} />
        <SelectInvalidPar
          visible={selectPar != undefined}
          condition={selectPar}
          hideHandle={(r) => {
            setSelectPar(undefined);
            if (r) uptParInfo(r.invoiceNo, r.parRemark);
          }}
        />
      </React.Fragment>
    );
  };

  return (
    <div className="invoceEdit">
      {renderButton()}
      {renderTable()}
      {renderModals()}
    </div>
  );
};

export default OpenInvoiceDetail;
