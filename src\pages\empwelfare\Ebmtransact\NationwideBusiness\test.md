# 全国业务维护弹窗测试说明

## 测试目标
验证使用 Codal 组件实现的业务内容和业务节点弹窗功能是否正常工作。

## 测试环境
- 组件：BusinessContentForm, BusinessNodeForm
- 技术栈：Codal + FormElement3 + EnumerateFields
- 表单库：Antd Form

## 测试用例

### 1. 业务内容弹窗测试

#### 1.1 新增模式测试
- [ ] 点击"新增"按钮，弹窗正常打开
- [ ] 弹窗标题显示"新增业务内容"
- [ ] 表单为空状态
- [ ] 所有字段均可编辑
- [ ] 业务类型选择后，业务项目联动更新
- [ ] 必填验证正常工作
- [ ] 点击"取消"按钮，弹窗关闭且表单重置
- [ ] 点击"确认"按钮，表单验证通过后调用回调函数

#### 1.2 修改模式测试
- [ ] 选择记录后点击"修改"按钮，弹窗正常打开
- [ ] 弹窗标题显示"修改业务内容"
- [ ] 表单预填充选中记录的数据
- [ ] 办理属性、办理对象、办理方式字段为只读状态
- [ ] 其他字段可正常编辑
- [ ] 业务类型变更时，业务项目选择被清空
- [ ] 表单验证正常工作

### 2. 业务节点弹窗测试

#### 2.1 新增模式测试
- [ ] 点击"新增"按钮，弹窗正常打开
- [ ] 弹窗标题显示"新增业务节点"
- [ ] 表单为空状态
- [ ] 业务节点名称字段可编辑
- [ ] 必填验证正常工作

#### 2.2 修改模式测试
- [ ] 选择记录后点击"修改"按钮，弹窗正常打开
- [ ] 弹窗标题显示"修改业务节点"
- [ ] 表单预填充选中记录的数据
- [ ] 业务节点名称字段可编辑

### 3. 交互测试

#### 3.1 按钮状态测试
- [ ] 加载状态下，确认按钮显示loading状态
- [ ] 取消按钮始终可点击
- [ ] 按钮样式符合设计要求

#### 3.2 表单联动测试
- [ ] 业务类型选择后，业务项目下拉选项正确更新
- [ ] 业务类型变更时，业务项目选择被清空
- [ ] 下拉选项数据正确加载

#### 3.3 只读字段测试
- [ ] 修改模式下，指定字段显示为只读状态
- [ ] 只读字段不可编辑但可显示值
- [ ] 只读字段样式区别于可编辑字段

## 预期结果

### 业务内容弹窗
- 弹窗宽度：80%
- 表单布局：2列
- 字段数量：8个
- 只读字段（修改模式）：办理属性、办理对象、办理方式

### 业务节点弹窗
- 弹窗宽度：50%
- 表单布局：1列
- 字段数量：1个
- 只读字段：无

## 错误处理测试
- [ ] 表单验证失败时，显示错误提示
- [ ] 网络请求失败时，显示错误消息
- [ ] 异常情况下，弹窗可正常关闭

## 性能测试
- [ ] 弹窗打开速度正常
- [ ] 表单渲染性能良好
- [ ] 联动操作响应及时

## 兼容性测试
- [ ] 不同浏览器下显示正常
- [ ] 不同屏幕尺寸下布局正确
- [ ] 移动端适配良好

## 测试数据

### 业务内容测试数据
```javascript
{
  businessType: '719001',
  businessProject: 'project001', 
  businessContent: '工伤认定申请',
  handleAttribute: '1',
  handleObject: '1', 
  handleMethod: '1',
  isWechatShow: '1',
  isClientShow: '1'
}
```

### 业务节点测试数据
```javascript
{
  businessNodeName: '材料审核'
}
```

## 测试完成标准
- 所有测试用例通过
- 无控制台错误
- 用户体验良好
- 符合设计要求
