# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an enterprise React application built with Umi.js framework (Ant Design Pro boilerplate). The application appears to be a Human Resources Management System with modules for employee hiring, welfare management, payroll, finance, and system administration.

## Common Development Commands

### Starting the Development Server
```bash
# Development mode
npm run start:dev

# Local development mode
npm run start:local

# JavaScript development mode
npm run start:js

# Test environment
npm run start:test

# Production preview
npm run start:pro
```

### Building the Application
```bash
# Production build
npm run build

# Development build
./build-dev.sh

# JavaScript build
./build-js.sh
```

Note: The build process may fail with Node.js v22 due to webpack's crypto function incompatibility. To resolve this, use:
```bash
NODE_OPTIONS=--openssl-legacy-provider npm run build
```

### Code Quality and Testing
```bash
# Run linter
npm run lint

# Run linter with auto-fix
npm run lint:fix

# Run tests
npm run test

# Run type checking
npm run tsc
```

### Code Generation
```bash
# Generate temporary files
npm run postinstall

# Fetch blocks
npm run fetch:blocks
```

## Project Architecture

### Key Directories
- `src/` - Main source code
  - `pages/` - Page components organized by module (Dashboard, User, Sales, CRM, etc.)
  - `components/` - Reusable UI components
  - `models/` - Dva.js state management models
  - `layouts/` - Application layout components
  - `utils/` - Utility functions
  - `apis/` - API service definitions
- `config/` - Umi configuration files
  - `config.ts` - Main Umi configuration
  - `router.config.ts` - Route definitions
- `scripts/` - Build and development scripts

### Framework and Libraries
- Umi.js 3.x - React framework
- Ant Design 4.x - UI component library
- Dva.js - State management (Redux wrapper)
- TypeScript - Type checking
- Less - CSS preprocessing

### Routing
The application uses a hierarchical routing system defined in `config/router.config.ts` with:
- User authentication routes under `/user`
- Main application routes under `/` with BasicLayout
- Dynamic routes loaded from JSON configuration files

### Build Process
The build process involves:
1. Loading functions with `loadfuncs` scripts
2. Installing dependencies with yarn
3. Building with Umi's build command
4. Custom webpack configuration in `config/config.ts` for code splitting and asset versioning

## Code Quality Guidelines

### Component Architecture
- Prefer functional components with React hooks over class components
- Keep components small and focused on a single responsibility
- Use proper memoization techniques (React.memo, useMemo, useCallback) for performance optimization
- Implement consistent styling approaches (prefer CSS modules over inline styles)

### State Management
- Use Dva.js models for global state management
- Separate synchronous reducers from asynchronous effects
- Implement proper error handling in effects
- Use TypeScript interfaces for model state and payloads
### API Integration
- Define API services in the `src/apis/` directory
- Handle API responses with proper error checking
- Implement request/response interceptors for common functionality
- Improve TypeScript typing for API responses

### Performance Optimization
- Use virtual scrolling for large datasets (implemented in BasicTable component)
- Implement windowing for long lists beyond the table component
- Review bundle sizes and optimize large dependencies
- Use code splitting with dynamic imports

### Security Considerations
- Implement comprehensive client-side input validation
- Ensure proper authentication flow with token management
- Regularly update dependencies to address security vulnerabilities
- Configure proper security headers

### Documentation and Maintainability
- Add English documentation for better collaboration
- Standardize code comments and documentation
- Establish and enforce consistent naming conventions
- Add README files or component documentation for complex components