# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an enterprise React application built with Umi.js framework (Ant Design Pro boilerplate). The application appears to be a Human Resources Management System with modules for employee hiring, welfare management, payroll, finance, and system administration.

## Common Development Commands

### Starting the Development Server
```bash
# Development mode
npm run start:dev

# Local development mode
npm run start:local

# JavaScript development mode
npm run start:js

# Test environment
npm run start:test

# Production preview
npm run start:pro
```

### Building the Application
```bash
# Production build
npm run build

# Development build
./build-dev.sh

# JavaScript build
./build-js.sh
```

### Code Quality and Testing
```bash
# Run linter
npm run lint

# Run linter with auto-fix
npm run lint:fix

# Run tests
npm run test

# Run type checking
npm run tsc
```

### Code Generation
```bash
# Generate temporary files
npm run postinstall

# Fetch blocks
npm run fetch:blocks
```

## Project Architecture

### Key Directories
- `src/` - Main source code
  - `pages/` - Page components organized by module (Dashboard, User, Sales, CRM, etc.)
  - `components/` - Reusable UI components
  - `models/` - Dva.js state management models
  - `layouts/` - Application layout components
  - `utils/` - Utility functions
  - `apis/` - API service definitions
- `config/` - Umi configuration files
  - `config.ts` - Main Umi configuration
  - `router.config.ts` - Route definitions
- `scripts/` - Build and development scripts

### Framework and Libraries
- Umi.js 3.x - React framework
- Ant Design 4.x - UI component library
- Dva.js - State management (Redux wrapper)
- TypeScript - Type checking
- Less - CSS preprocessing

### Routing
The application uses a hierarchical routing system defined in `config/router.config.ts` with:
- User authentication routes under `/user`
- Main application routes under `/` with BasicLayout
- Dynamic routes loaded from JSON configuration files

### Build Process
The build process involves:
1. Loading functions with `loadfuncs` scripts
2. Installing dependencies with yarn
3. Building with Umi's build command
4. Custom webpack configuration in `config/config.ts` for code splitting and asset versioning