# 在线 excel

在本项目中，拥有有大量的行内编辑场景，支持行内输入非常简单，困难在于单元格的验证。本组件致力于解决此问题。

![](../assets/images/2.9-excetable.png)

## 1.组件实现

```tsx
// src\components\Writable\index.tsx
// Excetable 组件属性定义
interface ExcetableProps<T> extends TableProps<T> {
  service: IServiceType;
  columns: ExcetableColumnProps<T>[];
  showPagination?: boolean;
  selectedRows?: T[];
  onSelectRow?: (rows: T[]) => void;
  onLatticeChange?: (index: number, key: string, value: object, record?: T) => void;
  onChange?: (
    pagination: PaginationConfig,
    filters: Partial<Record<keyof T, string[]>>,
    sorter: SorterResult<T>,
    extra?: TableCurrentDataSource<T>,
  ) => void;
  noAddButton?: boolean;
  noDeleteButton?: boolean;
  addButtons?: { [propName: string]: any }[];
  duplicated?: number;
  box?: ISheetBox;
  archive?: ISheetAchive;
  excelLoading?: boolean;
  dispatch?: Dispatch;
}

// 具体实现
class Excetable<T> extends React.Component<ExcetableProps<T>> {
  cacheKey: string;
  constructor(props: ExcetableProps<T>) {
    super(props);
    const { service, duplicated } = props;
    this.cacheKey = getCacheKey(service, duplicated);
  }

  componentDidMount() {
    const { dispatch, dataSource } = this.props;
    const incoming = [...(dataSource || [])];
    if (incoming.length > 0) {
      indexUp(incoming);
      dispatch!({
        type: 'excetable/setNewData',
        payload: { cacheKey: this.cacheKey, sheet: incoming },
      });
    }
  }

  // ... ...

  render() {
    // ... ...
    return (
      <div className={styles.excetable}>
        <Table
          loading={loading || excelLoading}
          rowKey={(record: T, index: number) => record[tableRowKey].toString()}
          columns={newColumns}
          dataSource={sheet}
          rowSelection={rowSelection}
          rowClassName={this.rowClassName}
          pagination={showPagination ? paginationProps : false}
          onChange={this.handleTableChange}
          {...rest}
        />
        {this.renderForm()}
      </div>
    );
  }
}
```

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| service | 必填。保存数据的 API 接口服务。 | `IServiceType` | - |
| duplicated | 选填。重复序号。在同一个页面内，重复使用相同`service`的个数。 | `number` | - |
| columns | 必填。表格列的配置。 | `ExcetableColumnProps<T>[]` | - |
| showPagination | 选填。是否显示分页。 | `WrappedFormUtils<any>` | `undefined` |
| selectedRows | 选填。与`StandardTable`中的`selectedRows`作用相同，用法相同。 | `T[]` | - |
| onSelectRow | 选填。与`StandardTable`中的`onSelectRow`作用相同，用法相同。 | `(rows: T[]) => void` | - |
| onLatticeChange | 选填。表格表单元格更改事件回调。用于父级组件上可能存在的监听函数。使用场景较少。 | `(index: number, key: string, value: object, record?: T) => void` | - |
| onChange | 选填。与`StandardTable`中的`onChange`作用相同，用法相同。本组件`change`的回调太多，为避免混乱，选用此名。 | `( pagination: PaginationConfig, filters: Partial<Record<keyof T, string[]>>, sorter: SorterResult<T>, extra?: TableCurrentDataSource<T>) => void;` | - |
| noAddButton | 选填。传递`true`时，将隐藏「添加按钮」。 | `boolean` | - |
| noDeleteButton | 选填。传递`true`时，将隐藏「删除按钮」。 | `boolean` | - |
| addButtons | 选填。自定义「添加按钮」。 | `POJO[]` | - |
| box | 不填。由`dva`传入。 | `ISheetBox` | - |
| archive | 不填。由`dva`传入。 | `ISheetAchive` | - |
| excelLoading | 不填。由`dva`传入。 | `boolean` | - |
| dispatch | 不填。由`dva`传入。 | `Dispatch` | - |

**columns**二级属性说明 columns 的类型定义

```tsx
// columns 的属性定义
interface ExcetableColumnProps<T extends POVO> extends ColumnProps<T> {
  inputRender?: GeneralInputRender<T>;
  inputProps?: Object;
  validators?: TValidationRule[];
  onGridChange?: (index: number, key: string, value: Partial<T>, record: T) => void;
}

const generalInputs = {
  string: stringInput,
  float: stringInput,
  number: numberInput,
  month: monthPicker,
  datetime: datetimePicker,
  date: datePicker,
  text: textArea,
};
```

属性说明属性 | 说明 | 类型 | 默认值 ----|------|----- | ---- inputRender| 选填。单元格的输入框类型，通常只需传入已定义的类型名字。若`generalInputs`未包含所需的输入框时，也可以直接编写实现方法。 | `GeneralInputRender<T>` | - validators| 选填。校验规则。| `TValidationRule[]` | - onGridChange| 选填。单元格更改时的回调。| `(index: number, key: string, value: Partial<T>, record: T) => void` | -

## 2.配套工具

`Excelib` 类。这是一种完全`Java`的代码方式。我们编写一个类，通过 `new` 实例化这个类，最后在实例上调用方法。定义：

```ts
// src\utils\excetable\excelib.ts
class Excelib<T> {
  public loading: boolean;

  private validateConf: TValidateConf;
  private service: IServiceType;
  private cacheKey: string;
  private dispatch: Dispatch | undefined;
  private duplicated: number | undefined;

  constructor(service: IServiceType, columns?: ExcetableColumnProps<any>[], duplicated?: number) {
    this.validateConf = columns ? gatherValidations(columns) : ({} as TValidateConf);
    this.service = service;
    this.cacheKey = getCacheKey(service, duplicated);
    this.dispatch = undefined;
    this.duplicated = duplicated;
    this.loading = false;
  }
  // ... ...
}
```

```tsx
// src\pages\payroll\WageArchive\index.tsx
const excelib: Excelib<TWageArchivesDTO> = new Excelib<TWageArchivesDTO>(service, columns);
```

Excelib 的方法说明方法 | 说明 | 类型 | 默认值 ----|------|----- | ---- constructor| 构造函数。`service`是保存数据的 API 接口服务。`columns`是`Table`的表格列定义。`duplicated`是重复次数。 | `~` | - validateSheet| 用于在提交数据前校验表格。 | `~` | - setSheet| 用于设置表格数据。在某些场景中，`Excetable`初始化时，数据不能通过`dataSource`属性传入，则可以使用此方法。 | `` | - setFileds| 用于手动设置单元格的值，在某些场景中，有些字段依赖于其他字段的值，此方法可满足这种需求。 | `~` | - setColumns| 用于用于手动设置`Table`的`columns`属性，在某些场景中，`columns`由后端动态加载，`Excelib`初始化时无法传入此参数，可在后来通过此方法补全 | `~` | - setFieldError| 用于手动设置单元格的错误指引，通常在`onGridChange`中进行此操作。 | `~` | - claerFieldError| 用于清除手动设置的单元格错误。须知：错误若不手动清除，将无法提交数据。 | `~` | -

## 3.应用

1. 基础使用

```tsx
// src\pages\payroll\WageArchive\index.tsx

import Excetable, { Excelib } from '@/components/Excetable';
const columns: ExcetableColumnProps<TWageArchivesDTO>[] = [
  {
    title: '唯一号',
    dataIndex: 'empId',
  },
  {
    title: '手机号码',
    dataIndex: 'phone',
    inputRender: 'string',
    validators: [
      {
        minLength: 8,
        message: '手机号码最短为8位',
      },
      {
        validator: (value: any, record: TWageArchivesDTO): boolean | void => {
          if (record.taxPayerType === 1 && record.employType === 1 && !value) {
            return true;
          }
        },
        message: '纳税人是居民，且从业类型是雇员时，手机号码必填',
      },
    ],
  },
  {
    title: '纳税人类型',
    dataIndex: 'taxPayerType',
    render: mapToRender<number>(taxPayerTypeMap),
    inputRender: mapToSelectorsInputRender<number>(taxPayerTypeMap),
    validators: [
      {
        required: true,
        message: '请选择纳税人类型',
      },
    ],
  },
  {
    title: '附加税率(%)',
    dataIndex: 'atr',
    inputRender: 'string',
  },
  {
    title: '是否在职',
    dataIndex: 'isDeleted',
    render: (text: number) => mapToSwitchView(stdBooleanMap, text),
  },
];

const excelib: Excelib<TWageArchivesDTO> = new Excelib<TWageArchivesDTO>(service, columns);

class WageArchive extends React.Component<WageArchiveProps, WageArchiveStates> {
  state = {
    selectedRows: [] as Array<TWageArchivesDTO>,
    onViewBankCard: false,
  };

  queryWageArchivees = (queryInput?: TWageArchiveQuery, pagination?: PaginationConfig) => {
    const { dispatch, wageArchiveQuery } = this.props;
    // 若传递进来的值包含数据，则覆盖state.formValues中的对应属性
    const values = { ...wageArchiveQuery, ...queryInput };
    const params = pageCacheRequest<TWageArchiveQuery>(values, pagination);
    dispatch!({
      type: 'cache/getPageQueries',
      payload: { service: service, params },
    }).then((resPage: TablePage<TWageArchivesDTO>) => excelib.setSheet(dispatch, resPage));
  };
}

onSave = () => {
  const { dispatch } = this.props;
  excelib.validateSheet(dispatch).then(({ sheet, error }) => {
    if (error) return ConfirmLoading.clearLoading(serviceSave);
    API.payroll.wageArchives.update.request(sheet).then((res: StdRes) => {
      if (resError(res)) return resErrorMsg(res, '保存失败');
      resErrorMsg(res, '保存成功');
    });
  });
};
```

说明：

这里演示了一些基础使用。可以看到， `columns` 的定义于普通的`StandardTable`非常相似。

- 「唯一号」、「是否在职」是普通数据展示，没有编辑功能。
- 「手机号码」是字符串输入框，`validators`是它的校验规则，当预定义的规则无法满足需求时，还可以自定义`validator`。
- 「纳税人类型」是下拉选择框。由于此组件在点击时，`inputRender`才会渲染，所以对于此类单元格，`render`，`inputRender`都需要手动设置。

关于`columns`内部元素的说明

`inputRender`的可选类型包括： "string" | "number" | "float" | "month" | "datetime" | "date" | "text"。各类型如其字面含义，不作赘叙。

- `validators`的默认校验规则有：

| 参数 | 说明 | 类型 | 默认 |
| --- | --- | --- | --- |
| type | 将值当做特定类型。通常用于`min`、`max`的校验中。 | `string`、`string[]` | - |
| enum | 枚举类型 | `string`、`string[]` | - |
| max | 最大值 | `number`、`string` | - |
| min | 最小值 | `number`、`string` | - |
| maxLength | 最大长度 | `number` | - |
| minLength | 最小长度 | `number` | - |
| message | 校验文案 | `string` | - |
| pattern | 正则表达式校验 | `RegExp` | - |
| required | 是否必填 | `boolean` | - |
| transform | 校验前转换字段值 | `(value: any) => any` | - |
| validator | 自定义校验 | `(value: any, record: T, options?: any) => boolean / void` | - |
| whitespace | 空格是否会被视为错误 | `boolean` | - |

需要特别说明的是 `validator` 在有错误时，应当返回`true`。

## 4.高级应用

这里主要说明各种特殊需求的应对。

### 4.1 以其它字段的值作为自己的参数

在薪资档案中，「扣缴义务人」是`API`版下拉框，请求参数中扣缴义务人类型`withholdAgentType`来自本行的数据。其定义如下：

```tsx
{
  title: '扣缴义务人类型',
  dataIndex: 'withholdAgentType',
  width: 200,
  render: mapToRender<number>(withholdAgentTypeMap),
  inputRender: mapToSelectorsInputRender<number>(withholdAgentTypeMap),
  validators: [
    {
      required: true,
      message: '请选择扣缴义务人类型',
    },
  ],
},
{
  title: '扣缴义务人',
  dataIndex: 'withholdAgentId',
  render: (text: number, record: T, index: number) => (
    <WithholdAgentSelector code={text} params={{ withholdAgentType: record.taxPayerType }} />
  ),
  inputRender: (onChange, options) => {
    const { defaultValue, record } = options;
    return (
      <WithholdAgentSelector
        onChange={onChange}
        defaultValue={defaultValue}
        params={{ withholdAgentType: record.taxPayerType }}
      />
    );
},
```

可以看到：「扣缴义务人」从`record`里面取到了本行的相关字段的值。

如果在`inputRender`中返回自定义组件，则`onChange`必须传入。`generalDateOnChange`是专用于日期控件的封装。

### 4.2 更改其它字段的值

「受雇日期」有一个特殊需求，当这个日期所属的月份，大于当前月份时，「金税申报月」应该更新为这个新的月份。

实现如下：

```tsx
{
  title: '受雇日期',
  dataIndex: 'hireDt',
  inputRender: 'date',
  onGridChange: (
    index: number,
    key: string,
    value: Partial<TWageArchivesDTO>,
    record: TWageArchivesDTO,
  ) => {
    const { hireDt } = value;
    if (hireDt) {
      const gtaxMonth = hireDt.slice(0, 7);
      if (gtaxMonth >= nowMonth) {
        excelib.setFileds(index, { gtaxMonth: gtaxMonth.replace('-', '') });
      }
    }
  },
  validators: [
    {
      required: true,
      message: '请选择受雇日期',
    },
    {
      dependency: '<=hireDt2',
    },
  ],
},
```

可以看到，这里使用了`onGridChange`属性来监听更改，并使用了`excelib.setFileds`来更新金税申报月`gtaxMonth`的值。

### 4.2 单独校验

「金税申报月」有一个特殊需求。这个值必须大于当前月份，但是这个要求仅适用于新更改的数据。历史数据未更改时，可以违背这个要求。所以这个字段不能通过设置`validators`中`min`来解决，需要在更新时校验。

实现如下：

```tsx
{
  title: '金税申报月',
  dataIndex: 'gtaxMonth',
  inputProps: { format: stdMonthFormatMoDash },
  onGridChange: (
    index: number,
    key: string,
    value: Partial<TWageArchivesDTO>,
    record: TWageArchivesDTO,
  ) => {
    const { gtaxMonth } = value;
    if (gtaxMonth && gtaxMonth < nowMonth) {
      excelib.setFieldError(index, key, '金税申报月必须大于当前月');
    } else {
      excelib.claerFieldError(index, key);
    }
  },
  validators: [
    {
      required: true,
      message: '请选择金税申报月',
    },
  ],
},
```

可以看到，`onGridChange`中做了校验。并通过`excelib.setFieldError`设置了这个单元格的错误信息。需要注意的是，在字段值校验通过时，必须通过`excelib.claerFieldError`清除错误信息，否则这个表单将永远无法提交。

这里还演示了`inputProps`的用法。

### 4.2 结合 Pop 组件使用

在「小合同维护」的拆分方表单中，有用到的 Pop 组件的地方。

实现如下：

```tsx
const serviceColumns: ExcetableColumnProps<TSubcontractServiceDTO>[] = [
  {
    title: '拆分方分公司',
    dataIndex: 'svcProviderName',
    inputRender: (onChange, options) => {
      const { record } = options;
      const { svcProviderId, svcProviderName } = record;
      return (
        <BranchPop
          rowValueOnly
          modalwidth={800}
          title="拆分方分公司-1"
          rowKey="svcProviderId"
          viewKey="svcProviderName"
          // rowValue="svcProviderId-svcProviderName-areaId-branchId"
          rowValue="svcProviderId-svcProviderName-svcProviderareaId"
          keyMap={{
            svcProviderId: 'branchId',
            svcProviderName: 'branchName',
            svcProviderareaId: 'areaId',
          }}
          rowValueData={{ svcProviderId, svcProviderName }}
          handdleConfirm={onChange}
        />
      );
    },
    onGridChange: (
      index: number,
      key: string,
      value: Partial<TSubcontractServiceDTO>,
      record: TSubcontractServiceDTO,
    ) => {
      excelib.setFileds(index, { csName: undefined, csId: undefined });
    },
    validators: [
      {
        required: true,
        message: '请选择拆分方分公司',
      },
    ],
  },
  {
    title: '拆分方客服',
    dataIndex: 'csName',
    inputRender: (onChange, options) => {
      const { record } = options;
      const { csId, csName, svcProviderareaId, svcProviderName } = record;
      return (
        <CsPop
          rowValueOnly
          modalwidth={800}
          title="拆分方客服"
          viewKey="csName"
          rowKey="csId"
          rowValue="csId-csName"
          keyMap={{ csId: 'userId', csName: 'chnName', svcProviderName: 'branchName' }}
          rowValueData={{ csId, csName }}
          fixedValues={{
            svcProviderName,
            areaId: svcProviderareaId,
            roleId: hrwRoleCode.SERVICE,
          }}
          handdleConfirm={onChange}
        />
      );
    },
    validators: [
      {
        required: true,
        message: '请选择拆分方客服',
      },
    ],
  },
  {
    title: '备注',
    dataIndex: 'remark',
    inputRender: 'text',
  },
];
```

可以看到，`inputRender` 中是如何使用`Pop`的，二者可以称得上绝配。「拆分方分公司」`onGridChange` 还轻易实现了本数据更新时，清空`csName`字段。「拆分方客服」中，轻易实现了，对「拆分方分公司」数据的监听，并将相关数据作为自己的组合条件。

`rowValueOnly`是`Pop`中的一个属性，其作用是在`handdleConfirm`中，仅返回`rowValue`中定义的字段，其他字段不返回。这样可以有效地避免对此行数据的干扰。
