/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2021-02-24 10:34:25
 * @LastAuthor: 侯成
 * @LastTime: 2021-02-24 14:47:21
 * @message: message
 * 请找出在feature-js中更新的文件。
 * 在“|”之后写上自己的名字。
 * 所有写了名字的文件，将直接覆盖dev分支下对应的文件。
 * 所有没有写名字的文件，将直接被dev分支下对应的文件覆盖。
 * 请慎重。
 * 18行-21行为示例。
 */


273 files changed, 8641 insertions(+), 7374 deletions(-)
.eslintignore                                      |   2 +-
config/browsers.json                               |   2 +-
config/config.ts                                   |   6 +-
config/defaultSettings.ts                          |   2 +-
config/locales/types.ts                            |   2 +-
config/routers/comp-basedata.json                  |   3 +-
config/routers/comp-emphiresep.json                |   1 -
config/routers/comp-payroll.json                   |  20 +-
config/routers/otherRoutes.json                    |   7 -
package.json                                       |   6 +-
scripts/loadFuncs-pro.js                           |  12 +-
scripts/loadFuncs.ts                               |  12 +-
src/components/Codal/index.tsx                     |   5 +-
src/components/ContentViewer/index.less            |   2 +-
src/components/ContentViewer/index.tsx             |   2 +-
src/components/DateComp/StrDatePicker.tsx          |  33 +-
src/components/DateRange4/dateRange.tsx            |  19 +-
src/components/EditeForm/AddForm.tsx               |   2 +-
src/components/Forms/index.less                    |  13 +-
src/components/GlobalHeader/NoticePending.tsx      |   4 +-
src/components/PageHeaderWrapper/breadcrumb.tsx    |   4 +-
src/components/Selectors/BaseDataSelectors.tsx     |  27 +-
src/components/Selectors/BaseDropDown.tsx          |  11 +
src/components/Selectors/BaseSelectors.tsx         |  42 +-
src/components/Selectors/FuncSelectors.tsx         |  26 +-
src/components/Selectors/index.tsx                 |   2 +-
src/components/StandardPop/SelectWithholdAgentPop.tsx         |  23 +-
src/components/StandardPop/SsGroupPop.tsx          |   5 +-
src/components/StandardTable/Pagination.tsx        |  12 +-
src/components/StandardTable/hooks/useStandardTable.tsx       |  12 +-
src/components/StandardTable/index.less            |  50 +-
src/components/StandardTable/index.tsx             | 100 ++--
src/components/UploadForm/ImportForm.tsx           |  78 ++-
src/components/UploadForm/ImportHistoryForm.tsx    |   2 -
src/components/UploadForm/index.tsx                |  10 +-
src/components/Writable/libs/GeneralInput.tsx      |   6 +-
src/components/Writable/libs/Grid.tsx              |  13 +-
src/components/Writable/libs/Writable.tsx          |   1 -
src/global.less                                    |  17 +-
src/layouts/BasicLayout.less                       |   9 +-
src/layouts/BasicLayout.tsx                        |  34 +-
src/layouts/HomeUser.tsx                           |  32 +-
src/layouts/components/index.tsx                   |  20 +-
src/models/cache.ts                                |   4 +-
src/models/notice.ts                               | 356 +++++------
src/pages/AddTransferAndSubcontract.tsx                  |  72 +--
src/pages/BillingApproval/components/InvoiceDetail.tsx   |  15 +-
src/pages/BillingApproval/components/ReceiveDetail.tsx   |   2 +-
src/pages/ClientEmpVisitedReport/index.tsx               |   2 +-
src/pages/ComplainAppSelect/components/AddFormWin.tsx    |  74 +--
src/pages/CountPayManage/Forms/ChangeCountPayWin.tsx     |  16 +-
src/pages/CountPayManage/Forms/DetailCountPay.tsx        |  31 +-
src/pages/CustomerQuery/components/AddCustMaterial.tsx   |  10 -
src/pages/CustomerQuery/components/CustomerDetail.tsx    |   6 +-
src/pages/CustomerQuery/components/LinkCustomer.tsx      |  25 +-
src/pages/CustomerQuery/components/SalaryCalculation.tsx |  28 +-
src/pages/DSPay/components/SubFilePayCustDetail.tsx      |   3 +-
src/pages/Dashboard/Welcome.tsx                    |  41 +-
src/pages/Dashboard/index.less                     |  38 +-
src/pages/Ebmtransact/HospitalTransact/index.tsx         |  23 +-
src/pages/EditEmpBankCardAdjust/relatedCustomerPop.tsx   |  78 ---
src/pages/FirstFeedBackSendManages/components/detail.tsx |  42 +-
src/pages/Forms/AddBatchDelProduct.tsx                   | 236 ++++----
src/pages/Forms/AddOrderForm.tsx                         | 132 ++--
src/pages/Forms/AddOrderForm.tsx                         | 134 ++---
src/pages/Forms/ApplyChangeTaskList.tsx                  |  21 +-
src/pages/Forms/BatchEmpAddPop.tsx                       |  35 +-
src/pages/Forms/DetailMinBaseAdjustment.tsx              |  41 +-
src/pages/Forms/EditEmpOrder.tsx                         | 114 ++--
src/pages/Forms/EditEmpOrder.tsx                         | 147 +++--
src/pages/Forms/EditQuotationOnly.tsx                    |  28 +-
src/pages/Forms/ImportResultTable.tsx                    | 191 ------
src/pages/Forms/MissionHistoryDetail.tsx                 | 110 ++++
src/pages/Forms/OrderProFormDetail.tsx                   | 188 ------
src/pages/Forms/PerfectSearchQuery.ts                    |  78 +--
src/pages/Forms/TransferEmpOrder.tsx                     | 165 +++--
src/pages/Forms/UpdateBatchAlterEmpOrderWin.tsx          | 315 +++++-----
src/pages/Forms/onConfirm.tsx                            |  36 +-
src/pages/Forms/onConfirm.tsx                            |  50 +-
src/pages/Gathering/UploadCash/components/Receipts.tsx   |  10 +
src/pages/HospitalTransact/Forms/ConfirmHpTransact.tsx   | 153 ++---
src/pages/HospitalTransact/Forms/addHpTransact.tsx       |  82 +--
src/pages/HospitalTransact/Forms/branchForms.tsx         | 14 +-
src/pages/JSDJ/AttachPay/components/DetailModal.tsx      | 沈彦霖  93 +++
src/pages/JSDJ/DonationdeducItemUpload/Detail.tsx        | 孙尚阳 123 ++++
src/pages/JSDJ/QueryEmpSubmitInfo/EmpSubmitHistory.tsx   | 孙尚阳 196 ++++++
src/pages/JSDJ/empDeclareReduce/components/Detail.tsx    | 沈彦霖 193 ++++++
src/pages/LaborcontractManage/components/main.tsx        |   2 +-
src/pages/LockManage/components/QuerySSLock.tsx          |  13 +-
src/pages/ManageTemplate/components/CustContract.tsx     |   2 +-
src/pages/Mrpay/index.tsx                          |  18 +
src/pages/Onecharges/components/OnechargesQuery.tsx      |  22 +-
src/pages/PayRollArchivesManage/Forms/MissionHistory.tsx | 122 ++++
src/pages/PaymentApply/components/DeliveryResult.tsx     |  42 --
src/pages/Policy/CalculationSsCust/index.tsx             | 206 +++++--
src/pages/Policy/CalculationSsSale/index.tsx             | 196 ++++--
src/pages/Policy/components/PolicyViewCodal/index.tsx    |  14 +-
src/pages/Policy/components/SingleCityForm/index.tsx     |   4 +-
src/pages/ProductRatio/Form/MaintainProductRatioForm.tsx |   4 +-
src/pages/ProvidentFund/QueryPfAdjustment/index.tsx      |   4 +-
src/pages/PureDataInterfaceManage/Forms/AddForms.tsx     |  57 +-
src/pages/QueryAdjustment/Forms/DetailAdjustForms.tsx    | 149 ++---
src/pages/QueryBill/components/LinkDetailForBill.tsx     |  13 +-
src/pages/QueryClientOrderForAdd/Forms/AddOrderForm.tsx  | 134 ++---
src/pages/QueryClientOrderForReduce/index.tsx            |  47 --
src/pages/QueryEmpOrderListForCon/Forms/AddOrderForm.tsx |  46 +-
src/pages/QueryEmpOrderListForGen/Forms/AddOrderForm.tsx | 185 +++---
src/pages/QueryEmpOrderListForPer/Forms/AddOrderForm.tsx | 133 +++--
src/pages/QueryEmpOrderListForSepApply/index.tsx         |  47 +-
src/pages/QueryEmpOrderListForSepCon/index.tsx           |   9 +-
src/pages/QueryExBill/components/LinkDetailForBill.tsx   |   8 +-
src/pages/QueryExEmpOrderListForSepCon/index.tsx         |   7 +-
src/pages/QueryImpWrongOrderPro/Forms/AddForms.tsx       | 300 ----------
src/pages/QueryImpWrongOrderPro/Forms/ImpOrderPro.tsx    | 137 -----
src/pages/QueryPfMinBaseAdjustment/index.tsx             |   4 +-
src/pages/QuerySocialMakeUp/components/index.tsx         |  10 +-
src/pages/QuerySocialSecurity/components/BaseInfo.tsx    |  15 +-
src/pages/QuerySocialSecurity/components/index.tsx       |  20 +-
src/pages/QuerySsMinBaseAdjustment/index.tsx             |   4 +-
src/pages/QueryTransferAndSubcontract/index.tsx          |   5 +-
src/pages/QuotationManage/Forms/QuotationAddForm.tsx     |   7 +-
src/pages/QuotationTempManage/TablePop/index.tsx         |   2 +-
src/pages/SZFFJGXN/components/QueryThisTimeDetailWin.tsx |   3 +-
src/pages/Sales/Contract/Manage/ContractApproveWin.tsx   |  13 +-
src/pages/Sales/Contract/Manage/ContractForm.tsx   |  10 +-
src/pages/Sales/Contract/Query/index.tsx           |   8 +-
src/pages/Sales/CustomerManage/CustomerQuery/index.tsx   |   5 -
src/pages/SetPayResult/components/SetBatchPayResult.tsx  |   3 +-
src/pages/SocialPay/components/FilePayApprove.tsx        | 103 +---
src/pages/SocialProcess/SocialFundProcess.tsx            | 184 ++----
src/pages/SocialProcess/SocialProcessBatch.tsx           |  36 +-
src/pages/SocialProcess/SocialProcessSingle.tsx          |  44 +-
src/pages/SocialUpdateSelect/SocialFundUpdateSelect.tsx  |  21 +-
src/pages/SocialUpdateSelect/SocialUpdateAll.tsx         |  19 +-
src/pages/SocialUpdateSelect/SocialUpdateBasic.tsx       |   5 +-
src/pages/Socialmanage/QuerySsAdjustment/index.tsx       |   4 +-
src/pages/Socialmanage/SocialApply/SocialFundApply.tsx   |  40 +-
src/pages/Socialmanage/SocialApply/components/tools.tsx  |  23 -
src/pages/Socialmanage/SocialChange/SocialChange.tsx     |   8 +-
src/pages/Socialmanage/SocialChange/SocialFundChange.tsx |  29 +-
src/pages/Socialmanage/SocialStop/SocialFundStop.tsx     |  21 +-
src/pages/SurveySsPolicyCust/components/tabCom.tsx       |  11 +-
src/pages/UpdatePayRollSendVirtualBatchWin.tsx           |  13 +-
src/pages/UploadCash/components/InvoiceEditModal.tsx     |  20 +-
src/pages/UploadCash/components/InvoiceEntry.tsx         |   3 -
src/pages/UploadCash/components/VerifyDetail.tsx         |  16 +-
src/pages/UploadTaxDeduction/components/ImpDeduction.tsx |  34 +-
src/pages/UploadTaxDeduction/components/UploadReslut.tsx |  10 +-
src/pages/UploadTaxDeduction/components/index.tsx        |   8 +-
src/pages/User/ForgotPwd.tsx                       |  66 --
src/pages/User/Login.less                          | 187 +-----
src/pages/User/Login.tsx                           |  30 +-
src/pages/User/MinicLogin.tsx                      |   2 +-
src/pages/User/clientLogin.less                    | 170 ++++++
src/pages/XJFFPCXN/components/ShowPayBatchDetail.tsx     |   7 +-
src/pages/XZFFPC/XJFFPC/components/WageApplyPay.tsx      |  18 +-
src/pages/XZFFPCXN/XJFFPCXN/components/PaySendDetail.tsx |  11 +-
src/pages/basedata/SpecialSignerTitleMaintance/index.tsx | 129 ----
src/pages/components/AddExportModal.tsx                  | 120 ++++
src/pages/components/AddModal.tsx                        | 139 -----
src/pages/components/AddModal.tsx                        | 160 -----
src/pages/components/DelayPayRollSendBatchWin.tsx        |  15 +-
src/pages/components/MaintainExport.tsx                  |  99 +++
src/pages/components/NewAddSIBackPayImp.tsx              |   5 -
src/pages/components/NewAddSIBackPayImp.tsx              |   5 -
src/pages/components/OpenInvoiceDetail.tsx               |  33 +-
src/pages/components/SendHistory.tsx                     | 133 +++++
src/pages/components/SetBatchPayResultVirtual.tsx        |   3 +-
src/pages/components/ShowFailureModal.tsx                |  77 +++
src/pages/components/UpdateLaborContract.tsx             |  26 +-
src/pages/components/UpdatePayRollSendBatchWin.tsx       |   5 +-
src/pages/components/WithholdAgentPopDanli.tsx           |  11 +
src/pages/crm/cust/QueryIfmCustApprove/index.tsx   |   6 +-
src/pages/emphiresep/emporder/QueryAdjustment/index.tsx  |  46 +-
src/pages/emphiresep/hire/HireClassify/index.tsx   |   2 +-
src/pages/emphiresep/quitManager/BatchQuitLite/index.tsx |  10 +-
src/pages/emporder/ConfirmEmpOrderForAssigner/index.tsx  |  34 +-
src/pages/emporder/EmpBankCardMaintainance/index.tsx     |   4 -
src/pages/emporder/EmployeeBaseInfoManage/index.tsx      |  11 +-
src/pages/emporder/NationWideEmpQuery/index.tsx          |  10 +-
src/pages/emporder/QueryEmpDetailInfo/index.tsx          |  38 +-
src/pages/emporder/QueryEmpOrderListForCon/index.tsx     |  34 +-
src/pages/emporder/QueryEmpOrderListForEdit/index.tsx    |  40 +-
src/pages/emporder/QueryEmpOrderListForPer/index.tsx     |  32 +-
src/pages/emporder/QueryEmployeeOrder/index.tsx          |  50 +-
src/pages/emporder/QueryExEmpOrderListForEdit/index.tsx  |  40 +-
src/pages/emporder/QueryExEmpOrderListForPer/index.tsx   |  20 -
src/pages/emporder/QueryExEmployeeOrder/index.tsx        |  14 +-
src/pages/emporder/QueryImpOrderLite/index.tsx           |   7 +-
src/pages/emporder/QueryImpWrongOrderPro/index.tsx       | 221 -------
src/pages/emporder/QueryMinBaseAdjustment/index.tsx      |  20 +-
src/pages/empwelfare/LaborcontractManage/index.tsx |   4 +-
src/pages/empwelfare/PayManage/components/index.tsx      |   8 -
src/pages/finance/Gathering/UploadCash/Query.tsx   |   2 +-
src/pages/finance/Receive/BillLockAndUnlock/index.tsx    |  57 +-
src/pages/finance/Receive/BillPrintReport/index.tsx      | 132 ++--
src/pages/finance/Receive/CreateBill/index.tsx     |  19 +-
src/pages/finance/Receive/OrderBill/index.less     |   2 +-
src/pages/hire/HireClassify/components/CustMaterial.tsx  |  16 +-
src/pages/infopublication/Policy/SiteQuery/index.tsx     |  17 +-
src/pages/payroll/CountPayManage/index.tsx         |  36 +-
src/pages/payroll/DataInterfaceManage/Forms/AddForms.tsx |  72 +--
src/pages/payroll/EditEmpBankCard/index.tsx        |   3 -
src/pages/payroll/JSDJ/AttachPay/index.tsx         | 沈彦霖 156 +++++
src/pages/payroll/JSDJ/DonationdeducItemUpload/index.tsx | 孙尚阳 110 ++++
src/pages/payroll/JSDJ/EndowmentItemUpload/Detail.tsx    |  孙尚阳 107 ++++
src/pages/payroll/JSDJ/EndowmentItemUpload/index.tsx     | 孙尚阳 110 ++++
src/pages/payroll/JSDJ/HealthItemUpload/Detail.tsx | 孙尚阳 111 ++++
src/pages/payroll/JSDJ/HealthItemUpload/index.tsx  | 孙尚阳 110 ++++
src/pages/payroll/JSDJ/QueryDonationdeducItem/index.tsx  | 孙尚阳 122 ++++
src/pages/payroll/JSDJ/QueryEmpSubmitInfo/Detail.tsx     | 孙尚阳 113 ++++
src/pages/payroll/JSDJ/QueryEmpSubmitInfo/index.tsx      | 孙尚阳 349 +++++++++++
src/pages/payroll/JSDJ/QueryEmpSubmitTask/Detail.tsx     | 孙尚阳 197 ++++++
src/pages/payroll/JSDJ/QueryEmpSubmitTask/index.tsx      | 孙尚阳 148 +++++
src/pages/payroll/JSDJ/QueryEndowmentItem/index.tsx      | 孙尚阳 108 ++++
src/pages/payroll/JSDJ/QueryHealthItem/index.tsx   | 孙尚阳 111 ++++
src/pages/payroll/JSDJ/QueryReliefItem/index.tsx   | 孙尚阳 107 ++++
src/pages/payroll/JSDJ/ReliefItemUpload/Detail.tsx | 孙尚阳 107 ++++
src/pages/payroll/JSDJ/ReliefItemUpload/index.tsx  | 孙尚阳 202 +++++++
src/pages/payroll/JSDJ/TaxDeclarationManages/index.tsx   | 孙尚阳 221 +++++++
src/pages/payroll/JSDJ/TaxPayQuery/Detail.tsx      | 孙尚阳 197 ++++++
src/pages/payroll/JSDJ/TaxPayQuery/index.tsx       | 孙尚阳 148 +++++
src/pages/payroll/JSDJ/TaxPaymentDownload/Detail.tsx     | 孙尚阳 197 ++++++
src/pages/payroll/JSDJ/TaxPaymentDownload/index.tsx      | 孙尚阳 148 +++++
src/pages/payroll/JSDJ/TriPartyQuery/Detail.tsx    | 孙尚阳 197 ++++++
src/pages/payroll/JSDJ/TriPartyQuery/index.tsx     | 孙尚阳 148 +++++
src/pages/payroll/JSDJ/UngenerateTaxPay/index.tsx  | 孙尚阳 103 ++++
src/pages/payroll/JSDJ/empDeclareReduce/index.tsx  | 孙尚阳 155 +++++
src/pages/payroll/JSDJ/uptEmpInfoManages/index.tsx | 孙尚阳 181 ++++++
src/pages/payroll/PayRollArchivesManage/index.tsx  | 374 +++++++++++-
src/pages/payroll/PayTaxes/index.tsx               | 219 -------
src/pages/payroll/WithholdAgentLimitMaintance/index.tsx  | 165 -----
src/pages/payroll/XZDFS/SENDMAIL/index.tsx         |   4 +-
src/pages/payroll/XZFFPC/QueryPayResult/index.tsx  |  20 +-
src/pages/payroll/XZFFPC/XJFFPC/Create.tsx         |   2 +-
src/pages/payroll/XZFFPC/XJFFPC/Query.tsx          |  29 +-
src/pages/payroll/XZFFPCXN/XJFFPCXN/Query.tsx      |   9 +-
src/pages/payroll/archives/CheckEmpTypeManages/index.tsx |  11 +-
src/pages/payroll/archives/CheckHrieDtManages/index.tsx  |  11 +-
src/pages/payroll/bswjdc/index.tsx                 |  11 +-
src/pages/payroll/deduction/CustTaxReport/index.tsx      |  73 +--
src/pages/payroll/withholdAgent/QueryWithholdAgent.tsx   |  13 +
src/pages/pending/BillingApproval/utils/createHtml.ts    |  51 +-
src/pages/pending/DSPay/index.tsx                  |   9 +-
src/pages/pending/EditEmpBankCardAdjust/index.tsx  | 663 ---------------------
src/pages/pending/OnechargesApproval/index.tsx     |  26 +-
src/pages/pending/PaymentApply/components/Delivery.tsx   |  82 ---
src/pages/pending/PaymentApply/index.tsx           | 199 -------
src/pages/pending/PdFilePay/index.tsx              |   4 +-
src/pages/pending/QueryPrvdPayApprove/index.tsx    |   3 +-
src/pages/pending/QuotationSale/index.tsx          |   3 +-
src/pages/pending/UnlockWfl/UnlockPopAudit.tsx     |   9 +-
src/pages/pending/WagePay/components/WagePayApprove.tsx  |  14 +-
src/pages/pending/WagePay/index.tsx                |   4 +-
src/pages/pending/WagePayVirtual/index.tsx         |   4 +-
src/pages/prvdPayApply/components/SubFilePayBase.tsx     |   4 +-
src/pages/qualitycontrol/ComplainAppSelect/index.tsx     |   1 +
src/pages/sendorder/CustomerSubcontract/ContractView.tsx |  16 +-
src/pages/sendorder/CustomerSubcontract/index.tsx        |  10 +-
src/pages/sendorder/ManageTemplate/components/Detail.tsx |   6 +-
src/pages/sysmanage/AllocateService/index.tsx      |  19 +-
src/pages/withholdAgent/components/WithholdAgentPop.tsx  |  11 +
src/pages/{SocialApply => }/SocialApplyReceivable.tsx    |   5 +-
src/utils/forms/validate.ts                        |   8 +-
src/utils/login/index.ts                           |   8 +-
src/utils/methods/file.ts                          |   5 +-
src/utils/methods/pagenation.ts                    |  11 -
src/utils/methods/times.ts                         |   4 +-
src/utils/model/index.tsx                          |   8 +-
src/utils/settings/payroll/jsdj.ts                 | 孙尚阳  34 ++
src/utils/settings/payroll/payRollArchivesManage.ts      |  25 +
src/utils/welfaremanage/Socialmanage/socialApply.ts      |  24 +-
yarn.lock                                          | 259 +-------
