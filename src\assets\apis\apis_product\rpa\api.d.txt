declare namespace defs {
  export namespace rpa {
    export class AnswerQuery {
      /** 城市名称 */
      cityName: string;

      /** 标准问题 */
      standardQuestion: string;
    }

    export class BaseEntity {
      /** 批次号,用于备份 */
      batchId: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 城市Id */
      cityId: number;

      /** 公司编码 */
      companyCode: string;

      /** 公司编码Id */
      companyCodeId: number;

      /** 公司名称 */
      companyName: string;

      /** 创建人 */
      createBy: string;

      /** 创建日期 */
      createDt: string;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 代理人 */
      proxyBy: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 办理方/小合同名称 */
      welfareProcessorName: string;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: object;

      /** message */
      message: string;

      /** t */
      t: object;
    }

    export class CompanyCodeVO {
      /** 城市名称 */
      cityName: string;

      /** 公司编码 */
      companyCode: string;

      /** 公司编码Id */
      companyCodeId: number;

      /** 公司名称 */
      companyName: string;

      /** 办理方/小合同名称 */
      welfareProcessorName: string;
    }

    export class FilterEntity {
      /** 批次号,用于备份 */
      batchId: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 城市Id */
      cityId: number;

      /** 公司编码 */
      companyCode: string;

      /** 公司编码Id */
      companyCodeId: number;

      /** 公司名称 */
      companyName: string;

      /** 创建人 */
      createBy: string;

      /** 创建日期 */
      createDt: string;

      /** 删除标记 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** processorName */
      processorName: string;

      /** 代理人 */
      proxyBy: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 办理方/小合同名称 */
      welfareProcessorName: string;
    }

    export class MedicalLevelVO {
      /** cityId */
      cityId: number;

      /** 一档 */
      levelOne: number;

      /** 三档 */
      levelThree: number;

      /** 二档 */
      levelTwo: number;

      /** 缴费档次转化成% */
      medicalLevel: string;

      /** 缴费档次Id */
      medicalLevelId: number;
    }

    export class PageInfo {
      /** endRow */
      endRow: number;

      /** hasNextPage */
      hasNextPage: boolean;

      /** hasPreviousPage */
      hasPreviousPage: boolean;

      /** isFirstPage */
      isFirstPage: boolean;

      /** isLastPage */
      isLastPage: boolean;

      /** list */
      list: Array<object>;

      /** navigateFirstPage */
      navigateFirstPage: number;

      /** navigateLastPage */
      navigateLastPage: number;

      /** navigatePages */
      navigatePages: number;

      /** navigatepageNums */
      navigatepageNums: Array<number>;

      /** nextPage */
      nextPage: number;

      /** pageNum */
      pageNum: number;

      /** pageSize */
      pageSize: number;

      /** pages */
      pages: number;

      /** prePage */
      prePage: number;

      /** size */
      size: number;

      /** startRow */
      startRow: number;

      /** total */
      total: number;
    }

    export class RpaFaqBatchDTO {
      /** 批次ID */
      batchId: number;

      /** 批次状态（1、导出发布中2、成功3、失败4、流程中断5、数据异常6、已通知训练师） */
      cityStatus: number;

      /** 批次状态（1、导出发布中2、成功3、失败4、流程中断5、数据异常6、已通知训练师） */
      nationalStatus: number;

      /** rpaFaqList */
      rpaFaqList: Array<defs.rpa.RpaFaqDTO>;

      /** 机器人状态（0、成功，1、失败） */
      rpaStatus: number;
    }

    export class RpaFaqBatchVO {
      /** 导入编号 */
      batchId: string;

      /** 批次状态（1、导出发布中2、成功3、失败4、流程中断5、数据异常6、已通知训练师） */
      cityStatusName: string;

      /** 导入日期 */
      createDt: string;

      /** 失败记录数 */
      failureCount: string;

      /** 导入文件 */
      fileName: string;

      /** 导入文件路径 */
      filePath: string;

      /** 批次状态（1、导出发布中2、成功3、失败4、流程中断5、数据异常6、已通知训练师） */
      nationalStatusName: string;

      /** 机器人状态（成功，失败） */
      rpaStatusName: string;

      /** 成功记录数 */
      successCount: string;

      /** 导入人 */
      userName: string;
    }

    export class RpaFaqDTO {
      /** 城市名称 */
      cityName: string;

      /** 问题编号 */
      faqId: number;

      /** 全国适用 */
      nationwide: string;

      /** 发布状态 */
      releaseStatus: string;

      /** 标准问题 */
      standardQuestion: string;
    }

    export class RpaFaqTempVO {
      /** 回答 */
      answer: string;

      /** 业务项目 */
      busnameClassName: string;

      /** 所属类型 */
      categoryName: string;

      /** 所属城市 */
      cityName: string;

      /** 修改回答 */
      editAnswer: string;

      /** 修改回答时间 */
      editDt: string;

      /** 错误描述 */
      errorInfo: string;

      /** 问题编号 */
      faqId: string;

      /** 导入结果 */
      impResult: string;

      /** 全国适用，是，否 */
      nationwide: string;

      /** 发布状态 */
      releaseStatusName: string;

      /** rnum */
      rnum: number;

      /** 标准问题 */
      standardQuestion: string;

      /** uploadType=0 为新增，uploadType=1 为修改 */
      uploadType: number;
    }

    export class RpaFaqVO {
      /** 回答 */
      answer: string;

      /** 业务项目 */
      busnameClassName: string;

      /** 所属类型 */
      categoryName: string;

      /** 所属城市 */
      cityName: string;

      /** 修改回答 */
      editAnswer: string;

      /** 修改回答时间 */
      editDt: string;

      /** 问题编号 */
      faqId: string;

      /** 全国适用，是，否 */
      nationwide: string;

      /** 发布状态 */
      releaseStatusName: string;

      /** 标准问题 */
      standardQuestion: string;

      /** uploadType=0 为新增，uploadType=1 为修改 */
      uploadType: number;
    }
  }
}

declare namespace API {
  export namespace rpa {
    /**
     * Call Controller
     */
    export namespace call {
      /**
        * 查询历史
查询历史
        * /rpa/call/callHistory
        */
      export namespace callHistory {
        export class Params {
          /** begin */
          begin: string;
          /** end */
          end?: string;
          /** taskId */
          taskId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /rpa/call/export
        */
      export namespace exporting {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出详情
导出详情
        * /rpa/call/exportDetail
        */
      export namespace exportDetail {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 测试定时任务
测试定时任务
        * /rpa/call/generateSendList
        */
      export namespace generateSendList {
        export class Params {
          /** type */
          type: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 重新自动外呼
重新自动外呼
        * /rpa/call/getRecallStatus
        */
      export namespace getRecallStatusFromRedis {
        export class Params {
          /** empHireSepId */
          empHireSepId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 手动启动任务
手动启动任务
        * /rpa/call/manualStartTask
        */
      export namespace manualStartTask {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 手动插入明细
手动插入明细
        * /rpa/call/queryAndInsertDetail
        */
      export namespace queryAndInsertDetail {
        export class Params {
          /** begin */
          begin: string;
          /** end */
          end?: string;
          /** taskId */
          taskId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询呼叫列表
查询调整任务列表
        * /rpa/call/queryHireCallList
        */
      export namespace queryHireCallList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.PageInfo;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询呼叫明细列表
查询调整任务列表
        * /rpa/call/queryHireDetailList
        */
      export namespace queryHireDetailList {
        export class Params {
          /** rpaHireCallId */
          rpaHireCallId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询呼叫明细列表
查询调整任务列表
        * /rpa/call/queryHireDetailList
        */
      export namespace postQueryHireDetailList {
        export class Params {
          /** rpaHireCallId */
          rpaHireCallId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 重新自动外呼
重新自动外呼
        * /rpa/call/recall
        */
      export namespace recall {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.rpa.BaseEntity>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.rpa.BaseEntity>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新是否已经人工通知
更新是否已经人工通知
        * /rpa/call/updateBatchList
        */
      export namespace updateBatchList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.rpa.BaseEntity>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.rpa.BaseEntity>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Faq Controller
     */
    export namespace faq {
      /**
        * 查询faqBatch列表
查询faqBatch列表
        * /rpa/faq/batch/list
        */
      export namespace batchList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.RpaFaqBatchVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改状态信息
修改状态信息
        * /rpa/faq/detail/put
        */
      export namespace updateInfos {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.RpaFaqDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.rpa.RpaFaqDTO>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.rpa.RpaFaqDTO>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * faq下载,uploadType=0 为新增，uploadType=1 为修改,如果下载文件就不传uploadType
faq上传文档
        * /rpa/faq/download
        */
      export namespace download {
        export class Params {
          /** fileName */
          fileName: string;
          /** minioPath */
          minioPath: string;
          /** uploadType */
          uploadType: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询faq下拉
查询faq下拉
        * /rpa/faq/dropdown/list
        */
      export namespace DropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.PageInfo;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * faq导出
faq导出
        * /rpa/faq/export
        */
      export namespace exporting {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询faq列表
查询faq列表
        * /rpa/faq/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.RpaFaqVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据批次号修改状态信息
根据批次号修改状态信息
        * /rpa/faq/put
        */
      export namespace putPut {
        export class Params {}

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.rpa.RpaFaqBatchDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.RpaFaqBatchDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 手动获取反馈
手动获取反馈
        * /rpa/faq/restart
        */
      export namespace restart {
        export class Params {
          /** batchId */
          batchId: string;
        }

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * faqTemp导出
faqTemp导出
        * /rpa/faq/temp/export
        */
      export namespace tempExport {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询faqTemp列表
查询faq列表
        * /rpa/faq/temp/list
        */
      export namespace faqTempList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.RpaFaqTempVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * faq上传文档,uploadType=0 为新增，uploadType=1 为修改
faq上传文档
        * /rpa/faq/upload
        */
      export namespace postUpload {
        export class Params {
          /** uploadType */
          uploadType: number;
          /** userId */
          userId: string;
        }

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Faq Call Back Controller
     */
    export namespace faqCallBack {
      /**
        * 根据城市名称和标准问题获取答案
根据城市名称和标准问题获取答案
        * /api/faq/answer
        */
      export namespace queryFaqByCity {
        export class Params {}

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.rpa.AnswerQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.AnswerQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * File Controller
     */
    export namespace file {
      /**
        * 和downloadFile的区别为,此方法下载的文件不转换
和downloadFile的区别为,此方法下载的文件不转换
        * /rpa/file/generalDownloadExcel
        */
      export namespace generalDownloadFile {
        export class Params {
          /** fileId */
          fileId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 直接传path，不根据id再查数据库
直接传path，不根据id再查数据库
        * /rpa/file/generalDownloadFileByPath
        */
      export namespace generalDownloadFileByPath {
        export class Params {
          /** filePath */
          filePath: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * app入职
     */
    export namespace hireFromApp {
      /**
        * 查询劳动合同信息
查询劳动合同信息
        * /rpa/hire/contract/info
        */
      export namespace queryContractInfo {
        export class Params {
          /** custCode */
          custCode: string;
          /** empId */
          empId: string;
        }

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询电脑号
查询电脑号
        * /rpa/hire/emp/account
        */
      export namespace queryEmpAccount {
        export class Params {
          /** ssDetailId */
          ssDetailId: string;
        }

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询入职信息
查询入职信息
        * /rpa/hire/info
        */
      export namespace queryHireAssociationList {
        export class Params {
          /** cityId */
          cityId: string;
          /** idCardNum */
          idCardNum: string;
        }

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 社保缴费档次
     */
    export namespace medicalLevel {
      /**
        * 新增缴费档次
新增缴费档次
        * /rpa/medical/level/add
        */
      export namespace add {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.BaseEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除缴费档次
删除缴费档次
        * /rpa/medical/level/del
        */
      export namespace del {
        export class Params {
          /** medicalLevelId */
          medicalLevelId: number;
        }

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询缴费档次列表
查询缴费档次列表
        * /rpa/medical/level/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.MedicalLevelVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Rpa Sz Social Rule Controller
     */
    export namespace rpaSzSocialRule {
      /**
        * 导出
导出
        * /rpa/rpaSzSocialRule/export
        */
      export namespace exporting {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增深圳社保自动导出规则
新增深圳社保自动导出规则
        * /rpa/rpaSzSocialRule/insertRpaSzSocialRule
        */
      export namespace insertRpaSzSocialRule {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询深圳社保自动导出规则列表
查询深圳社保自动导出规则列表
        * /rpa/rpaSzSocialRule/queryRpaSzSocialRuleList
        */
      export namespace queryRpaSzSocialRuleList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.PageInfo;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量更新修改深圳社保自动导出规则
批量更新修改深圳社保自动导出规则
        * /rpa/rpaSzSocialRule/updateBatchList
        */
      export namespace updateBatchList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.rpa.BaseEntity>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.rpa.BaseEntity>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新深圳社保自动导出规则
更新深圳社保自动导出规则
        * /rpa/rpaSzSocialRule/updateRpaSzSocialRule
        */
      export namespace updateRpaSzSocialRule {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 公司编码
     */
    export namespace ssCompanyCode {
      /**
        * 新增公司编码
新增公司编码
        * /rpa/company/code/add
        */
      export namespace add {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.BaseEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除公司编码
删除公司编码
        * /rpa/company/code/del
        */
      export namespace del {
        export class Params {}

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<defs.rpa.CompanyCodeVO>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.rpa.CompanyCodeVO>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改公司编码
修改公司编码
        * /rpa/company/code/edit
        */
      export namespace edit {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.BaseEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据办理方/小合同名称获取公司名称和编码
根据办理方/小合同名称获取公司名称和编码
        * /rpa/company/code/info
        */
      export namespace info {
        export class Params {
          /** welfareProcessorName */
          welfareProcessorName: string;
        }

        export type Response<T> = defs.rpa.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询公司编码列表
查询公司编码列表
        * /rpa/company/code/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.CompanyCodeVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.rpa.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询公司编码和名称
查询公司编码和名称
        * /rpa/company/code/query
        */
      export namespace queryByDetailId {
        export class Params {
          /** detailId */
          detailId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.rpa.CompanyCodeVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
