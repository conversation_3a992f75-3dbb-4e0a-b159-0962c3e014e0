<!--
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2019-11-08 18:24:42
 * @LastAuthor   : 侯成
 * @LastTime     : 2019-12-25 14:54:38
 * @message: message
 -->
# 开发教程
本文讲述的是如何开始一个开发任务，将包含几乎所有常见问题。

我们以【大合同管理】这一任务为例，该目录的入口文件为`src\pages\Sales\ContractManage\index.tsx`。
## 术语

**术语列表**：
* 一级菜单
* 一级模块
* 二级菜单
* 三级菜单
* 一级目录
* 二级目录
* 三级目录

**术语释义**：

一级菜单： 指用户界面中导航条的级别处于第一级的菜单。下图所示即为一级菜单。
![图1.1 一级菜单](../assets/images/4-implements/4.0.1-一级菜单.png)

一级模块：一级菜单下所包含的所有子菜单，作为一个整体，称为一级模块。

二级菜单： 指用户界面中导航条的级别处于第二级的菜单。下图所示即为二级菜单。
![图1.2 二级目录](../assets/images/4-implements/4.0.2-二级菜单.png)

三级菜单： 指用户界面中导航条的级别处于第三级的菜单。下图所示即为三级目录。
![图1.3 二级目录](../assets/images/4-implements/4.0.3-三级菜单.png)

**规范释义**：

目录和文件名称大小写问题：所有直接包含`*.tsx`的目录，应当以大写开头。所有`*.tsx`文件，应当以大写开头。

——

一级目录，二级目录，三级目录特指工程项目中包含`index.tsx`入口文件的目录。

一级目录：指前端项目工程目录中，`src\pages`目录下级别处于第一级的目录。下图所示即为一级目录。像`basedata`, `Dashboard`及以下，均为一级目录，且都包含`index.tsx`入口文件。
![图1.4 一级目录](../assets/images/4-implements/4.0.4-一级目录.png)

二级目录：指前端项目工程目录中，`src\pages`目录下级别处于第二级的目录。下图所示即为二级目录。`CityInfo`, `CompanyMaintains`, `CountryInfo`等，均为二级目录，且都包含`index.tsx`入口文件
![图1.5 二级目录](../assets/images/4-implements/4.0.5-二级目录.png)

三级目录：指前端项目工程目录中，`src\pages`目录下级别处于第三级的目录。下图所示即为三级目录。`Maintenance`, `PersonCategory`, `ProductRatio`等，均为三级目录，且都包含`index.tsx`入口文件
![图1.5 二级目录](../assets/images/4-implements/4.0.6-三级目录.png)

## 示例

本文档将演示如何开始一个任务。内容包括：
* 确定入口文件`index.tsx`的位置。
* 配置路由`url`并与入口文件关联起来。
* 通过pont插件生成`API`调用代码。
* 如何定义类型，`interface`和`type alias`。
* 使用带有泛型`<T>`的类型。
* `Antd`的`Form`组件如何封装。
* 创我们自己的`model`，定义`state`的类型，定义`model`的类型。
* 在一级模块组织我们的`model`，通过`connect.d.ts`，在整个模块中引用我们的`model`。
* connect起来。
* 使用全局缓存`cache`这个`modle`。
* 使用加载状态`loading`这个`modle`。
* 如何编写一个表单。
* `components/Forms/FormLayouts`公共组件的使用。
* 如何定义`formRules`和它的类型。
* 如何使用与表单元素有关插件，`CustomerPop`，`AreaSelector`，`mapToSelectors`等等。
* 实现查询方法`handleSearch`。
* 如何引入表格`EnhancedTable`。实现回调方法，`handleSelectRows`，`handleStandardTableChange`等。
* 如何定义表格的`columns`。

**示例代码**位于`src\pages\Sales\ContractManage\index-example.tsx`

### 1 入口文件的位置
当我遇到一个任务，通常是开发一个新的页面。当需求要求我们开发一个叫做【大合同管理】的页面时，我们遇到了第一个问题：这个入口文件应该放在哪儿？

通常需求文档的位置就可以指示出这个目录的位置，【大合同管理】大需求文档位于`chro_doc\10销售管理\10-10客户.docx`文件中，所以【大合同管理】是【销售】下的二级菜单，入口文件位于`src\pages\Sales`的下的二及目录。

![图1.5 二级目录](../assets/images/4-implements/4.0.7-需求文档.png)

在某些情况下，无法通过需求文档找出菜单所属的位置，这时也可以通过`chro_doc\HRW菜单.xls`文件来查看层级关系。文件内容如下：

![图1.8 菜单大全](../assets/images/4-implements/4.0.8-菜单大全.png)
图1.8 菜单大全

可以看到【合同管理】是位于【销售管理】下的二级菜单。

确定这些关系后，我们就可以创建入口文件了。

首先，`src\pages`下应当有会一个一级目录，对应着【销售管理】这个一级菜单。如果没有，那说明，我们是第一位进入这个一级菜单模块的人，应当创建这个一级目录，目录名称应当清晰简明，我们取名叫做`sales`，然后在其下创建名称为`ContractManage`的目录，最后在`ContractManage`目录下创建名为`index.tsx`。
至此，我们完成了一个任务的第一步，在正确的位置创建入口文件。

### 2 配置路由
仅仅创建`index.tsx`文件还不足以完成起步，还需要配置路由，以便从路由器访问这个文件。这部分的工作，就是路由配置。

首先，启动项目，至用户界面下【系统功能维护】菜单。在系统功能列表中找到【合同管理】菜单。如图所示：
![图1.9 系统功能维护-初始](../assets/images/4-implements/4.0.9-系统功能维护-初始.png)

找到【合同管理】的『链接地址』输入栏，可以看见，初始值是系统生成的。我们需要更改成用户可以识别的url地址，这个地址应当以右侧的『父级地址』开头，然后添加上自己的地址即`contractmanage`，然后保存。url一般使用全小写，无需使用驼峰法。最后效果如下：
![图1.10 系统功能维护-初始](../assets/images/4-implements/4.0.10-系统功能维护.png)
至此，二级菜单【合同管理】的url地址`/sales/contractmanage`就保存到了数据库，登录时将换回接口返回。

第二步，是配置本地的`Router`。在工程项目中找到`config\routers`目录，结构如下：
![图1.11 系统功能维护-初始](../assets/images/4-implements/4.0.11-路由配置.png)

以`comp`开头的就是我们的配置文件，这些配置根据项目中的一级菜单分成了各个子文件，以`comp-`+ `一级目录的url`的方式命名。我们当前任务所在的一级菜单是【销售管理】，所以我们的路由配置应当位于`comp-sales.json`文件中，文件的大致内容如下：
![图1.11 系统功能维护-初始](../assets/images/4-implements/4.0.12-路由配置示例.png)
第5行，路由的名字`contractmanage`作为`json`格式的键名，其值是我们入口文件的地址，这个地址不是绝对地址，而是以`src/pages`为基准的相对地址。在`|`符号左侧的，是这个菜单的名称，其注释作用。
至此，`Router`中的url `/sales/contractmanage`就和我们的入口文件`src\pages\Sales\ContractManage\index.tsx`关联起来了，重启我们的项目，就可以通过这个菜单访问到我们文件了。

### 3 标准实现
我们的项目类似于控制管理，提供给用户管理业务数据的功能。所以项目最常见的需求，便是数据展示，本示例中的【合同管理】即是如此。以下将说明如何实现一个标准任务。

首先，需要更新后端提供的接口配置。找到我们的接口管理操作栏，示例如下：
![图1.11 系统功能维护-初始](../assets/images/4-implements/4.0.14-pont操作栏.png)
，点击操作栏中的`origin`，即可弹出接口模块选择窗口：
![图1.11 系统功能维护-初始](../assets/images/4-implements/4.0.13-选择接口.png)
选择正确的接口模块，本例中是`sale`。选中之后，由于操作系统存在文件占用问题，我们需要停止`yarn start`运行的项目。然后在操作栏一次点击`sync`，`all`和`generate`。我们接口文件便创建成功了，文件位于`src\apis`目录下。
![图1.11 系统功能维护-初始](../assets/images/4-implements/4.0.14-apis.png)

#### 3.1 模板代码
至此，我们就可以实现业务代码了。在`src\pages\Sales\ContractManage\index.tsx`中，创建模板代码：

```tsx
import React, { Component } from 'react';
import { Dispatch } from '@/models/connect';
import { Card } from 'antd';
import { TablePage } from '@/utils/methods/pagenation';

type TContractDTO = defs.sale.ContractDTO
type TContractQuery = Partial<defs.sale.ContractDTO>

interface ContractProps {
  contracts?: TablePage<TContractDTO>;
  loading?: boolean;
  dispatch?: Dispatch;
}

interface ContractStates {
  selectedRows: Array<TContractDTO>;
  formValues: TContractQuery;
}

class Contract extends Component<ContractProps, ContractStates> {
  render() {
    return (
      <Card>
        合同管理
      </Card>
    )
  }
}

export default Contract;
```
访问页面，效果如下：
![图1.11 系统功能维护-初始](../assets/images/4-implements/4.0.15-合同页面.png)
我们的代码已经被框架装载了。

代码释义：
#### 3.1.1 外部引用
```tsx
import React, { Component } from 'react';
import { Dispatch } from '@/models/connect';
import { Card } from 'antd';
import { TablePage } from '@/utils/methods/pagenation';
```
`Dispatch`  是一个`type`别名，用来指示`ContractProps`接口类型(`interface`)中`dispatch`属性的类型。
`TablePage` 是一个拥有泛型参数的`interface`，其定义如下：
```ts
export interface TablePage<T> {
  list: Array<T>;
  pagination: PaginationConfig;
}
```
在使用时，`<T>`的`T`应当被替换为现实中的类型。

#### 3.1.2 内部类型定义
在这段代码中，我们还定义了自己的类型。
```tsx
type TContractDTO = defs.sale.ContractDTO
type TContractQuery = Partial<defs.sale.ContractDTO>

interface ContractProps {
  contracts?: TablePage<TContractDTO>;
  loading?: boolean;
  dispatch?: Dispatch;
}

interface ContractStates {
  selectedRows: Array<TContractDTO>;
  formValues: TContractQuery;
}
```
`TContractDTO`是一个`type`别名，如其名称所示，它是类型`defs.sale.ContractDTO`的别名，这个类型是后端返回的合同列表的数据主体结构。由之前生成代码时自动生成，可以在`src\apis`目录下找到。
`TContractQuery`是另一个`type`别名，`Partial`的作用是将原始类型中的所有属性变成可选属性。也就是说，`defs.sale.ContractDTO`中必填的属性，在`TContractQuery`中将变全部成可选的。

`ContractProps`是`Contract`组件的`props`的类型。我们在其中定义了一些基础属性，`contracts`是表格中数据列表，它的类型是`TablePage<TContractDTO>`，将由后端API提供具体数据。
* `loading`是`dva`的运行状态，可以在页面上展示加载状态。
* `dispatch`是`dva`提供的转发器，用于调用相关的`models`。

`ContractStates`是`Contract`组件的`state`的类型。
* `selectedRows`是表格中被选中的条目。
* `formValues`是查询表格中的表单数据。

#### 3.1.3 Form封装
一个组件如果要使用`antd`中`Form`组件提供相关功能，就要用`Form`来封装。对上面代码做以下更改：
```tsx
import { Card, Form } from 'antd';
import { FormComponentProps } from 'antd/lib/form';

interface ContractProps extends FormComponentProps {
  contracts: TablePage<TContractDTO>;
  loading?: boolean;
  dispatch?: Dispatch;
}

export default Form.create<ContractProps>()(Contract);
```
我们引入了一个新的组件`Form`，用来包装`Contract`组件，这样在`Contract`组件中就能使用`Form`提供的表单验证和数据收集功能了。

#### 3.1.4 connect起来
组件如果要连接`model`，就需要使用`connect`来包装。为此，我们需要做一系列准备工作。

##### 3.1.4.1 我们自己的`model`
在我们所属的一级目录下找到`models`目录，如果没有，则说明我们是第一个开发这个一级菜单模块的人，需要自己新建这个目录。然后在`models`目录中创建我们的`model`文件，在此例中我们`model`的名字叫做`contract`。
然后就可以编写这个`model`了：
```ts
// src\pages\Sales\models\contract.ts
import { Effect } from 'umi';
import { Reducer } from 'umi';
import { resError, resErrorMsg, resOkMsg } from '@/utils/methods/message';

type TContractDetailVO = defs.sale.ContractDetailVO

export interface ContractModelState {
  // 当前被选中的合同
  contractDetail: TContractDetailVO;
}

export interface ContractModel {
  namespace: 'contract';
  state: ContractModelState;
  effects: {
    getContractInfo: Effect;
    updateContractInfo: Effect;
  };
  reducers: {
    setContractDetail: Reducer<ContractModelState>;
  };
}

const ContractModel: ContractModel = {
  namespace: 'contract',
  state: {
    contractDetail: {} as TContractDetailVO,
  },
  effects: {
    *getContractInfo({ payload }, { call, put }) {
      const res = yield call(API.sale.contract.getCustomer.request, payload);
      if (resError(res)) return resErrorMsg(res, '获取合同信息失败');
      resErrorMsg(res, '获取合同信息成功');
      const response = responseData(res);
      yield put({
        type: 'setContractDetail',
        payload: response,
      });
    },
  },
  reducers: {
    setContractDetail(state, action) {
      const payload = action.payload as TContractDetailVO;
      return {
        ...state,
        contractDetail: payload,
      };
    },
  }
}
```
这段代码包含了非常多的内容。
第一部分：
基础类型定义
```ts
type TContractDetailVO = defs.sale.ContractDetailVO

export interface ContractModelState {
  // 当前被选中的合同
  contractDetail: TContractDetailVO;
}
```
我们定义一个`type`别名`TContractDetailVO`，它是合同详情的数据结构所对应的类型。
`ContractModelState` 是我们的`model`中的`state`的类型，它目前含有一个属性，`contractDetail`，它的类型是`TContractDetailVO`。

第二部分：
定义`model`的类型
```ts
export interface ContractModel {
  namespace: 'contract';
  state: ContractModelState;
  effects: {
    getContractInfo: Effect;
    updateContractInfo: Effect;
  };
  reducers: {
    setContractDetail: Reducer<ContractModelState>;
  };
}
```
紧接着，我们定义了`model`的类型，名字叫做`ContractModel`，这个类型可以用来指示和限制我们的`model`。

* `namespace`是`model`的名字，类型是 `'contract'`。
* `state`是`model`中数据存储的结果，它的类型是我们上面定义的`ContractModelState`。
* `effects`中定义了两个方法，分别是`getContractInfo`，`updateContractInfo`，他们的类型都是`Effect`。
* `reducers`中定义了一个方法，叫做`setContractDetail`，类型是`Reducer<ContractModelState>`。

第三部分：
在我们定义完`ContractModel`类型之后，就可以按照这个类型的指示来编写`model`了。在强类型语言中，这一步称为实现(implement)，例如在`java`中，这个步骤叫做实现(implement)`ContractModel`接口(interface)。示例如下：
```ts
const ContractModel: ContractModel = {
  namespace: 'contract',
  state: {
    contractDetail: {} as TContractDetailVO,
  },
  effects: {
    *getContractInfo({ payload }, { call, put }) {
      const res = yield call(API.sale.contract.getCustomer.request, payload);
      if (resError(res)) return resErrorMsg(res, '获取合同信息失败');
      resErrorMsg(res, '获取合同信息成功');
      const response = responseData(res);
      yield put({
        type: 'setContractDetail',
        payload: response,
      });
    },
  },
  reducers: {
    setContractDetail(state, action) {
      const payload = action.payload as TContractDetailVO;
      return {
        ...state,
        contractDetail: payload,
      };
    },
  }
}
```
这样，我们就实现了`ContractModel`这个接口。
* `contractDetail`的初始值是一个空对象，并且做了类型转换。
* `getContractInfo`中展示了一个标准的`API`如何编写。`resError`将检查错误，`resErrorMsg`给出错误信息。`resOkMsg`给出请求成功的信息，这个信息通常是不需要的，视需求而定。
* `setContractDetail`中更新了`state`。

##### 3.1.4.2 组织我们的`model`
`model`编写完成后，下一个问题就是如何使用。为此，我们需要进行一系列工作。
在我们所属的一级目录下找到`models`目录，在其下寻找`connect.d.ts`，此例中是`src\pages\Sales\models\connect.d.ts`。如果没有，则说明我们是第一个开发这个一级菜单模块的人，需要自己新建这个`connect.d.ts`文件。它看起来通常是这样：
```ts
import { StdLoading, CacheState, CacheLoading } from '@/models/connect';
import { ContractModelState } from './contract';
import { CustomerPayModelState } from './customerPay';

interface SalesLoadingModel extends CacheLoading  {
  contract?: boolean;
  customerPay?: boolean;
}

interface SalesConnectState extends CacheState  {
  loading: StdLoading<SalesLoadingModel>;
  contract?: ContractModelState;
  customerPay?: CustomerPayModelState;
}

export default SalesConnectState;
```
在这里，我们定义了两个新的`interface`。
`SalesLoadingModel`是`Sales`这个一级模块下，各个`model`加载状态的定义区。用`model`的名字作为键，类型是`boolean`。**cache**是必须定义的属性。
`SalesConnectState`是`Sales`这个一级模块下，各个`model`自身的定义区。在`connect`方法中作为类型指示来使用。**cache**是必须定义的属性。`SalesLoadingModel`在泛型分装后，被塞进了`loading`这个属性，我们将在`connect`方法中看到如何使用这个属性。

##### 3.1.4.3 connect起来
通过以上步骤，我们的`model`就全部定意思完成了，接下来就可以使用了。回到入口文件，本例中是`src\pages\Sales\ContractManage\index.tsx`。我们来添加以下内容：
```tsx
import SalesConnectState from '@/pages/Sales/models/connect'
import { getTableQueries, getTableParams } from '@/utils/methods/cache';

const service = API.query.contract.getContractList

@connect(({ cache, contract, loading }: SalesConnectState) => ({
  contracts: getTableQueries<TContractDTO>(service, cache!.tableQueries),
  contractQuery: getTableParams<TContractQuery>(service, cache!.tableParams),
  loading: loading.models.contract,
  contractDetail: contract!.contractDetail,
}))
class Contract extends Component<ContractProps, ContractStates> {
  // ... ...
}
```
这样，我们的`Contract`组件就和`dva`中的`model`连接起来了。这个组件一共连接了三个`model`，分别是:
* `cache`：全局缓存`model`，定义在`src\models\cache.ts`中。
* `contract`：我们自己定义的合同`model`，定义在`src\pages\Sales\models\contract.ts`中。
* `loading`：我们自己定义的加载状态`model`，定义在`src\pages\Sales\models\connect.d.ts`中。
然后我们取出了四个属性，分别是：
* `contracts`，表格中展示的合同列表，来自于`cache`这个全局缓存`model`。
* `contractQuery`，查询合同列表的表单数据，来自于`cache`这个全局缓存`model`。
* `cacheLoading`，`cache`这个`model`的加载状态，来自于`loading`这个`model`。
* `contractDetail`，某一个合同的详情，来自于`contract`这个`model`，也就是我们自己定义的`model`。

至此，与`model`有关的功能就演示结束了。

#### 3.1.5 添加表单
编写表单
目标效果：
![图1.16 查询表单](../assets/images/4-implements/4.0.16-查询表单.png)
```tsx
import { TFormRules } from '@/utils/settings/forms';
import { QUERY_TYPE } from '@/utils/settings/sales';
import { FormElement4, RowElement, ColElement4 } from '@/components/Forms/FormLayouts';
import FormItem from '@/components/Forms/FormItem';
import CustomerPop from '@/components/StandardPop/CustomerPop';
import DateRange, { DateRangeFormItem, formatPickedDate, MomentRange } from '@/components/DateRange';
import { column4 } from '@/utils/forms/typography';

const formRules: TFormRules<TContractDTO> = {
  contractName: [
    {
      required: true,
      message: '请输入大合同名称',
    },
    {
      max: 100,
      message: '大合同名称长度应在100以内',
    },
  ],
}

class Contract extends Component<ContractProps, ContractStates> {

  createDtRange = () => {
    const { form } = this.props;
    const fields: DateRangeFormItem[] = [
      {
        title: '创建时间≥',
        dataIndex: 'createDtStart',
        // range: '2019-07-13~2019-08-13',
      },
      {
        title: '创建时间≤',
        dataIndex: 'createDtEnd',
        // range: '2019-08-13~2020-08-13',
      },
    ];
    return (
      <DateRange
        fields={fields}
        outerForm={form}
        colConf={column4}
      />
    );
  };
  approveDtRange = () => {
    const { form } = this.props;
    const fields: DateRangeFormItem[] = [
      {
        title: '审批时间≥',
        dataIndex: 'approveDtStart',
      },
      {
        title: '审批时间≤',
        dataIndex: 'approveDtEnd',
      },
    ];
    return (
      <DateRange
        fields={fields}
        outerForm={form}
        colConf={column4}
      />
    );
  };

  renderForm() {
    const {
      form: { getFieldDecorator },
    } = this.props;
    const [marginLeft, marginBottom] = [8, 24];
    const {
      selectedRows,
      onModal,
      onLiabilityBranchPop,
      onSignBranchPop,
      onInnerUserPop,
    } = this.state;
    const onZero = selectedRows.length === 0;
    const onOne = selectedRows.length === 1;

    return (
      <FormElement4 onSubmit={this.handleSearch}>
        <RowElement>
          <ColElement4>
            <CustomerPop
              label="客户名称"
              rowValue="custId-custName"
              onModal={onModal}
              taggleModal={this.taggleModal}
              fixedValues={\{ queryType: queryType }\}
              outerForm={this.props.form}
            />
          </ColElement4>
          <ColElement4>
            <FormItem label="大合同名称">
              {getFieldDecorator('contractName', { initialValue: '开发测试客户-外包004', rules: formRules.contractName })(
                <Input placeholder="请输入" />,
              )}
            </FormItem>
          </ColElement4>
          <ColElement4>
            <FormItem label="大合同编号">
              {getFieldDecorator('contractId')(<Input placeholder="请输入" />)}
            </FormItem>
          </ColElement4>

          <ColElement4>
            <FormItem label="签单大区">{getFieldDecorator('signArea')(<AreaSelector allowClear />)}</FormItem>
          </ColElement4>
        </RowElement>
        <RowElement>
          <ColElement4>
            <BranchPop
              modalwidth={800}
              label="签单分公司"
              viewKey="signBranchhName"
              rowValue="signBranch"
              keyMap={\{ signBranch: 'branchId', signBranchhName: 'branchName' }\}
              onModal={onSignBranchPop}
              outerForm={this.props.form}
              taggleModal={this.taggleSignBranchPop}
            />
          </ColElement4>
          <ColElement4>
            <InnerUserPop
              modalwidth={800}
              label="现销售"
              fixedValues={\{ bizCategory: bizCategoryCode.SALE, roleId: 30501 }\}
              viewKey="liabilityCs"
              rowValue="saleId"
              keyMap={\{ saleId: 'userId' }\}
              onModal={onInnerUserPop}
              outerForm={this.props.form}
              taggleModal={this.taggleInnerUserPop}
              // fieldOptions={\{rules: formRules.liabilityCs}\}
            />
          </ColElement4>
          <ColElement4>
            <FormItem label="签约实体公司">
              {getFieldDecorator('entityId')(<Input placeholder="请输入" />)}
            </FormItem>
          </ColElement4>
          <ColElement4>
            {/* <FormItem label="责任分公司">
              {getFieldDecorator('liabilityBranch')(<Input placeholder="请输入" />)}
            </FormItem> */}
            <BranchPop
              modalwidth={800}
              label="责任分公司"
              viewKey="liabilityBranchName"
              rowValue="liabilityBranch"
              keyMap={\{ liabilityBranch: 'branchId', liabilityBranchName: 'branchName' }\}
              onModal={onLiabilityBranchPop}
              outerForm={this.props.form}
              taggleModal={this.taggleLiabilityBranchPop}
            />
          </ColElement4>
        </RowElement>
        <RowElement>
          <ColElement4>
            <FormItem label="到款类型">
              {getFieldDecorator('paymentType')(mapToSelectors(paymentTypeMap))}
            </FormItem>
          </ColElement4>
          <ColElement4>
            <FormItem label="缴费类型">
              {getFieldDecorator('payType')(<Input placeholder="请输入" />)}
            </FormItem>
          </ColElement4>
          <ColElement4>
            <FormItem label="合同类别">
              {getFieldDecorator('contractCategery')(<Input placeholder="请输入" />)}
            </FormItem>
          </ColElement4>
          <ColElement4>
            <FormItem label="创建人">
              {getFieldDecorator('creator')(<Input placeholder="请输入" />)}
            </FormItem>
          </ColElement4>
        </RowElement>
        <RowElement>
          {this.createDtRange()}
          {this.approveDtRange()}
        </RowElement>
        <RowElement style={\{ marginBottom }\}>
          <Button type="primary" htmlType="submit">
            查询
          </Button>
          <Button onClick={this.handleOnContractInfo} style={\{ marginLeft }\} disabled={!onOne}>
            查看
          </Button>
          <Button onClick={this.handleOnContractAdd} style={\{ marginLeft }\}>
            新增
          </Button>
          <Button onClick={this.handleOnContractModify} style={\{ marginLeft }\} disabled={!onOne}>
            修改
          </Button>
          <Button onClick={this.handleOnAddHandOver} style={\{ marginLeft }\} disabled={!onOne}>
            填写交接单
          </Button>
          <Button
            onClick={this.handleOnApprove}
            style={\{ marginLeft }\}
            disabled={!onOne || !this.approvable()}
          >
            提交审批
          </Button>
          <Button style={\{ marginLeft }\} disabled={!onOne || !this.renewable()}>
            续签
          </Button>
          <Button style={\{ marginLeft }\} disabled={onZero} onClick={this.handleDeleteContract}>
            删除
          </Button>
          <Button style={\{ marginLeft }\} disabled={!onOne}>
            更换现销售
          </Button>
          <Button style={\{ marginLeft }\} onClick={this.exportContract}>
            导出
          </Button>
        </RowElement>
      </FormElement4>
    );
  }

  render() {
    return (
      <Card>
        {this.renderForm()}
      </Card>
    )
  }
}
```
由查询表单的内容较多，通常会独立出来写入单独的方法，此处使用的是`renderForm`。
我们又引入了很多新代码，下来逐一说明：

表单验证规则的定义。
```tsx
import { TFormRules } from '@/utils/settings/forms';

const formRules: TFormRules<TContractDTO> = {
  custId: [
    {
      required: true,
      message: '请选择客户',
    },
  ],
  contractName: [
    {
      required: true,
      message: '请输入大合同名称',
    },
    {
      max: 100,
      message: '大合同名称长度应在100以内',
    },
  ],
}
```

检查当前选中的条数。
```tsx
const { selectedRows } = this.state;
const onZero = selectedRows.length === 0;
const onOne = selectedRows.length === 1;
```

关于`FormElement4`，`RowElement`，`ColElement4`的使用。
```tsx
 <FormElement4 onSubmit={this.handleSearch}>
  <RowElement>
    <ColElement4>
    // ... ...
    </ColElement4>
  </RowElement>
</FormElement4>
```
此外，这个表单中还演示了一些常见输入项。
* 客户名称，`CustomerPop` 是弹窗选择框，在`src\components\StandardPop`下还可以找到大量此类型的组件。这里还展示了`fixedValues`的用法，这个属性用于传递默认参数。详情参见文档 [标准弹窗](2-components/2.1-standard-pop.md)。
* 签单大区，`signArea`，展示了`AreaSelector`公共组件的使用，在`src\components\Selectors\BaseDataSelectors.tsx`下还可以找到大量此类型的组件。具体说明参见文档 [通用选择器](2-components/2.5-base-selectors.md)。
* 签单分公司，`BranchPop`展示了`keyMap`的用法，具体说明参见文档 [标准弹窗](2-components/2.1-standard-pop.md)。
* 到款类型，`paymentType`，展示了`mapToSelectors`的使用，这是一个将`Map`数据结构渲染成下拉选择框的方法。具体说明参见文档 [通用选择器](2-components/2.5-base-selectors.md)。
* 创建时间， `createDtRange()`展示了`DateRange`时间范围选择器的使用，具体说明参见文档 [日期范围选择](2-components/2.4-date-range.md)。
* 查看按钮中，`disabled={!onOne}`展示了如何通过当前被选中得条数，来控制按钮的`disabled`属性。

这些基本涵盖了一个表单会遇到的所有情形。

最后，我们要实现一个核心方法，`handleSearch`，用来响应查询按钮的点击事件。从上面代码可以看到，`FormElement4`的`onSubmit`属性指向回调方法`handleSearch`，以下是其实现：
```tsx
class Contract extends Component<ContractProps, ContractStates> {

  handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const { form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      this.queryContracts(fieldsValue, pageInit);
    });
  };
  render() {
    return (
      <Card>
        {this.renderForm()}
      </Card>
    )
  }
}

```

#### 3.1.6 添加表格
在模块中加入更多的组件，来实现业务功能。
目标效果：
![图1.17 表单和表格](../assets/images/4-implements/4.0.17-表单和表格.png)

##### 3.1.6.1 引入`EnhancedTable`
```tsx
import { EnhancedTable, StandardTableColumnProps } from '@/components/StandardTable'

class Contract extends Component<ContractProps, ContractStates> {
  render() {
    return (
      <Card>
        <EnhancedTable
          // rowKey="contractId"
          loading={cacheLoading}
          data={contracts}
          selectedRows={selectedRows}
          onSelectRow={this.handleSelectRows}
          columns={columns}
          onChange={this.handleStandardTableChange}
        />
      </Card>
    )
  }
}
```
从以上代码可以看到， `EnhancedTable`依赖的参数非常多。我们需要逐一实现。

##### 3.1.6.2 实现`columns`
```tsx
import { EnhancedTable, StandardTableColumnProps } from '@/components/StandardTable'
import { areaTypeMap } from '@/utils/settings/sales';
import {
  contractTypeMap,
  contractSubTypeMap,
  paymentTypeMap,
  contractSvcStateMap,
  contractAppStateMap,
} from '@/utils/settings/sales/contract';
import { AreaSelector, BranchSelector, mapToSelectors } from '@/components/Selectors/BaseDataSelectors';
import { CsIdToName } from '@/components/Selectors/CodeToView';
import { sToNumber } from '@/utils/methods/transfer';

const columns: StandardTableColumnProps<TContractDTO>[] = [
  {
    title: '客户名称',
    dataIndex: 'custName',
  },
  {
    title: '客户编号',
    dataIndex: 'custId',
  },
  {
    title: '大合同名称',
    dataIndex: 'contractName',
  },
  {
    title: '大合同编号',
    dataIndex: 'contractId',
  },
  {
    title: '合同大类',
    dataIndex: 'contractType',
    render: (text: string) => mapToSelectView(contractTypeMap, sToNumber(text)),
  },
  {
    title: '合同小类',
    dataIndex: 'contractSubType',
    render: (text: string) => mapToSelectView(contractSubTypeMap, sToNumber(text)),
  },
  {
    title: '服务区域类型',
    dataIndex: 'areaType',
    render: (text: string) => mapToSelectView(areaTypeMap, sToNumber(text)),
  },
  {
    title: '签单分公司',
    dataIndex: 'signBranch',
    render: (text: number) => <BranchSelector code={text} />,
  },
  {
    title: '现销售',
    dataIndex: 'salesMan',
    render: (text: number) => <CsIdToName id={text} />,
  },
  {
    title: '签订状态',
    dataIndex: 'contractSvcState',
    render: (text: string) => mapToSelectView(contractSvcStateMap, sToNumber(text)),
  },
  {
    title: '审批状态',
    dataIndex: 'contractAppState',
    render: (text: string) => mapToSelectView(contractAppStateMap, sToNumber(text)),
  },
];
```
这里基本涵盖了`render`的各种实现。
* 合同大类`contractType`中使用了`mapToSelectView`，用于`mapToSelect`选择框类型的数据展示。
* 合同小类`contractSubType`中使用了`sToNumber`，用于`string`至`number`的转换，作用相反的方法是`nToString`。
* 签单分公司`signBranch`中使用了`BranchSelector`公共组件，用于`BaseDataSelector`选择框类型的数据展示。
* 现销售`salesMan`中使用了`CsIdToName`公共组件，用于通过`id`展示名称。

##### 3.1.6.3 实现回调方法
```tsx
import { QUERY_TYPE } from '@/utils/settings/sales';
import { TablePage, pageCacheRequest, pageInit } from '@/utils/methods/pagenation';
import { PaginationConfig, SorterResult } from 'antd/lib/table';
import { filterToQuery } from '@/utils/methods/tables';

class Contract extends Component<ContractProps, ContractStates> {
  state: ContractStates = {
    selectedRows: [],
  };

  queryContracts = (queryInput?: TContractQuery, pagination?: PaginationConfig) => {
    const { dispatch, contractQuery } = this.props;
    // 若传递进来的值包含数据，则覆盖state.formValues中的对应属性
    const values = { ...contractQuery, ...queryInput, queryType: QUERY_TYPE.sale };
    const params = pageCacheRequest<TContractQuery>(values, pagination);
    dispatch!({
      type: 'cache/getPageQueries',
      payload: { service: service, params },
    }).then(() => this.setState({ selectedRows: [] }));
  };

  handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const { form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      this.queryContracts(fieldsValue, pageInit);
    });
  };

  handleSelectRows = (rows: TContractDTO[]) => {
    this.setState({ selectedRows: rows });
  }
  
  handleStandardTableChange = (
    pagination: PaginationConfig,
    filtersArg: Record<keyof TContractDTO, string[]>,
    sorter: SorterResult<TContractDTO>,
  ) => {
    const filtered = filterToQuery(filtersArg);
    this.queryContracts(filtered, pagination);
  };
  render() {
    const { cacheLoading, contracts } = this.props
    const { selectedRows } = this.state
    return (
      <Card>
        <EnhancedTable
          rowKey="contractId"
          loading={cacheLoading}
          data={contracts}
          selectedRows={selectedRows}
          onSelectRow={this.handleSelectRows}
          columns={columns}
          onChange={this.handleStandardTableChange}
        />
      </Card>
    )
  }
}
```
* `queryContracts` 是查询数据的主体方法。
* `handleSearch` 是查询按钮的回调方法。
* `handleSelectRows` 是响应表格数据行选中事件的方法。
* `handleStandardTableChange` 是响应表格变动事件的方法，这些事件有：分页页码变化，分页容量变化，表头筛选变化等事件。

至此，一个基础查询页面就完成了。

##### 3.1.6.4 更改数据后清除缓存

* 以下是几种示例

```tsx
import { getTableQueries, getTableParams, CacheLib } from '@/utils/methods/cache';

handleOnAddHandOver = () => {
  const { dispatch } = this.props;
  const { selectedRows, onAddHandOver } = this.state;
  const id = selectedRows[0].contractId;
  dispatch!({ type: 'contract/getContractInfo', payload: { id, fillForm: true } }).then(() => {
    this.setState({ onAddHandOver: !onAddHandOver });
    CacheLib.clearPageQueries(service);
  }
  );
};
handleOnContractAdd = () => {
  const { dispatch } = this.props;
  const { onContractAdd } = this.state;
  dispatch!({ type: 'contract/uContractAddForm', payload: {} });
  this.setState({ onContractAdd: !onContractAdd });
  CacheLib.clearPageQueries(service);
};
handleDeleteContract = () => {
  const { selectedRows } = this.state;
  API.sale.contract.deleteUser.request({ id: selectedRows[0].contractId }).then((res: any) => {
    if (resError(res)) return resErrorMsg(res, '删除合同失败');
    resErrorMsg(res, '删除合同成功');
    CacheLib.clearPageQueries(service);
    this.queryContracts();
  });
};
```
