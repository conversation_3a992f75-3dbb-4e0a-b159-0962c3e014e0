{"receive.QueryExBill": "陈国祥 供应商管理 - 应收查询 | ./externalSupplier/Receive/QueryExBill/index", "receive.CreateExBillPrc": "陈国祥 供应商管理 - 生成账单 | ./externalSupplier/Receive/CreateExBillPrc/index", "receive.ExOnecharges": "陈国祥 供应商管理 - 客户一次性项目合并 | ./externalSupplier/Receive/ExOnecharges/index", "report.ExSupPayByDeptReport": "陈国祥 公司汇总表 | ./externalSupplier/report/ExSupPayByDeptReport", "report.ExSupPayByPrvdReport": "陈国祥 供应商汇总表 | ./externalSupplier/report/ExSupPayByPrvdReport", "emporder.QueryExEmpOrderListForPer": "严小强 完善个人订单 | ./externalSupplier/emporder/QueryExEmpOrderListForPer/index", "emporder.QueryExEmpOrderListForEdit": "严小强 变更个人订单 | ./externalSupplier/emporder/QueryExEmpOrderListForEdit/index", "emporder.QueryExEmployeeOrder": "严小强 个人订单查询 | ./externalSupplier/emporder/QueryExEmployeeOrder/index", "emporder.QueryChangeFeeTemplate": "严小强 变更收费模板 | ./externalSupplier/emporder/QueryChangeFeeTemplate/index", "emporder.QueryExEmpProOrderListForPer": "赵煜颢 接单方离职确认 | ./externalSupplier/emporder/QueryExEmpProOrderListForPer/index", "emporder.QueryImpOrderProEx": "赵煜颢 批量导入个人订单产品 | ./externalSupplier/emporder/QueryImpOrderProEx/index", "suppliermanage.QueryPrvdAssignCS": "赵煜颢 供应商管理 分配供应商客服 | ./externalSupplier/suppliermanage/QueryPrvdAssignCS/index", "suppliermanage.Provider": "赵煜颢 供应商管理 外部供应商维护 | ./externalSupplier/suppliermanage/Provider/index", "suppliermanage.QueryProviderGroup": "赵煜颢 供应商管理 供应商集团设置 | ./externalSupplier/suppliermanage/QueryProviderGroup/index", "payApprove.prvdPayApprove": "赵煜灏 支付申请 | ./externalSupplier/payApprove/prvdPayApprove/index", "payApprove.prvdPayApply": "赵煜灏 档案外部供应商支付 | ./externalSupplier/payApprove/prvdPayApply/index", "payApprove.filePrvdPayApply": "赵煜灏 残疾保障金外部供应商支付 | ./externalSupplier/payApprove/filePrvdPayApply/index", "receive.ExBillLockAndUnLock": "孙尚阳 供应商管理 - 账单锁定解锁删除 | ./externalSupplier/Receive/ExBillLockAndUnLock/index", "receive.ExBillPrintReport": "孙尚阳 供应商管理 - 账单打印 | ./externalSupplier/Receive/ExBillPrintReport/index", "payApprove.prvdPayQuery": "赵煜灏 档案外部供应商支付查询 | ./externalSupplier/payApprove/prvdPayQuery/index", "payApprove.prvdPaySummaryQuery": "赵煜灏 档案外部供应商支付汇总查询 | ./externalSupplier/payApprove/prvdPaySummaryQuery/index", "payApprove.filePrvdPayQuery": "赵煜灏 残疾保障金外部供应商支付查询 | ./externalSupplier/payApprove/filePrvdPayQuery/index", "payApprove.filePrvdPaySummaryQuery": "赵煜灏 残疾保障金外部供应商支付汇总查询 | ./externalSupplier/payApprove/filePrvdPaySummaryQuery/index", "payApprove.QueryDisabilityImp": "赵煜灏 残疾保障金数据维护 | ./externalSupplier/payApprove/QueryDisabilityImp/index", "payApprove.QueryPrvdTotalPayment": "赵煜灏 支付管理 支付汇总查询| ./externalSupplier/payApprove/QueryPayment/QueryPrvdTotalPayment", "payApprove.QueryPrvdPayment": "赵煜灏 支付管理 支付查询| ./externalSupplier/payApprove/QueryPayment/QueryPrvdPayment", "payApprove.QueryPrvdPaymentDetail": "赵煜灏 支付管理 支付明细查询| ./externalSupplier/payApprove/QueryPrvdPaymentDetail", "emporder.QueryExClientOrderForChange": "严小强 客户端变更确认 | ./externalSupplier/emporder/QueryExClientOrderForChange/index", "supplierSalary.paid": "孙尚阳 供应商工资 - 供应商工资支付查询 | ./externalSupplier/supplierSalary/Paid/index", "supplierSalary.unPaid": "王正荣 供应商工资 - 供应商工资未支付数据查询 | ./externalSupplier/supplierSalary/UnPaid/index", "suppliermanage.QueryProviderGroupContract": "刘夏梅 供应商管理 - 供应商集团合同维护 | ./externalSupplier/suppliermanage/QueryProviderGroupContract/index"}