import React, { useEffect, useState } from 'react';
import { Button, Form, FormInstance, message } from 'antd';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { WritableColumnProps } from '@/utils/writable/types';
import { ConfirmButton } from '@/components/Forms/Confirm';
import { WritableInstance } from '@/components/Writable';
import {
  BusTypeDropdownListSelector,
  CitySelector,
  CommonBaseDataSelector,
  mapToSelectors,
} from '@/components/Selectors';
import UpdateForm from '@/components/EditeForm/UpdateForm';
import { handleAttributeMap, handleMethodMap, handleObjectMap } from '../NationwideBusiness';
import { EmployeePop } from '@/components/StandardPop/EmployeePop';
import { InnerUserPop, LiabilityCsPop } from '@/components/StandardPop/InnerUserPop';
import { BizUtil } from '@/utils/settings/bizUtil';

const columns: WritableColumnProps<any>[] = [
  { title: '城市', dataIndex: 'cityId' },
  { title: '业务类型', dataIndex: 'categoryName' },
  { title: '业务项目', dataIndex: 'busnameClassName' },
  { title: '业务内容', dataIndex: 'busContent' },
  { title: '办理属性', dataIndex: 'transactPropertyName' },
  { title: '办理对象', dataIndex: 'transactObjectName' },
  { title: '姓名', dataIndex: 'empName' },
  { title: '证件号码', dataIndex: 'idCardNum' },
  { title: '业务来源', dataIndex: 'sourceType' },
  { title: '业务状态', dataIndex: 'status' },
  { title: '业务进度', dataIndex: 'busProgress' },
  { title: '业务阶段', dataIndex: 'busJd' },
  { title: '办理结果', dataIndex: 'result' },
  { title: '创建人', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createDt' },
  { title: '派单地', dataIndex: 'assignerAddress' },
  { title: '项目客服', dataIndex: 'projectCsName' },
  { title: '接单地', dataIndex: 'areaCode' },
  { title: '接单客服', dataIndex: 'asigneeCsName' },
  { title: '后道客服', dataIndex: 'bankendName' },
  { title: '当前办理人', dataIndex: 'areaCode' },
  { title: '办结时间', dataIndex: 'areaCode' },
  { title: '办结人', dataIndex: 'areaCode' },
  { title: '办理周期', dataIndex: 'areaCode' },
  { title: '是否超过办理周期', dataIndex: 'areaCode' },
  { title: '操作', dataIndex: 'areaCode' },
];

const service = API.basedata.areaCode.list;
const BusinessQuery: React.FC = () => {
  let options: WritableInstance;

  const [processModal, setProcessModal] = useState<boolean>(false);
  const [changeModal, setChangeModal] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<POJO[]>([]);
  const [busProgressMap, setBusProgressMap] = useState<POJO[]>([]);
  //限制项：项目取消业务、项目结束业务、接单取消业务、接单结束业务、后道取消业务、后道结束业务
  const busProgressLimt = [1, 2, 3];
  //项目客服、接单客服、后道客服
  const [customerServiceStatus, setCustomerServiceStatus] = useState<Array<boolean>>([
    false,
    false,
    false,
  ]);
  const [form] = Form.useForm();

  const formColumns: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityId',
      inputRender: () => (
        <CitySelector allowClear keyMap={{ cityId: 'key', cityName: 'shortName' }} />
      ),
    },

    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm: FormInstance) => (
        <CommonBaseDataSelector
          allowClear
          params={{ type: '719' }}
          onConfirm={() => {
            outerForm.setFieldsValue({ busnameClassId: undefined });
          }}
        />
      ),
    },
    {
      label: '业务项目',
      fieldName: 'busNameClassId',
      inputRender: (outerForm: FormInstance) => (
        <BusTypeDropdownListSelector
          allowClear
          params={{ categoryId: outerForm.getFieldValue('categoryId') ?? '' }}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
    },
    { label: '业务内容', fieldName: 'busContent', inputRender: 'string' },
    {
      label: '办理属性',
      fieldName: 'transactPropertyName',
      inputRender: () => mapToSelectors(handleAttributeMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'transactObjectName',
      inputRender: () => mapToSelectors(handleObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'transactTypeStr',
      inputRender: () => mapToSelectors(handleMethodMap, { allowClear: true }),
    },
    { label: '客户名称', fieldName: 'custName', inputRender: 'string' },
    { label: '客户规模', fieldName: 'areaName', inputRender: 'string' },
    { label: '唯一号', fieldName: 'empId', inputRender: 'string' },
    { label: '姓名', fieldName: 'empName', inputRender: 'string' },
    { label: '证件号码', fieldName: 'idCardNum', inputRender: 'string' },
    { label: '业务来源', fieldName: 'sourceType', inputRender: 'string' },
    { label: '业务状态', fieldName: 'status', inputRender: 'string' },
    { label: '业务进度', fieldName: 'busProgress', inputRender: 'string' },
    { label: '办理结果', fieldName: 'result', inputRender: 'string' },
    { label: '业务开始时间起', fieldName: 'createDtStart', inputRender: 'date' },
    { label: '业务开始时间止', fieldName: 'createDtEnd', inputRender: 'date' },
    { label: '办结时间起', fieldName: 'finishDtStart', inputRender: 'date' },
    { label: '办结时间止', fieldName: 'finishDtEnd', inputRender: 'date' },
    {
      label: '项目客服',
      fieldName: 'projectCsId',
      inputRender: () => (
        <InnerUserPop
          fixedValues={{ roleCode: BizUtil.ROLE_CS }}
          rowValue="projectCsId-projectCsName"
          keyMap={{
            projectCsId: 'EMPID',
            projectCsName: 'REALNAME',
          }}
        />
      ),
    },
    {
      label: '接单客服',
      fieldName: 'asigneeCsId',
      inputRender: () => (
        <InnerUserPop
          fixedValues={{ roleCode: BizUtil.ROLE_CS }}
          rowValue="assigneeCsId-assigneeCsName"
          keyMap={{
            assigneeCsId: 'EMPID',
            assigneeCsName: 'REALNAME',
          }}
        />
      ),
    },
    {
      label: '后道客服',
      fieldName: 'bankendCsId',
      inputRender: () => (
        <InnerUserPop
          fixedValues={{ roleCode: BizUtil.ROLE_CS }}
          rowValue="bankendCsId-bankendCsName"
          keyMap={{
            bankendCsId: 'EMPID',
            bankendCsName: 'REALNAME',
          }}
        />
      ),
    },
  ];

  const handleExport = (table: WritableInstance) => {
    table.handleExport(
      {
        service: API.sale.withholdAgentCustNoCheck.exportCust,
      },
      { columns: columns, condition: options.queries, fileName: '业务办理查询.xlsx' },
    );
  };

  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    const rows = options.selectedRows;
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={() => {}}>进入办理</Button>
        <Button
          onClick={() => {
            if (rows?.length === 0) {
              message.warning('请至少选择一条数据！');
              return;
            }

            const { projectCsId, asigneeCsId, bankendCsId } = form?.getFieldsValue();

            if (!projectCsId && !asigneeCsId && !bankendCsId) {
              message.warning(
                '查询条件中 项目客服、接单客服、后道客服 三选一必填，请选择后再提交。',
              );
              return;
            }

            setCustomerServiceStatus([!!projectCsId, !!asigneeCsId, !!bankendCsId]);
            setSelectedRows(rows);
            setChangeModal(true);
          }}
          disabled={
            selectedRows?.filter((f) => busProgressLimt.includes(f.busProgress))?.length > 0
          }
        >
          变更客服
        </Button>
        <Button onClick={() => handleExport(options)}>导出数据</Button>
      </>
    );
  };

  return (
    <CachedPage
      service={service}
      columns={columns}
      formColumns={formColumns}
      form={form}
      renderButtons={renderButtons}
    >
      <UpdateForm
        title="变更客服"
        visible={changeModal}
        colNumber={1}
        formColumns={[
          {
            label: '项目客服',
            fieldName: 'projectCsId',
            inputRender: () => (
              <InnerUserPop
                fixedValues={{ roleCode: BizUtil.ROLE_CS_SUB_ASSIGNER }}
                rowValue="projectCsId-projectCsName"
                keyMap={{
                  projectCsId: 'EMPID',
                  projectCsName: 'REALNAME',
                }}
              />
            ),
            inputProps: { disabled: customerServiceStatus[0] },
          },
          {
            label: '接单客服',
            fieldName: 'asigneeCsId',
            inputRender: () => (
              <InnerUserPop
                fixedValues={{ roleCode: BizUtil.ROLE_CS_SUB_ASSIGNER }}
                rowValue="assigneeCsId-assigneeCsName"
                keyMap={{
                  assigneeCsId: 'EMPID',
                  assigneeCsName: 'REALNAME',
                }}
              />
            ),
            inputProps: { disabled: customerServiceStatus[1] },
          },
          {
            label: '后道客服',
            fieldName: 'bankendCsId',
            inputRender: () => (
              <InnerUserPop
                fixedValues={{ roleCode: BizUtil.ROLE_CS_SUB_ASSIGNER }}
                rowValue="bankendCsId-bankendCsName"
                keyMap={{
                  bankendCsId: 'EMPID',
                  bankendCsName: 'REALNAME',
                }}
              />
            ),
            inputProps: { disabled: customerServiceStatus[2] },
          },
        ]}
        updateitem={{}}
        hideHandle={() => {
          setChangeModal(false);
        }}
        submitHandle={() => {}}
      />
    </CachedPage>
  );
};

export default BusinessQuery;
