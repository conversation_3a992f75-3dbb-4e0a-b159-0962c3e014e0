import { CachedPage } from '@/components/CachedPage';
import { DateRange } from '@/components/DateRange4';
import { EditeFormProps } from '@/components/EditeForm';
import UpdateForm from '@/components/EditeForm/UpdateForm';
import {
  BusinessBigByBusTypeSelector,
  BusTypeDropdownListSelector,
  CitySelector,
  mapToSelectors,
} from '@/components/Selectors';
import {
  GetBaseBusnameClassDropdownList,
  GetBusContentSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import { WritableInstance } from '@/components/Writable';
import { msgErr, msgOk, msgWarn, resError } from '@/utils/methods/message';
import { getCurrentUserCityId } from '@/utils/model';
import { BizUtil } from '@/utils/settings/bizUtil';
import {
  busResultMap,
  busSourceMap,
  busStatusMap,
  busTypeMap,
  customSizeMap,
  transactObjectMap,
  transactPropertyMap,
  transactTypeMap,
} from '@/utils/settings/empwelfare/businessProcessing';
import { WritableColumnProps } from '@/utils/writable/types';
import { Button, Form, FormInstance } from 'antd';
import React, { useEffect, useState } from 'react';

const columns: WritableColumnProps<any>[] = [
  { title: '城市', dataIndex: 'cityName' },
  { title: '业务类型', dataIndex: 'categoryName' },
  { title: '业务项目', dataIndex: 'busnameClassName' },
  { title: '业务内容', dataIndex: 'busContent' },
  { title: '办理属性', dataIndex: 'transactPropertyName' },
  { title: '办理对象', dataIndex: 'transactObjectName' },
  { title: '姓名', dataIndex: 'empName' },
  { title: '证件号码', dataIndex: 'cardNum' },
  { title: '业务来源', dataIndex: 'sourceType' },
  { title: '业务状态', dataIndex: 'status' },
  { title: '业务进度', dataIndex: 'busSchedule' },
  { title: '业务阶段', dataIndex: 'busStage' },
  { title: '办理结果', dataIndex: 'result' },
  { title: '创建人', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createDt' },
  { title: '派单地', dataIndex: 'projectCsCity' },
  { title: '项目客服', dataIndex: 'projectCsName' },
  { title: '接单地', dataIndex: 'assigneeCsCity' },
  { title: '接单客服', dataIndex: 'asigneeCsName' },
  { title: '后道客服', dataIndex: 'bankendName' },
  { title: '当前办理人', dataIndex: 'areaCode' },
  { title: '办结时间', dataIndex: 'overDt' },
  { title: '办结人', dataIndex: 'areaCode' },
  { title: '办理周期', dataIndex: 'transactPeriod' },
  { title: '是否超过办理周期', dataIndex: 'areaCode' },
];

const service = API.welfaremanage.ebmBusinessQuery.getEbmBusinessQueryPage;
const BusinessQuery: React.FC = () => {
  let options: WritableInstance;

  const [processModal, setProcessModal] = useState<boolean>(false);
  const [changeModal, setChangeModal] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<POJO[]>([]);
  const [busProgressMap, setBusProgressMap] = useState<POJO[]>([]);
  //限制项：项目取消业务、项目结束业务、接单取消业务、接单结束业务、后道取消业务、后道结束业务
  const busProgressLimt = [1, 2, 3];
  //项目客服、接单客服、后道客服
  const [customerServiceStatus, setCustomerServiceStatus] = useState<Array<boolean>>([
    false,
    false,
    false,
  ]);

  const [projectCsBranchId, setProjectCsBranchId] = useState<string>('');
  const [asigneeCsBranchId, setAsigneeCsBranchId] = useState<string>('');
  const [bankendCsBranchId, setBankendCsBranchId] = useState<string>('');

  const [form] = Form.useForm();

  const defaultCityId = getCurrentUserCityId();

  const formColumns: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityId',
      inputProps: {
        initialValue: defaultCityId,
      },
      inputRender: () => (
        <CitySelector allowClear keyMap={{ cityId: 'key', cityName: 'shortName' }} />
      ),
    },

    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: () => mapToSelectors(busTypeMap),
    },
    {
      label: '业务项目',
      fieldName: 'busNameClassId',
      inputRender: (outerForm: FormInstance) => {
        const { categoryId } = outerForm.getFieldsValue();
        return (
          <GetBaseBusnameClassDropdownList
            allowClear
            params={{
              pageNum: 1,
              pageSize: **********,
              categoryId,
            }}
          />
        );
      },
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
    },
    {
      label: '业务内容',
      fieldName: 'busContent',
      inputRender: (outerForm: FormInstance) => {
        const { categoryId, busnameClassId, cityId } = outerForm.getFieldsValue();
        return <GetBusContentSelector params={{ categoryId, busnameClassId, cityId }} />;
      },
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.categoryId !== curValues.categoryId ||
          prevValues.busnameClassId !== curValues.busnameClassId
        );
      },
    },
    {
      label: '办理属性',
      fieldName: 'transactPropertyName',
      inputRender: () => mapToSelectors(transactPropertyMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'transactObjectName',
      inputRender: () => mapToSelectors(transactObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'transactTypeStr',
      inputRender: () => mapToSelectors(transactTypeMap, { allowClear: true }),
    },
    { label: '客户名称', fieldName: 'custName', inputRender: 'string' },
    {
      label: '客户规模',
      fieldName: 'eSizeID',
      inputRender: () => mapToSelectors(customSizeMap, { allowClear: true }),
    },
    { label: '唯一号', fieldName: 'empId', inputRender: 'string' },
    { label: '姓名', fieldName: 'empName', inputRender: 'string' },
    { label: '证件号码', fieldName: 'cardNum', inputRender: 'string' },
    {
      label: '业务来源',
      fieldName: 'sourceType',
      inputRender: () => mapToSelectors(busSourceMap, { allowClear: true }),
    },
    {
      label: '业务状态',
      fieldName: 'status',
      inputRender: () => mapToSelectors(busStatusMap, { allowClear: true }),
    },
    { label: '业务进度', fieldName: 'busSchedule', inputRender: 'string' },
    {
      label: '办理结果',
      fieldName: 'result',
      inputRender: () => mapToSelectors(busResultMap, { allowClear: true }),
    },
    {
      label: '',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            { title: '业务开始时间起', dataIndex: 'createDtBegin' },
            { title: '业务开始时间止', dataIndex: 'createDtEnd' },
          ]}
        />
      ),
    },
    {
      label: '',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            { title: '办结时间起', dataIndex: 'overDtBegin' },
            { title: '办结时间止', dataIndex: 'overDtEnd' },
          ]}
        />
      ),
    },
    {
      label: '项目客服',
      fieldName: 'projectCsId',
      inputRender: () => (
        <InnerUserPop
          fixedValues={{ roleCode: BizUtil.ROLE_CS }}
          rowValue="projectCsId-projectCsName"
          keyMap={{
            projectCsId: 'EMPID',
            projectCsName: 'REALNAME',
          }}
          onConfirm={(value) => {
            setProjectCsBranchId(value?.branchId);
          }}
        />
      ),
    },
    {
      label: '接单客服',
      fieldName: 'asigneeCsId',
      inputRender: () => (
        <InnerUserPop
          fixedValues={{ roleCode: BizUtil.ROLE_CS }}
          rowValue="assigneeCsId-assigneeCsName"
          keyMap={{
            assigneeCsId: 'EMPID',
            assigneeCsName: 'REALNAME',
          }}
          onConfirm={(value) => {
            setAsigneeCsBranchId(value?.branchId);
          }}
        />
      ),
    },
    {
      label: '后道客服',
      fieldName: 'bankendCsId',
      inputRender: () => (
        <InnerUserPop
          fixedValues={{ roleCode: BizUtil.ROLE_CS }}
          rowValue="bankendCsId-bankendCsName"
          keyMap={{
            bankendCsId: 'EMPID',
            bankendCsName: 'REALNAME',
          }}
          onConfirm={(value) => {
            setBankendCsBranchId(value?.branchId);
          }}
        />
      ),
    },
  ];

  const handleExport = (table: WritableInstance) => {
    table.handleExport(
      {
        service: API.welfaremanage.ebmBusinessQuery.exportEbmBusinessQueryExcel,
      },
      { columns: columns, condition: options.queries, fileName: '业务办理查询.xlsx' },
    );
  };

  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    const rows = options.selectedRows;
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={() => {}}>进入办理</Button>
        <Button
          onClick={() => {
            if (rows?.length === 0) {
              msgWarn('请至少选择一条数据！');
              return;
            }

            const { projectCsId, asigneeCsId, bankendCsId } = form?.getFieldsValue();

            if (!projectCsId && !asigneeCsId && !bankendCsId) {
              msgWarn('查询条件中 项目客服、接单客服、后道客服 三选一必填，请选择后再提交。');
              return;
            }

            setCustomerServiceStatus([!!projectCsId, !!asigneeCsId, !!bankendCsId]);
            setSelectedRows(rows);
            setChangeModal(true);
          }}
          disabled={
            selectedRows?.filter((f) => busProgressLimt.includes(f.busSchedule))?.length > 0
          }
        >
          变更客服
        </Button>
        <Button onClick={() => handleExport(options)}>导出数据</Button>
      </>
    );
  };

  const clearCache = () => {
    setProjectCsBranchId('');
    setAsigneeCsBranchId('');
    setBankendCsBranchId('');
    form?.setFieldsValue({ wechatProgressQueryName: '' });
  };

  useEffect(() => {
    if (!changeModal || !processModal) {
      clearCache();
      return;
    }
  }, [changeModal, processModal]);

  return (
    <CachedPage
      service={service}
      columns={columns}
      formColumns={formColumns}
      form={form}
      renderButtons={renderButtons}
    >
      <UpdateForm
        title="变更客服"
        visible={changeModal}
        colNumber={1}
        formColumns={[
          {
            label: '项目客服',
            fieldName: 'projectCsId',
            inputRender: () => (
              <InnerUserPop
                fixedValues={{ roleCode: BizUtil.ROLE_CS, branchId: projectCsBranchId }}
                rowValue="projectCsId-projectCsName"
                keyMap={{
                  projectCsId: 'EMPID',
                  projectCsName: 'REALNAME',
                }}
              />
            ),
            inputProps: { disabled: customerServiceStatus[0] },
          },
          {
            label: '接单客服',
            fieldName: 'asigneeCsId',
            inputRender: () => (
              <InnerUserPop
                fixedValues={{ roleCode: BizUtil.ROLE_CS, branchId: asigneeCsBranchId }}
                rowValue="assigneeCsId-assigneeCsName"
                keyMap={{
                  assigneeCsId: 'EMPID',
                  assigneeCsName: 'REALNAME',
                }}
              />
            ),
            inputProps: { disabled: customerServiceStatus[1] },
          },
          {
            label: '后道客服',
            fieldName: 'bankendCsId',
            inputRender: () => (
              <InnerUserPop
                fixedValues={{ roleCode: BizUtil.ROLE_CS, branchId: bankendCsBranchId }}
                rowValue="bankendCsId-bankendCsName"
                keyMap={{
                  bankendCsId: 'EMPID',
                  bankendCsName: 'REALNAME',
                }}
              />
            ),
            inputProps: { disabled: customerServiceStatus[2] },
          },
        ]}
        updateitem={{}}
        hideHandle={() => {
          setChangeModal(false);
        }}
        submitHandle={async (values) => {
          const params = Object.keys(values).filter(
            (key) => values[key] !== '' && values[key] !== null && values[key] !== undefined,
          );

          const data = selectedRows?.map((item) => {
            return { applicationId: item?.applicationId, ...params };
          });
          const res = await API.welfaremanage.ebmBusinessQuery.updateCustomerService.request(data);
          if (resError(res)) {
            msgErr(res.message);
            return;
          }
          msgOk('修改成功');
          options.request();
        }}
      />
    </CachedPage>
  );
};

export default BusinessQuery;
