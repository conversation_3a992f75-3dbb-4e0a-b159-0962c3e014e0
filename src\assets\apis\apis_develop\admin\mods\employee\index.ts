/**
 * @description 内部雇员信息
 */
import * as delEmpInfoInter from './delEmpInfoInter';
import * as deleteEmpDeptRelation from './deleteEmpDeptRelation';
import * as deleteEmpRelative from './deleteEmpRelative';
import * as deleteEmployee from './deleteEmployee';
import * as getContractFinishedCount from './getContractFinishedCount';
import * as getDepartmentWithCheckBox from './getDepartmentWithCheckBox';
import * as getEmpDeptCount from './getEmpDeptCount';
import * as postGetEmpDeptCount from './postGetEmpDeptCount';
import * as getEmpInfoInter from './getEmpInfoInter';
import * as getEmployeeCodeCount from './getEmployeeCodeCount';
import * as getEmployeeDropdownList from './getEmployeeDropdownList';
import * as getIdCardNumCount from './getIdCardNumCount';
import * as getOtherUserList from './getOtherUserList';
import * as getPersonInChargeDropdownList from './getPersonInChargeDropdownList';
import * as getUserCount from './getUserCount';
import * as getUserDepartmentWithCheckBox from './getUserDepartmentWithCheckBox';
import * as getUserList from './getUserList';
import * as insertEmpDeptRelation from './insertEmpDeptRelation';
import * as insertEmpRelative from './insertEmpRelative';
import * as insertEmployee from './insertEmployee';
import * as insertEmployeeAndRelatives from './insertEmployeeAndRelatives';
import * as queryEmpRelative from './queryEmpRelative';
import * as queryEmployee from './queryEmployee';
import * as queryEmployeeAndRelatives from './queryEmployeeAndRelatives';
import * as queryFunctionListByUserId from './queryFunctionListByUserId';
import * as saveEmpInfoInter from './saveEmpInfoInter';
import * as updateEmpRelative from './updateEmpRelative';
import * as updateEmployee from './updateEmployee';
import * as updateEmployeeAndDept from './updateEmployeeAndDept';
import * as updateEmployeeAndRelatives from './updateEmployeeAndRelatives';
import * as updateToOtherUser from './updateToOtherUser';
import * as updateUserFunction from './updateUserFunction';
import * as updateUserManage from './updateUserManage';
import * as updateUserManageDept from './updateUserManageDept';

export {
  delEmpInfoInter,
  deleteEmpDeptRelation,
  deleteEmpRelative,
  deleteEmployee,
  getContractFinishedCount,
  getDepartmentWithCheckBox,
  getEmpDeptCount,
  postGetEmpDeptCount,
  getEmpInfoInter,
  getEmployeeCodeCount,
  getEmployeeDropdownList,
  getIdCardNumCount,
  getOtherUserList,
  getPersonInChargeDropdownList,
  getUserCount,
  getUserDepartmentWithCheckBox,
  getUserList,
  insertEmpDeptRelation,
  insertEmpRelative,
  insertEmployee,
  insertEmployeeAndRelatives,
  queryEmpRelative,
  queryEmployee,
  queryEmployeeAndRelatives,
  queryFunctionListByUserId,
  saveEmpInfoInter,
  updateEmpRelative,
  updateEmployee,
  updateEmployeeAndDept,
  updateEmployeeAndRelatives,
  updateToOtherUser,
  updateUserFunction,
  updateUserManage,
  updateUserManageDept,
};
