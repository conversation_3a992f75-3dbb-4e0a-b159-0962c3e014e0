{"origins": [{"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=basedata", "name": "basedata", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=admin", "name": "admin", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=workflow", "name": "workflow", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=sale", "name": "sale", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=crm", "name": "crm", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=common", "name": "commons", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=welfaremanage", "name": "welfaremanage", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=emphiresep", "name": "emphiresep", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=client", "name": "client", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=combo", "name": "combo", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=externalsupplier", "name": "externalsupplier", "usingMultipleOrigins": true}, {"originUrl": "http://*************:1600/v2/api-docs?group=supplier", "name": "supplier", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=information", "name": "information", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=report", "name": "report", "usingMultipleOrigins": true}, {"originUrl": "http://*************:1600/v2/api-docs?group=payroll", "name": "payroll", "usingMultipleOrigins": true}, {"originUrl": "http://*************:1600/v2/api-docs?group=finance", "name": "finance", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=exfinance", "name": "exfinance", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=sysmanage", "name": "sysmanage", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=cmp", "name": "cmp", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=ec", "name": "ec", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=minio", "name": "minio", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8100/rhro-service-1.0/v2/api-docs?group=rpa", "name": "rpa", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=bigdata", "name": "bigdata", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=sysmanage", "name": "sysmanage2", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=basedata", "name": "basedata2", "usingMultipleOrigins": true}, {"originUrl": "http://*************:8090/rhro-service-1.0/api-docs?group=bigcustomer", "name": "<PERSON><PERSON><PERSON><PERSON>", "usingMultipleOrigins": true}], "outDir": "../../src/apis", "templatePath": "./pont-template-chro", "mocks": {"type": "object", "description": "自动化mock服务，mocks 数据可以在 .mocks/mocks.ts 下自定义编辑。", "properties": {"enable": {"type": "boolean", "default": true, "description": "是否开启"}, "host": {"type": "number", "default": 8000, "description": "mock服务端口号"}, "wrapper": {"type": "string", "description": "是否把接口返回的数据包在固定格式下, {response} 表示返回数据结构", "default": "{\"code\": 0, \"data\": {response}, \"message\": \"\"}"}, "basePath": {"type": "string", "default": "", "description": "接口基础路径"}}}}