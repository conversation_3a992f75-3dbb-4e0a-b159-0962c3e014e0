import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/providerContract/insert
     * @desc 新增
新增
     * hasForm: true
     * hasBody: true
     */

export class Params {
  /** activityNameEn */
  activityNameEn?: any;
  /** undefined */
  add?: any;
  /** 提交人 */
  approveCommitBy?: any;
  /** 提交人名称 */
  approveCommitByName?: any;
  /** 审批意见 */
  approveOpinion?: any;
  /** 审批过程 */
  approveProcess?: any;
  /** 审批状态 */
  approveStatus?: any;
  /** undefined */
  approveStatusName?: any;
  /** 批次号,用于备份 */
  batchId?: any;
  /** 账单表别名,控制客户权限用 */
  billAlias?: any;
  /** 财务大类 */
  bizCategory?: any;
  /** 业务类型,控制小合同权限用 */
  bizmanType?: any;
  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias?: any;
  /** 城市id */
  cityId?: any;
  /** 城市名称 */
  cityName?: any;
  /** clientOperation */
  clientOperation?: any;
  /** flex是否行编号 */
  clientRowSeq?: any;
  /** flex是否选择 */
  clientSelected?: any;
  /** 联系人 */
  contact?: any;
  /** 联系电话 */
  contactTel?: any;
  /** 合同表别名,控制合同权限用 */
  contractAlias?: any;
  /** undefined */
  contractCode?: any;
  /** 合同终止期 */
  contractEndDate?: any;
  /** 合同名称 */
  contractName?: any;
  /** 合同生效期 */
  contractStartDate?: any;
  /** 合同状态 */
  contractStatus?: any;
  /** undefined */
  contractStatusName?: any;
  /** 创建人 */
  createBy?: any;
  /** createBy2 */
  createBy2?: any;
  /** 创建日期 */
  createDt?: any;
  /** 客户表别名,控制客户权限用 */
  customerAlias?: any;
  /** undefined */
  del?: any;
  /** undefined */
  endIndex?: any;
  /** 导入类型,扩充使用 */
  expType?: any;
  /** file */
  file?: File;
  /** 附件名称 */
  fileName?: any;
  /** 合同附件 */
  filePath?: any;
  /** filterByAuthNum */
  filterByAuthNum?: any;
  /** 提供查询是做为排除条件使用 */
  filterId?: any;
  /** 正式合同名称 */
  formalContractName?: any;
  /** 正式合同路径 */
  formalContractPath?: any;
  /** funBtnActiveStr */
  funBtnActiveStr?: any;
  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch?: any;
  /** 人力资源许可证 */
  humanResourceLicence?: any;
  /** inId */
  inId?: any;
  /** 是否账单查询 */
  isBillQuery?: any;
  /** flex是否变化 */
  isChanged?: any;
  /** 删除标记 */
  isDeleted?: any;
  /** 劳务派遣许可证 */
  laborDispatchLicence?: any;
  /** 最晚支付日 */
  latestPayDate?: any;
  /** 模拟人 */
  mimicBy?: any;
  /** undefined */
  noChange?: any;
  /** 原供应商集团合同Id */
  oldContractId?: any;
  /** 页数 */
  pageNum?: any;
  /** 每页记录数,默认65536条 */
  pageSize?: any;
  /** 流程审批角色名字 */
  processAprRoleName?: any;
  /** 流程INSID */
  processInsId?: any;
  /** 供应商集团权限添加 */
  providerIdAlias?: any;
  /** undefined */
  provinceId?: any;
  /** undefined */
  provinceName?: any;
  /** 代理人 */
  proxyBy?: any;
  /** 供应商集团Code */
  prvdGroupCode?: any;
  /** 供应商集团合同Id */
  prvdGroupContractId?: any;
  /** 供应商集团Id */
  prvdGroupId?: any;
  /** prvdGroupIdAlias */
  prvdGroupIdAlias?: any;
  /** 供应商集团名称 */
  prvdGroupName?: any;
  /** undefined */
  remark?: any;
  /** 卡纯代发人员,默认过滤 */
  restrictPure?: any;
  /** 卡权限 */
  restrictType?: any;
  /** undefined */
  roleId?: any;
  /** 保存0，提交1 */
  saveOrCommit?: any;
  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth?: any;
  /** 获取前台勾选key的字符串 */
  selectKeyStr?: any;
  /** 社会统一信用代码 */
  socialCreditCode?: any;
  /** undefined */
  startIndex?: any;
  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias?: any;
  /** 修改人 */
  updateBy?: any;
  /** 修改日期 */
  updateDt?: any;
  /** undefined */
  upt?: any;
  /** 用户id,控制小合同权限用 */
  userId?: any;
  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias?: any;
  /** undefined */
  workItemId?: any;
  /** 流程id */
  workitemId?: any;
}
export const init = new defs.externalsupplier.CommonResponse();
export const url = '/rhro-service-1.0/providerContract/insert:POST';
export const initialUrl = '/rhro-service-1.0/providerContract/insert';
export const cacheKey = '_providerContract_insert_POST';
export async function request(data: Params, options?: RequestConfig) {
  const formData = new FormData();
  Object.keys(data).forEach((item: any) => {
    formData.append(item, data[item]);
  });
  const reqUrl = `/rhro-service-1.0/providerContract/insert`;
  const fetchOption = {
    reqUrl,
    requestType: 'form-data',
    data: formData,
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const formData = new FormData();
  Object.keys(data).forEach((item: any) => {
    formData.append(item, data[item]);
  });
  const reqUrl = `/rhro-service-1.0/providerContract/insert`;
  const fetchOption = {
    reqUrl,
    requestType: 'form-data',
    data: formData,
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
