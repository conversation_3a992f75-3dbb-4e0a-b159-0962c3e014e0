import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employee/getPersonInChargeDropdownList
     * @desc 获取负责人
获取负责人
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.DropdownList();
export const url =
  '/rhro-service-1.0/employee/getPersonInChargeDropdownList:GET';
export const initialUrl =
  '/rhro-service-1.0/employee/getPersonInChargeDropdownList';
export const cacheKey = '_employee_getPersonInChargeDropdownList_GET';
export async function request(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/getPersonInChargeDropdownList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/getPersonInChargeDropdownList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
