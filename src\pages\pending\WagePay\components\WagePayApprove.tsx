import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import Codal from '@/components/Codal';
import { RadioItem } from '@/components/EditeForm/Item/CheckboxItem';
import { AsyncButton } from '@/components/Forms/Confirm';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import {
  CommonDropSelector,
  DeptmentSelector,
  mapToSelectors,
  PostscriptSelector,
} from '@/components/Selectors';
import { Switchs } from '@/components/Selectors/Switch';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import WagePayApproveDetail from '@/pages/finance/Pay/Query/components/WagePayApproveDetail';
import { msgErr, msgOk } from '@/utils/methods/message';
import { stdDateFormat, stdMonthFormatMoDash } from '@/utils/methods/times';
import { getCurrentUser } from '@/utils/model';
import { But<PERSON>, Form, Spin, Modal } from 'antd';
import React, { useEffect, useState } from 'react';

interface WagePayApproveProps {
  visible: boolean;
  hideHandle: (refresh?: boolean) => void;
  refreshTable: () => void;
  data?: POJO;
  isActualPayHGroup: boolean;
  isView: boolean;
}

let isActualPayOld: string | undefined = undefined;
let list: POJO[] = [];
const WagePayApprove = (props: WagePayApproveProps) => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [newData, setNewData] = useState<POJO>({});
  const [sendWayDisable, setSendWayDisable] = useState<boolean>(true);
  const [bankRequired, setBankRequired] = useState<boolean>(false);
  const [localIsActualPay, setIsActualPay] = useState<string>('');

  const [bankAcctEdit, setBankAcctEdit] = useState<boolean>(false);
  const [bankInfoComboList, setBankInfoComboList] = useState<Map<string, string>>(new Map());

  const [textDisabled, setTextDisabled] = useState<boolean>(true);
  const [scriptRmkList, setScriptRmkList] = useState<POJO[]>([]);

  const currentUser = getCurrentUser();

  const payTypeDataSet = new Map<number, string>([
    [1, '社保'],
    [2, '公积金'],
    [4, '工资'],
    [6, '其他'],
  ]);

  const payDetailTypeDataSet = new Map<number, string>([
    [1, '实发+税'],
    [2, '实发'],
    [3, '税'],
  ]);

  const payMethodDataSet = new Map<number, string>([
    [1, '银行汇款'],
    [2, '转账支票'],
    [3, '现金'],
    [4, '其它'],
  ]);

  const isActualPayDataSet = new Map<string, string>([
    ['1', '已审核未到账'],
    ['2', '已审核已到账'],
    ['3', '合同垫付'],
    ['4', '核查到款'],
  ]);

  const sendWaySet = new Map<string, string>([
    ['1', '资金代收代付系统'],
    ['2', '其他'],
  ]);

  const isSupplierList = new Map<string, string>([
    ['1', '是'],
    ['0', '否'],
  ]);

  const formColumns1: EditeFormProps[] = [
    {
      label: '支付类型',
      fieldName: 'payType',
      inputProps: { disabled: true },
      inputRender: () => mapToSelectors(payTypeDataSet),
    },
    {
      label: '支付明细类型',
      fieldName: 'payDetailType',
      inputProps: { disabled: true },
      inputRender: () => mapToSelectors(payDetailTypeDataSet),
    },
    {
      label: '申请人',
      fieldName: 'applicantName',
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    {
      label: '申请时间',
      fieldName: 'applyDt',
      inputProps: { disabled: true },
      inputRender: 'date',
    },
    {
      label: '发放日期',
      fieldName: 'sendDt',
      inputProps: { disabled: true },
      inputRender: 'date',
    },
    {
      label: '支付所属年月',
      fieldName: 'payDt',
      inputProps: { disabled: true, format: stdMonthFormatMoDash },
      inputRender: 'month',
    },
    {
      label: '到账日期',
      fieldName: 'payeeDt',
      inputProps: { disabled: true, format: stdMonthFormatMoDash },
      inputRender: 'month',
    },
    {
      label: '客户',
      fieldName: 'custId',
      inputProps: { disabled: true },
      inputRender: () => <CustomerPop />,
    },
    {
      label: '系统数据调整，不实际支付',
      fieldName: 'isAdjustNoPay',
      inputRender: () => {
        return (
          <RadioItem
            disabled={props.data?.activityNameEn != 1}
            onChange={(isAdjustNoPay) => {
              const isSupplier = form.getFieldValue('isSupplier') || newData.isSupplier;
              if (isSupplier === undefined) {
                if (isAdjustNoPay === '1') {
                  form.setFieldsValue({ sendWay: '2' });
                  setSendWayDisable(true);
                }
              } else {
                getDefaultSendWayByCustId(
                  newData.custId,
                  newData.depTitleId,
                  newData.payAuditId,
                  isSupplier,
                  isAdjustNoPay,
                );
              }
            }}
          />
        );
      },
    },
  ];

  const formColumns2: EditeFormProps[] = [
    {
      label: '支付方式',
      fieldName: 'payMethod',
      inputProps: { disabled: true },
      inputRender: () => mapToSelectors(payMethodDataSet),
    },
    {
      label: '支付地',
      fieldName: 'payAddress',
      inputProps: { disabled: true },
      inputRender: () => <DeptmentSelector keyMap={{ payAddress: 'key' }} />,
    },
    {
      label: '支付地抬头',
      fieldName: 'depTitleId',
      inputProps: { disabled: true },
      inputRender: () => (
        <CommonDropSelector
          params={{
            statementName: 'wgSendBatch.getApplyTitleByPayAddress',
            payAddress: props.data?.payAddress,
          }}
        />
      ),
    },
    {
      label: '供应商工资或其他',
      fieldName: 'isSupplier',
      rules: [{ required: true, message: '请选择供应商工资或其他' }],
      inputRender: () =>
        mapToSelectors(isSupplierList, {
          allowClear: false,
          disabled: props.data?.activityNameEn == 12,
          onChange: (isSupplier: string) => {
            // 供应商工资或其他为是，通道为其他，不能编辑，为否，按照通道字段可编辑逻辑进行
            if (isSupplier === '1') {
              form.setFieldsValue({ sendWay: '2' });
              setSendWayDisable(true);
              return;
            }
            const isAdjustNoPay = form.getFieldValue('isAdjustNoPay') || newData.isAdjustNoPay;
            getDefaultSendWayByCustId(
              newData.custId,
              newData.depTitleId,
              newData.payAuditId,
              isSupplier,
              isAdjustNoPay,
            );
          },
        }),
    },
    {
      label: '发放通道',
      fieldName: 'sendWay',
      hidden: !props.isActualPayHGroup,
      inputProps: { disabled: sendWayDisable || props.data?.activityNameEn == 12 },
      rules: [{ required: true, message: '请选择发放通道' }],
      inputRender: () => mapToSelectors(sendWaySet),
    },
    {
      label: '银行账号/支票号',
      fieldName: 'bankAcct',
      hidden: !props.isActualPayHGroup,
      rules: [{ required: true, message: '请输入 银行账号/支票号' }],
      inputRender: 'string',
    },
    {
      label: '银行账号/支票号',
      fieldName: 'bankAcctId',
      hidden: !props.isActualPayHGroup,
      rules: [{ required: true, message: '请选择 银行账号/支票号' }],
      inputRender: () =>
        mapToSelectors(bankInfoComboList, {
          allowClear: true,
          showSearch: true,
          onChange: onBankAcctIdChange,
        }),
    },
    {
      label: '开户行',
      hidden: !props.isActualPayHGroup,
      fieldName: 'acctBankName',
      rules: [{ required: true, message: '请输入开户行' }],
      inputProps: {
        onChange: (e: POJO) => form.setFieldsValue({ payeeBank: e.target.value }),
      },
      inputRender: 'string',
    },
    {
      label: '开户名',
      hidden: !props.isActualPayHGroup,
      fieldName: 'bankName',
      rules: [{ required: true, message: '请输入开户名' }],
      inputRender: 'string',
    },
    {
      label: '收款银行',
      hidden: !props.isActualPayHGroup,
      rules: [{ required: true, message: '请输入收款银行' }],
      fieldName: 'payeeBank',
      inputRender: 'string',
    },
    {
      label: '附言',
      fieldName: 'postscript',
      inputRender: () => (
        <PostscriptSelector
          onChange={(postscript: string) => {
            const keys = scriptRmkList?.map((i) => i.key) || [];
            if (keys.includes(postscript)) {
              setTextDisabled(false);
            } else {
              setTextDisabled(true);
              form.setFieldsValue({
                postscriptRmk: '',
              });
            }
          }}
          disabled={props.data?.activityNameEn == 12}
        />
      ),
      rules: props.data?.activityNameEn == 12 ? [] : [{ required: true, message: '请选择附言' }],
    },
    {
      label: '应付总额',
      hidden: !props.isActualPayHGroup,
      fieldName: 'amt',
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    {
      label: '申请支付金额',
      fieldName: 'applyPayAmt',
      inputProps: { disabled: props.data?.activityNameEn != 1 },
      inputRender: 'string',
    },
    {
      label: '单据数量',
      fieldName: 'invoiceQty',
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    {
      label: '最晚支付时间',
      fieldName: 'lastPayDt',
      inputProps: { disabled: props.data?.activityNameEn != 1, format: stdDateFormat },
      inputRender: 'date',
    },
    {
      label: '接单地申请单抬头',
      fieldName: 'applyTitle',
      inputProps: { disabled: true },
      inputRender: () => (
        <CommonDropSelector
          params={{
            statementName: 'wgSendBatch.getApplyTitleByPayAddress',
            payAddress: props.data?.payAddress,
          }}
        />
      ),
    },
    {
      label: '派单地',
      fieldName: 'assignerDepartmentId',
      inputProps: { disabled: true },
      inputRender: () => (
        <CommonDropSelector
          params={{
            statementName: 'wgSendBatch.getAssignerDepartmentIdByAuditId',
            payAuditId: props.data?.payAuditId,
          }}
        />
      ),
    },
    {
      label: '派单地申请单抬头',
      fieldName: 'assignerApplyTitle',
      inputProps: { disabled: true },
      inputRender: () => (
        <CommonDropSelector
          params={{
            statementName: 'wgSendBatch.getAssignerApplyTitleByPayAddress',
            payAuditId: props.data?.payAuditId,
            payAddress: props.data?.assignerDepartmentId,
          }}
        />
      ),
    },
    {
      label: '是否实际到款',
      fieldName: 'isActualPay',
      hidden: props.data?.activityNameEn != 11,
      rules: [{ required: true, message: '请选择是否实际到款' }],
      inputRender: () =>
        mapToSelectors(isActualPayDataSet, {
          allowClear: true,
          disabled: props.data?.status == '8' || props.data?.activityNameEn == 12 ? true : false,
          onChange: (isActualPay) => {
            setIsActualPay(isActualPay || '');
          },
        }),
    },
    {
      label: '附言说明',
      fieldName: 'postscriptRmk',
      inputProps: { disabled: props.data?.activityNameEn == 12 || textDisabled },
      inputRender: 'text',
      rules: [{ required: false, max: 25, message: '不能超过50字符' }],
      colNumber: 1,
    },
    {
      label: '接单地支付目的及摘要',
      fieldName: 'payPurpose',
      colNumber: 1,
      inputProps: {
        disabled: props.data?.activityNameEn != 1 && props.data?.activityNameEn != 12,
        style: { minHeight: 80 },
      },
      inputRender: 'text',
    },
    {
      label: '派单地支付目的及摘要',
      fieldName: 'payReason',
      colNumber: 1,
      inputProps: {
        disabled: props.data?.activityNameEn != 1 && props.data?.activityNameEn != 12,
        style: { minHeight: 80 },
      },
      inputRender: 'text',
    },
    {
      label: '备注',
      fieldName: 'remark',
      colNumber: 1,
      inputProps: {
        disabled: props.data?.activityNameEn != 1 && props.data?.activityNameEn != 12,
        style: { minHeight: 80 },
      },
      inputRender: 'text',
    },
  ];

  const formColumns3: EditeFormProps[] = [
    {
      label: '已审批意见',
      fieldName: 'approveOpinion',
      colNumber: 1,
      inputProps: { disabled: true, style: { minHeight: 80 } },
      inputRender: 'text',
    },
    {
      label: '审批意见',
      fieldName: 'newRemark',
      colNumber: 1,
      inputProps: {
        disabled: props.data?.activityNameEn == 1 || props.data?.activityNameEn == 9,
        style: { minHeight: 80 },
      },
      inputRender: 'text',
    },
    {
      label: '上传',
      fieldName: 'fileId',
      inputRender: 'upload',
      inputProps: {
        fileName: 'fileName',
        disabled: props.data?.activityNameEn != 1,
        bizType: '40451000',
      },
    },
  ];

  useEffect(() => {
    if (props.visible) {
      getPayAuditDataById();
    } else {
      form.resetFields();
      isActualPayOld = undefined;
      setNewData({});
      setSendWayDisable(true);
      list = [];
    }
  }, [props.visible]);

  const getIsBlackListByCustId = async (custId: number, address: string, depTitleId: string) => {
    const res = await API.payroll.payBatch.getIsBlackListByCustId.request({
      custId: `${custId}`,
      payTitleId: depTitleId,
    });
    if (res.code !== 200) return;
    const data = res.data as any;
    if (data === '1') {
      // 黑名单里
      setBankRequired(true);
    } else {
      setBankRequired(false);
    }
    getBankInfo(address || '', custId, depTitleId, data === '1');
  };

  const getDefaultBlackListAcct = async (
    custId: number,
    depTitleId: string,
    payAddress: string,
  ) => {
    const r = await API.payroll.payBatch.getDefaultBlackListAcct.requests({
      custId,
      payAddress,
      depTitleId,
    });
    form.setFieldsValue({
      bankAcct: r.bankAcct,
      bankName: r.bankName,
      acctBankName: r.acctBankName,
      payeeBank: r.acctBankName,
      bankAcctId: r.bankAcctId,
    });
  };

  const getDefaultPorviderBankAcct = async (
    custId: number,
    depTitleId: string,
    payAddress: string,
  ) => {
    const r = await API.payroll.payBatch.getDefaultPorviderBankAcct.requests({
      custId,
      payAddress,
      depTitleId,
    });
    form.setFieldsValue({
      bankAcct: r.bankAcct,
      bankName: r.bankName,
      acctBankName: r.acctBankName,
      payeeBank: r.acctBankName,
      bankAcctId: r.bankAcctId,
    });
  };

  const getBankInfo = async (id: string, custId: number, depTitleId: string, isBlack: boolean) => {
    const res = await API.basedata.baseDataCls.getDorpDownList.requests({
      statementName: 'wgSendBatch.getBankInfoByPayAddress',
      payAddress: id,
    });
    list = res.list as POJO[];
    const map = new Map<string, string>();
    list.forEach((e) => map.set(e.key, e.shortName));
    setBankAcctEdit(list.length <= 0);
    setBankInfoComboList(map);
    if (isBlack) getDefaultBlackListAcct(custId, depTitleId, id);
    else getDefaultPorviderBankAcct(custId, depTitleId, id);
    // else if (list.length > 0) {
    //   form.setFieldsValue({
    //     bankAcct: list[0].shortName,
    //     bankName: list[0].reserveObj,
    //     acctBankName: list[0].name,
    //     payeeBank: list[0].name,
    //     bankAcctId: list[0].key,
    //   });
    // }
  };

  const onBankAcctIdChange = () => {
    if (list.length > 0) {
      const bankAcctId = form.getFieldValue('bankAcctId');
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (item.key == bankAcctId) {
          form.setFieldsValue({
            bankAcct: item.shortName,
            bankName: item.reserveObj,
            acctBankName: item.name,
            payeeBank: item.name,
          });
          break;
        } else {
          form.setFieldsValue({
            bankName: undefined,
            acctBankName: undefined,
            payeeBank: undefined,
          });
        }
      }
    } else {
      form.setFieldsValue({
        bankAcct: undefined,
        bankName: undefined,
        acctBankName: undefined,
        bankAcctId: undefined,
        payeeBank: undefined,
      });
    }
  };

  const getDefaultSendWayByCustId = async (
    custId: string,
    payTitleId: string,
    payAuditId: string,
    isSupplier: string,
    isAdjustNoPay: string,
  ) => {
    const r = await API.payroll.payBatch.getDefaultSendWayByCustId.requests({
      custId,
      payTitleId,
      payAuditId,
      isSupplier,
      isAdjustNoPay,
    });
    if (isSupplier === '1') {
      form.setFieldsValue({ sendWay: '2' });
      setSendWayDisable(true);
      return;
    }
    if (r == '3') {
      form.setFieldsValue({ sendWay: '2' });
      setSendWayDisable(false);
    } else if (r == '2') {
      form.setFieldsValue({ sendWay: '2' });
      setSendWayDisable(true);
    } else {
      form.setFieldsValue({ sendWay: '1' });
      setSendWayDisable(false);
    }
  };

  const getPayAuditDataById = async () => {
    try {
      setLoading(true);
      const res = await API.welfaremanage.socialManage.getPayAuditDataById.requests({
        payAuditId: props.data?.payAuditId,
      });
      // 获取可以更改的附言选项
      API.payroll.payBatch.getpostscriptRmkList.requests({}).then((_res: any) => {
        setScriptRmkList(_res?.list || []);
        if (_res?.list?.findIndex((i) => i.key == res?.postscript) > -1) {
          setTextDisabled(false);
        }
      });
      setNewData(res);
      isActualPayOld = res.isActualPay;
      if (
        props.data?.verifyStatus &&
        props.data?.verifyStatus?.split(',')?.length === 1 &&
        props.data?.verifyStatus?.split(',')?.[0] == '完全核销' &&
        (!res.isActualPay || res.isActualPay == '')
      ) {
        setIsActualPay('2');
        form.setFieldsValue({
          isActualPay: '2',
        });
      }
      setIsActualPay(res.isActualPay);
      form.setFieldsValue({
        ...res,
        bankAcct: undefined,
        bankName: undefined,
        acctBankName: undefined,
        payeeBank: undefined,
        bankAcctId: undefined,
      });
      if (props.isActualPayHGroup) {
        getIsBlackListByCustId(res.custId, res.payAddress, res.depTitleId);
        getDefaultSendWayByCustId(
          res.custId,
          res.depTitleId,
          res.payAuditId,
          res.isSupplier,
          res.isAdjustNoPay,
        );
      }
      getAllAmt(res);
    } finally {
      setLoading(false);
    }
  };

  const getAllAmt = async (_data: POJO) => {
    form.setFieldsValue({ amt: 0 });
    const p = { ...props.data, ..._data } as POJO;
    const payDetailType = _data.payDetailType;
    if (p.paySends && payDetailType) {
      const service =
        payDetailType == '1'
          ? API.payroll.payBatch.getAllF3AndF10AmtBySendIds
          : payDetailType == '2'
          ? API.payroll.payBatch.getAllF10AmtBySendIds
          : API.payroll.payBatch.getTaxAmtBySendIds;
      p.isDelayAmtFlag = null;
      form.setFieldsValue({ applyPayAmt: 0 });
      const res = await service.requests(p);
      const r = res[0];
      if (r) {
        form.setFieldsValue({ amt: r?.toString() });
        getApplyPayAmt(_data);
      }
    }
  };

  const getApplyPayAmt = async (_data: POJO) => {
    const p = { ...props.data, ..._data } as POJO;
    const payDetailType = _data.payDetailType;
    if (p.paySends && payDetailType) {
      const service =
        payDetailType == '1'
          ? API.payroll.payBatch.getAllF3AndF10AmtBySendIds
          : payDetailType == '2'
          ? API.payroll.payBatch.getAllF10AmtBySendIds
          : API.payroll.payBatch.getTaxAmtBySendIds;
      p.isDelayAmtFlag = '1';
      const res = await service.requests(p);
      const r = res[0];
      if (r) {
        form.setFieldsValue({ applyPayAmt: r?.toString() });
      }
    }
  };

  const onSave = async () => {
    const values = await form.validateFields();
    if (values.isActualPay == isActualPayOld) return msgErr('到款信息没有变化，不用保存');
    if (values.postscriptRmk && values.postscriptRmk !== '') {
      const _str = values.postscriptRmk?.replaceAll(' ', '');
      if (_str.includes('工资') || _str.includes('奖金')) {
        return msgErr('附言说明内容不能输入工资、奖金等敏感字眼');
      }
    }
    await API.payroll.payBatch.savePayAuditByFundManager.requests({
      ...values,
      payAuditId: props.data?.payAuditId,
    });
    msgOk('操作成功');
    props.refreshTable();
    getPayAuditDataById();
  };

  const onResubmit = async () => {
    const values = await form.validateFields();
    const p = { ...props.data, ...newData, ...values };
    if (values.postscriptRmk && values.postscriptRmk !== '') {
      const _str = values.postscriptRmk?.replaceAll(' ', '');
      if (_str.includes('工资') || _str.includes('奖金')) {
        return msgErr('附言说明内容不能输入工资、奖金等敏感字眼');
      }
    }
    if (p.f10008Sum && Number(p?.f10008Sum) > 0 && (!values.fileId || values.fileId == '')) {
      return msgErr('请选择上传文件');
    }
    const payDetailType = await form.getFieldValue('payDetailType');
    if (props.data?.paySends && payDetailType) {
      const service =
        payDetailType == 1
          ? API.payroll.payBatch.getAllF3AndF10AmtBySendIds
          : payDetailType == 2
          ? API.payroll.payBatch.getAllF10AmtBySendIds
          : API.payroll.payBatch.getTaxAmtBySendIds;
      p.isDelayAmtFlag = '1';
      const r = await service.requests(p);
      if (!r) return msgErr('申请支付金额为空,操作不成功');
      if (+r != +p.applyPayAmt)
        return msgErr(`申请支付金额与明细汇总金额不一致，明细汇总金额为 : ${+r}`);
      const param: POJO = {
        ...p,
        remarkText: values.newRemark,
        workitemId: p.workitemId,
        userId: currentUser.profile.userId,
        userName: currentUser.profile?.realName,
        activityNameEn: props.data?.activityNameEn,
      };
      await API.payroll.payBatch.approve.requests(param);
      msgOk('操作成功');
      props.hideHandle(true);
    }
  };

  const onPass = async (isUseCheckPayApprove?: string) => {
    let values = {} as POJO;
    if (isUseCheckPayApprove !== '1') {
      values = (await form.validateFields()) as POJO;
      const remark = doValdation(values.newRemark);
      if (!remark) return;
      if (props.data?.activityNameEn == '11' && values.isActualPay == '4') {
        return msgErr(
          '该批次同一批数据核查到款中，如果需要审批通过，请修改是否实际到款后再进行操作',
        );
        // const res = await API.payroll.payBatch.getExistCheckPayStatusByCustIdAndAuditName.requests({
        //   custId: values.custId,
        //   payAuditName: props.data?.payAuditName,
        //   payAuditId: props.data?.payAuditId,
        // });
        // if (res == '1') {
        //   return msgErr(
        //     '该批次同一批数据核查到款中，如果需要审批通过，请修改是否实际到款后再进行操作',
        //   );
        // }
      }
    } else {
      values = form.getFieldsValue();
    }
    if (values.postscriptRmk && values.postscriptRmk !== '') {
      const _str = values.postscriptRmk?.replaceAll(' ', '');
      if (_str.includes('工资') || _str.includes('奖金')) {
        return msgErr('附言说明内容不能输入工资、奖金等敏感字眼');
      }
    }
    const p = { ...props.data, ...values };
    const activityNameEn = p.activityNameEn;
    if (activityNameEn == '11' || props.data?.status == '11') {
      const isActualPay = p.isActualPay;
      if (isActualPay == '1' && isUseCheckPayApprove !== '1')
        return msgErr('已审核未到账不能提交审核');
    }
    Modal.confirm({
      content:
        isUseCheckPayApprove && isUseCheckPayApprove == '1'
          ? '将激活核查到款审批步骤，是否继续？'
          : '是否通过这个审批?',
      onOk: async () => {
        const param: POJO = {
          ...newData,
          remarkText: values.newRemark,
          workitemId: p.workitemId,
          userId: currentUser.profile?.userId,
          userName: currentUser.profile?.realName,
          activityNameEn: props.data?.activityNameEn,
          sendWay: values?.sendWay,
          acctBankName: p.acctBankName,
          bankName: p.bankName,
          payeeBank: p.payeeBank,
          bankAcctId: p.bankAcctId,
          bankAcct: p.bankAcctId ? bankInfoComboList.get(p.bankAcctId) : p.bankAcct,
          isActualPay: values.isActualPay,
          isSupplier: p.isSupplier,
          postscript: values.postscript || newData.postscript,
          postscriptRmk: values.postscriptRmk,
          isUseCheckPayApprove,
        };
        await API.payroll.payBatch.approve.requests(param);
        msgOk('操作成功');
        props.hideHandle(true);
      },
    });
  };

  // 核查到款的审批通过
  const onPass2 = async () => {
    const values = (await form.validateFields()) as POJO;
    const remark = doValdation(values.newRemark);
    if (!remark) return;
    if (values.postscriptRmk && values.postscriptRmk !== '') {
      const _str = values.postscriptRmk?.replaceAll(' ', '');
      if (_str.includes('工资') || _str.includes('奖金')) {
        return msgErr('附言说明内容不能输入工资、奖金等敏感字眼');
      }
    }
    const p = { ...props.data, ...values };
    Modal.confirm({
      content: '是否通过这个审批?',
      onOk: async () => {
        const param: POJO = {
          ...newData,
          remarkText: values.newRemark,
          workitemId: p.workitemId,
          userId: currentUser.profile?.userId,
          userName: currentUser.profile?.realName,
          activityNameEn: props.data?.activityNameEn,
          sendWay: values?.sendWay,
          acctBankName: p.acctBankName,
          bankName: p.bankName,
          payeeBank: p.payeeBank,
          bankAcctId: p.bankAcctId,
          bankAcct: p.bankAcctId ? bankInfoComboList.get(p.bankAcctId) : p.bankAcct,
          isActualPay: values.isActualPay,
          isSupplier: p.isSupplier,
          postscript: values.postscript || newData.postscript,
          postscriptRmk: values.postscriptRmk,
          payPurpose: p.payPurpose,
          payReason: p.payReason,
          remark: p.remark,
        };
        await API.payroll.payBatch.approveBackPrevious.requests(param);
        msgOk('操作成功');
        props.hideHandle(true);
      },
    });
  };

  const onAbort = () => {
    const values = form.getFieldsValue() as POJO;
    const remark = doValdation(values.newRemark);
    if (!remark) return;
    if (values.postscriptRmk && values.postscriptRmk !== '') {
      const _str = values.postscriptRmk?.replaceAll(' ', '');
      if (_str.includes('工资') || _str.includes('奖金')) {
        return msgErr('附言说明内容不能输入工资、奖金等敏感字眼');
      }
    }
    const p = { ...props.data, ...values };
    Modal.confirm({
      content: '是否驳回这个审批?',
      onOk: async () => {
        const param: POJO = {
          ...newData,
          remarkText: values.newRemark,
          workitemId: p.workitemId,
          userId: currentUser.profile.userId,
          userName: currentUser.profile?.realName,
          activityNameEn: props.data?.activityNameEn,
          postscript: values.postscript || newData.postscript,
          postscriptRmk: values.postscriptRmk,
        };
        await API.payroll.payBatch.backProcess.requests(param);
        msgOk('操作成功');
        props.hideHandle(true);
      },
    });
  };

  const onEnd = () => {
    const values = form.getFieldsValue() as POJO;
    const remark = doValdation(values.newRemark);
    if (!remark) return;
    if (values.postscriptRmk && values.postscriptRmk !== '') {
      const _str = values.postscriptRmk?.replaceAll(' ', '');
      if (_str.includes('工资') || _str.includes('奖金')) {
        return msgErr('附言说明内容不能输入工资、奖金等敏感字眼');
      }
    }
    const p = { ...props.data, ...values };
    Modal.confirm({
      content: '是否终止这个审批?',
      onOk: async () => {
        const param: POJO = {
          ...newData,
          remarkText: values.newRemark,
          workitemId: p.workitemId,
          userId: currentUser.profile.userId,
          userName: currentUser.profile?.realName,
          activityNameEn: props.data?.activityNameEn,
          postscript: values.postscript || newData.postscript,
          postscriptRmk: values.postscriptRmk,
        };
        await API.payroll.payBatch.terminal.requests(param);
        msgOk('操作成功');
        props.hideHandle(true);
      },
    });
  };

  const onPay = async () => {
    const values = form.getFieldsValue() as POJO;
    const remark = doValdation(values.newRemark);
    if (!remark) return;
    if (values.postscriptRmk && values.postscriptRmk !== '') {
      const _str = values.postscriptRmk?.replaceAll(' ', '');
      if (_str.includes('工资') || _str.includes('奖金')) {
        return msgErr('附言说明内容不能输入工资、奖金等敏感字眼');
      }
    }
    const p = { ...props.data, ...values };
    const param: POJO = {
      ...newData,
      remarkText: values.newRemark,
      workitemId: p.workitemId,
      userId: currentUser.profile.userId,
      userName: currentUser.profile?.realName,
      activityNameEn: props.data?.activityNameEn,
    };
    await API.payroll.payBatch.approve.requests(param);
    msgOk('操作成功');
    props.hideHandle(true);
  };

  const doValdation = (newRemark: string | undefined) => {
    if (newRemark) return true;
    msgErr('请填写审批意见');
    return false;
  };

  const renderFooter = () => {
    const activityNameEn = props.data?.activityNameEn;
    const activityStatus = props.data?.activityStatus;
    const notRenderApprove = activityNameEn == 9 || activityNameEn == 1 || activityNameEn == 12;
    const notRenderBack = activityStatus == 1 || activityStatus == 0 || activityNameEn == 12;
    const notRenderTerminal = activityStatus == 0 || activityStatus == 2;
    const showSave = activityNameEn == 11;
    return (
      <React.Fragment>
        {showSave && props.data?.status != '8' && (
          <AsyncButton onClick={onSave}>保存到款信息</AsyncButton>
        )}
        {activityNameEn == 1 && <AsyncButton onClick={onResubmit}>重新申请</AsyncButton>}
        {!notRenderApprove && <AsyncButton onClick={onPass}>审批通过</AsyncButton>}
        {/* 核查到款的审批通过 */}
        {activityNameEn == 12 && <AsyncButton onClick={onPass2}>审批通过</AsyncButton>}
        {!notRenderBack && <AsyncButton onClick={onAbort}>驳回</AsyncButton>}
        {!notRenderTerminal && <AsyncButton onClick={onEnd}>终止</AsyncButton>}
        <AsyncButton onClick={() => props.hideHandle()}>返回</AsyncButton>
        {activityNameEn == 9 && <AsyncButton onClick={onPay}>支付</AsyncButton>}
        {activityNameEn == 11 && localIsActualPay !== '2' && localIsActualPay !== '4' && (
          <AsyncButton onClick={() => onPass('1')}>核查到款</AsyncButton>
        )}
      </React.Fragment>
    );
  };

  const next = formColumns2.filter((e) => {
    if (e.fieldName === 'bankAcct') return bankAcctEdit;
    else if (e.fieldName === 'bankAcctId') return !bankAcctEdit;
    return true;
  });

  return (
    <Codal
      title="相关审批查看页面"
      visible={props.visible}
      onCancel={() => props.hideHandle()}
      width={1200}
      footer={renderFooter()}
    >
      <Spin spinning={loading}>
        <FormElement3 form={form}>
          <EnumerateFields outerForm={form} colNumber={3} formColumns={formColumns1} />
          <div style={{ marginTop: 5, marginBottom: 5 }}>
            <Button onClick={() => setVisible(true)}>进入明细</Button>
          </div>
          <EnumerateFields outerForm={form} colNumber={3} formColumns={next} />
          <div>支付审批</div>
          <EnumerateFields outerForm={form} colNumber={3} formColumns={formColumns3} />
        </FormElement3>
      </Spin>
      <WagePayApproveDetail
        visible={visible}
        hideHandle={() => setVisible(false)}
        data={{
          ...props.data,
          ...newData,
          assignerDepartmentId: form.getFieldValue('assignerDepartmentId'),
        }}
      />
    </Codal>
  );
};

export default WagePayApprove;
