import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/areacode/saveOrUpdate
     * @desc 批量新增或修改地区码信息
批量新增或修改地区码信息
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = false;
export const url = '/rhro-service-1.0/areacode/saveOrUpdate:POST';
export const initialUrl = '/rhro-service-1.0/areacode/saveOrUpdate';
export const cacheKey = '_areacode_saveOrUpdate_POST';
export async function request(
  data: defs.basedata.BatchSaveOrUpdateAreaCodeVO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/areacode/saveOrUpdate`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.basedata.BatchSaveOrUpdateAreaCodeVO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/areacode/saveOrUpdate`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
