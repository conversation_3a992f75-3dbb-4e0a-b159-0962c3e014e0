{"policy.siteQuery": "陈国祥 服务网点查询 | ./infopublication/Policy/SiteQuery/index", "policy.calculationSs.calculationSsCust": "刘夏梅 社保公积金试算表（单个城市）客服 | ./infopublication/Policy/CalculationSsCust/index", "policy.calculationSs.calculationSsSale": "刘夏梅 社保公积金试算表（单个城市）销售 | ./infopublication/Policy/CalculationSsSale/index", "policy.calculationSs.calculationSsCustMult": "刘夏梅 社保公积金试算表（多城市） 客户 | ./infopublication/Policy/CalculationSsCustMult/index", "policy.calculationSs.calculationSsCityMult": "刘夏梅 社保公积金试算表（多城市） 按城市 | ./infopublication/Policy/CalculationSsCityMult/index", "policy.surveySsPolicy.surveySsPolicyCust": "陈国祥 社保公积金政策一览（单个城市）客服 | ./infopublication/Policy/SurveySsPolicyCust/index", "policy.surveySsPolicy.surveySsPolicySale": "陈国祥 社保公积金政策一览（单个城市）销售 | ./infopublication/Policy/SurveySsPolicySale/index", "policy.surveySsPolicy.surveySsPolicyCustMult": "陈国祥 社保公积金政策一览（多个城市）客户 | ./infopublication/Policy/SurveySsPolicyCustMult/index", "policy.surveySsPolicy.surveySsPolicyCityMult": "陈国祥 社保公积金政策一览（多个城市）城市 | ./infopublication/Policy/SurveySsPolicyCityMult/index", "marketAnnouncement": "陈国祥 市场文宣 | ./infopublication/MarketPropaganda/index", "policy.other.SingleUseFee": "孙尚阳 一次性费用一览 | ./infopublication/Policy/SingleUseFee/index", "policy.other.LegalHoliday": "孙尚阳 法定假日一览 | ./infopublication/Policy/LegalHoliday/index", "policy.other.surveyPayrollPolicyCity": "陈国祥 年度工资标准一览(省份/城市) | ./infopublication/Policy/SurveyPayrollPolicyCity/index", "policy.Disability.surveyDisabilityPolicy": "陈国祥 残疾人保障金一览 | ./infopublication/Policy/SurveyDisabilityPolicy/index", "policy.Disability.calculationDisabilityPolicy": "陈国祥 残保金方案测算 | ./infopublication/Policy/CalculationDisabilityPolicy/index", "welfare.welfareHandleQueryMult": "赵煜颢 福利待遇办理查询-多城市（横表） | ./infopublication/Welfare/WelfareHandleQueryMult/index", "welfare.welfareHandleQueryCust": "赵煜颢 福利待遇办理查询-按客户（横表） | ./infopublication/Welfare/WelfareHandleQueryCust/index", "welfare.welfareHandleQuery": "赵煜颢 福利待遇办理查询-单城市（竖表） | ./infopublication/Welfare/WelfareHandleQuery/index", "policy.maintenance.LevelMaintenance": "涂澳 政策层级维护 | ./infopublication/Policy/LevelMaintenance/index", "policy.maintenance.LabelMaintenance": "涂澳 政策标签维护 | ./infopublication/Policy/LabelMaintenance/index", "policy.maintenance.policyDetail": "陈国祥 政策详情维护 | ./infopublication/Policy/PolicyDetail/index", "policy.maintenance.specialZone": "陈国祥 特区名称维护 | ./infopublication/Policy/SpecialZone/index", "policy.maintenance.policyTitleManage": "谭金晶 政策标题管理 | ./infopublication/Policy/PolicyTitleManage/index", "policy.maintenance.policyTemplateDetail": "谭金晶 政策详情模板 | ./infopublication/Policy/PolicyTemplateDetail/index", "policy.maintenance.ProvinceManage": "谭金晶 省份政策维护人管理 | ./infopublication/Policy/ProvinceManage/index", "policy.PolicyQuery.SingleQuery": "谭金晶 政策查询(单个) | ./infopublication/Policy/PolicyQuery/SingleQuery/index", "policy.PolicyQuery.SingleQueryByCustomer": "谭金晶 政策查询(单个-按客户) | ./infopublication/Policy/PolicyQuery/SingleQueryByCustomer/index", "policy.PolicyQuery.BatchQuery": "谭金晶 政策查询(批量) | ./infopublication/Policy/PolicyQuery/BatchQuery/index", "policy.PolicyQuery.BatchQueryByCustomer": "谭金晶 政策查询(批量-按客户) | ./infopublication/Policy/PolicyQuery/BatchQueryByCustomer/index", "policy.PolicyQuery.SaleQuery": "谭金晶 政策查询(标准) | ./infopublication/Policy/PolicyQuery/SaleQuery/index"}