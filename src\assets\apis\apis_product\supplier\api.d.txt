declare namespace defs {
  export namespace supplier {
    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: boolean;

      /** message */
      message: string;

      /** t */
      t: boolean;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class FilterEntity {
      /** activityNameCn */
      activityNameCn: string;

      /** activityNameEn */
      activityNameEn: string;

      /** activityStatus */
      activityStatus: string;

      /** add */
      add: boolean;

      /** applicant */
      applicant: string;

      /** applicantName */
      applicantName: string;

      /** applyDt */
      applyDt: string;

      /** applyEndDt */
      applyEndDt: string;

      /** applyPayAddress */
      applyPayAddress: string;

      /** applyPayAddressName */
      applyPayAddressName: string;

      /** applyPayAddressTitle */
      applyPayAddressTitle: string;

      /** applyPayAddressTitleId */
      applyPayAddressTitleId: string;

      /** applyPayAmt */
      applyPayAmt: number;

      /** applyStartDt */
      applyStartDt: string;

      /** 审批意见 */
      approveOpinion: string;

      /** approveStatus */
      approveStatus: string;

      /** assignerAddress */
      assignerAddress: string;

      /** assignerApplyTitleBranchId */
      assignerApplyTitleBranchId: string;

      /** assignerTitleName */
      assignerTitleName: string;

      /** 出款账号开户行 */
      backBank: string;

      /** 出款账号 */
      backBankAcct: string;

      /** 出款账号开户名 */
      backBankName: string;

      /** 出款账号所属行 */
      backPayeeBank: string;

      /** bank */
      bank: string;

      /** bankAcct */
      bankAcct: string;

      /** bankName */
      bankName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** createDtEnd */
      createDtEnd: string;

      /** createDtStart */
      createDtStart: string;

      /** custCode */
      custCode: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** depTitleBranchId */
      depTitleBranchId: string;

      /** deptTitleId */
      deptTitleId: string;

      /** deptTitleName */
      deptTitleName: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** f10Sum */
      f10Sum: number;

      /** 残障金(工资) */
      f15Sum: string;

      /** f3Sum */
      f3Sum: number;

      /** 工会费(工资) */
      f42Sum: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** groupId */
      groupId: string;

      /** groupName */
      groupName: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** lastPayDt */
      lastPayDt: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** participant */
      participant: string;

      /** payAddress */
      payAddress: string;

      /** payAddressTitleId */
      payAddressTitleId: string;

      /** payAmt */
      payAmt: string;

      /** payApplyCode */
      payApplyCode: string;

      /** payAuditId */
      payAuditId: string;

      /** payBatchId */
      payBatchId: string;

      /** payBatchName */
      payBatchName: string;

      /** payDetailMethod */
      payDetailMethod: string;

      /** payDetailType */
      payDetailType: string;

      /** payDt */
      payDt: string;

      /** payMethod */
      payMethod: string;

      /** paySends */
      paySends: string;

      /** payType */
      payType: string;

      /** payeeBank */
      payeeBank: string;

      /** postscript */
      postscript: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processDefId */
      processDefId: string;

      /** 流程实例化id */
      processInsId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** remark */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** supplierId */
      supplierId: string;

      /** supplierName */
      supplierName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 供应商工资支付id */
      wgSupplierPayId: string;

      /** withholdAgentName */
      withholdAgentName: string;

      /** withholdAgentType */
      withholdAgentType: string;

      /** workItemId */
      workItemId: string;
    }

    export class SupplierTaskDTO {
      /** activityNameEn */
      activityNameEn: string;

      /** add */
      add: boolean;

      /** 附言 */
      addComments: string;

      /** 应付总额 */
      amt: string;

      /** 申请人 */
      applicant: string;

      /** applicantName */
      applicantName: string;

      /** 申请时间 */
      applyDt: string;

      /** 申请日期到 */
      applyEndDt: string;

      /** 扣缴义务人 */
      applyLastPayDt: string;

      /** (待支付)支付地 */
      applyPayAddress: string;

      /** (待支付)支付地 */
      applyPayAddressName: string;

      /** applyPayAddressTitle */
      applyPayAddressTitle: string;

      /** (待支付)支付地抬头ID */
      applyPayAddressTitleId: string;

      /** 申请金额 */
      applyPayAmt: string;

      /** 申请日期从 */
      applyStartDt: string;

      /** 审批通过日期 */
      approveDt: string;

      /** 审批通过日期到 */
      approveEndDt: string;

      /** 审批意见 */
      approveOpinion: string;

      /** 审批通过日期从 */
      approveStartDt: string;

      /** 审批状态 0初始 1审批中 2审批通过 3终止 */
      approveStatus: string;

      /** 审批状态中文 */
      approveStatusStr: string;

      /** 派单地 */
      assignerApplyBranchId: string;

      /** 派单地 */
      assignerApplyBranchName: string;

      /** 派单地抬头对应分公司 */
      assignerApplyTitleBranchId: string;

      /** 派单地抬头对应分公司名称 */
      assignerApplyTitleBranchName: string;

      /** 派单地 */
      assignerDepartmentId: string;

      /** 派单地name */
      assignerDepartmentName: string;

      /** 派单地抬头 */
      assignerDepartmentTitleId: string;

      /** 派单地抬头name */
      assignerDepartmentTitleName: string;

      /** 出款账号开户行 */
      backBank: string;

      /** 出款账号 */
      backBankAcct: string;

      /** 出款账号开户名 */
      backBankName: string;

      /** 出款账号所属行 */
      backPayeeBank: string;

      /** 收款账号开户行 */
      bank: string;

      /** 收款账号 */
      bankAcct: string;

      /** 收款账号开户名 */
      bankName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 发票金额 */
      billAmt: string;

      /** 发票时间 */
      billDt: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 支付地抬头对应分公司 */
      deptTitleBranchId: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 税合计总额 */
      f10Sum: string;

      /** 残障金(工资) */
      f15Sum: string;

      /** 实发金额 */
      f3: string;

      /** 实付金额 */
      f3Sum: string;

      /** 工会费(工资) */
      f42Sum: string;

      /** 失败原因 */
      failureReason: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 供应商集团 */
      groupId: string;

      /** 供应商集团名称 */
      groupName: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否首次 */
      isFrist: string;

      /** 是否已支付 */
      isPay: string;

      /** 最晚支付日期 */
      lastPayDt: string;

      /** 最晚支付日期到 */
      lastPayEndDt: string;

      /** 最晚支付日期从 */
      lastPayStartDt: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 支付地 */
      payAddress: string;

      /** 支付地name */
      payAddressName: string;

      /** 支付地抬头 */
      payAddressTitle: string;

      /** 支付地抬头ID */
      payAddressTitleId: string;

      /** 支付金额 */
      payAmt: string;

      /** payAuditId */
      payAuditId: string;

      /** payBatchId */
      payBatchId: string;

      /** payBatchName */
      payBatchName: string;

      /** payDetailMethod */
      payDetailMethod: string;

      /** 支付日期所属月 */
      payDt: string;

      /** payMethod */
      payMethod: string;

      /** 支付方式中文 */
      payMethodStr: string;

      /** payNO */
      payNO: string;

      /** 支付状态 */
      payStatus: string;

      /** 支付类型 */
      payType: string;

      /** 收款账号收款银行 */
      payeeBank: string;

      /** paysends */
      paysends: string;

      /** 摘要 */
      postScript: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processDefId */
      processDefId: string;

      /** 流程实例化id */
      processInsId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 公私标识 */
      publicFlag: string;

      /** 用途 */
      purpose: string;

      /** 备注 */
      remark: string;

      /** 审批时输入的审批意见 */
      remarkInput: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 供应商ID */
      supplierId: string;

      /** 供应商名称 */
      supplierName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 供应商工资支付id */
      wgSupplierPayId: string;

      /** 扣缴义务人 */
      withholdAgent: string;

      /** 扣缴义务人名称 */
      withholdAgentName: string;

      /** 扣缴义务人类型 */
      withholdAgentType: string;

      /** workitemId */
      workitemId: string;
    }

    export class SupplierTaskQuery {
      /** activityNameEn */
      activityNameEn: string;

      /** 申请人 */
      applicant: string;

      /** 申请时间 */
      applyDt: string;

      /** 申请日期到 */
      applyEndDt: string;

      /** 最晚支付日期 */
      applyLastPayDt: string;

      /** 审批通过日期到 */
      applyPayAddress: string;

      /** 审批通过日期到 */
      applyPayAddressTitleId: string;

      /** 申请金额 */
      applyPayAmt: string;

      /** 申请日期从 */
      applyStartDt: string;

      /** 审批通过日期 */
      approveDt: string;

      /** 审批通过日期到 */
      approveEndDt: string;

      /** 审批意见 */
      approveOpinion: string;

      /** 审批通过日期从 */
      approveStartDt: string;

      /** 审批状态 0初始 1审批中 2审批通过 3终止 */
      approveStatus: string;

      /** 客户名称 */
      assignerApplyBranchId: string;

      /** 派单地抬头对应分公司 */
      assignerApplyTitleBranchId: string;

      /** 派单地 */
      assignerDepartmentId: string;

      /** 派单地name */
      assignerDepartmentName: string;

      /** 派单地抬头 */
      assignerDepartmentTitleId: string;

      /** 派单地抬头name */
      assignerDepartmentTitleName: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 支付地抬头对应分公司 */
      deptTitleBranchId: string;

      /** endIndex */
      endIndex: number;

      /** 税合计总额 */
      f10Sum: string;

      /** 实发金额 */
      f3: string;

      /** 供应商集团 */
      groupId: string;

      /** 供应商集团名称 */
      groupName: string;

      /** 最晚支付日期 */
      lastPayDt: string;

      /** 最晚支付日期到 */
      lastPayEndDt: string;

      /** 最晚支付日期从 */
      lastPayStartDt: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 支付地 */
      payAddress: string;

      /** 支付地name */
      payAddressName: string;

      /** 支付地抬头 */
      payAddressTitle: string;

      /** 支付地抬头ID */
      payAddressTitleId: string;

      /** payBatchId */
      payBatchId: string;

      /** payBatchName */
      payBatchName: string;

      /** 支付日期所属月 */
      payDt: string;

      /** payMethod */
      payMethod: string;

      /** payNO */
      payNO: string;

      /** 支付类型 */
      payType: string;

      /** processDefId */
      processDefId: string;

      /** 流程实例化id */
      processInsId: string;

      /** 备注 */
      remark: string;

      /** startIndex */
      startIndex: number;

      /** 供应商ID */
      supplierId: string;

      /** 供应商名称 */
      supplierName: string;

      /** 供应商工资支付id */
      wgSupplierPayId: string;

      /** 扣缴义务人 */
      withholdAgent: string;

      /** 扣缴义务人名称 */
      withholdAgentName: string;

      /** 扣缴义务人类型 */
      withholdAgentType: string;
    }
  }
}

declare namespace API {
  export namespace supplier {
    /**
     * 供应商工资
     */
    export namespace supplierTask {
      /**
        * 新增供应商工资支付
新增供应商工资支付
        * /supplierTask/addSupplierTask
        */
      export namespace addSupplierTask {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 退回修改
退回修改
        * /supplierTask/backWageSupplierPay
        */
      export namespace backWageSupplierPay {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 供应商工资支付提交审核
供应商工资支付提交审核
        * /supplierTask/commitWageSupplierPay
        */
      export namespace commitWageSupplierPay {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除供应商薪资支付
删除供应商薪资支付
        * /supplierTask/delSupplierTask
        */
      export namespace delSupplierTask {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 供应商工资支付审批
供应商工资支付审批
        * /supplierTask/doApproveWageSupplierPay
        */
      export namespace doApproveWageSupplierPay {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.supplier.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出网银模板
导出网银模板
        * /supplierTask/expEbankTemplate
        */
      export namespace expEbankTemplate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<string>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /supplierTask/expTaskHis
        */
      export namespace expTaskHis {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.supplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出未支付供应商
导出
        * /supplierTask/expWageSupplierNonPayTask
        */
      export namespace expWageSupplierNonPayTask {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.supplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询
供应商工资查询
        * /supplierTask/getSupplierTaskPage
        */
      export namespace getSupplierTaskPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.supplier.SupplierTaskDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 供应商工资明细查询(已生成)
供应商工资明细查询(已生成)
        * /supplierTask/queryWageSupplierDetail
        */
      export namespace queryWageSupplierDetail {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.supplier.FilterEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询未支付供应商的薪资
查询未支付供应商的薪资
查询条件：{custCode 客户编号，custName 客户名称，  payBatchId 发放批次号， payBatchName 发放批次名称 ，createDtStart 审批通过日期从 createDtEnd 审批通过日期到}
返回结果列:{payBatchId发放批次号  payBatchName发放批次名称 applyPayAmt申请金额 ，f3Sum实发金额，  f10Sum税 ，createDt审批通过时间，remark 备注， payAddress支付地， deptTitleName支付地抬头， assignerAddress派单地 assignerTitleName派单地抬头  withholdAgentType扣缴义务人类型， withholdAgentName扣缴义务人}
custName 客户， applicantName 申请人，applyDt 支付日期，payDetailType 支付明细类型，lastPayDt 最后到款日期 
        * /supplierTask/queryWageSupplierNonPay
        */
      export namespace queryWageSupplierNonPay {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询未支付供应商的薪资
查询未支付供应商的薪资
查询条件：{custCode 客户编号，custName 客户名称，  payBatchId 发放批次号， payBatchName 发放批次名称 ，createDtStart 审批通过日期从 createDtEnd 审批通过日期到}
返回结果列:{payBatchId发放批次号  payBatchName发放批次名称 applyPayAmt申请金额 ，f3Sum实发金额，  f10Sum税 ，createDt审批通过时间，remark 备注， payAddress支付地， deptTitleName支付地抬头， assignerAddress派单地 assignerTitleName派单地抬头  withholdAgentType扣缴义务人类型， withholdAgentName扣缴义务人}
custName 客户， applicantName 申请人，applyDt 支付日期，payDetailType 支付明细类型，lastPayDt 最后到款日期 
        * /supplierTask/queryWageSupplierNonPay
        */
      export namespace postQueryWageSupplierNonPay {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询未支付供应商的薪资(待办任务查询)
查询未支付供应商的薪资(待办任务查询)
查询条件：{supplierId 供应商编号， assignerApplyBranchId 派单地  payBatchId 发放批次号， payBatchName 发放批次名称 ，createDtStart 审批通过日期从 createDtEnd 审批通过日期到}
返回结果列:{payType 支付类型，payApplyCode 支付申请单号，activityStatus 审批步骤，payMethod 支付方式，groupId 供应商集团编号，groupName 供应商集团，supplierId 供应商Id,supplierName 供应商，applicantName 申请人，applyDt 申请时间，lastPayDt 最后到款日期，payDt 支付所属年月，
payBatchId发放批次号  payBatchName发放批次名称 applyPayAmt申请金额 ，f3Sum实发金额，  f10Sum税 ，createDt审批通过时间，remark 备注， payAddress支付地， deptTitleName支付地抬头，
 assignerAddress派单地 assignerTitleName派单地抬头  withholdAgentType扣缴义务人类型， withholdAgentName扣缴义务人}
        * /supplierTask/queryWageSupplierNonPayTask
        */
      export namespace queryWageSupplierNonPayTask {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询未支付供应商的薪资(待办任务查询)
查询未支付供应商的薪资(待办任务查询)
查询条件：{supplierId 供应商编号， assignerApplyBranchId 派单地  payBatchId 发放批次号， payBatchName 发放批次名称 ，createDtStart 审批通过日期从 createDtEnd 审批通过日期到}
返回结果列:{payType 支付类型，payApplyCode 支付申请单号，activityStatus 审批步骤，payMethod 支付方式，groupId 供应商集团编号，groupName 供应商集团，supplierId 供应商Id,supplierName 供应商，applicantName 申请人，applyDt 申请时间，lastPayDt 最后到款日期，payDt 支付所属年月，
payBatchId发放批次号  payBatchName发放批次名称 applyPayAmt申请金额 ，f3Sum实发金额，  f10Sum税 ，createDt审批通过时间，remark 备注， payAddress支付地， deptTitleName支付地抬头，
 assignerAddress派单地 assignerTitleName派单地抬头  withholdAgentType扣缴义务人类型， withholdAgentName扣缴义务人}
        * /supplierTask/queryWageSupplierNonPayTask
        */
      export namespace postQueryWageSupplierNonPayTask {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 终止
终止
        * /supplierTask/terminalWageSupplierPay
        */
      export namespace terminalWageSupplierPay {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改供应商薪资支付
修改供应商薪资支付
        * /supplierTask/updateWageSupplierDto
        */
      export namespace updateWageSupplierDto {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存发票信息
保存发票信息
        * /supplierTask/updateWageSupplierPayBill
        */
      export namespace updateWageSupplierPayBill {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 手工修改支付结果
手工修改支付结果
        * /supplierTask/updateWgSupplierPayStatusById
        */
      export namespace updateWgSupplierPayStatusById {
        export class Params {}

        export type Response<T> = defs.supplier.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.supplier.SupplierTaskDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
