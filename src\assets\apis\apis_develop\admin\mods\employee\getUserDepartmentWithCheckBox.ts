import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employee/getUserDepartmentWithCheckBox
     * @desc 获取有复选框的用户和部门菜单列表,显示用
获取有复选框的用户和部门菜单列表,部门列表deptList，用户列表userList
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** userId */
  userId: string;
}

export const init = new defs.admin.CommonResponse();
export const url =
  '/rhro-service-1.0/employee/getUserDepartmentWithCheckBox:GET';
export const initialUrl =
  '/rhro-service-1.0/employee/getUserDepartmentWithCheckBox';
export const cacheKey = '_employee_getUserDepartmentWithCheckBox_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/getUserDepartmentWithCheckBox`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employee/getUserDepartmentWithCheckBox`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
