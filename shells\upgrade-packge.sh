npm install --save eslint-plugin-jsx-a11y@^6.2.3 dva@^2.6.0-beta.20 babel-eslint@^10.1.0 eslint-plugin-import@^2.20.1 eslint-plugin-react@^7.19.0 less@^3.11.1 antd@^4.0.4 antd-editable@^3.0.0 path-to-regexp@^6.1.0 umi@^3.0.14 antd-pro-merge-less@^3.0.4 chalk@^3.0.0 cross-env@^7.0.2 eslint@^6.8.0 eslint-config-airbnb@^18.1.0 eslint-config-prettier@^6.10.1 eslint-plugin-compat@^3.5.1 husky@^4.2.3 lint-staged@^10.0.9 pont-engine@^1.0.0 prettier@^2.0.2 serverless-http@^2.3.2 stylelint@^13.2.1 stylelint-config-css-modules@^2.2.0 stylelint-config-prettier@^8.0.1 stylelint-config-standard@^20.0.0 stylelint-declaration-block-no-ignored-properties@^2.3.0 stylelint-order@^4.0.0 tslint@^6.1.0 tslint-react@^4.2.0 umi-plugin-pro-block@^2.0.2 stylelint-config-rational-order@^0.1.2 umi-types@^0.5.13
