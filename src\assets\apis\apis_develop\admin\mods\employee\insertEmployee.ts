import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employee/insertEmployee
     * @desc 插入员工信息
插入员工信息,返回userId
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.CommonResponse();
export const url = '/rhro-service-1.0/employee/insertEmployee:POST';
export const initialUrl = '/rhro-service-1.0/employee/insertEmployee';
export const cacheKey = '_employee_insertEmployee_POST';
export async function request(
  data: defs.admin.Employee,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employee/insertEmployee`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.Employee,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employee/insertEmployee`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
