# chro 前端文档

## 前言

本文档为HRO新项目技术文档，涉及工程结构、公共组件说明、公共方法说明、业务功能实现、技术介绍。

相关技术：

`React` 基础开发框架，`TypeScript`基础开发语言, `Redux (dva)`数据管理, `umi`项目构建管理。

 项目仓库地址：
 git@*************:chro_web

 需求文档地址：
 git@*************:chro_doc
### [第一卷 工程](projects/1.1-struct.md)
  关于本项目工程结构与设计。

### 第二卷 公共组件
公共组件是复用代码思想的产物。在代码解耦，集中潜在bug，最小化不确定性方面，拥有大量益处。

### [第三卷 公共方法](modules/1.1-utils.md)
公共方法多为可复用函数，基于函数输出结果的确定性，集中模板代码，减少重复开发。

### [第四卷 业务功能](implements/1.0-configs.md)
将重点解释奇葩功能，详细说明过于复杂的代码实现，降低理解难度。

### [第五卷 前沿技术](technics/1.1-sysfuncs.md)
技术发展史，是人的历史，是现实世界的交往史。由于视野的局限，资源的匮乏，交流的偏差，各项目会遗留下大量反直觉设计和蹩脚的实现，此部分致力于梳理相关设计和实现发展史。
