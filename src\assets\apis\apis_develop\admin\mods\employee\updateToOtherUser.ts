import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employee/updateToOtherUser
     * @desc 换这个人的其他部门做默认部门
换这个人的其他部门做默认部门,部门departmentId，用户userId，原部门人员origDeptUserId，部门人员deptUserId
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.admin.CommonResponse();
export const url = '/rhro-service-1.0/employee/updateToOtherUser:POST';
export const initialUrl = '/rhro-service-1.0/employee/updateToOtherUser';
export const cacheKey = '_employee_updateToOtherUser_POST';
export async function request(
  data: defs.admin.EmployeeVO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employee/updateToOtherUser`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.admin.EmployeeVO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employee/updateToOtherUser`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
