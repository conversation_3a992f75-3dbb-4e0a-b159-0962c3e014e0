import React, { useState } from 'react';
import { Button, Space, Card } from 'antd';
import BusinessContentForm from './components/BusinessContentForm';
import BusinessNodeForm from './components/BusinessNodeForm';

/**
 * 演示页面 - 展示弹窗表单功能
 * 这个文件仅用于开发测试，不会在生产环境中使用
 * 使用Codal组件实现弹窗表单，参考AddThreeInOneIntegration模式
 */
const NationwideBusinessDemo: React.FC = () => {
  const [businessContentVisible, setBusinessContentVisible] = useState(false);
  const [businessNodeVisible, setBusinessNodeVisible] = useState(false);
  const [businessContentInitialInfo, setBusinessContentInitialInfo] = useState<any>({});
  const [businessNodeInitialInfo, setBusinessNodeInitialInfo] = useState<any>({});

  const mockBusinessContentData = {
    businessType: '719001',
    businessProject: 'project001',
    businessContent: '工伤认定申请',
    handleAttribute: '1',
    handleObject: '1',
    handleMethod: '1',
    isWechatShow: '1',
    isClientShow: '1',
  };

  const mockBusinessNodeData = {
    businessNodeName: '材料审核',
  };

  // 模拟列表操作实例
  const mockListOptions = {
    request: (queries: any) => {
      console.log('刷新列表数据:', queries);
    },
    queries: {},
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="全国业务维护弹窗演示（AddThreeInOneIntegration模式）" style={{ marginBottom: 24 }}>
        <Space>
          <Button
            type="primary"
            onClick={() => {
              setBusinessContentInitialInfo({});
              setBusinessContentVisible(true);
            }}
          >
            新增业务内容
          </Button>
          <Button
            onClick={() => {
              setBusinessContentInitialInfo(mockBusinessContentData);
              setBusinessContentVisible(true);
            }}
          >
            修改业务内容（带初始值）
          </Button>
          <Button
            type="primary"
            onClick={() => {
              setBusinessNodeInitialInfo({});
              setBusinessNodeVisible(true);
            }}
          >
            新增业务节点
          </Button>
          <Button
            onClick={() => {
              setBusinessNodeInitialInfo(mockBusinessNodeData);
              setBusinessNodeVisible(true);
            }}
          >
            修改业务节点（带初始值）
          </Button>

          {/* 业务内容弹窗 - 集成在按钮区域 */}
          <BusinessContentForm
            modal={[businessContentVisible, setBusinessContentVisible]}
            listOptions={mockListOptions}
            initialInfo={businessContentInitialInfo}
          />

          {/* 业务节点弹窗 - 集成在按钮区域 */}
          <BusinessNodeForm
            modal={[businessNodeVisible, setBusinessNodeVisible]}
            listOptions={mockListOptions}
            initialInfo={businessNodeInitialInfo}
          />
        </Space>
      </Card>
    </div>
  );
};

export default NationwideBusinessDemo;
