/*
 * @Author: “liuxiamei” “<EMAIL>”
 * @Date: 2025-08-26 16:30:06
 * @LastEditors: “liuxiamei” “<EMAIL>”
 * @LastEditTime: 2025-08-29 10:52:41
 * @FilePath: \rhro_web2\src\pages\empwelfare\Ebmtransact\BusinessProcessingProject\components\AddCodal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Typography } from 'antd';
import Codal from '@/components/Codal';
import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { AsyncButton } from '@/components/Forms/Confirm';
import { CachedPage } from '@/components/CachedPage';
import { WritableInstance } from '@/components/Writable';
import { WritableColumnProps } from '@/utils/writable/types';
import {
  CitySelector,
  GetBaseBusnameClassDropdownList,
  mapToSelectors,
} from '@/components/Selectors';
import {
  busSourceMap,
  busTypeMap,
  processObjectMap,
} from '@/utils/settings/empwelfare/businessProcessing';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';

interface Props {
  [props: string]: any;
  //锁定类型
  visible: string | undefined;
  hideHandle: CallableFunction;
  title: string;
  initValues?: any;
}

const initRules = {};

const initDisabledFields = {};

const servicePackage = API.basedata.areaCode.list;

const AddCodal: React.FC<Props> = (props) => {
  let options: WritableInstance;
  const { Paragraph } = Typography;

  const { visible, hideHandle, title } = props;
  const [disabledFields1, setDisabledFields1] = useState({ ...initDisabledFields });
  const [rules1, setRules1] = useState({ ...initRules });
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();
  const [form3] = Form.useForm();
  const [form4] = Form.useForm();
  const [form5] = Form.useForm();

  const [processObject, setprocessObject] = useState<string | undefined>();

  useEffect(() => {
    if (!visible) {
      form1.resetFields();
      form2.resetFields();
      form3.resetFields();
    }
  }, [visible]);

  const columns: WritableColumnProps<any>[] = [
    { title: '材料编号', dataIndex: 'index' },
    { title: '材料名称', dataIndex: 'materialName' },
    { title: '是否原件', dataIndex: 'num' },
    { title: '材料数量', dataIndex: 'price' },
    { title: '是否返还材料', dataIndex: 'a' },
    { title: '材料模板', dataIndex: 'b' },
    { title: '材料上传情况', dataIndex: 'c' },
    { title: '材料确认情况', dataIndex: 'd' },
    { title: '用印签字方', dataIndex: 'g', hidden: visible !== 'receive' },
    { title: '材料收集进度', dataIndex: 'h', hidden: visible !== 'receive' },
    { title: '原件是否已寄出', dataIndex: 'e' },
    { title: '操作', dataIndex: 'f', render: (text, record, index) => <Button>下载</Button> },
  ];

  const formColumns1: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityId',
      inputRender: 'string',
      rules: [{ required: true, message: '请选择' }],
    },

    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm) =>
        mapToSelectors(busTypeMap, {
          onChange: (value) => {
            if (value === '1' || value === '2') {
              outerForm.setFieldsValue({
                sfsf: '0',
                busnameClassId: undefined,
              });
              setRules1({ ...rules1, sqfy: [{ required: false }], sqje: [{ required: false }] });
            } else if (value === '3') {
              outerForm.setFieldsValue({
                sfsf: '1',
                busnameClassId: undefined,
              });
              setRules1({
                ...rules1,
                sqfy: [{ required: true, message: '请选择' }],
                sqje: [{ required: true, message: '请输入金额' }],
              });
            }
          },
        }),
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      inputRender: (outerForm) => {
        const { categoryId } = outerForm.getFieldsValue();
        return (
          <GetBaseBusnameClassDropdownList
            allowClear
            // keyMap={{ busnameClassName: 'busnameClassName', busnameClassId: 'busnameClassId' }}
            params={{
              pageNum: 1,
              pageSize: 2147483647,
              categoryId,
            }}
          />
        );
      },
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务内容',
      fieldName: 'newRemark',
      inputRender: () =>
        mapToSelectors(processObjectMap, {
          onChange: (value) => {
            setprocessObject(value);
            form1.setFieldsValue({
              processObject: value,
              empName: undefined,
              idCardCode: undefined,
              empCode: undefined,
              phone: undefined,
              custId: undefined,
              isIndependent: undefined,
              custPayEntityName: undefined,
            });
            if (value == '1') {
              // 办理对象 1：客户，
              setRules1({
                ...rules1,
                custId: [{ required: true, message: '请选择' }],
                empName: [{ required: false }],
                custPayEntityName: [{ required: false }],
              });
            } else {
              // 2 员工
              setRules1({
                ...rules1,
                custId: [{ required: false }],
                empName: [{ required: true, message: '请选择员工' }],
                custPayEntityName: [{ required: false }],
              });
            }
          },
        }),
    },
    {
      label: '办理属性',
      fieldName: 'banlishuxing',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '办理对象',
      fieldName: 'processObject',
      inputRender: (outerForm) => mapToSelectors(processObjectMap, { disabled: true }),
    },
    {
      label: '业务来源',
      fieldName: 'ywly',
      inputRender: (outerForm) =>
        mapToSelectors(busSourceMap, {
          disabled: true,
          onChange: (value) => {
            if (value === '1' || value === '2') {
              setRules1({
                ...rules1,
                tjzl: [{ required: true, message: '请选择' }],
              });
            } else {
              setRules1({
                ...rules1,
                tjzl: [{ required: false }],
              });
              outerForm.setFieldsValue({
                tjzl: '0',
              });
            }
          },
        }),
    },
    {
      label: '客户名称',
      fieldName: 'custId',
      inputRender: (outerForm) => {
        // 当“办理对象”为 员工 时，根据所选员工对应的客户默认载入只读。办理对象 1：客户，2员工
        return <CitySelector disabled={processObject !== '1'} />;
      },
    },
    { label: '客户规模', fieldName: 'khgm', inputRender: 'string' },
    {
      label: '姓名',
      fieldName: 'empName',
      inputRender: (outerForm) => {
        // 当“办理对象”为 客户 时，清空只读
        return <CitySelector disabled={processObject === '1'} />;
      },
    },
    {
      label: '证件号码',
      fieldName: 'idCardCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '唯一号',
      fieldName: 'empCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '手机号码',
      fieldName: 'phone',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '入离职状态',
      fieldName: 'status',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否单立户',
      fieldName: 'isIndependent',
      inputRender: (outerForm) =>
        mapToSelectors(yesNoDataMap, {
          disabled: processObject === '2',
          onChange: (value) => {
            outerForm.setFieldsValue({
              custPayEntityName: undefined,
            });
            if (processObject === '1' && value === '1') {
              setRules1({
                ...rules1,
                custPayEntityName: [{ required: true, message: '请输入缴费实体名称' }],
              });
            } else {
              setRules1({
                ...rules1,
                custPayEntityName: [{ required: false }],
              });
            }
          },
        }),
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '缴费实体名称',
      fieldName: 'custPayEntityName',
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.isIndependent !== curValues.isIndependent ||
          prevValues.processObject !== curValues.processObject
        );
      },
      inputRender: (outerForm) => {
        const { isIndependent } = outerForm.getFieldsValue();
        return (
          <Input
            maxLength={200}
            disabled={(isIndependent === '0' && processObject === '1') || processObject === '2'}
          />
        );
      },
      //  inputProps: { maxLength: 200,disabled:processObject==='2'} , // 当“办理对象”为 员工 时，默认值只读
    },
    {
      label: '是否收费业务',
      fieldName: 'sfsf',
      inputRender: () => mapToSelectors(yesNoDataMap, { disabled: true }),
    },
    {
      label: '是否收取客户费用',
      fieldName: 'sqfy',
      inputRender: () => mapToSelectors(yesNoDataMap),
    },
    { label: '收取金额', fieldName: 'sqje', inputRender: 'string', inputProps: { maxLength: 200 } },
    {
      label: '是否需要联系员工提交材料',
      fieldName: 'tjzl',
      inputRender: () => mapToSelectors(yesNoDataMap),
    },
    {
      label: '微信端业务进度查询',
      fieldName: 'wxyw',
      inputRender: 'string',
      rules: [{ required: true, message: '请选择' }],
    },
    { label: '是否确认办理', fieldName: 'qrbl', inputRender: 'string' },
    { label: '业务状态', fieldName: 'ywzt', inputRender: 'string', inputProps: { disabled: true } },
    { label: '业务进度', fieldName: 'ywjd', inputRender: 'string', inputProps: { disabled: true } },
  ];

  const formColumns2: EditeFormProps[] = [
    { label: '养老状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '养老福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '失业福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '工伤福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '医疗福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '生育福利起始月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金状态', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利办理方', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利办理月', fieldName: 'newRemark', inputRender: 'string' },
    { label: '公积金福利起始月', fieldName: 'newRemark', inputRender: 'string' },
  ];

  const formColumns3: EditeFormProps[] = [
    { label: '项目客服', fieldName: 'newRemark', inputRender: 'string' },
    { label: '接单客服', fieldName: 'newRemark', inputRender: 'string' },
    { label: '后道客服', fieldName: 'newRemark', inputRender: 'string' },
    { label: '备注', fieldName: 'newRemark', inputRender: 'text', colNumber: 1 },
  ];

  const formColumns5: EditeFormProps[] = [
    { label: '当前业务节点', fieldName: 'newRemark', inputRender: 'string' },
    { label: '是否收取材料', fieldName: 'newRemark', inputRender: 'string' },
    { label: '是否支持津贴待遇', fieldName: 'newRemark', inputRender: 'string' },
    { label: '办理周期', fieldName: 'newRemark', inputRender: 'string' },
    { label: '是否有政府性收费', fieldName: 'newRemark', inputRender: 'string' },
    { label: '政府性收费金额', fieldName: 'newRemark', inputRender: 'string' },
  ];

  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    return (
      <>
        <Button> 材料原件寄件维护</Button>
      </>
    );
  };
  // 保存、提交
  const save_clickHandler = async (type: string) => {
    // const values1 = await form1.validateFields();
    // const values2 = await form2.validateFields();
    // const values3 = await form3.validateFields();
    options.request();
  };
  // 操作按钮
  const CodalButtons = () => {
    return (
      <>
        <AsyncButton type="primary" onClick={() => save_clickHandler('add')}>
          保存
        </AsyncButton>
        {visible === 'project' && (
          <AsyncButton onClick={() => save_clickHandler('submit')}>提交接单确认</AsyncButton>
        )}
        {visible === 'receive' && (
          <>
            <AsyncButton onClick={() => save_clickHandler('submit')}>提交项目确认</AsyncButton>
            <AsyncButton onClick={() => save_clickHandler('submit')}>确认办理</AsyncButton>
          </>
        )}

        <Button onClick={() => hideHandle()}>取消</Button>
      </>
    );
  };

  return (
    <Codal
      title={title}
      width="90vw"
      visible={!!visible}
      onCancel={() => hideHandle()}
      footer={CodalButtons}
    >
      <Paragraph>业务基本信息</Paragraph>
      <FormElement3 form={form1}>
        <EnumerateFields outerForm={form1} formColumns={formColumns1} rules={rules1} />
      </FormElement3>
      {processObject === '2' && (
        <>
          <Paragraph>员工基本信息</Paragraph>
          <FormElement3 form={form2}>
            <EnumerateFields outerForm={form2} formColumns={formColumns2} disabled={true} />
          </FormElement3>
        </>
      )}
      <FormElement3 form={form5} style={{ marginTop: '30px' }}>
        <EnumerateFields outerForm={form5} formColumns={formColumns5} disabled={true} />
      </FormElement3>
      <Paragraph>材料清单</Paragraph>
      <CachedPage
        service={servicePackage}
        columns={columns}
        formColumns={[]}
        renderButtons={renderButtons}
      />

      <FormElement3 form={form3}>
        <EnumerateFields outerForm={form3} formColumns={formColumns3} />
      </FormElement3>
    </Codal>
  );
};

export default AddCodal;
