# 这个文件在rhro_web2目录下运行，复制部份文件到featurejs

cp --path .eslintignore ../featurejs/
cp --path config/browsers.json ../featurejs/
cp --path config/config.ts ../featurejs/
cp --path config/defaultSettings.ts ../featurejs/
cp --path config/locales/types.ts ../featurejs/
cp --path package.json ../featurejs/
cp --path scripts/loadFuncs-pro.js ../featurejs/
cp --path scripts/loadFuncs.ts ../featurejs/
cp --path src/components/Codal/index.tsx ../featurejs/
cp --path src/components/ContentViewer/index.less ../featurejs/
cp --path src/components/ContentViewer/index.tsx ../featurejs/
cp --path src/components/DateComp/StrDatePicker.tsx ../featurejs/
cp --path src/components/DateRange4/dateRange.tsx ../featurejs/
cp --path src/components/EditeForm/AddForm.tsx ../featurejs/
cp --path src/components/Forms/index.less ../featurejs/
cp --path src/components/GlobalHeader/NoticePending.tsx ../featurejs/
cp --path src/components/PageHeaderWrapper/breadcrumb.tsx ../featurejs/
cp --path src/components/Selectors/BaseDataSelectors.tsx ../featurejs/
cp --path src/components/Selectors/BaseDropDown.tsx ../featurejs/
cp --path src/components/Selectors/BaseSelectors.tsx ../featurejs/
cp --path src/components/Selectors/FuncSelectors.tsx ../featurejs/
cp --path src/components/Selectors/index.tsx ../featurejs/
cp --path src/components/StandardPop/SelectWithholdAgentPop.tsx ../featurejs/
cp --path src/components/StandardPop/SsGroupPop.tsx ../featurejs/
cp --path src/components/StandardTable/Pagination.tsx ../featurejs/
cp --path src/components/StandardTable/hooks/useStandardTable.tsx ../featurejs/
cp --path src/components/StandardTable/index.less ../featurejs/
cp --path src/components/StandardTable/index.tsx ../featurejs/
cp --path src/components/UploadForm/ImportForm.tsx ../featurejs/
cp --path src/components/UploadForm/ImportHistoryForm.tsx ../featurejs/
cp --path src/components/UploadForm/index.tsx ../featurejs/
cp --path src/components/Writable/libs/GeneralInput.tsx ../featurejs/
cp --path src/components/Writable/libs/Grid.tsx ../featurejs/
cp --path src/components/Writable/libs/Writable.tsx ../featurejs/
cp --path src/global.less ../featurejs/
cp --path src/layouts/BasicLayout.less ../featurejs/
cp --path src/layouts/BasicLayout.tsx ../featurejs/
cp --path src/layouts/HomeUser.tsx ../featurejs/
cp --path src/layouts/components/index.tsx ../featurejs/
cp --path src/models/cache.ts ../featurejs/
cp --path src/models/notice.ts ../featurejs/
cp --path src/pages/emphiresep/sendorder/CustomerSubcontract/AddTransferAndSubcontract.tsx ../featurejs/
cp --path src/pages/pending/BillingApproval/components/InvoiceDetail.tsx ../featurejs/
cp --path src/pages/pending/BillingApproval/components/ReceiveDetail.tsx ../featurejs/
cp --path src/pages/report/qualitycontrol/ClientEmpVisitedReport/index.tsx ../featurejs/
cp --path src/pages/report/qualitycontrol/ComplainAppSelect/components/AddFormWin.tsx ../featurejs/
cp --path src/pages/payroll/CountPayManage/Forms/ChangeCountPayWin.tsx ../featurejs/
cp --path src/pages/payroll/CountPayManage/Forms/DetailCountPay.tsx ../featurejs/
cp --path src/pages/Sales/CustomerManage/CustomerQuery/components/AddCustMaterial.tsx ../featurejs/
cp --path src/pages/Sales/CustomerManage/CustomerQuery/components/CustomerDetail.tsx ../featurejs/
cp --path src/pages/Sales/CustomerManage/CustomerQuery/components/LinkCustomer.tsx ../featurejs/
cp --path src/pages/Sales/CustomerManage/CustomerQuery/components/SalaryCalculation.tsx ../featurejs/
cp --path src/pages/pending/DSPay/components/SubFilePayCustDetail.tsx ../featurejs/
cp --path src/pages/Dashboard/Welcome.tsx ../featurejs/
cp --path src/pages/Dashboard/index.less ../featurejs/
cp --path src/pages/empwelfare/Ebmtransact/HospitalTransact/index.tsx ../featurejs/
cp --path src/pages/pending/EditEmpBankCardAdjust/relatedCustomerPop.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPC/FirstFeedBackSendManages/components/detail.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryBatchDelProduct/Forms/AddBatchDelProduct.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/ConfirmEmpOrderForAssigner/Forms/AddOrderForm.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/ConfirmEmpOrderForAssigner/Forms/AddOrderForm.tsx ../featurejs/
cp --path src/pages/externalSupplier/emporder/QueryChangeFeeTemplate/Forms/ApplyChangeTaskList.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryClientOrderForAdd/Forms/BatchEmpAddPop.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryMinBaseAdjustment/Forms/DetailMinBaseAdjustment.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForEdit/Forms/EditEmpOrder.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForEdit/Forms/EditEmpOrder.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForEdit/Forms/EditQuotationOnly.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryImpWrongOrderPro/Forms/ImportResultTable.tsx ../featurejs/
# cp --path src/pages/payroll/PayRollArchivesManage/Forms/MissionHistoryDetail.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryImpWrongOrderPro/Forms/OrderProFormDetail.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForPer/Forms/PerfectSearchQuery.ts ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForTransfer/Forms/TransferEmpOrder.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/BatchAlterEmpOrderManage/Forms/UpdateBatchAlterEmpOrderWin.tsx ../featurejs/
cp --path src/pages/emphiresep/quitManager/QueryExEmpOrderListForSepCon/Forms/onConfirm.tsx ../featurejs/
cp --path src/pages/emphiresep/quitManager/QueryExEmpOrderListForSepCon/Forms/onConfirm.tsx ../featurejs/
cp --path src/pages/finance/Gathering/UploadCash/components/Receipts.tsx ../featurejs/
cp --path src/pages/empwelfare/Ebmtransact/HospitalTransact/Forms/ConfirmHpTransact.tsx ../featurejs/
cp --path src/pages/empwelfare/Ebmtransact/HospitalTransact/Forms/addHpTransact.tsx ../featurejs/
cp --path src/pages/empwelfare/Ebmtransact/HospitalTransact/Forms/branchForms.tsx ../featurejs/
cp --path src/pages/empwelfare/LaborcontractManage/components/main.tsx ../featurejs/
cp --path src/pages/empwelfare/LockManage/components/QuerySSLock.tsx ../featurejs/
cp --path src/pages/emphiresep/sendorder/ManageTemplate/components/CustContract.tsx ../featurejs/
# cp --path src/pages/Mrpay/index.tsx ../featurejs/
cp --path src/pages/finance/Receive/Onecharges/components/OnechargesQuery.tsx ../featurejs/
# cp --path src/pages/payroll/PayRollArchivesManage/Forms/MissionHistory.tsx ../featurejs/
cp --path src/pages/pending/PaymentApply/components/DeliveryResult.tsx ../featurejs/
cp --path src/pages/infopublication/Policy/CalculationSsCust/index.tsx ../featurejs/
cp --path src/pages/infopublication/Policy/CalculationSsSale/index.tsx ../featurejs/
cp --path src/pages/infopublication/Policy/components/PolicyViewCodal/index.tsx ../featurejs/
cp --path src/pages/infopublication/Policy/components/SingleCityForm/index.tsx ../featurejs/
cp --path src/pages/basedata/Socialsecurity/ProductRatio/Form/MaintainProductRatioForm.tsx ../featurejs/
cp --path src/pages/empwelfare/ProvidentFund/QueryPfAdjustment/index.tsx ../featurejs/
cp --path src/pages/payroll/PureDataInterfaceManage/Forms/AddForms.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryAdjustment/Forms/DetailAdjustForms.tsx ../featurejs/
cp --path src/pages/finance/Receive/QueryBill/components/LinkDetailForBill.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryClientOrderForAdd/Forms/AddOrderForm.tsx ../featurejs/
cp --path src/pages/emphiresep/quitManager/QueryClientOrderForReduce/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForCon/Forms/AddOrderForm.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForGen/Forms/AddOrderForm.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForPer/Forms/AddOrderForm.tsx ../featurejs/
cp --path src/pages/emphiresep/quitManager/QueryEmpOrderListForSepApply/index.tsx ../featurejs/
cp --path src/pages/emphiresep/quitManager/QueryEmpOrderListForSepCon/index.tsx ../featurejs/
cp --path src/pages/externalSupplier/Receive/QueryExBill/components/LinkDetailForBill.tsx ../featurejs/
cp --path src/pages/emphiresep/quitManager/QueryExEmpOrderListForSepCon/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryImpWrongOrderPro/Forms/AddForms.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryImpWrongOrderPro/Forms/ImpOrderPro.tsx ../featurejs/
cp --path src/pages/empwelfare/ProvidentFund/QueryPfMinBaseAdjustment/index.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/QuerySocialMakeUp/components/index.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/QuerySocialSecurity/components/BaseInfo.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/QuerySocialSecurity/components/index.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/QuerySsMinBaseAdjustment/index.tsx ../featurejs/
cp --path src/pages/emphiresep/sendorder/QueryTransferAndSubcontract/index.tsx ../featurejs/
cp --path src/pages/Sales/Quotation/QuotationManage/Forms/QuotationAddForm.tsx ../featurejs/
cp --path src/pages/Sales/Quotation/QuotationTempManage/TablePop/index.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPCXN/SZFFJGXN/components/QueryThisTimeDetailWin.tsx ../featurejs/
cp --path src/pages/Sales/Contract/Manage/ContractApproveWin.tsx ../featurejs/
cp --path src/pages/Sales/Contract/Manage/ContractForm.tsx ../featurejs/
cp --path src/pages/Sales/Contract/Query/index.tsx ../featurejs/
cp --path src/pages/Sales/CustomerManage/CustomerQuery/index.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPC/SetPayResult/components/SetBatchPayResult.tsx ../featurejs/
cp --path src/pages/pending/SocialPay/components/FilePayApprove.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialProcess/SocialFundProcess.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialProcess/SocialProcessBatch.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialProcess/SocialProcessSingle.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialUpdateSelect/SocialFundUpdateSelect.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialUpdateSelect/SocialUpdateAll.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialUpdateSelect/SocialUpdateBasic.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/QuerySsAdjustment/index.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialApply/SocialFundApply.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialApply/components/tools.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialChange/SocialChange.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialChange/SocialFundChange.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialStop/SocialFundStop.tsx ../featurejs/
cp --path src/pages/infopublication/Policy/SurveySsPolicyCust/components/tabCom.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPCXN/XJFFPCXN/components/UpdatePayRollSendVirtualBatchWin.tsx ../featurejs/
cp --path src/pages/finance/Gathering/UploadCash/components/InvoiceEditModal.tsx ../featurejs/
cp --path src/pages/finance/Gathering/UploadCash/components/InvoiceEntry.tsx ../featurejs/
cp --path src/pages/finance/Gathering/UploadCash/components/VerifyDetail.tsx ../featurejs/
cp --path src/pages/payroll/deduction/UploadTaxDeduction/components/ImpDeduction.tsx ../featurejs/
cp --path src/pages/payroll/deduction/UploadTaxDeduction/components/UploadReslut.tsx ../featurejs/
cp --path src/pages/payroll/deduction/UploadTaxDeduction/components/index.tsx ../featurejs/
cp --path src/pages/User/ForgotPwd.tsx ../featurejs/
cp --path src/pages/User/Login.less ../featurejs/
cp --path src/pages/User/Login.tsx ../featurejs/
cp --path src/pages/User/MinicLogin.tsx ../featurejs/
# cp --path src/pages/User/clientLogin.less ../featurejs/
cp --path src/pages/payroll/XZFFPCXN/XJFFPCXN/components/ShowPayBatchDetail.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPC/XJFFPC/components/WageApplyPay.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPCXN/XJFFPCXN/components/PaySendDetail.tsx ../featurejs/
cp --path src/pages/basedata/SpecialSignerTitleMaintance/index.tsx ../featurejs/
# cp --path src/pages/payroll/JSDJ/TaxDeclarationManages/components/AddExportModal.tsx ../featurejs/
cp --path src/pages/basedata/SpecialSignerTitleMaintance/components/AddModal.tsx ../featurejs/
cp --path src/pages/basedata/SpecialSignerTitleMaintance/components/AddModal.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPCXN/XJFFPCXN/components/DelayPayRollSendBatchWin.tsx ../featurejs/
# cp --path src/pages/payroll/JSDJ/TaxDeclarationManages/components/MaintainExport.tsx ../featurejs/
cp --path src/pages/empwelfare/ProvidentFund/AdditionalSiQuery/components/NewAddSIBackPayImp.tsx ../featurejs/
cp --path src/pages/empwelfare/ProvidentFund/AdditionalSiQuery/components/NewAddSIBackPayImp.tsx ../featurejs/
cp --path src/pages/pending/BillingApproval/components/OpenInvoiceDetail.tsx ../featurejs/
# cp --path src/pages/payroll/JSDJ/TaxDeclarationManages/components/SendHistory.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPCXN/SZFFJGXN/components/SetBatchPayResultVirtual.tsx ../featurejs/
# cp --path src/pages/payroll/JSDJ/TaxDeclarationManages/components/ShowFailureModal.tsx ../featurejs/
cp --path src/pages/empwelfare/LaborcontractManage/components/UpdateLaborContract.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPC/XJFFPC/components/UpdatePayRollSendBatchWin.tsx ../featurejs/
cp --path src/pages/payroll/withholdAgent/components/WithholdAgentPopDanli.tsx ../featurejs/
cp --path src/pages/crm/cust/QueryIfmCustApprove/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryAdjustment/index.tsx ../featurejs/
cp --path src/pages/emphiresep/hire/HireClassify/index.tsx ../featurejs/
cp --path src/pages/emphiresep/quitManager/BatchQuitLite/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/ConfirmEmpOrderForAssigner/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/EmpBankCardMaintainance/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/EmployeeBaseInfoManage/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/NationWideEmpQuery/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpDetailInfo/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForCon/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForEdit/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmpOrderListForPer/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryEmployeeOrder/index.tsx ../featurejs/
cp --path src/pages/externalSupplier/emporder/QueryExEmpOrderListForEdit/index.tsx ../featurejs/
cp --path src/pages/externalSupplier/emporder/QueryExEmpOrderListForPer/index.tsx ../featurejs/
cp --path src/pages/externalSupplier/emporder/QueryExEmployeeOrder/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryImpOrderLite/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryImpWrongOrderPro/index.tsx ../featurejs/
cp --path src/pages/emphiresep/emporder/QueryMinBaseAdjustment/index.tsx ../featurejs/
cp --path src/pages/empwelfare/LaborcontractManage/index.tsx ../featurejs/
cp --path src/pages/empwelfare/PayManage/components/index.tsx ../featurejs/
cp --path src/pages/finance/Gathering/UploadCash/Query.tsx ../featurejs/
cp --path src/pages/finance/Receive/BillLockAndUnlock/index.tsx ../featurejs/
cp --path src/pages/finance/Receive/BillPrintReport/index.tsx ../featurejs/
cp --path src/pages/finance/Receive/CreateBill/index.tsx ../featurejs/
cp --path src/pages/finance/Receive/OrderBill/index.less ../featurejs/
cp --path src/pages/emphiresep/hire/HireClassify/components/CustMaterial.tsx ../featurejs/
cp --path src/pages/infopublication/Policy/SiteQuery/index.tsx ../featurejs/
cp --path src/pages/payroll/CountPayManage/index.tsx ../featurejs/
cp --path src/pages/payroll/DataInterfaceManage/Forms/AddForms.tsx ../featurejs/
cp --path src/pages/payroll/EditEmpBankCard/index.tsx ../featurejs/
cp --path src/pages/payroll/PayRollArchivesManage/index.tsx ../featurejs/
cp --path src/pages/payroll/PayTaxes/index.tsx ../featurejs/
cp --path src/pages/payroll/WithholdAgentLimitMaintance/index.tsx ../featurejs/
cp --path src/pages/payroll/XZDFS/SENDMAIL/index.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPC/QueryPayResult/index.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPC/XJFFPC/Create.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPC/XJFFPC/Query.tsx ../featurejs/
cp --path src/pages/payroll/XZFFPCXN/XJFFPCXN/Query.tsx ../featurejs/
cp --path src/pages/payroll/archives/CheckEmpTypeManages/index.tsx ../featurejs/
cp --path src/pages/payroll/archives/CheckHrieDtManages/index.tsx ../featurejs/
cp --path src/pages/payroll/bswjdc/index.tsx ../featurejs/
cp --path src/pages/payroll/deduction/CustTaxReport/index.tsx ../featurejs/
cp --path src/pages/payroll/withholdAgent/QueryWithholdAgent.tsx ../featurejs/
cp --path src/pages/pending/BillingApproval/utils/createHtml.ts ../featurejs/
cp --path src/pages/pending/DSPay/index.tsx ../featurejs/
cp --path src/pages/pending/EditEmpBankCardAdjust/index.tsx ../featurejs/
cp --path src/pages/pending/OnechargesApproval/index.tsx ../featurejs/
cp --path src/pages/pending/PaymentApply/components/Delivery.tsx ../featurejs/
cp --path src/pages/pending/PaymentApply/index.tsx ../featurejs/
cp --path src/pages/pending/PdFilePay/index.tsx ../featurejs/
cp --path src/pages/pending/QueryPrvdPayApprove/index.tsx ../featurejs/
cp --path src/pages/pending/QuotationSale/index.tsx ../featurejs/
cp --path src/pages/pending/UnlockWfl/UnlockPopAudit.tsx ../featurejs/
cp --path src/pages/pending/WagePay/components/WagePayApprove.tsx ../featurejs/
cp --path src/pages/pending/WagePay/index.tsx ../featurejs/
cp --path src/pages/pending/WagePayVirtual/index.tsx ../featurejs/
cp --path src/pages/externalSupplier/payApprove/prvdPayApply/components/SubFilePayBase.tsx ../featurejs/
cp --path src/pages/report/qualitycontrol/ComplainAppSelect/index.tsx ../featurejs/
cp --path src/pages/emphiresep/sendorder/CustomerSubcontract/ContractView.tsx ../featurejs/
cp --path src/pages/emphiresep/sendorder/CustomerSubcontract/index.tsx ../featurejs/
cp --path src/pages/emphiresep/sendorder/ManageTemplate/components/Detail.tsx ../featurejs/
cp --path src/pages/sysmanage/AllocateService/index.tsx ../featurejs/
cp --path src/pages/payroll/withholdAgent/components/WithholdAgentPop.tsx ../featurejs/
cp --path src/pages/empwelfare/Socialmanage/SocialApply/SocialApplyReceivable.tsx ../featurejs/
cp --path src/utils/forms/validate.ts ../featurejs/
cp --path src/utils/login/index.ts ../featurejs/
cp --path src/utils/methods/file.ts ../featurejs/
cp --path src/utils/methods/pagenation.ts ../featurejs/
cp --path src/utils/methods/times.ts ../featurejs/
cp --path src/utils/model/index.tsx ../featurejs/
cp --path src/utils/settings/payroll/payRollArchivesManage.ts ../featurejs/
cp --path src/utils/settings/welfaremanage/Socialmanage/socialApply.ts ../featurejs/
cp --path yarn.lock ../featurejs/
