# 全局API公共方法

严格来是 `pont`模块属于项目结构问题，其文件组织，管理与维护均有一定复杂度，但是其调用却异常简单，故在此仅对调用方式作简单介绍。

## `pont` 模块
`pont` 将两个全局属性绑定与`window` 对象上，分别是`defs`， `API`， 见：
```ts
// src/apis/index.ts
export function bindAPI(): void {
  (window as any).defs = defs;
  (window as any).API = API;
};

// src/app.ts
import { bindAPI } from './apis';
bindAPI()
```
`defs` ，是类型声明，位于`src/apis`目录下的各`api.d.ts`文件中，可以给IDE提供类型推导指示，非常有用。`defs` 统一了前后的对象类型声明，在每一个交互接口中，都提供了请求参数和返回参数的类型指示。
`API` ，是api方法所挂载的地方，位于`src/apis`目录下的各`mods`文件夹中，其目录组织方式，与后端java工程结构相同，也和文档结构相同。

由于是全局对象，所以调用api时，不在需要`import` 任何方法，以下是调用示例：
```ts
// src/pages/sysmanage/Role/index.tsx

  const { selectedRows } = this.state;
  if (selectedRows.length < 1) return msgCall({ code: 400, msg: '当前未选择任何角色哦' });
  const roleIdList = selectedRows.map((role: RoleTable): number => role.roleId);
  const roleIds = roleIdList.join(',');
  interface RoleUsage {
    roleId: number;
    amount: number;
  }
  API.admin.role.selectRoleUserAmount.request({ roleIds }).then(res => {
    if (res.code !== 200) return msgCall({ code: res.code, msg: res.msg });
    const counts = res.data as Array<RoleUsage>;
    const used: Array<number> = counts
      .filter(count => count.amount !== 0)
      .map(count => count.roleId);
    // ... ...
  });
```
`API.admin.role.selectRoleUserAmount.request` 虽然看起来很长，但是`pont`作了非常完善的`namespace`声明，供IDE检索并提供代码建议，减少实际调用时的负担。并且，由于`defs`的完善定义，`request` 所接收的参数，有完整的类型指示，将鼠标置于其上时，会看到IDE提供的类型指示。同样`then`回调中的 `res` 对象，也有明确的类型指引，特别是`res`对象中的`data`属性，与后端的数据类型定义完全一致，将鼠标置于其上时，会看到IDE提供的类型指示。

## 高级部分
本部分将介绍`pont`的管理与维护。